#!/usr/bin/env python3
"""
调试版瞬时突增检测器
Debug Transient Burst Detector
分析为什么没有检测到瞬时突增
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class DebugTransientDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # 更敏感的STFT参数
        self.stft_params = {
            'nperseg': 512,         # 更短的窗口 (约10ms)
            'noverlap': 384,        # 75%重叠
            'window': 'hann',
            'nfft': 512
        }
        
        # 更敏感的检测参数
        self.detection_params = {
            'energy_threshold_sigma': 2.0,     # 降低阈值
            'min_affected_bands': 0.2,         # 降低最少影响频段
            'max_burst_duration': 0.2,         # 增加最大持续时间
            'frequency_range': (100, 8000),
            'baseline_percentile': 20           # 提高基线百分位
        }
        
        print(f"调试版瞬时突增检测器初始化完成")
        print(f"时间分辨率: {self.stft_params['nperseg']/sample_rate*1000:.1f}ms")
        print(f"频率分辨率: {sample_rate/self.stft_params['nfft']:.1f}Hz")
    
    def debug_single_file(self, audio_path, plot=False):
        """调试单个文件的能量分布"""
        try:
            print(f"\n调试文件: {os.path.basename(audio_path)}")
            print("-" * 50)
            
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # 短时傅里叶变换
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 计算功率谱
            power_spectrogram = np.abs(Zxx) ** 2
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            power_spectrogram = power_spectrogram[freq_mask, :]
            
            # 分析能量分布
            self._analyze_energy_distribution(power_spectrogram, frequencies, times, audio_path, plot)
            
            return power_spectrogram, frequencies, times
            
        except Exception as e:
            print(f"处理失败: {e}")
            return None
    
    def _analyze_energy_distribution(self, power_spectrogram, frequencies, times, audio_path, plot=False):
        """分析能量分布"""
        # 计算每个时间片的总能量
        time_energy = np.sum(power_spectrogram, axis=0)
        
        # 统计信息
        print(f"时间片数量: {len(times)}")
        print(f"频率点数量: {len(frequencies)}")
        print(f"音频时长: {times[-1]:.2f}秒")
        
        # 能量统计
        print(f"\n能量统计:")
        print(f"  平均能量: {np.mean(time_energy):.6f}")
        print(f"  能量标准差: {np.std(time_energy):.6f}")
        print(f"  最大能量: {np.max(time_energy):.6f}")
        print(f"  最小能量: {np.min(time_energy):.6f}")
        print(f"  能量变异系数: {np.std(time_energy)/np.mean(time_energy):.3f}")
        
        # 基线能量分析
        baseline_energy = np.percentile(time_energy, self.detection_params['baseline_percentile'])
        energy_std = np.std(time_energy)
        burst_threshold = baseline_energy + self.detection_params['energy_threshold_sigma'] * energy_std
        
        print(f"\n阈值分析:")
        print(f"  基线能量 ({self.detection_params['baseline_percentile']}%): {baseline_energy:.6f}")
        print(f"  能量标准差: {energy_std:.6f}")
        print(f"  突增阈值 ({self.detection_params['energy_threshold_sigma']}σ): {burst_threshold:.6f}")
        
        # 检测能量突增
        burst_indices = np.where(time_energy > burst_threshold)[0]
        print(f"  超过阈值的时间片: {len(burst_indices)}/{len(time_energy)} ({len(burst_indices)/len(time_energy)*100:.1f}%)")
        
        if len(burst_indices) > 0:
            print(f"  突增时刻: {[f'{times[i]:.3f}s' for i in burst_indices[:5]]}")  # 只显示前5个
            
            # 分析突增的频率特征
            for i, burst_idx in enumerate(burst_indices[:3]):  # 只分析前3个突增
                print(f"\n  突增事件 {i+1} (时刻: {times[burst_idx]:.3f}s):")
                
                # 该时刻的频率功率分布
                burst_power = power_spectrogram[:, burst_idx]
                
                # 计算基线功率（排除突增时段）
                baseline_mask = np.ones(power_spectrogram.shape[1], dtype=bool)
                baseline_mask[max(0, burst_idx-2):min(len(time_energy), burst_idx+3)] = False
                baseline_power = np.mean(power_spectrogram[:, baseline_mask], axis=1)
                
                # 相对增幅
                relative_increase = (burst_power - baseline_power) / (baseline_power + 1e-12)
                
                # 受影响的频段
                significant_increase_mask = relative_increase > 1.0  # 增加100%以上
                affected_bands_ratio = np.sum(significant_increase_mask) / len(frequencies)
                
                print(f"    受影响频段比例: {affected_bands_ratio:.1%}")
                print(f"    最大相对增幅: {np.max(relative_increase):.2f}")
                print(f"    平均相对增幅: {np.mean(relative_increase[significant_increase_mask]):.2f}" if np.any(significant_increase_mask) else "    无显著增幅")
                
                if np.any(significant_increase_mask):
                    affected_freqs = frequencies[significant_increase_mask]
                    print(f"    受影响频率范围: {np.min(affected_freqs):.0f}-{np.max(affected_freqs):.0f}Hz")
        
        # 绘图（如果需要）
        if plot and len(burst_indices) > 0:
            self._plot_energy_analysis(time_energy, times, burst_threshold, burst_indices, audio_path)
    
    def _plot_energy_analysis(self, time_energy, times, burst_threshold, burst_indices, audio_path):
        """绘制能量分析图"""
        plt.figure(figsize=(12, 6))
        
        plt.subplot(2, 1, 1)
        plt.plot(times, time_energy, 'b-', alpha=0.7, label='总能量')
        plt.axhline(y=burst_threshold, color='r', linestyle='--', label=f'突增阈值')
        plt.scatter(times[burst_indices], time_energy[burst_indices], color='red', s=50, label='检测到的突增')
        plt.xlabel('时间 (秒)')
        plt.ylabel('能量')
        plt.title(f'能量时间序列 - {os.path.basename(audio_path)}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 1, 2)
        plt.plot(times, time_energy, 'b-', alpha=0.7)
        plt.yscale('log')
        plt.axhline(y=burst_threshold, color='r', linestyle='--')
        plt.scatter(times[burst_indices], time_energy[burst_indices], color='red', s=50)
        plt.xlabel('时间 (秒)')
        plt.ylabel('能量 (对数尺度)')
        plt.title('能量时间序列 (对数尺度)')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'energy_analysis_{os.path.basename(audio_path)}.png', dpi=150, bbox_inches='tight')
        plt.close()
        print(f"  能量分析图已保存: energy_analysis_{os.path.basename(audio_path)}.png")
    
    def debug_negative_samples(self, plot_first_few=True):
        """调试负样本"""
        print("\n" + "="*80)
        print("调试负样本瞬时突增特征")
        print("="*80)
        
        # 收集负样本
        neg_dir = "../test20250717/neg"
        negative_samples = []
        
        if os.path.exists(neg_dir):
            for file in os.listdir(neg_dir):
                if file.endswith('.wav'):
                    negative_samples.append(os.path.join(neg_dir, file))
        
        print(f"找到负样本: {len(negative_samples)}个")
        
        # 调试每个负样本
        for i, sample in enumerate(negative_samples):
            plot_this = plot_first_few and i < 3  # 只为前3个样本绘图
            result = self.debug_single_file(sample, plot=plot_this)
            
            if i < len(negative_samples) - 1:
                print("\n" + "="*50)
        
        return negative_samples
    
    def suggest_parameter_adjustments(self):
        """建议参数调整"""
        print("\n" + "="*80)
        print("参数调整建议")
        print("="*80)
        
        print("基于调试结果，建议以下参数调整:")
        print("1. 降低能量阈值: energy_threshold_sigma = 1.5 (当前: 2.0)")
        print("2. 降低最少影响频段: min_affected_bands = 0.1 (当前: 0.2)")
        print("3. 提高基线百分位: baseline_percentile = 30 (当前: 20)")
        print("4. 使用更短的时间窗口: nperseg = 256 (当前: 512)")
        print("5. 考虑使用相对能量变化而非绝对阈值")

def main():
    """主函数"""
    # 初始化调试检测器
    detector = DebugTransientDetector()
    
    # 调试负样本
    negative_samples = detector.debug_negative_samples(plot_first_few=True)
    
    # 参数调整建议
    detector.suggest_parameter_adjustments()
    
    return detector, negative_samples

if __name__ == "__main__":
    detector, negative_samples = main()
