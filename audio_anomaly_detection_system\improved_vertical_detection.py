import numpy as np
import librosa

def detect_vertical_line_interference_v2(S, freqs, f0, threshold_ratio=5, min_affected_freqs=10):
    """
    改进的竖线干扰检测
    
    竖线干扰特点：某时间点很多非主频和谐波的频点能量突然变高，形成垂直线条

    参数:
    S: 频谱矩阵 (freq_bins, time_frames)
    freqs: 频率数组
    f0: 主频率
    threshold_ratio: 能量突增阈值倍数
    min_affected_freqs: 最小受影响频率点数

    返回:
    vertical_score: 竖线干扰评分
    interference_times: 检测到的干扰时间点
    affected_freq_counts: 每个时间点受影响的频率数量
    """
    if S.shape[1] < 3:  # 时间帧太少
        return 0, [], []
    
    # 识别主频和谐波频率（需要排除的频率）
    harmonics = []
    for h in range(1, 8):  # 前7个谐波
        harm_freq = h * f0
        if harm_freq < freqs[-1]:
            # 更宽的谐波排除范围（±100Hz或±10%）
            harm_bandwidth = max(100, f0 * 0.1)
            harm_mask = np.abs(freqs - harm_freq) <= harm_bandwidth
            harmonics.extend(np.where(harm_mask)[0])

    # 去重并排序
    harmonic_indices = sorted(list(set(harmonics)))

    # 创建非主频非谐波的频率掩码
    non_harmonic_mask = np.ones(len(freqs), dtype=bool)
    if harmonic_indices:
        non_harmonic_mask[harmonic_indices] = False

    # 提取非主频非谐波的频谱
    S_non_harmonic = S[non_harmonic_mask, :]

    if S_non_harmonic.size == 0:
        return 0, [], []

    # 改进的基线计算：使用时间维度的统计
    # 计算每个频率bin在时间上的平均能量
    freq_avg_energy = np.mean(S_non_harmonic, axis=1)
    
    # 计算全局能量统计，用于更严格的阈值
    global_median = np.median(S_non_harmonic)
    global_std = np.std(S_non_harmonic)

    # 检测每个时间帧的异常能量突增
    interference_times = []
    affected_freq_counts = []
    intensity_scores = []

    for t in range(S.shape[1]):
        frame_energy = S_non_harmonic[:, t]

        # 方法1：相对于频率平均能量的突增
        energy_ratios = frame_energy / (freq_avg_energy + 1e-12)
        method1_increases = energy_ratios > threshold_ratio
        
        # 方法2：相对于全局统计的突增
        global_threshold = global_median + threshold_ratio * global_std
        method2_increases = frame_energy > global_threshold
        
        # 结合两种方法
        significant_increases = method1_increases & method2_increases
        affected_freqs = np.sum(significant_increases)

        # 计算突增强度
        if affected_freqs > 0:
            intensity = np.mean(energy_ratios[significant_increases])
        else:
            intensity = 0

        # 如果受影响的频率点足够多，认为是竖线干扰
        if affected_freqs >= min_affected_freqs:
            interference_times.append(t)
            affected_freq_counts.append(affected_freqs)
            intensity_scores.append(intensity)

    # 计算竖线干扰评分
    if len(interference_times) == 0:
        vertical_score = 0
    else:
        # 基于多个因素的综合评分
        interference_ratio = len(interference_times) / S.shape[1]
        avg_affected_freqs = np.mean(affected_freq_counts)
        max_possible_affected = np.sum(non_harmonic_mask)
        avg_intensity = np.mean(intensity_scores)

        # 更严格的评分标准
        freq_ratio_score = min(avg_affected_freqs / max_possible_affected, 1.0)
        time_ratio_score = interference_ratio
        intensity_score = min((avg_intensity - threshold_ratio) / threshold_ratio, 1.0)

        # 综合评分，要求同时满足多个条件
        vertical_score = min(
            freq_ratio_score * 0.4 + 
            time_ratio_score * 0.3 + 
            intensity_score * 0.3,
            1.0
        )

        # 额外的严格条件：如果干扰太普遍，可能是正常的宽频信号
        if interference_ratio > 0.8 and avg_intensity < threshold_ratio * 2:
            vertical_score *= 0.5  # 降低评分
            
        # 如果受影响频率数太少，也降低评分
        if avg_affected_freqs < min_affected_freqs * 2:
            vertical_score *= 0.7

    return vertical_score, interference_times, affected_freq_counts

def test_improved_detection():
    """
    测试改进的竖线干扰检测
    """
    import os
    from freq_split import split_freq_steps
    
    # 测试样本
    samples = [
        ("dataset/pos/录音_步进扫频_100Hz至20000Hz_20250714_152023_156.wav", "正样本", 156),
        ("dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav", "负样本", 153)
    ]
    
    for sample_path, label, min_dur in samples:
        if not os.path.exists(sample_path):
            print(f"[WARN] 文件不存在: {sample_path}")
            continue
            
        print(f"\n[INFO] 测试 {label}: {os.path.basename(sample_path)}")
        
        # 分割频段
        step_bounds, freq_table = split_freq_steps(
            sample_path,
            min_duration=min_dur,
            energy_threshold_db=-45,
            plot=False
        )
        
        # 加载音频
        y, sr = librosa.load(sample_path, sr=None)
        
        # 测试几个频段
        test_segments = [10, 20, 30, 40, 50]
        scores = []
        
        for i in test_segments:
            if i >= len(step_bounds) or i >= len(freq_table):
                continue
                
            t0, t1 = step_bounds[i]
            f0 = freq_table[i]
            
            # 提取音频段
            L = t1 - t0
            seg_start = t0 + 0.1 * L
            seg_end = t1 - 0.1 * L
            y_seg = y[int(seg_start*sr):int(seg_end*sr)]
            
            if len(y_seg) < 256:
                continue
            
            # STFT分析
            n_fft = min(2048, len(y_seg))
            S = np.abs(librosa.stft(y_seg, n_fft=n_fft, hop_length=512))**2
            freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
            
            # 检测竖线干扰
            vertical_score, interference_times, affected_freq_counts = detect_vertical_line_interference_v2(
                S, freqs, f0, threshold_ratio=5, min_affected_freqs=10
            )
            
            scores.append(vertical_score)
            
            if i == test_segments[0]:  # 详细输出第一个测试段
                print(f"  频段 {i}: {f0:.1f}Hz")
                print(f"    竖线干扰评分: {vertical_score:.3f}")
                print(f"    干扰时间帧数: {len(interference_times)}")
                print(f"    总时间帧数: {S.shape[1]}")
                if affected_freq_counts:
                    print(f"    平均受影响频率数: {np.mean(affected_freq_counts):.1f}")
        
        if scores:
            print(f"  平均竖线干扰评分: {np.mean(scores):.3f}")
            print(f"  最大竖线干扰评分: {np.max(scores):.3f}")
            print(f"  高干扰频段数 (>0.3): {sum(s > 0.3 for s in scores)}/{len(scores)}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试改进的竖线干扰检测")
    print("=" * 60)
    
    test_improved_detection()
