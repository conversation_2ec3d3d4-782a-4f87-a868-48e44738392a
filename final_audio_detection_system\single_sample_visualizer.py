#!/usr/bin/env python3
"""
单样本竖线检测可视化器
Single Sample Vertical Line Detection Visualizer
对单个样本进行详细的竖线检测分析和可视化
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SingleSampleVisualizer:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 竖线检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.3,
            'min_peak_prominence_ratio': 0.3,
            'min_peak_height_ratio': 0.4,
            'min_frequency_span': 1000,
            'min_line_strength': 0.5,
        }
        
        print(f"单样本竖线检测可视化器初始化完成")
    
    def analyze_single_sample(self, audio_path):
        """分析单个样本的竖线检测"""
        print(f"\n分析样本: {os.path.basename(audio_path)}")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            print(f"频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
            
            # 使用freq_split进行频段分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path, **self.freq_split_params, plot=False
            )
            
            if len(step_boundaries) == 0:
                print("freq_split分割失败")
                return None
            
            print(f"freq_split分割: {len(step_boundaries)}个频段")
            
            # 分析每个频段的竖线
            all_vertical_lines = []
            segment_analysis = []
            
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                segment_result = self._analyze_segment_detailed(
                    power_db, frequencies, times, seg_start_time, seg_end_time, 
                    seg_idx, freq_table[seg_idx] if seg_idx < len(freq_table) else None
                )
                
                segment_analysis.append(segment_result)
                
                if segment_result['vertical_lines']:
                    all_vertical_lines.extend(segment_result['vertical_lines'])
            
            # 可视化结果
            self._visualize_complete_analysis(
                power_db, frequencies, times, step_boundaries, freq_table,
                all_vertical_lines, segment_analysis, audio_path
            )
            
            # 统计结果
            total_lines = len(all_vertical_lines)
            anomalous_segments = sum(1 for seg in segment_analysis if seg['vertical_lines'])
            
            print(f"\n检测结果统计:")
            print(f"  总频段数: {len(step_boundaries)}")
            print(f"  异常频段数: {anomalous_segments}")
            print(f"  总竖线数: {total_lines}")
            print(f"  异常频段比例: {anomalous_segments/len(step_boundaries)*100:.1f}%")
            
            if all_vertical_lines:
                strengths = [line['line_strength'] for line in all_vertical_lines]
                print(f"  竖线强度范围: {min(strengths):.3f} - {max(strengths):.3f}")
                print(f"  平均竖线强度: {np.mean(strengths):.3f}")
            
            return {
                'power_db': power_db,
                'frequencies': frequencies,
                'times': times,
                'step_boundaries': step_boundaries,
                'freq_table': freq_table,
                'all_vertical_lines': all_vertical_lines,
                'segment_analysis': segment_analysis,
                'total_lines': total_lines,
                'anomalous_segments': anomalous_segments
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _analyze_segment_detailed(self, power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq):
        """详细分析单个频段"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'expected_frequency': expected_freq,
            'vertical_lines': [],
            'energy_stats': {},
            'peak_info': {}
        }
        
        # 找到频段对应的时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除频段边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            return result
        
        # 提取频段核心部分的数据
        segment_power = power_db[:, seg_core_start:seg_core_end]
        segment_times = times[seg_core_start:seg_core_end]
        
        # 计算时间维度总能量
        total_energy = np.sum(segment_power, axis=0)
        
        if len(total_energy) == 0:
            return result
        
        # 记录能量统计
        result['energy_stats'] = {
            'min_energy': np.min(total_energy),
            'max_energy': np.max(total_energy),
            'mean_energy': np.mean(total_energy),
            'std_energy': np.std(total_energy),
            'energy_range': np.max(total_energy) - np.min(total_energy)
        }
        
        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        
        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        
        if energy_range < 100:  # 能量变化太小
            return result
        
        min_height = np.min(smoothed_energy) + energy_range * self.detection_params['min_peak_height_ratio']
        min_prominence = energy_range * self.detection_params['min_peak_prominence_ratio']
        
        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy, 
                                     height=min_height,
                                     distance=2,
                                     prominence=min_prominence)
        
        # 记录峰值信息
        result['peak_info'] = {
            'total_peaks': len(peaks),
            'min_height': min_height,
            'min_prominence': min_prominence,
            'energy_range': energy_range,
            'peak_times': [segment_times[p] for p in peaks] if len(peaks) > 0 else []
        }
        
        # 分析每个峰值
        for peak_idx in peaks:
            if peak_idx < len(segment_times):
                peak_time = segment_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = segment_power[:, peak_idx]
                
                # 分析该时刻的频谱是否为竖线
                line_analysis = self._analyze_spectrum_for_line_detailed(
                    peak_power_spectrum, frequencies, peak_time, peak_energy, seg_idx
                )
                
                if line_analysis and line_analysis['line_strength'] >= self.detection_params['min_line_strength']:
                    result['vertical_lines'].append(line_analysis)
        
        return result
    
    def _analyze_spectrum_for_line_detailed(self, power_spectrum, frequencies, peak_time, peak_energy, seg_idx):
        """详细分析频谱是否为竖线"""
        # 计算功率分布统计
        power_mean = np.mean(power_spectrum)
        power_std = np.std(power_spectrum)
        power_median = np.median(power_spectrum)
        
        # 使用多种方法确定高能量频率点
        methods = {
            'percentile_90': np.percentile(power_spectrum, 90),
            'mean_plus_std': power_mean + 1.0 * power_std,
            'median_plus_range': power_median + (np.max(power_spectrum) - power_median) * 0.3
        }
        
        best_method = None
        best_score = 0
        
        for method_name, threshold in methods.items():
            high_energy_mask = power_spectrum > threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) >= 3:
                # 计算频率跨度
                freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                freq_ratio = len(high_energy_indices) / len(frequencies)
                
                # 计算连续性分数
                continuous_segments = self._find_continuous_segments(high_energy_indices)
                continuity_score = sum(len(seg) for seg in continuous_segments) / len(high_energy_indices) if high_energy_indices.size > 0 else 0
                
                # 综合评分
                span_score = min(1.0, freq_span / 5000.0)
                ratio_score = min(1.0, freq_ratio / 0.3)
                total_score = span_score * ratio_score * continuity_score
                
                if (total_score > best_score and 
                    freq_span >= self.detection_params['min_frequency_span']):
                    best_score = total_score
                    best_method = {
                        'name': method_name,
                        'threshold': threshold,
                        'high_energy_indices': high_energy_indices,
                        'freq_span': freq_span,
                        'freq_ratio': freq_ratio,
                        'total_score': total_score,
                        'continuous_segments': continuous_segments
                    }
        
        if best_method and best_score > 0.05:
            return {
                'segment_index': seg_idx,
                'time': peak_time,
                'peak_energy': peak_energy,
                'frequency_span': best_method['freq_span'],
                'frequency_ratio': best_method['freq_ratio'],
                'line_strength': best_score,
                'detection_method': f"segment_{best_method['name']}",
                'threshold': best_method['threshold'],
                'high_energy_indices': best_method['high_energy_indices'],
                'continuous_segments': best_method['continuous_segments'],
                'max_power': np.max(power_spectrum[best_method['high_energy_indices']]),
                'mean_power': np.mean(power_spectrum[best_method['high_energy_indices']]),
                'power_stats': {
                    'power_mean': power_mean,
                    'power_std': power_std,
                    'power_median': power_median
                }
            }
        
        return None
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _visualize_complete_analysis(self, power_db, frequencies, times, step_boundaries, freq_table,
                                   all_vertical_lines, segment_analysis, audio_path):
        """完整的可视化分析"""
        print(f"\n生成完整可视化分析...")
        
        # 创建大图表
        fig = plt.figure(figsize=(20, 16))
        
        # 创建网格布局
        gs = fig.add_gridspec(4, 3, height_ratios=[2, 1, 1, 1], width_ratios=[2, 1, 1])
        
        # 1. 主频谱图 + 竖线标记
        ax_main = fig.add_subplot(gs[0, :])
        im = ax_main.imshow(power_db, aspect='auto', origin='lower', 
                           extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                           cmap='viridis')
        
        # 标记freq_split分割线
        for i, (start_time, end_time) in enumerate(step_boundaries[::5]):  # 每5个显示一条
            ax_main.axvline(x=start_time, color='white', alpha=0.3, linewidth=0.5)
        
        # 标记检测到的竖线
        colors = ['red', 'orange', 'yellow', 'cyan', 'magenta']
        for i, line in enumerate(all_vertical_lines):
            color = colors[i % len(colors)]
            ax_main.axvline(x=line['time'], color=color, linewidth=2, alpha=0.8)
            
            # 标记竖线的频率范围
            if 'high_energy_indices' in line:
                freq_indices = line['high_energy_indices']
                freq_range = [frequencies[freq_indices[0]], frequencies[freq_indices[-1]]]
                ax_main.plot([line['time'], line['time']], freq_range, 
                           color=color, linewidth=4, alpha=0.6)
        
        ax_main.set_title(f'完整频谱分析: {os.path.basename(audio_path)}\n'
                         f'检测到 {len(all_vertical_lines)} 条竖线', fontsize=14)
        ax_main.set_xlabel('时间 (s)')
        ax_main.set_ylabel('频率 (Hz)')
        plt.colorbar(im, ax=ax_main, label='功率 (dB)')
        
        # 2. 竖线时间分布
        ax_time = fig.add_subplot(gs[1, 0])
        if all_vertical_lines:
            line_times = [line['time'] for line in all_vertical_lines]
            line_strengths = [line['line_strength'] for line in all_vertical_lines]
            
            scatter = ax_time.scatter(line_times, line_strengths, c=line_strengths, 
                                    cmap='Reds', s=100, alpha=0.7)
            ax_time.set_title('竖线时间分布')
            ax_time.set_xlabel('时间 (s)')
            ax_time.set_ylabel('竖线强度')
            ax_time.grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=ax_time, label='强度')
        else:
            ax_time.text(0.5, 0.5, '未检测到竖线', ha='center', va='center', 
                        transform=ax_time.transAxes, fontsize=12)
            ax_time.set_title('竖线时间分布')
        
        # 3. 频段异常统计
        ax_seg = fig.add_subplot(gs[1, 1])
        anomalous_segments = [seg for seg in segment_analysis if seg['vertical_lines']]
        normal_segments = [seg for seg in segment_analysis if not seg['vertical_lines']]
        
        labels = ['正常频段', '异常频段']
        sizes = [len(normal_segments), len(anomalous_segments)]
        colors_pie = ['lightblue', 'lightcoral']
        
        if sum(sizes) > 0:
            ax_seg.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
            ax_seg.set_title(f'频段异常分布\n总计: {len(segment_analysis)}个频段')
        else:
            ax_seg.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax_seg.transAxes)
        
        # 4. 竖线强度分布
        ax_strength = fig.add_subplot(gs[1, 2])
        if all_vertical_lines:
            strengths = [line['line_strength'] for line in all_vertical_lines]
            ax_strength.hist(strengths, bins=10, alpha=0.7, color='orange', edgecolor='black')
            ax_strength.axvline(x=self.detection_params['min_line_strength'], 
                              color='red', linestyle='--', label='检测阈值')
            ax_strength.set_title('竖线强度分布')
            ax_strength.set_xlabel('强度')
            ax_strength.set_ylabel('数量')
            ax_strength.legend()
            ax_strength.grid(True, alpha=0.3)
        else:
            ax_strength.text(0.5, 0.5, '无竖线数据', ha='center', va='center', 
                           transform=ax_strength.transAxes)
            ax_strength.set_title('竖线强度分布')
        
        # 5. 频段能量分析
        ax_energy = fig.add_subplot(gs[2, :])
        segment_times = [(seg['start_time'] + seg['end_time'])/2 for seg in segment_analysis]
        segment_energies = [seg['energy_stats'].get('max_energy', 0) for seg in segment_analysis]
        segment_colors = ['red' if seg['vertical_lines'] else 'blue' for seg in segment_analysis]
        
        scatter_energy = ax_energy.scatter(segment_times, segment_energies, c=segment_colors, alpha=0.6)
        ax_energy.set_title('各频段最大能量分布')
        ax_energy.set_xlabel('频段中心时间 (s)')
        ax_energy.set_ylabel('最大能量 (dB)')
        ax_energy.grid(True, alpha=0.3)
        
        # 添加图例
        red_patch = plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='异常频段')
        blue_patch = plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='正常频段')
        ax_energy.legend(handles=[red_patch, blue_patch])
        
        # 6. 详细统计信息
        ax_stats = fig.add_subplot(gs[3, :])
        ax_stats.axis('off')
        
        # 准备统计文本
        stats_text = f"""
检测统计信息:
• 总频段数: {len(segment_analysis)}
• 异常频段数: {len(anomalous_segments)} ({len(anomalous_segments)/len(segment_analysis)*100:.1f}%)
• 总竖线数: {len(all_vertical_lines)}
• 检测参数: 边界排除={self.detection_params['edge_exclude_ratio']*100:.0f}%, 最小强度={self.detection_params['min_line_strength']:.1f}
"""
        
        if all_vertical_lines:
            strengths = [line['line_strength'] for line in all_vertical_lines]
            freq_spans = [line['frequency_span'] for line in all_vertical_lines]
            stats_text += f"""
• 竖线强度范围: {min(strengths):.3f} - {max(strengths):.3f}
• 平均竖线强度: {np.mean(strengths):.3f}
• 频率跨度范围: {min(freq_spans):.0f}Hz - {max(freq_spans):.0f}Hz
• 平均频率跨度: {np.mean(freq_spans):.0f}Hz
"""
        
        ax_stats.text(0.05, 0.95, stats_text, transform=ax_stats.transAxes, 
                     fontsize=11, verticalalignment='top', fontfamily='monospace',
                     bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'single_sample_analysis_{os.path.splitext(os.path.basename(audio_path))[0]}.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  可视化结果已保存: {output_filename}")
        
        plt.show()

def main():
    """主函数"""
    # 初始化可视化器
    visualizer = SingleSampleVisualizer()
    
    # 分析指定样本 - 可以修改这里来分析不同的样本
    audio_path = "../test20250717/pos/sd1_1.wav"
    
    # 执行分析
    results = visualizer.analyze_single_sample(audio_path)
    
    if results:
        print(f"\n分析完成！")
        print(f"可视化图表已生成，请查看详细的竖线检测结果。")
    else:
        print(f"分析失败！")
    
    return visualizer, results

if __name__ == "__main__":
    visualizer, results = main()
