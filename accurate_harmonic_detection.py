#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确谐波检测：提高谐波识别准确性，避免误识别
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import find_peaks

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def accurate_harmonic_detection():
    """精确谐波检测"""
    print("🎯 精确谐波检测 - 低音戳洞文件前4个频段")
    print("="*50)
    print("改进算法:")
    print("1. 严格的SNR阈值 (15-20dB)")
    print("2. 峰值锐度检查")
    print("3. 频率精度验证")
    print("4. 相对功率限制")
    print("5. 局部背景噪声分析")
    print("="*50)
    
    # 低音戳洞文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析文件
    hole_data = analyze_with_strict_criteria(hole_file)
    
    if hole_data:
        create_accurate_visualization(hole_data)

def analyze_with_strict_criteria(audio_path):
    """使用严格标准分析"""
    try:
        print(f"\n使用严格标准分析...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前4段
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 超高分辨率FFT分析
            fft_size = 65536  # 64k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power_db = 20 * np.log10(magnitude + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            print(f"  分析频段 {seg_idx} ({expected_freq:.0f}Hz)")
            
            # 1. 精确主频分析
            fundamental_analysis = analyze_fundamental_precise(display_freqs, display_power_db, expected_freq)
            
            # 2. 精确噪声底噪估计
            noise_analysis = estimate_noise_precise(display_freqs, display_power_db, fundamental_analysis)
            
            # 3. 严格谐波检测
            harmonic_analysis = detect_harmonics_strict(
                display_freqs, display_power_db, fundamental_analysis, noise_analysis
            )
            
            # 4. 频谱峰值分析 (用于对比)
            peak_analysis = analyze_all_peaks(display_freqs, display_power_db, noise_analysis)
            
            if fundamental_analysis:
                print(f"    主频: {fundamental_analysis['freq']:.3f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
                print(f"    噪声底噪: {noise_analysis['noise_floor_db']:.1f}dB")
                print(f"    严格谐波: {len(harmonic_analysis)}个")
                print(f"    所有峰值: {len(peak_analysis)}个")
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': display_freqs,
                'power_db': display_power_db,
                'fundamental_analysis': fundamental_analysis,
                'noise_analysis': noise_analysis,
                'harmonic_analysis': harmonic_analysis,
                'peak_analysis': peak_analysis,
                'freq_resolution': display_freqs[1] - display_freqs[0]
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def analyze_fundamental_precise(freqs, power_db, expected_freq):
    """精确主频分析"""
    
    # 精确搜索范围
    search_bandwidth = 1.0  # ±1Hz
    search_mask = (freqs >= expected_freq - search_bandwidth) & (freqs <= expected_freq + search_bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power_db[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power_db = power_db[actual_idx]
    
    # 计算峰值宽度 (3dB带宽)
    half_power_db = fundamental_power_db - 3
    left_idx = actual_idx
    right_idx = actual_idx
    
    while left_idx > 0 and power_db[left_idx] > half_power_db:
        left_idx -= 1
    while right_idx < len(power_db) - 1 and power_db[right_idx] > half_power_db:
        right_idx += 1
    
    peak_width_hz = freqs[right_idx] - freqs[left_idx]
    
    # 计算峰值锐度
    peak_sharpness = fundamental_power_db / np.mean(search_powers)
    
    return {
        'freq': fundamental_freq,
        'power_db': fundamental_power_db,
        'freq_error': fundamental_freq - expected_freq,
        'peak_width_hz': peak_width_hz,
        'peak_sharpness': peak_sharpness,
        'index': actual_idx
    }

def estimate_noise_precise(freqs, power_db, fundamental_analysis):
    """精确噪声底噪估计"""
    
    if not fundamental_analysis:
        return {'noise_floor_db': -80}
    
    fundamental_freq = fundamental_analysis['freq']
    
    # 创建噪声掩码 - 排除主频和可能的谐波位置
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频±3Hz
    main_exclude = (freqs >= fundamental_freq - 3) & (freqs <= fundamental_freq + 3)
    noise_mask &= ~main_exclude
    
    # 排除前20个谐波位置±3Hz
    for order in range(2, 21):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 3) & (freqs <= harmonic_freq + 3)
            noise_mask &= ~harm_exclude
    
    if np.any(noise_mask):
        noise_powers = power_db[noise_mask]
        # 使用更保守的噪声估计
        noise_floor_db = np.percentile(noise_powers, 10)  # 10th percentile
        noise_std_db = np.std(noise_powers)
        
        return {
            'noise_floor_db': noise_floor_db,
            'noise_std_db': noise_std_db,
            'noise_sample_count': np.sum(noise_mask)
        }
    else:
        return {
            'noise_floor_db': -80,
            'noise_std_db': 0,
            'noise_sample_count': 0
        }

def detect_harmonics_strict(freqs, power_db, fundamental_analysis, noise_analysis):
    """严格的谐波检测"""
    
    if not fundamental_analysis:
        return []
    
    fundamental_freq = fundamental_analysis['freq']
    fundamental_power_db = fundamental_analysis['power_db']
    noise_floor_db = noise_analysis['noise_floor_db']
    
    detected_harmonics = []
    
    print(f"      严格谐波检测 (噪声底噪: {noise_floor_db:.1f}dB):")
    
    for order in range(2, 21):  # 检测2-20次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 精确搜索带宽
        search_bandwidth = 2.0  # ±2Hz
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power_db[search_mask]
        search_freqs = freqs[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power_db = power_db[actual_idx]
        
        # 计算各种指标
        snr_db = harmonic_power_db - noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 计算峰值锐度
        local_background = np.median(search_powers)
        peak_prominence = harmonic_power_db - local_background
        
        # 计算局部SNR (相对于局部背景)
        local_snr = harmonic_power_db - local_background
        
        # 严格的谐波判断条件
        conditions = {
            'snr_sufficient': snr_db >= 15.0,  # 严格SNR阈值 15dB
            'relative_power_ok': relative_power_db >= -40.0,  # 相对主频不超过40dB衰减
            'frequency_accurate': abs(freq_error) <= 1.0,  # 频率误差不超过1Hz
            'peak_prominent': peak_prominence >= 8.0,  # 峰值突出度至少8dB
            'local_snr_ok': local_snr >= 10.0  # 局部SNR至少10dB
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'peak_prominence': peak_prominence,
                'local_snr': local_snr,
                'index': actual_idx
            })
            
            print(f"        ✅ {order:2d}次谐波: {actual_freq:7.1f}Hz, {harmonic_power_db:6.1f}dB, "
                  f"SNR={snr_db:5.1f}dB, 突出度={peak_prominence:5.1f}dB")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"        ❌ {order:2d}次谐波: {actual_freq:7.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            SNR={snr_db:5.1f}dB, 相对功率={relative_power_db:5.1f}dB, "
                  f"误差={freq_error:+5.1f}Hz, 突出度={peak_prominence:5.1f}dB")
    
    return detected_harmonics

def analyze_all_peaks(freqs, power_db, noise_analysis):
    """分析所有频谱峰值 (用于对比)"""
    
    noise_floor_db = noise_analysis['noise_floor_db']
    
    # 使用scipy找峰值
    peaks, properties = find_peaks(
        power_db, 
        height=noise_floor_db + 10,  # 高于噪声底噪10dB
        distance=int(5.0 / (freqs[1] - freqs[0])),  # 最小间距5Hz
        prominence=5.0  # 最小突出度5dB
    )
    
    peak_analysis = []
    for i, peak_idx in enumerate(peaks):
        if peak_idx < len(freqs):
            peak_freq = freqs[peak_idx]
            peak_power_db = power_db[peak_idx]
            peak_prominence = properties['prominences'][i]
            peak_snr = peak_power_db - noise_floor_db
            
            peak_analysis.append({
                'freq': peak_freq,
                'power_db': peak_power_db,
                'prominence': peak_prominence,
                'snr_db': peak_snr
            })
    
    return peak_analysis

def create_accurate_visualization(hole_data):
    """创建精确谐波检测可视化"""
    print(f"\n🎨 生成精确谐波检测可视化...")
    
    # 创建图表 (4行1列)
    fig, axes = plt.subplots(4, 1, figsize=(20, 24))
    fig.suptitle(f'Accurate Harmonic Detection - Low Frequency Hole Sample\nFile: {hole_data["filename"]}', 
                 fontsize=16, fontweight='bold')
    
    segments = hole_data['segments']
    
    # 谐波颜色
    harmonic_colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, segment in enumerate(segments):
        ax = axes[i]
        
        fundamental_analysis = segment['fundamental_analysis']
        harmonic_analysis = segment['harmonic_analysis']
        noise_analysis = segment['noise_analysis']
        peak_analysis = segment['peak_analysis']
        
        # 绘制频谱
        ax.plot(segment['freqs'], segment['power_db'], 'k-', linewidth=0.8, alpha=0.7, 
               label='Spectrum')
        
        # 标记噪声底噪
        noise_floor_db = noise_analysis['noise_floor_db']
        ax.axhline(y=noise_floor_db, color='gray', linestyle='--', alpha=0.6, 
                  label=f'Noise Floor {noise_floor_db:.1f}dB')
        
        # 标记严格SNR阈值线
        strict_snr_line = noise_floor_db + 15
        ax.axhline(y=strict_snr_line, color='orange', linestyle=':', alpha=0.8,
                  label=f'Strict SNR Threshold {strict_snr_line:.1f}dB')
        
        # 标记主频
        if fundamental_analysis:
            main_freq = fundamental_analysis['freq']
            main_power = fundamental_analysis['power_db']
            
            ax.plot(main_freq, main_power, 'ro', markersize=12, 
                   label=f'Fundamental {main_freq:.2f}Hz ({main_power:.1f}dB)')
            
            # 主频标注
            ax.annotate(f'Fundamental\n{main_freq:.2f}Hz\n{main_power:.1f}dB\nWidth: {fundamental_analysis["peak_width_hz"]:.3f}Hz', 
                       xy=(main_freq, main_power), 
                       xytext=(main_freq + 1000, main_power + 10),
                       ha='left', va='bottom', fontweight='bold', color='red', fontsize=10,
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记所有峰值 (灰色小点)
        for peak in peak_analysis:
            ax.plot(peak['freq'], peak['power_db'], 'o', color='lightgray', 
                   markersize=3, alpha=0.5)
        
        # 标记严格检测的谐波 (大标记)
        for j, harmonic in enumerate(harmonic_analysis):
            color = harmonic_colors[j % len(harmonic_colors)]
            order = harmonic['order']
            freq = harmonic['freq']
            power_db = harmonic['power_db']
            snr_db = harmonic['snr_db']
            prominence = harmonic['peak_prominence']
            
            # 谐波标记
            ax.plot(freq, power_db, 's', color=color, markersize=10, alpha=0.9,
                   label=f'{order}th Harmonic {freq:.0f}Hz' if j < 8 else "")
            
            # 标注所有严格检测的谐波
            ax.annotate(f'{order}th\n{freq:.0f}Hz\nSNR={snr_db:.1f}dB\nProm={prominence:.1f}dB', 
                       xy=(freq, power_db), 
                       xytext=(freq, power_db + 12),
                       ha='center', va='bottom', fontsize=8, color=color, fontweight='bold',
                       arrowprops=dict(arrowstyle='->', color=color, lw=1.5),
                       bbox=dict(boxstyle='round,pad=0.2', facecolor=color, alpha=0.3))
        
        # 设置图表属性
        expected_freq = segment['expected_freq']
        ax.set_title(f'Segment {segment["seg_idx"]} - Expected: {expected_freq:.0f}Hz, '
                    f'Strict Harmonics: {len(harmonic_analysis)}, All Peaks: {len(peak_analysis)}', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power (dB)')
        
        # 设置x轴范围和刻度
        ax.set_xlim(0, 20000)
        ax.set_xticks([0, 1000, 2000, 5000, 10000, 15000, 20000])
        ax.set_xticklabels(['0', '1k', '2k', '5k', '10k', '15k', '20k'])
        
        # 设置y轴范围
        y_min = np.percentile(segment['power_db'], 1) - 5
        y_max = np.percentile(segment['power_db'], 99) + 20
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8, loc='upper right', ncol=2)
        ax.grid(True, alpha=0.3)
        
        # 添加详细信息文本框
        info_text = f"Strict Detection Criteria:\n"
        info_text += f"• SNR ≥ 15dB (vs noise floor)\n"
        info_text += f"• Relative Power ≥ -40dB (vs fundamental)\n"
        info_text += f"• Frequency Error ≤ 1Hz\n"
        info_text += f"• Peak Prominence ≥ 8dB\n"
        info_text += f"• Local SNR ≥ 10dB\n\n"
        
        if fundamental_analysis:
            info_text += f"Results:\n"
            info_text += f"Fundamental: {fundamental_analysis['freq']:.3f}Hz\n"
            info_text += f"Frequency Error: {fundamental_analysis['freq_error']:+.3f}Hz\n"
            info_text += f"Peak Width: {fundamental_analysis['peak_width_hz']:.3f}Hz\n"
            info_text += f"Strict Harmonics: {len(harmonic_analysis)}\n"
            info_text += f"All Peaks: {len(peak_analysis)}\n"
            info_text += f"Detection Rate: {len(harmonic_analysis)}/{len(peak_analysis)} = {len(harmonic_analysis)/max(1,len(peak_analysis))*100:.1f}%"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.4', facecolor='lightgreen', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'accurate_harmonic_detection.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 精确谐波检测可视化已保存: {filename}")
    plt.close()
    
    # 打印对比统计
    print(f"\n📊 精确检测 vs 所有峰值对比:")
    print(f"{'='*70}")
    
    for i, segment in enumerate(segments):
        harmonic_count = len(segment['harmonic_analysis'])
        peak_count = len(segment['peak_analysis'])
        detection_rate = harmonic_count / max(1, peak_count) * 100
        
        print(f"\n频段 {i} ({segment['expected_freq']:.0f}Hz):")
        print(f"  严格谐波检测: {harmonic_count}个")
        print(f"  所有频谱峰值: {peak_count}个")
        print(f"  检测准确率: {detection_rate:.1f}%")
        print(f"  误识别减少: {peak_count - harmonic_count}个")

if __name__ == "__main__":
    accurate_harmonic_detection()
