#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的谐波峰值检测算法
必须进行峰值判断才算检测到谐波
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks, peak_prominences
import librosa

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def corrected_harmonic_peak_detection():
    """修正的谐波峰值检测演示"""
    print("🔧 修正的谐波峰值检测算法")
    print("="*70)
    print("修正要点:")
    print("1. 谐波必须是真正的频谱峰值")
    print("2. 使用峰值检测算法 (find_peaks)")
    print("3. 设置峰值显著性阈值 (prominence)")
    print("4. 验证峰值高度和宽度")
    print("5. 排除噪声误判为谐波")
    print("="*70)
    
    # 生成测试信号
    test_cases = [
        {'freq': 1000, 'name': '1kHz信号', 'harmonics': [0.3, 0.1, 0.05]},
        {'freq': 500, 'name': '500Hz信号', 'harmonics': [0.4, 0.2, 0.1, 0.05]},
        {'freq': 5000, 'name': '5kHz信号', 'harmonics': [0.3, 0.1]},
        {'freq': 15000, 'name': '15kHz信号', 'harmonics': []}
    ]
    
    for case in test_cases:
        print(f"\n🎯 分析 {case['name']} ({case['freq']}Hz)")
        print("-" * 40)
        
        # 生成信号
        signal_data = generate_test_signal_with_peaks(case['freq'], case['harmonics'])
        
        # 修正的谐波峰值检测
        peak_analysis = corrected_harmonic_peak_analysis(signal_data)
        
        # 可视化峰值检测结果
        visualize_peak_detection(signal_data, peak_analysis, case['name'])

def generate_test_signal_with_peaks(fundamental_freq, harmonic_amplitudes):
    """生成带有明确峰值的测试信号"""
    
    fs = 48000
    duration = 1.0
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    # 基频信号
    signal = np.sin(2 * np.pi * fundamental_freq * t)
    
    # 添加指定的谐波
    for i, amp in enumerate(harmonic_amplitudes):
        harmonic_order = i + 2
        harmonic_freq = fundamental_freq * harmonic_order
        
        if harmonic_freq < fs / 2:
            signal += amp * np.sin(2 * np.pi * harmonic_freq * t)
    
    # 添加适量白噪声 (不要太多，以免掩盖谐波峰值)
    noise_level = 0.02
    signal += noise_level * np.random.randn(len(t))
    
    # 添加一些非谐波干扰 (测试峰值检测的鲁棒性)
    interference_freqs = [fundamental_freq * 1.3, fundamental_freq * 2.7]
    for interference_freq in interference_freqs:
        if interference_freq < fs / 2:
            interference_amp = 0.01  # 很小的干扰
            signal += interference_amp * np.sin(2 * np.pi * interference_freq * t)
    
    return {
        'signal': signal,
        'fs': fs,
        'fundamental_freq': fundamental_freq,
        'expected_harmonics': harmonic_amplitudes,
        'duration': duration,
        'time': t
    }

def corrected_harmonic_peak_analysis(signal_data):
    """修正的谐波峰值分析"""
    
    signal = signal_data['signal']
    fs = signal_data['fs']
    fundamental_freq = signal_data['fundamental_freq']
    
    # 高分辨率FFT
    fft_size = max(16384, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    # 只分析正频率
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    power_db = 10 * np.log10(positive_power + 1e-12)
    
    freq_resolution = positive_freqs[1] - positive_freqs[0]
    nyquist_freq = fs / 2
    
    print(f"   频率分辨率: {freq_resolution:.3f} Hz")
    print(f"   奈奎斯特频率: {nyquist_freq} Hz")
    
    # 1. 首先找到主频峰值
    fundamental_peak = find_fundamental_peak(positive_freqs, positive_power, fundamental_freq)
    
    # 2. 使用峰值检测算法找到所有显著峰值
    all_peaks = find_all_significant_peaks(positive_freqs, positive_power, power_db)
    
    # 3. 从所有峰值中识别谐波峰值
    harmonic_peaks = identify_harmonic_peaks(positive_freqs, positive_power, 
                                           fundamental_freq, all_peaks, nyquist_freq)
    
    # 4. 验证谐波峰值的有效性
    validated_harmonics = validate_harmonic_peaks(positive_freqs, positive_power, 
                                                 harmonic_peaks, fundamental_peak)
    
    return {
        'freqs': positive_freqs,
        'power': positive_power,
        'power_db': power_db,
        'fundamental_peak': fundamental_peak,
        'all_peaks': all_peaks,
        'harmonic_peaks': harmonic_peaks,
        'validated_harmonics': validated_harmonics,
        'freq_resolution': freq_resolution
    }

def find_fundamental_peak(freqs, power, expected_freq):
    """找到主频峰值"""
    
    # 自适应带宽
    bandwidth = calculate_adaptive_bandwidth(expected_freq)
    
    # 搜索范围
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 在搜索范围内使用峰值检测
    local_peaks, properties = find_peaks(search_powers, 
                                        height=np.max(search_powers) * 0.5,  # 至少是最大值的50%
                                        prominence=np.max(search_powers) * 0.1)  # 显著性阈值
    
    if len(local_peaks) > 0:
        # 选择功率最大的峰值
        max_peak_idx = local_peaks[np.argmax(search_powers[local_peaks])]
        actual_idx = search_indices[max_peak_idx]
        
        return {
            'freq': freqs[actual_idx],
            'power': power[actual_idx],
            'power_db': 10 * np.log10(power[actual_idx] + 1e-12),
            'index': actual_idx,
            'bandwidth': bandwidth,
            'is_peak': True,
            'peak_prominence': properties['prominences'][np.argmax(search_powers[local_peaks])] if 'prominences' in properties else 0
        }
    else:
        # 如果没有找到峰值，返回最大功率点但标记为非峰值
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        return {
            'freq': freqs[actual_idx],
            'power': power[actual_idx],
            'power_db': 10 * np.log10(power[actual_idx] + 1e-12),
            'index': actual_idx,
            'bandwidth': bandwidth,
            'is_peak': False,
            'peak_prominence': 0
        }

def find_all_significant_peaks(freqs, power, power_db):
    """找到所有显著的频谱峰值"""
    
    # 峰值检测参数
    min_height_db = -40  # 最小高度阈值 (dB)
    min_prominence_db = 3  # 最小显著性 (dB)
    min_distance = 10  # 最小距离 (频率bin)
    
    # 转换为线性功率阈值
    min_height = 10 ** (min_height_db / 10)
    
    # 计算显著性阈值 (相对于局部背景)
    # 使用滑动窗口计算局部背景水平
    window_size = 50
    local_background = np.convolve(power, np.ones(window_size)/window_size, mode='same')
    prominence_threshold = local_background * (10 ** (min_prominence_db / 10))
    
    # 找到峰值
    peaks, properties = find_peaks(power,
                                  height=min_height,
                                  prominence=prominence_threshold,
                                  distance=min_distance)
    
    # 计算峰值的详细属性
    if len(peaks) > 0:
        prominences = peak_prominences(power, peaks)[0]
        
        peak_info = []
        for i, peak_idx in enumerate(peaks):
            peak_info.append({
                'index': peak_idx,
                'freq': freqs[peak_idx],
                'power': power[peak_idx],
                'power_db': power_db[peak_idx],
                'prominence': prominences[i],
                'prominence_db': 10 * np.log10(prominences[i] + 1e-12),
                'height': properties['peak_heights'][i] if 'peak_heights' in properties else power[peak_idx]
            })
        
        # 按功率排序
        peak_info.sort(key=lambda x: x['power'], reverse=True)
        
        print(f"   找到 {len(peak_info)} 个显著峰值")
        for i, peak in enumerate(peak_info[:10]):  # 只显示前10个
            print(f"     峰值{i+1}: {peak['freq']:.1f}Hz, {peak['power_db']:.1f}dB, "
                  f"显著性{peak['prominence_db']:.1f}dB")
        
        return peak_info
    else:
        print(f"   未找到显著峰值")
        return []

def identify_harmonic_peaks(freqs, power, fundamental_freq, all_peaks, nyquist_freq):
    """从所有峰值中识别谐波峰值"""
    
    harmonic_peaks = []
    
    if not all_peaks:
        return harmonic_peaks
    
    print(f"\n   谐波峰值识别:")
    
    for order in range(2, 20):  # 2次到19次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            print(f"     {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率")
            break
        
        # 计算搜索带宽
        harmonic_bandwidth = calculate_adaptive_bandwidth(expected_harmonic_freq)
        
        # 在所有峰值中寻找匹配的谐波
        matching_peaks = []
        for peak in all_peaks:
            freq_error = abs(peak['freq'] - expected_harmonic_freq)
            if freq_error <= harmonic_bandwidth:
                matching_peaks.append({
                    'peak': peak,
                    'freq_error': freq_error,
                    'order': order,
                    'expected_freq': expected_harmonic_freq
                })
        
        if matching_peaks:
            # 选择频率误差最小的峰值
            best_match = min(matching_peaks, key=lambda x: x['freq_error'])
            
            # 验证这个峰值是否足够显著
            peak = best_match['peak']
            if peak['prominence_db'] >= 1.0:  # 显著性至少1dB
                harmonic_peaks.append(best_match)
                print(f"     {order}次谐波: 找到峰值 {peak['freq']:.1f}Hz, "
                      f"{peak['power_db']:.1f}dB, 误差{best_match['freq_error']:.1f}Hz, "
                      f"显著性{peak['prominence_db']:.1f}dB")
            else:
                print(f"     {order}次谐波: 峰值显著性不足 ({peak['prominence_db']:.1f}dB < 1.0dB)")
        else:
            print(f"     {order}次谐波: 未找到匹配峰值 (搜索范围: ±{harmonic_bandwidth:.1f}Hz)")
    
    return harmonic_peaks

def validate_harmonic_peaks(freqs, power, harmonic_peaks, fundamental_peak):
    """验证谐波峰值的有效性"""
    
    if not fundamental_peak or not harmonic_peaks:
        return []
    
    validated_harmonics = []
    fundamental_power_db = fundamental_peak['power_db']
    
    print(f"\n   谐波峰值验证:")
    print(f"     基频功率: {fundamental_power_db:.1f}dB")
    
    for harmonic in harmonic_peaks:
        peak = harmonic['peak']
        order = harmonic['order']
        
        # 验证条件
        validations = {
            'power_reasonable': True,  # 功率合理性
            'prominence_sufficient': True,  # 显著性充分
            'frequency_accurate': True,  # 频率准确性
            'harmonic_relationship': True  # 谐波关系
        }
        
        # 1. 功率合理性检查 (谐波功率应该小于基频)
        power_ratio_db = peak['power_db'] - fundamental_power_db
        if power_ratio_db > 3:  # 谐波功率不应该比基频高太多
            validations['power_reasonable'] = False
        
        # 2. 显著性检查
        if peak['prominence_db'] < 1.0:
            validations['prominence_sufficient'] = False
        
        # 3. 频率准确性检查
        if harmonic['freq_error'] > calculate_adaptive_bandwidth(harmonic['expected_freq']) * 0.5:
            validations['frequency_accurate'] = False
        
        # 4. 谐波关系检查 (功率应该随次数递减)
        expected_power_drop = order * 6  # 每次谐波大约衰减6dB
        actual_power_drop = fundamental_power_db - peak['power_db']
        if actual_power_drop < expected_power_drop * 0.3:  # 允许30%的偏差
            validations['harmonic_relationship'] = False
        
        # 综合判断
        is_valid = all(validations.values())
        
        if is_valid:
            validated_harmonics.append(harmonic)
            print(f"     ✅ {order}次谐波: {peak['freq']:.1f}Hz, {peak['power_db']:.1f}dB "
                  f"(相对基频: {power_ratio_db:+.1f}dB)")
        else:
            failed_checks = [k for k, v in validations.items() if not v]
            print(f"     ❌ {order}次谐波: 验证失败 - {', '.join(failed_checks)}")
    
    print(f"\n   验证结果: {len(validated_harmonics)}/{len(harmonic_peaks)} 个谐波通过验证")
    
    return validated_harmonics

def calculate_adaptive_bandwidth(frequency):
    """计算自适应带宽"""
    if frequency <= 200:
        return 2.0
    elif frequency <= 500:
        return 3.0
    elif frequency <= 1000:
        return 5.0
    elif frequency <= 2000:
        return 8.0
    elif frequency <= 5000:
        return 12.0
    elif frequency <= 10000:
        return 20.0
    else:
        return 30.0

def visualize_peak_detection(signal_data, peak_analysis, signal_name):
    """可视化峰值检测结果"""
    
    freqs = peak_analysis['freqs']
    power_db = peak_analysis['power_db']
    fundamental_peak = peak_analysis['fundamental_peak']
    all_peaks = peak_analysis['all_peaks']
    validated_harmonics = peak_analysis['validated_harmonics']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 上图: 时域信号
    t = signal_data['time']
    signal = signal_data['signal']
    ax1.plot(t[:2000], signal[:2000], 'b-', linewidth=1)
    ax1.set_title(f'{signal_name} - 时域信号', fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('幅度')
    ax1.grid(True, alpha=0.3)
    
    # 下图: 频域峰值检测
    ax2.plot(freqs, power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
    
    # 标记所有检测到的峰值
    if all_peaks:
        peak_freqs = [p['freq'] for p in all_peaks]
        peak_powers = [p['power_db'] for p in all_peaks]
        ax2.plot(peak_freqs, peak_powers, 'go', markersize=4, alpha=0.6, label='所有峰值')
    
    # 标记主频峰值
    if fundamental_peak and fundamental_peak['is_peak']:
        ax2.plot(fundamental_peak['freq'], fundamental_peak['power_db'], 'ro', 
                markersize=10, label=f'主频峰值 {fundamental_peak["freq"]:.1f}Hz')
        
        # 主频标注
        ax2.annotate(f'主频峰值\n{fundamental_peak["freq"]:.1f}Hz\n{fundamental_peak["power_db"]:.1f}dB', 
                    xy=(fundamental_peak['freq'], fundamental_peak['power_db']), 
                    xytext=(fundamental_peak['freq'], fundamental_peak['power_db'] + 15),
                    ha='center', va='bottom', fontweight='bold', color='red',
                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 标记验证通过的谐波峰值
    harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, harmonic in enumerate(validated_harmonics[:8]):  # 只显示前8个
        peak = harmonic['peak']
        order = harmonic['order']
        color = harmonic_colors[i % len(harmonic_colors)]
        
        ax2.plot(peak['freq'], peak['power_db'], 'o', color=color, markersize=8,
                label=f'{order}次谐波 {peak["freq"]:.0f}Hz')
        
        # 谐波标注 (只标注前4个)
        if i < 4:
            ax2.annotate(f'{order}次谐波\n{peak["freq"]:.0f}Hz\n显著性{peak["prominence_db"]:.1f}dB', 
                        xy=(peak['freq'], peak['power_db']), 
                        xytext=(peak['freq'], peak['power_db'] + 8),
                        ha='center', va='bottom', fontsize=8, color=color,
                        arrowprops=dict(arrowstyle='->', color=color, lw=1))
    
    # 设置图表属性
    ax2.set_title(f'{signal_name} - 峰值检测结果 (验证通过的谐波)', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('功率 (dB)')
    
    # 设置显示范围
    fundamental_freq = signal_data['fundamental_freq']
    if fundamental_freq <= 1000:
        ax2.set_xlim(0, 8000)
    elif fundamental_freq <= 5000:
        ax2.set_xlim(0, 20000)
    else:
        ax2.set_xlim(0, 24000)
    
    ax2.set_ylim(np.min(power_db) - 5, np.max(power_db) + 20)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'corrected_harmonic_peaks_{signal_name.replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 峰值检测可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    corrected_harmonic_peak_detection()
