#!/usr/bin/env python3
"""
详细频段可视化器
Detailed Segment Visualizer
显示频段切割和各频段的竖线检测过程
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DetailedSegmentVisualizer:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 竖线检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.3,
            'min_peak_prominence_ratio': 0.3,
            'min_peak_height_ratio': 0.4,
            'min_frequency_span': 1000,
            'min_line_strength': 0.5,
        }
        
        print(f"详细频段可视化器初始化完成")
    
    def visualize_segment_detection(self, audio_path, max_segments=12):
        """可视化频段切割和检测过程"""
        print(f"\n详细分析: {os.path.basename(audio_path)}")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            print(f"频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
            
            # 使用freq_split进行频段分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path, **self.freq_split_params, plot=False
            )
            
            if len(step_boundaries) == 0:
                print("freq_split分割失败")
                return None
            
            print(f"freq_split分割: {len(step_boundaries)}个频段")
            
            # 选择要显示的频段（避免太多）
            if len(step_boundaries) > max_segments:
                # 均匀选择频段
                indices = np.linspace(0, len(step_boundaries)-1, max_segments, dtype=int)
                selected_boundaries = [step_boundaries[i] for i in indices]
                selected_freq_table = [freq_table[i] for i in indices]
                selected_indices = indices
            else:
                selected_boundaries = step_boundaries
                selected_freq_table = freq_table
                selected_indices = list(range(len(step_boundaries)))
            
            print(f"选择显示 {len(selected_boundaries)} 个频段进行详细分析")
            
            # 创建可视化
            self._create_detailed_visualization(
                power_db, frequencies, times, step_boundaries, freq_table,
                selected_boundaries, selected_freq_table, selected_indices, audio_path
            )
            
            return {
                'power_db': power_db,
                'frequencies': frequencies,
                'times': times,
                'step_boundaries': step_boundaries,
                'freq_table': freq_table
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _create_detailed_visualization(self, power_db, frequencies, times, all_boundaries, all_freq_table,
                                     selected_boundaries, selected_freq_table, selected_indices, audio_path):
        """创建详细的可视化"""
        print(f"\n生成详细可视化...")
        
        num_segments = len(selected_boundaries)
        
        # 计算网格布局
        cols = min(4, num_segments)
        rows = (num_segments + cols - 1) // cols + 1  # +1 for the main plot
        
        # 创建大图表
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        fig.suptitle(f'详细频段分析: {os.path.basename(audio_path)}', fontsize=16)
        
        # 如果只有一行，确保axes是二维数组
        if rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)
        
        # 第一行：完整频谱图
        ax_main = plt.subplot2grid((rows, cols), (0, 0), colspan=cols)
        im = ax_main.imshow(power_db, aspect='auto', origin='lower', 
                           extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                           cmap='viridis')
        
        # 标记所有freq_split分割线
        for i, (start_time, end_time) in enumerate(all_boundaries):
            alpha = 0.8 if i in selected_indices else 0.2
            linewidth = 2 if i in selected_indices else 0.5
            color = 'red' if i in selected_indices else 'white'
            ax_main.axvline(x=start_time, color=color, alpha=alpha, linewidth=linewidth)
            ax_main.axvline(x=end_time, color=color, alpha=alpha, linewidth=linewidth)
            
            # 标记选中的频段
            if i in selected_indices:
                mid_time = (start_time + end_time) / 2
                expected_freq = all_freq_table[i] if i < len(all_freq_table) else 0
                ax_main.text(mid_time, frequencies[-1]*0.9, f'S{i}\n{expected_freq:.0f}Hz', 
                           ha='center', va='top', color='yellow', fontsize=8,
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="black", alpha=0.7))
        
        ax_main.set_title(f'完整频谱 + freq_split分割 (红线=选中频段, 白线=其他频段)')
        ax_main.set_xlabel('时间 (s)')
        ax_main.set_ylabel('频率 (Hz)')
        plt.colorbar(im, ax=ax_main, label='功率 (dB)')
        
        # 分析每个选中的频段
        for idx, (seg_idx, (seg_start_time, seg_end_time)) in enumerate(zip(selected_indices, selected_boundaries)):
            row = (idx // cols) + 1
            col = idx % cols
            
            if row < rows and col < cols:
                ax = axes[row, col] if rows > 1 else axes[col]
                
                expected_freq = selected_freq_table[idx] if idx < len(selected_freq_table) else None
                
                # 分析这个频段
                segment_analysis = self._analyze_single_segment_detailed(
                    power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq
                )
                
                # 可视化这个频段
                self._plot_segment_analysis(ax, segment_analysis, seg_idx, expected_freq)
        
        # 隐藏多余的子图
        for idx in range(num_segments, rows * cols - cols):  # -cols because first row is main plot
            row = (idx // cols) + 1
            col = idx % cols
            if row < rows and col < cols:
                if rows > 1:
                    axes[row, col].set_visible(False)
                else:
                    axes[col].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'detailed_segment_analysis_{os.path.splitext(os.path.basename(audio_path))[0]}.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  详细可视化结果已保存: {output_filename}")
        
        plt.show()
    
    def _analyze_single_segment_detailed(self, power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq):
        """详细分析单个频段"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'expected_frequency': expected_freq,
            'segment_power': None,
            'segment_times': None,
            'total_energy': None,
            'smoothed_energy': None,
            'peaks': [],
            'peak_info': {},
            'vertical_lines': [],
            'analysis_success': False
        }
        
        # 找到频段对应的时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除频段边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            result['error'] = '频段太短，排除边界后无数据'
            return result
        
        # 提取频段核心部分的数据
        segment_power = power_db[:, seg_core_start:seg_core_end]
        segment_times = times[seg_core_start:seg_core_end]
        
        result['segment_power'] = segment_power
        result['segment_times'] = segment_times
        
        # 计算时间维度总能量
        total_energy = np.sum(segment_power, axis=0)
        result['total_energy'] = total_energy
        
        if len(total_energy) == 0:
            result['error'] = '无能量数据'
            return result
        
        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        result['smoothed_energy'] = smoothed_energy
        
        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        
        if energy_range < 100:
            result['error'] = f'能量变化太小 ({energy_range:.1f})'
            return result
        
        min_height = np.min(smoothed_energy) + energy_range * self.detection_params['min_peak_height_ratio']
        min_prominence = energy_range * self.detection_params['min_peak_prominence_ratio']
        
        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy, 
                                     height=min_height,
                                     distance=2,
                                     prominence=min_prominence)
        
        result['peaks'] = peaks
        result['peak_info'] = {
            'total_peaks': len(peaks),
            'min_height': min_height,
            'min_prominence': min_prominence,
            'energy_range': energy_range,
            'peak_energies': [total_energy[p] for p in peaks] if len(peaks) > 0 else []
        }
        
        # 分析每个峰值是否为竖线
        for peak_idx in peaks:
            if peak_idx < len(segment_times):
                peak_time = segment_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = segment_power[:, peak_idx]
                
                # 分析该时刻的频谱是否为竖线
                line_analysis = self._analyze_spectrum_for_line(
                    peak_power_spectrum, frequencies, peak_time, peak_energy
                )
                
                if line_analysis:
                    line_analysis['peak_index'] = peak_idx
                    result['vertical_lines'].append(line_analysis)
        
        result['analysis_success'] = True
        return result
    
    def _analyze_spectrum_for_line(self, power_spectrum, frequencies, peak_time, peak_energy):
        """分析频谱是否为竖线"""
        # 计算功率分布统计
        power_mean = np.mean(power_spectrum)
        power_std = np.std(power_spectrum)
        
        # 使用90%分位数作为阈值
        threshold = np.percentile(power_spectrum, 90)
        high_energy_mask = power_spectrum > threshold
        high_energy_indices = np.where(high_energy_mask)[0]
        
        if len(high_energy_indices) < 3:
            return None
        
        # 计算频率跨度
        freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
        freq_ratio = len(high_energy_indices) / len(frequencies)
        
        # 计算连续性分数
        continuous_segments = self._find_continuous_segments(high_energy_indices)
        continuity_score = sum(len(seg) for seg in continuous_segments) / len(high_energy_indices) if high_energy_indices.size > 0 else 0
        
        # 综合评分
        span_score = min(1.0, freq_span / 5000.0)
        ratio_score = min(1.0, freq_ratio / 0.3)
        total_score = span_score * ratio_score * continuity_score
        
        return {
            'time': peak_time,
            'peak_energy': peak_energy,
            'frequency_span': freq_span,
            'frequency_ratio': freq_ratio,
            'line_strength': total_score,
            'threshold': threshold,
            'high_energy_indices': high_energy_indices,
            'continuous_segments': continuous_segments,
            'power_stats': {
                'power_mean': power_mean,
                'power_std': power_std,
                'max_power': np.max(power_spectrum),
                'min_power': np.min(power_spectrum)
            }
        }
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _plot_segment_analysis(self, ax, segment_analysis, seg_idx, expected_freq):
        """绘制单个频段的分析结果"""
        if not segment_analysis['analysis_success']:
            ax.text(0.5, 0.5, f"频段 {seg_idx}\n{segment_analysis.get('error', '分析失败')}", 
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            ax.set_title(f'频段 {seg_idx} (失败)')
            return
        
        segment_times = segment_analysis['segment_times']
        total_energy = segment_analysis['total_energy']
        smoothed_energy = segment_analysis['smoothed_energy']
        peaks = segment_analysis['peaks']
        peak_info = segment_analysis['peak_info']
        vertical_lines = segment_analysis['vertical_lines']
        
        # 绘制能量曲线
        ax.plot(segment_times, total_energy, 'b-', linewidth=1, alpha=0.7, label='总能量')
        ax.plot(segment_times, smoothed_energy, 'g-', linewidth=2, label='平滑能量')
        
        # 标记峰值检测阈值
        ax.axhline(y=peak_info['min_height'], color='orange', linestyle='--', alpha=0.7, label='峰值阈值')
        
        # 标记检测到的峰值
        if len(peaks) > 0:
            peak_times = [segment_times[p] for p in peaks]
            peak_energies = [total_energy[p] for p in peaks]
            ax.scatter(peak_times, peak_energies, color='red', s=50, zorder=5, label=f'峰值({len(peaks)})')
        
        # 标记竖线
        line_count = len(vertical_lines)
        for i, line in enumerate(vertical_lines):
            ax.axvline(x=line['time'], color='purple', linewidth=2, alpha=0.8)
            # 在峰值上方标记竖线信息
            y_pos = ax.get_ylim()[1] * (0.9 - i * 0.1)
            ax.text(line['time'], y_pos, f"L{i+1}\n{line['line_strength']:.2f}", 
                   ha='center', va='top', fontsize=8, color='purple',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.7))
        
        # 设置标题和标签
        title = f"频段 {seg_idx}"
        if expected_freq:
            title += f" ({expected_freq:.0f}Hz)"
        title += f"\n峰值:{len(peaks)}, 竖线:{line_count}"
        
        ax.set_title(title, fontsize=10)
        ax.set_xlabel('时间 (s)', fontsize=8)
        ax.set_ylabel('能量 (dB)', fontsize=8)
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
        
        # 显示统计信息
        stats_text = f"能量范围: {peak_info['energy_range']:.0f}dB"
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8, 
               verticalalignment='top', bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.7))

def main():
    """主函数"""
    # 初始化可视化器
    visualizer = DetailedSegmentVisualizer()
    
    # 分析指定样本
    audio_path = "../test20250717/pos/sd卡/sd1_1.wav"
    
    # 执行详细分析
    results = visualizer.visualize_segment_detection(audio_path, max_segments=12)
    
    if results:
        print(f"\n详细分析完成！")
        print(f"可视化图表已生成，显示了频段切割和各频段的竖线检测过程。")
    else:
        print(f"分析失败！")
    
    return visualizer, results

if __name__ == "__main__":
    visualizer, results = main()
