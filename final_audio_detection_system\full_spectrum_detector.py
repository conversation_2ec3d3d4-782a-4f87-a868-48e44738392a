#!/usr/bin/env python3
"""
全频段音频异常检测器
Full Spectrum Audio Anomaly Detector
使用所有频段进行异常检测，提高鲁棒性
"""

import numpy as np
import pandas as pd
from scipy import stats
from accuracy_first_detector import AccuracyFirstDetector
import os
import warnings
warnings.filterwarnings('ignore')

class FullSpectrumDetector(AccuracyFirstDetector):
    def __init__(self, sample_rate=48000):
        super().__init__(sample_rate)
        
        # 全频段检测策略
        self.detection_strategies = {
            'statistical_outlier': 0.3,      # 统计离群值检测
            'frequency_group': 0.25,         # 频段组分析
            'spectral_pattern': 0.2,         # 频谱模式分析
            'cross_band_correlation': 0.15,  # 跨频段相关性
            'original_features': 0.1         # 原始8特征
        }
        
        # 频段分组
        self.frequency_groups = {
            'low_freq': {'range': (100, 1000), 'bands': []},
            'mid_freq': {'range': (1000, 8000), 'bands': []},
            'high_freq': {'range': (8000, 20000), 'bands': []}
        }
        
        # 初始化频段分组
        self._initialize_frequency_groups()
        
        print(f"全频段检测器初始化完成")
        print(f"使用{len(self.frequency_bands)}个频段的全特征检测")
    
    def _initialize_frequency_groups(self):
        """初始化频段分组"""
        for band_name, band_info in self.frequency_bands.items():
            center_freq = band_info['center_freq']
            
            if center_freq <= 1000:
                self.frequency_groups['low_freq']['bands'].append(band_name)
            elif center_freq <= 8000:
                self.frequency_groups['mid_freq']['bands'].append(band_name)
            else:
                self.frequency_groups['high_freq']['bands'].append(band_name)
        
        for group_name, group_info in self.frequency_groups.items():
            print(f"{group_name}: {len(group_info['bands'])}个频段")
    
    def detect_anomaly_full_spectrum(self, audio_path):
        """全频段异常检测"""
        # 提取所有特征
        features = self.extract_features(audio_path)
        
        if not features:
            return self._get_error_result(audio_path)
        
        # 多策略检测
        detection_scores = {}
        anomaly_details = []
        
        # 策略1: 统计离群值检测
        outlier_score, outlier_details = self._statistical_outlier_detection(features)
        detection_scores['statistical_outlier'] = outlier_score
        anomaly_details.extend(outlier_details)
        
        # 策略2: 频段组分析
        group_score, group_details = self._frequency_group_analysis(features)
        detection_scores['frequency_group'] = group_score
        anomaly_details.extend(group_details)
        
        # 策略3: 频谱模式分析
        pattern_score, pattern_details = self._spectral_pattern_analysis(features)
        detection_scores['spectral_pattern'] = pattern_score
        anomaly_details.extend(pattern_details)
        
        # 策略4: 跨频段相关性分析
        correlation_score, correlation_details = self._cross_band_correlation_analysis(features)
        detection_scores['cross_band_correlation'] = correlation_score
        anomaly_details.extend(correlation_details)
        
        # 策略5: 原始8特征检测
        original_score, original_details = self._original_features_detection(features)
        detection_scores['original_features'] = original_score
        anomaly_details.extend(original_details)
        
        # 融合所有策略
        final_score = self._fuse_all_strategies(detection_scores)
        
        # 全频段异常判断
        anomaly_detected = self._full_spectrum_decision(detection_scores, final_score)
        
        # 计算置信度
        confidence = self._calculate_full_spectrum_confidence(detection_scores, anomaly_detected)
        
        return {
            'file_path': audio_path,
            'anomaly_detected': anomaly_detected,
            'confidence': confidence,
            'final_score': final_score,
            'strategy_scores': detection_scores,
            'anomaly_details': anomaly_details[:8],
            'detection_method': 'full_spectrum',
            'error': False
        }
    
    def _statistical_outlier_detection(self, features):
        """统计离群值检测"""
        score = 0.0
        details = []
        
        # 按特征类型分组
        feature_types = ['energy', 'snr', 'snr_db', 'spectral_rolloff', 'response_flatness']
        
        for feature_type in feature_types:
            # 收集该类型的所有特征值
            type_features = {k: v for k, v in features.items() 
                           if feature_type in k and isinstance(v, (int, float)) and not np.isnan(v)}
            
            if len(type_features) < 5:  # 至少需要5个值进行统计
                continue
            
            values = list(type_features.values())
            
            # Z-score异常检测
            z_scores = np.abs(stats.zscore(values))
            outlier_indices = np.where(z_scores > 2.5)[0]  # 2.5-sigma
            
            outlier_ratio = len(outlier_indices) / len(values)
            
            if outlier_ratio > 0.2:  # 超过20%的频段异常
                type_score = min(0.3, outlier_ratio)
                score += type_score
                details.append(f'{feature_type}类特征异常频段过多 ({len(outlier_indices)}/{len(values)})')
        
        return score, details
    
    def _frequency_group_analysis(self, features):
        """频段组分析"""
        score = 0.0
        details = []
        
        for group_name, group_info in self.frequency_groups.items():
            group_anomalies = 0
            group_total = 0
            
            for band_name in group_info['bands']:
                # 检查该频段的关键特征
                key_features = ['spectral_rolloff', 'snr', 'response_flatness']
                
                for feature_type in key_features:
                    feature_name = f"{band_name}_{feature_type}"
                    if feature_name in features:
                        group_total += 1
                        
                        # 使用简化的异常判断
                        feature_value = features[feature_name]
                        if self._is_feature_anomalous_simple(feature_type, feature_value):
                            group_anomalies += 1
            
            if group_total > 0:
                anomaly_ratio = group_anomalies / group_total
                if anomaly_ratio > 0.3:  # 超过30%异常
                    group_score = min(0.25, anomaly_ratio)
                    score += group_score
                    details.append(f'{group_name}频段组异常 ({group_anomalies}/{group_total})')
        
        return score, details
    
    def _spectral_pattern_analysis(self, features):
        """频谱模式分析"""
        score = 0.0
        details = []
        
        # 收集所有频段的频谱滚降值
        rolloff_features = {}
        for feature_name, value in features.items():
            if 'spectral_rolloff' in feature_name and isinstance(value, (int, float)):
                band_num = feature_name.split('_')[1]
                if band_num.isdigit():
                    rolloff_features[int(band_num)] = value
        
        if len(rolloff_features) < 10:
            return 0.0, []
        
        # 检查频谱滚降的单调性
        sorted_bands = sorted(rolloff_features.items())
        rolloff_values = [v for k, v in sorted_bands]
        
        # 计算单调性违反
        violations = 0
        for i in range(1, len(rolloff_values)):
            if rolloff_values[i] < rolloff_values[i-1] * 0.95:  # 允许5%波动
                violations += 1
        
        violation_ratio = violations / (len(rolloff_values) - 1)
        if violation_ratio > 0.3:  # 超过30%违反单调性
            score += 0.2
            details.append(f'频谱滚降单调性异常 (违反率:{violation_ratio:.1%})')
        
        # 检查频谱滚降的异常值
        rolloff_z_scores = np.abs(stats.zscore(rolloff_values))
        outliers = np.sum(rolloff_z_scores > 2.0)
        outlier_ratio = outliers / len(rolloff_values)
        
        if outlier_ratio > 0.15:  # 超过15%的离群值
            score += 0.15
            details.append(f'频谱滚降离群值过多 ({outliers}/{len(rolloff_values)})')
        
        return score, details
    
    def _cross_band_correlation_analysis(self, features):
        """跨频段相关性分析"""
        score = 0.0
        details = []
        
        # 收集相邻频段的SNR值
        snr_features = {}
        for feature_name, value in features.items():
            if 'snr' in feature_name and 'db' not in feature_name and isinstance(value, (int, float)):
                band_num = feature_name.split('_')[1]
                if band_num.isdigit():
                    snr_features[int(band_num)] = value
        
        if len(snr_features) < 5:
            return 0.0, []
        
        # 检查相邻频段SNR的相关性
        sorted_snr = sorted(snr_features.items())
        snr_values = [v for k, v in sorted_snr]
        
        # 计算相邻值的差异
        adjacent_diffs = []
        for i in range(1, len(snr_values)):
            diff = abs(snr_values[i] - snr_values[i-1])
            adjacent_diffs.append(diff)
        
        if adjacent_diffs:
            mean_diff = np.mean(adjacent_diffs)
            std_diff = np.std(adjacent_diffs)
            
            # 检查是否有异常大的跳跃
            large_jumps = sum(1 for diff in adjacent_diffs if diff > mean_diff + 2*std_diff)
            jump_ratio = large_jumps / len(adjacent_diffs)
            
            if jump_ratio > 0.2:  # 超过20%的大跳跃
                score += 0.15
                details.append(f'频段间SNR跳跃异常 ({large_jumps}/{len(adjacent_diffs)})')
        
        return score, details
    
    def _original_features_detection(self, features):
        """原始8特征检测"""
        score = 0.0
        details = []
        
        for feature_name, config in self.detection_thresholds.items():
            if feature_name in features:
                feature_value = features[feature_name]
                threshold = config
                weight = self.feature_weights.get(feature_name, 0.01)
                
                if self._is_feature_anomalous_original(feature_name, feature_value, threshold):
                    score += weight
                    details.append(f'原始特征{feature_name}异常')
        
        return score, details
    
    def _is_feature_anomalous_simple(self, feature_type, feature_value):
        """简化的特征异常判断"""
        # 使用经验阈值
        thresholds = {
            'spectral_rolloff': 800,
            'snr': 2.5,
            'response_flatness': 0.4,
            'energy': 0.001
        }
        
        if feature_type == 'spectral_rolloff':
            return feature_value > thresholds.get(feature_type, 800)
        elif feature_type == 'snr':
            return feature_value > thresholds.get(feature_type, 2.5)
        elif feature_type == 'response_flatness':
            return feature_value > thresholds.get(feature_type, 0.4)
        elif feature_type == 'energy':
            return feature_value < thresholds.get(feature_type, 0.001)
        
        return False
    
    def _is_feature_anomalous_original(self, feature_name, feature_value, threshold):
        """原始特征异常判断"""
        if 'spectral_rolloff' in feature_name or ('snr' in feature_name and 'db' not in feature_name):
            return feature_value > threshold
        elif 'snr_db' in feature_name:
            return feature_value < threshold
        elif 'energy' in feature_name:
            return feature_value < threshold
        elif 'response_flatness' in feature_name or 'spectral_irregularity' in feature_name:
            return feature_value > threshold
        
        return False
    
    def _fuse_all_strategies(self, detection_scores):
        """融合所有策略分数"""
        final_score = 0.0
        
        for strategy, score in detection_scores.items():
            weight = self.detection_strategies.get(strategy, 0.1)
            final_score += score * weight
        
        return final_score
    
    def _full_spectrum_decision(self, detection_scores, final_score):
        """全频段异常判断"""
        # 多层次判断
        
        # 层次1: 最终分数阈值
        if final_score > 0.3:  # 比原来的0.5更敏感
            return True
        
        # 层次2: 多策略一致性
        anomaly_strategies = sum(1 for score in detection_scores.values() if score > 0.15)
        if anomaly_strategies >= 3:  # 至少3个策略检测到异常
            return True
        
        # 层次3: 单策略强异常
        max_strategy_score = max(detection_scores.values())
        if max_strategy_score > 0.5:  # 单策略强异常
            return True
        
        return False
    
    def _calculate_full_spectrum_confidence(self, detection_scores, anomaly_detected):
        """计算全频段置信度"""
        # 基于策略一致性和分数分布
        strategy_count = len([s for s in detection_scores.values() if s > 0.1])
        max_score = max(detection_scores.values())
        avg_score = np.mean(list(detection_scores.values()))
        
        if anomaly_detected:
            # 异常文件：策略越多，分数越高，置信度越高
            base_confidence = 0.6
            strategy_bonus = strategy_count * 0.05
            score_bonus = min(0.25, max_score * 0.5)
            confidence = base_confidence + strategy_bonus + score_bonus
        else:
            # 正常文件：策略越少，分数越低，置信度越高
            base_confidence = 0.9
            strategy_penalty = strategy_count * 0.03
            score_penalty = avg_score * 0.3
            confidence = base_confidence - strategy_penalty - score_penalty
        
        return np.clip(confidence, 0.01, 0.99)
    
    def _get_error_result(self, audio_path):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'final_score': 0.0,
            'strategy_scores': {},
            'anomaly_details': ['特征提取失败'],
            'detection_method': 'full_spectrum',
            'error': True
        }
    
    def compare_with_original(self, directories):
        """与原始方法对比"""
        print("\n" + "="*80)
        print("全频段检测 vs 原始8特征检测对比")
        print("="*80)
        
        # 原始检测
        print("执行原始8特征检测...")
        original_results = self.batch_detect(directories)
        
        # 全频段检测
        print("执行全频段检测...")
        full_spectrum_results = []
        for directory in directories:
            if not os.path.exists(directory):
                continue
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.wav'):
                        file_path = os.path.join(root, file)
                        result = self.detect_anomaly_full_spectrum(file_path)
                        result['directory'] = directory
                        result['filename'] = file
                        full_spectrum_results.append(result)
        
        # 对比分析
        comparison = []
        for orig, full in zip(original_results, full_spectrum_results):
            comparison.append({
                'filename': orig['filename'],
                'original_anomaly': orig['anomaly_detected'],
                'original_confidence': orig['confidence'],
                'full_spectrum_anomaly': full['anomaly_detected'],
                'full_spectrum_confidence': full['confidence'],
                'full_spectrum_score': full['final_score'],
                'agreement': orig['anomaly_detected'] == full['anomaly_detected']
            })
        
        # 统计结果
        total_files = len(comparison)
        agreement_count = sum(1 for c in comparison if c['agreement'])
        
        original_anomalies = sum(1 for c in comparison if c['original_anomaly'])
        full_spectrum_anomalies = sum(1 for c in comparison if c['full_spectrum_anomaly'])
        
        print(f"\n对比结果:")
        print(f"  总文件数: {total_files}")
        print(f"  原始方法异常: {original_anomalies}")
        print(f"  全频段方法异常: {full_spectrum_anomalies}")
        print(f"  检测结果一致: {agreement_count} ({agreement_count/total_files*100:.1f}%)")
        
        # 显示差异文件
        disagreements = [c for c in comparison if not c['agreement']]
        if disagreements:
            print(f"\n检测结果差异文件:")
            for d in disagreements:
                print(f"  {d['filename']}:")
                print(f"    原始方法: {'异常' if d['original_anomaly'] else '正常'} ({d['original_confidence']:.1%})")
                print(f"    全频段方法: {'异常' if d['full_spectrum_anomaly'] else '正常'} ({d['full_spectrum_confidence']:.1%})")
        
        return comparison

def main():
    """主函数"""
    
    # 初始化全频段检测器
    detector = FullSpectrumDetector()
    
    # 定义要检测的目录
    directories = [
        "../test20250717",
        "../待定"
    ]
    
    # 执行对比检测
    comparison = detector.compare_with_original(directories)
    
    # 保存对比结果
    comparison_df = pd.DataFrame(comparison)
    comparison_df.to_csv('full_spectrum_comparison.csv', index=False)
    
    print(f"\n全频段检测对比结果已保存: full_spectrum_comparison.csv")
    
    return detector, comparison

if __name__ == "__main__":
    detector, comparison = main()
