#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效测试test20250717目录下所有文件的谐波异常检测
使用多进程提升效率，避免重复检测
"""

import os
import sys
import time
import multiprocessing as mp
from multiprocessing import Pool
from collections import defaultdict

# 添加当前目录到路径
sys.path.append('harmonic_detection_system')
from harmonic_detection_system.harmonic_anomaly_detector import HarmonicAnomalyDetector

def detect_single_file(file_path):
    """单文件检测工作函数 - 用于多进程"""
    try:
        # 创建检测器
        detector = HarmonicAnomalyDetector()
        
        # 分析音频
        analysis_result = detector.analyze_audio_file(file_path)
        if not analysis_result:
            return None
        
        # 异常检测
        detection_result = detector.detect_anomaly(analysis_result)
        if not detection_result:
            return None
        
        return {
            'filename': os.path.basename(file_path),
            'filepath': file_path,
            'is_anomaly': detection_result['is_anomaly'],
            'anomaly_segments_count': detection_result['anomaly_segments_count'],
            'anomaly_segments_threshold': detection_result['anomaly_segments_threshold'],
            'anomaly_segments': detection_result['anomaly_segments'][:3],  # 只保留前3个异常段
            'dir_name': os.path.dirname(os.path.relpath(file_path, "test20250717"))
        }
        
    except Exception as e:
        return {
            'filename': os.path.basename(file_path),
            'filepath': file_path,
            'error': str(e),
            'dir_name': os.path.dirname(os.path.relpath(file_path, "test20250717"))
        }

def test_all_files_fast():
    """高效测试所有文件"""
    
    print("🎯 高效测试test20250717目录下所有文件")
    print("="*60)
    
    # 扫描test20250717目录
    test_dir = "test20250717"
    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return
    
    # 查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"📊 找到{len(wav_files)}个wav文件")
    
    if len(wav_files) == 0:
        print("❌ 未找到wav文件")
        return
    
    # 使用多进程检测
    num_processes = min(mp.cpu_count(), len(wav_files))
    print(f"🚀 使用{num_processes}个进程并行检测...")
    print()
    
    start_time = time.time()
    
    with Pool(processes=num_processes) as pool:
        results = pool.map(detect_single_file, wav_files)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # 过滤成功的结果
    successful_results = [r for r in results if r is not None and 'error' not in r]
    failed_results = [r for r in results if r is not None and 'error' in r]
    
    print(f"⏱️  检测完成! 总耗时: {processing_time:.1f}秒")
    print(f"📊 成功: {len(successful_results)}个, 失败: {len(failed_results)}个")
    print()
    
    # 按目录分组显示结果
    results_by_dir = defaultdict(list)
    for result in successful_results:
        dir_name = result['dir_name'] if result['dir_name'] else '根目录'
        results_by_dir[dir_name].append(result)
    
    # 统计变量
    total_normal = 0
    total_anomaly = 0
    
    # 按目录显示结果
    for dir_name, dir_results in sorted(results_by_dir.items()):
        dir_normal = sum(1 for r in dir_results if not r['is_anomaly'])
        dir_anomaly = sum(1 for r in dir_results if r['is_anomaly'])
        
        print(f"📁 {dir_name} ({len(dir_results)}个文件)")
        print("-" * 40)
        
        for result in sorted(dir_results, key=lambda x: x['filename']):
            if result['is_anomaly']:
                status = "🔴 异常"
                total_anomaly += 1
            else:
                status = "🟢 正常"
                total_normal += 1
            
            print(f"  {status} {result['filename']}")
            print(f"    异常段个数: {result['anomaly_segments_count']}个")
            
            # 显示异常段详情（如果有）
            if result['anomaly_segments'] and len(result['anomaly_segments']) > 0:
                print(f"    异常段详情:", end="")
                for i, seg in enumerate(result['anomaly_segments']):
                    if i == 0:
                        print(f" 段{seg['seg_idx']}({seg['expected_freq']:.0f}Hz):{seg['harmonic_count']}>{seg['threshold']}", end="")
                    else:
                        print(f", 段{seg['seg_idx']}({seg['expected_freq']:.0f}Hz):{seg['harmonic_count']}>{seg['threshold']}", end="")
                
                # 如果原始结果有更多异常段
                original_count = result['anomaly_segments_count']
                shown_count = len(result['anomaly_segments'])
                if original_count > shown_count:
                    print(f" ...还有{original_count - shown_count}个")
                else:
                    print()
        
        print(f"  📊 {dir_name}统计: {dir_anomaly}异常/{len(dir_results)}总数 ({dir_anomaly/len(dir_results)*100:.1f}%异常)")
        print()
        
        total_normal += dir_normal
        total_anomaly += dir_anomaly
    
    # 显示失败的文件
    if failed_results:
        print("❌ 检测失败的文件:")
        for result in failed_results:
            print(f"  {result['filename']}: {result['error']}")
        print()
    
    # 显示总体统计
    total_files = total_normal + total_anomaly
    print("📊 总体检测结果")
    print("="*30)
    print(f"总文件数: {total_files}个")
    print(f"正常文件: {total_normal}个 ({total_normal/total_files*100:.1f}%)")
    print(f"异常文件: {total_anomaly}个 ({total_anomaly/total_files*100:.1f}%)")
    print(f"检测阈值: >=2个异常段")
    print(f"平均处理速度: {processing_time/len(wav_files):.1f}秒/文件")

if __name__ == "__main__":
    test_all_files_fast()
