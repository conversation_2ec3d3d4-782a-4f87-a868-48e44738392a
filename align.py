import numpy as np
import librosa
import matplotlib.pyplot as plt
import scipy.signal
import os
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


# ========== 对齐函数 ==========
def align_signals_by_energy(y_ref, y_test, fs=44100, n_fft=2048, hop_length=512):
    # 1. 计算帧能量（STFT幅度总和）
    energy_ref = np.sum(np.abs(librosa.stft(y_ref, n_fft=n_fft, hop_length=hop_length)), axis=0)
    energy_test = np.sum(np.abs(librosa.stft(y_test, n_fft=n_fft, hop_length=hop_length)), axis=0)

    # 2. 互相关找延迟
    corr = scipy.signal.correlate(energy_test, energy_ref, mode='full')
    delay_frames = np.argmax(corr) - len(energy_ref) + 1
    delay_samples = delay_frames * hop_length

    print(f"检测到帧延迟: {delay_frames} 帧 ({delay_samples} 采样点, {delay_samples / fs:.3f} 秒)")

    # 3. 对齐信号
    if delay_samples > 0:
        y_test_aligned = y_test[delay_samples:]
        y_ref_aligned = y_ref[:len(y_test_aligned)]
    elif delay_samples < 0:
        y_ref_aligned = y_ref[-delay_samples:]
        y_test_aligned = y_test[:len(y_ref_aligned)]
    else:
        y_ref_aligned = y_ref
        y_test_aligned = y_test

    return y_ref_aligned, y_test_aligned


def align_signals_by_dominant_freq(y_ref, y_test, fs=44100, n_fft=2048, hop_length=512):
    def get_dominant_freq(y):
        S = np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length))
        dom_bin = np.argmax(S, axis=0)
        freqs = librosa.fft_frequencies(sr=fs, n_fft=n_fft)
        return freqs[dom_bin]

    freq_ref = get_dominant_freq(y_ref)
    freq_test = get_dominant_freq(y_test)

    # 能量低的位置主频波动大，使用中值滤波平滑
    freq_ref_smooth = scipy.signal.medfilt(freq_ref, kernel_size=5)
    freq_test_smooth = scipy.signal.medfilt(freq_test, kernel_size=5)

    corr = scipy.signal.correlate(freq_test_smooth, freq_ref_smooth, mode='full')
    delay_frames = np.argmax(corr) - len(freq_ref_smooth) + 1
    delay_samples = delay_frames * hop_length

    print(f"[频率轨迹对齐] 检测到帧延迟: {delay_frames} 帧 ({delay_samples} 采样点, {delay_samples / fs:.3f} 秒)")

    # 对齐
    if delay_samples > 0:
        y_test_aligned = y_test[delay_samples:]
        y_ref_aligned = y_ref[:len(y_test_aligned)]
    elif delay_samples < 0:
        y_ref_aligned = y_ref[-delay_samples:]
        y_test_aligned = y_test[:len(y_ref_aligned)]
    else:
        y_ref_aligned = y_ref
        y_test_aligned = y_test

    min_len = min(len(y_ref_aligned), len(y_test_aligned))
    return y_ref_aligned[:min_len], y_test_aligned[:min_len]


if __name__ == "__main__":
    # ========== 配置 ==========
    REF_LIST = [
        r"20250707\sweep_test_0db.wav",
        # r"20250707\test0.wav",
    ]
    TEST_LIST = [
        r"20250707\test0.wav",
        r"20250707\test1.wav",
        r"20250707\test2.wav",
        r"20250707\test3.wav",
    ]
    OUTPUT_DIR = "align_result"
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    fs = 44100

    for ref_path in REF_LIST:
        for test_path in TEST_LIST:
            # 加载音频
            y_ref, sr1 = librosa.load(ref_path, sr=fs, mono=True)
            y_test, sr2 = librosa.load(test_path, sr=fs, mono=True)

            # 对齐
            y_ref_aligned, y_test_aligned = align_signals_by_energy(y_ref, y_test)

            # 可视化
            ref_name = os.path.splitext(os.path.basename(ref_path))[0]
            test_name = os.path.splitext(os.path.basename(test_path))[0]
            img_name = f"wave_aligned_{ref_name}_vs_{test_name}.png"

            plt.figure(figsize=(12, 4))
            plt.plot(y_ref_aligned, label="参考信号", alpha=0.7)
            plt.plot(y_test_aligned, label="待测信号", alpha=0.7)
            plt.legend()
            plt.title("对齐后波形对比")
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, img_name))
            plt.close()

            print(f"对齐完成，对比图已保存：{img_name}")