#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证纵坐标范围是否固定为-80~40dB
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def verify_fixed_y_range():
    """
    验证纵坐标范围固定效果
    """
    print("🔍 验证纵坐标范围固定为-80~40dB")
    print("="*60)
    
    # 检查输出目录
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    print(f"✅ 输出目录存在: {output_dir}")
    
    # 检查PNG文件
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    print(f"📊 找到 {len(png_files)} 个对数频谱图像文件")
    
    if len(png_files) != 93:
        print(f"⚠️  警告: 期望93个文件，实际找到{len(png_files)}个")
    else:
        print("✅ 文件数量正确: 93个频段")
    
    # 显示文件大小统计
    if png_files:
        file_sizes = []
        for png_file in png_files:
            size = os.path.getsize(png_file)
            file_sizes.append(size)
        
        avg_size = sum(file_sizes) / len(file_sizes)
        min_size = min(file_sizes)
        max_size = max(file_sizes)
        total_size = sum(file_sizes)
        
        print(f"\n📈 文件大小统计:")
        print(f"  平均大小: {avg_size/1024:.1f} KB")
        print(f"  最小文件: {min_size/1024:.1f} KB")
        print(f"  最大文件: {max_size/1024:.1f} KB")
        print(f"  总大小: {total_size/1024/1024:.1f} MB")
    
    # 检查图像规格
    if png_files:
        try:
            with Image.open(png_files[0]) as img:
                width, height = img.size
                print(f"\n🖼️  图像规格:")
                print(f"  尺寸: {width}×{height}px")
                print(f"  模式: {img.mode}")
                print(f"  格式: {img.format}")
        except Exception as e:
            print(f"  ❌ 无法读取图像信息: {e}")
    
    # 显示代表性频段
    print(f"\n🎵 代表性频段:")
    representative_indices = [0, 20, 40, 60, 80, 92]
    
    for idx in representative_indices:
        if idx < len(png_files):
            filename = os.path.basename(png_files[idx])
            freq_str = filename.split('_')[3].replace('Hz.png', '')
            file_size = os.path.getsize(png_files[idx]) / 1024
            print(f"  段 {idx:2d}: {freq_str:>6}Hz - {filename} ({file_size:.1f}KB)")
    
    print(f"\n✅ 验证完成!")
    print(f"📁 所有文件保存在: {output_dir}")
    print(f"🎯 当前版本特点:")
    print(f"   ✅ 使用semilogx对数频率轴")
    print(f"   ✅ 每段去除首尾8%数据")
    print(f"   ✅ 显示动态噪声阈值曲线 (红色线)")
    print(f"   ✅ 频谱曲线 (黑色线)")
    print(f"   ✅ 主频标记 (蓝色圆点)")
    print(f"   ✅ 多进程加速 (8进程)")
    print(f"   ✅ 与harmonic_detector_api_fast.py参数一致")
    print(f"   ✅ 固定纵坐标范围: -80dB ~ 40dB")

def create_y_range_comparison():
    """
    创建纵坐标范围对比图
    """
    print(f"\n🎨 创建纵坐标范围对比概览...")
    
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    # 选择6个代表性频段 - 覆盖低中高频
    selected_indices = [0, 15, 30, 45, 70, 92]
    
    if len(png_files) >= max(selected_indices) + 1:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, seg_idx in enumerate(selected_indices):
            if i < len(axes) and seg_idx < len(png_files):
                try:
                    img = mpimg.imread(png_files[seg_idx])
                    axes[i].imshow(img)
                    axes[i].axis('off')
                    
                    # 从文件名提取频率
                    basename = os.path.basename(png_files[seg_idx])
                    freq_str = basename.split('_')[3].replace('Hz.png', '')
                    axes[i].set_title(f'段 {seg_idx}: {freq_str}Hz\n纵坐标: -80~40dB', 
                                    fontsize=12, fontweight='bold')
                    
                except Exception as e:
                    axes[i].text(0.5, 0.5, f'无法加载\n段 {seg_idx}', 
                               ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].axis('off')
        
        plt.suptitle('固定纵坐标范围 (-80dB ~ 40dB) 效果展示', fontsize=16, fontweight='bold')
        plt.tight_layout()
        overview_file = os.path.join(output_dir, "固定纵坐标范围概览.png")
        plt.savefig(overview_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 概览图已保存: {overview_file}")
    else:
        print(f"❌ 无法创建概览图，文件数量不足")

def analyze_y_range_benefits():
    """
    分析固定纵坐标范围的好处
    """
    print(f"\n📊 固定纵坐标范围的优势分析:")
    
    print(f"\n🎯 统一比较标准:")
    print(f"  ✅ 所有频段使用相同的dB刻度")
    print(f"  ✅ 便于不同频段间的直接对比")
    print(f"  ✅ 信号强度差异一目了然")
    
    print(f"\n📈 动态范围覆盖:")
    print(f"  ✅ -80dB: 覆盖典型噪声底噪")
    print(f"  ✅ 40dB: 覆盖强信号峰值")
    print(f"  ✅ 120dB动态范围: 满足音频分析需求")
    
    print(f"\n🔍 分析便利性:")
    print(f"  ✅ 噪声阈值变化清晰可见")
    print(f"  ✅ 信号与噪声对比明确")
    print(f"  ✅ 频谱形状特征突出")
    
    print(f"\n📋 应用场景:")
    print(f"  ✅ 批量频段对比分析")
    print(f"  ✅ 异常频段快速识别")
    print(f"  ✅ 信号质量评估")
    print(f"  ✅ 噪声特性研究")

def compare_with_auto_range():
    """
    与自动范围的对比
    """
    print(f"\n⚖️  固定范围 vs 自动范围对比:")
    
    print(f"\n固定范围 (-80~40dB):")
    print(f"  ✅ 优点:")
    print(f"    - 统一标准，便于对比")
    print(f"    - 突出信号强度差异")
    print(f"    - 适合批量分析")
    print(f"  ⚠️  注意:")
    print(f"    - 可能截断极值")
    print(f"    - 弱信号可能不够突出")
    
    print(f"\n自动范围:")
    print(f"  ✅ 优点:")
    print(f"    - 充分利用显示空间")
    print(f"    - 突出每段的细节")
    print(f"  ❌ 缺点:")
    print(f"    - 不同段间难以对比")
    print(f"    - 掩盖真实强度差异")
    
    print(f"\n🎯 推荐使用场景:")
    print(f"  固定范围: 多频段对比分析、质量评估")
    print(f"  自动范围: 单频段详细分析、特征提取")

if __name__ == "__main__":
    verify_fixed_y_range()
    create_y_range_comparison()
    analyze_y_range_benefits()
    compare_with_auto_range()
