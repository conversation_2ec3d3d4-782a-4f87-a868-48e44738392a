#!/usr/bin/env python3
"""
基于峰值的竖线检测器
Peak-Based Vertical Line Detector
直接基于时间维度能量峰值检测竖线
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PeakBasedLineDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        print(f"基于峰值的竖线检测器初始化完成")
        print(f"检测策略: 时间维度能量峰值 → 对应时刻的频谱竖线分析")
    
    def detect_peak_based_lines(self, audio_path, start_time, end_time):
        """基于峰值检测竖线"""
        print(f"\n基于峰值检测竖线: {os.path.basename(audio_path)}")
        print(f"时间段: {start_time:.3f}s - {end_time:.3f}s")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选 (100-20000Hz)
            freq_mask = (frequencies >= 100) & (frequencies <= 20000)
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 提取指定时间段
            segment_mask = (times >= start_time) & (times <= end_time)
            segment_times = times[segment_mask]
            segment_Zxx = Zxx[:, segment_mask]
            
            if segment_Zxx.shape[1] == 0:
                print(f"指定时间段无数据")
                return None
            
            print(f"分析数据: {segment_Zxx.shape[1]}个时间片, {len(frequencies)}个频率点")
            
            # 计算功率谱
            power_spectrum = np.abs(segment_Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 基于峰值检测竖线
            vertical_lines = self._detect_lines_by_energy_peaks(
                power_db, frequencies, segment_times
            )
            
            # 可视化结果
            self._visualize_peak_detection(
                power_db, frequencies, segment_times, vertical_lines, 
                audio_path, start_time, end_time
            )
            
            return {
                'power_spectrum': power_db,
                'frequencies': frequencies,
                'times': segment_times,
                'vertical_lines': vertical_lines,
                'detection_summary': self._summarize_detection(vertical_lines)
            }
            
        except Exception as e:
            print(f"检测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _detect_lines_by_energy_peaks(self, power_db, frequencies, times):
        """基于能量峰值检测竖线"""
        print("\n1. 计算时间维度总能量...")
        
        # 计算每个时间片的总能量
        total_energy = np.sum(power_db, axis=0)
        
        print(f"   能量范围: {np.min(total_energy):.1f} - {np.max(total_energy):.1f} dB")
        
        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        
        print("\n2. 检测能量峰值...")
        
        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        min_height = np.min(smoothed_energy) + energy_range * 0.2  # 峰值至少要比最低值高20%
        min_prominence = energy_range * 0.1  # 峰值突出度至少10%
        
        print(f"   峰值检测参数:")
        print(f"     最小高度: {min_height:.1f} dB")
        print(f"     最小突出度: {min_prominence:.1f} dB")
        
        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy, 
                                     height=min_height,
                                     distance=2,  # 峰值间至少间隔2个时间片
                                     prominence=min_prominence)
        
        print(f"   检测到 {len(peaks)} 个能量峰值")
        
        # 分析每个峰值对应的竖线
        vertical_lines = []
        
        print("\n3. 分析峰值对应的频谱竖线...")
        
        for i, peak_idx in enumerate(peaks):
            peak_time = times[peak_idx]
            peak_energy = total_energy[peak_idx]
            peak_power_spectrum = power_db[:, peak_idx]
            
            print(f"\n   峰值 {i+1}: 时刻={peak_time:.3f}s, 能量={peak_energy:.1f}dB")
            
            # 分析该时刻的频谱特征
            line_analysis = self._analyze_spectrum_at_peak(
                peak_power_spectrum, frequencies, peak_time, peak_idx, peak_energy
            )
            
            if line_analysis:
                vertical_lines.append(line_analysis)
                print(f"     ✅ 确认为竖线: 频率跨度={line_analysis['frequency_span']:.0f}Hz, "
                      f"覆盖比例={line_analysis['frequency_ratio']:.1%}")
            else:
                print(f"     ❌ 不符合竖线特征")
        
        return vertical_lines
    
    def _analyze_spectrum_at_peak(self, power_spectrum, frequencies, peak_time, peak_idx, peak_energy):
        """分析峰值时刻的频谱是否为竖线"""
        
        # 计算功率分布统计
        power_mean = np.mean(power_spectrum)
        power_std = np.std(power_spectrum)
        power_median = np.median(power_spectrum)
        
        # 使用多种方法确定高能量频率点
        methods = {
            'percentile_90': np.percentile(power_spectrum, 90),
            'mean_plus_std': power_mean + 1.0 * power_std,
            'median_plus_range': power_median + (np.max(power_spectrum) - power_median) * 0.3
        }
        
        best_method = None
        best_score = 0
        
        for method_name, threshold in methods.items():
            high_energy_mask = power_spectrum > threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) >= 3:  # 至少3个频率点
                # 计算频率跨度和覆盖比例
                freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                freq_ratio = len(high_energy_indices) / len(frequencies)
                
                # 计算连续性分数
                continuous_segments = self._find_continuous_segments(high_energy_indices)
                continuity_score = sum(len(seg) for seg in continuous_segments) / len(high_energy_indices)
                
                # 综合评分
                span_score = min(1.0, freq_span / 5000.0)  # 频率跨度分数
                ratio_score = min(1.0, freq_ratio / 0.3)   # 覆盖比例分数
                total_score = span_score * ratio_score * continuity_score
                
                if total_score > best_score and freq_span >= 300:  # 至少300Hz跨度
                    best_score = total_score
                    best_method = {
                        'name': method_name,
                        'threshold': threshold,
                        'high_energy_indices': high_energy_indices,
                        'freq_span': freq_span,
                        'freq_ratio': freq_ratio,
                        'continuity_score': continuity_score,
                        'total_score': total_score,
                        'continuous_segments': continuous_segments
                    }
        
        if best_method and best_score > 0.1:  # 最低分数阈值
            return {
                'time_index': peak_idx,
                'time': peak_time,
                'peak_energy': peak_energy,
                'frequency_span': best_method['freq_span'],
                'frequency_ratio': best_method['freq_ratio'],
                'line_strength': best_score,
                'detection_method': f"peak_analysis_{best_method['name']}",
                'threshold': best_method['threshold'],
                'high_energy_indices': best_method['high_energy_indices'],
                'continuous_segments': best_method['continuous_segments'],
                'max_power': np.max(power_spectrum[best_method['high_energy_indices']]),
                'mean_power': np.mean(power_spectrum[best_method['high_energy_indices']]),
                'continuity_score': best_method['continuity_score']
            }
        
        return None
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:  # 至少2个连续点
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        # 处理最后一段
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _summarize_detection(self, vertical_lines):
        """总结检测结果"""
        if not vertical_lines:
            return {
                'total_lines': 0,
                'average_strength': 0,
                'frequency_spans': [],
                'time_points': []
            }
        
        return {
            'total_lines': len(vertical_lines),
            'average_strength': np.mean([line['line_strength'] for line in vertical_lines]),
            'frequency_spans': [line['frequency_span'] for line in vertical_lines],
            'time_points': [line['time'] for line in vertical_lines],
            'max_strength': max(line['line_strength'] for line in vertical_lines),
            'peak_energies': [line['peak_energy'] for line in vertical_lines]
        }
    
    def _visualize_peak_detection(self, power_db, frequencies, times, vertical_lines, 
                                audio_path, start_time, end_time):
        """可视化峰值检测结果"""
        print(f"\n4. 生成可视化图表...")
        
        # 计算总能量
        total_energy = np.sum(power_db, axis=0)
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'基于峰值的竖线检测: {os.path.basename(audio_path)}\n时间段: {start_time:.3f}s - {end_time:.3f}s', fontsize=14)
        
        # 1. 原始功率谱 + 竖线标记
        im1 = axes[0, 0].imshow(power_db, aspect='auto', origin='lower', 
                               extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                               cmap='viridis')
        axes[0, 0].set_title('功率谱 + 检测到的竖线')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('频率 (Hz)')
        
        # 标记检测到的竖线
        colors = ['red', 'orange', 'yellow', 'green', 'blue']
        for i, line in enumerate(vertical_lines):
            color = colors[i % len(colors)]
            axes[0, 0].axvline(x=line['time'], color=color, linewidth=3, alpha=0.8,
                              label=f"竖线{i+1}: {line['time']:.3f}s")
        
        if vertical_lines:
            axes[0, 0].legend()
        plt.colorbar(im1, ax=axes[0, 0], label='功率 (dB)')
        
        # 2. 时间维度能量分析 + 峰值标记
        axes[0, 1].plot(times, total_energy, 'b-', linewidth=1, label='总能量', alpha=0.7)
        axes[0, 1].plot(times, smoothed_energy, 'g-', linewidth=2, label='平滑能量')
        
        # 标记峰值
        for i, line in enumerate(vertical_lines):
            color = colors[i % len(colors)]
            axes[0, 1].axvline(x=line['time'], color=color, linewidth=2, alpha=0.8)
            axes[0, 1].scatter(line['time'], line['peak_energy'], color=color, s=100, zorder=5,
                              label=f"峰值{i+1}")
        
        axes[0, 1].set_title('时间维度能量分析 + 峰值检测')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('总能量 (dB)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 竖线特征分析
        if vertical_lines:
            line_indices = range(len(vertical_lines))
            freq_spans = [line['frequency_span'] for line in vertical_lines]
            freq_ratios = [line['frequency_ratio'] * 100 for line in vertical_lines]
            strengths = [line['line_strength'] for line in vertical_lines]
            
            x_pos = np.arange(len(line_indices))
            width = 0.25
            
            bars1 = axes[1, 0].bar(x_pos - width, freq_spans, width, label='频率跨度 (Hz)', alpha=0.7)
            
            ax_twin = axes[1, 0].twinx()
            bars2 = ax_twin.bar(x_pos, freq_ratios, width, label='覆盖比例 (%)', alpha=0.7, color='orange')
            bars3 = ax_twin.bar(x_pos + width, [s*100 for s in strengths], width, label='强度 (×100)', alpha=0.7, color='green')
            
            axes[1, 0].set_title('竖线特征分析')
            axes[1, 0].set_xlabel('竖线编号')
            axes[1, 0].set_ylabel('频率跨度 (Hz)')
            ax_twin.set_ylabel('比例/强度')
            
            axes[1, 0].set_xticks(x_pos)
            axes[1, 0].set_xticklabels([f'L{i+1}' for i in line_indices])
            
            axes[1, 0].legend(loc='upper left')
            ax_twin.legend(loc='upper right')
        else:
            axes[1, 0].text(0.5, 0.5, '未检测到竖线', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('竖线特征分析')
        
        # 4. 检测方法对比
        if vertical_lines:
            methods = [line['detection_method'] for line in vertical_lines]
            method_counts = {}
            for method in methods:
                method_counts[method] = method_counts.get(method, 0) + 1
            
            axes[1, 1].pie(method_counts.values(), labels=method_counts.keys(), autopct='%1.1f%%')
            axes[1, 1].set_title('检测方法分布')
        else:
            axes[1, 1].text(0.5, 0.5, '无检测结果', ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('检测方法分布')
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'peak_based_line_detection_{start_time:.3f}s_{end_time:.3f}s.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"   可视化结果已保存: {output_filename}")
        
        plt.show()

def main():
    """主函数"""
    # 初始化检测器
    detector = PeakBasedLineDetector()
    
    # 检测指定频段的竖线
    audio_path = "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav"
    start_time = 8.009
    end_time = 8.162
    
    # 执行检测
    results = detector.detect_peak_based_lines(audio_path, start_time, end_time)
    
    if results and results['vertical_lines']:
        print(f"\n" + "="*60)
        print(f"最终检测结果:")
        summary = results['detection_summary']
        print(f"  检测到竖线数量: {summary['total_lines']}")
        print(f"  平均强度: {summary['average_strength']:.3f}")
        print(f"  最大强度: {summary['max_strength']:.3f}")
        
        for i, line in enumerate(results['vertical_lines']):
            print(f"  竖线{i+1}: 时刻={line['time']:.3f}s, "
                  f"频率跨度={line['frequency_span']:.0f}Hz, "
                  f"强度={line['line_strength']:.3f}, "
                  f"方法={line['detection_method']}")
    else:
        print(f"\n未检测到竖线异常")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
