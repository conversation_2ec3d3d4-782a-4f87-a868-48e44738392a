#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对任意音频文件进行93段动态噪声阈值可视化
每段生成一张图片，显示动态噪声阈值曲线和频谱
支持命令行参数指定音频文件路径

用法:
    python visualize_audio_segments.py <音频文件路径>
    python visualize_audio_segments.py <音频文件路径> -o <输出目录>
    python visualize_audio_segments.py <音频文件路径> --no-trim
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import argparse
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_for_segment_detailed(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声 - 返回详细数据用于可视化
    与harmonic_detector_api.py完全一致的算法
    """
    
    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    raw_noise_levels = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            raw_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features,
        'local_noise_levels': smoothed_noise,
        'window_centers': window_centers,
        'raw_noise_levels': raw_noise_levels,
        'exclude_mask': exclude_mask
    }

def analyze_single_segment_detailed(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    详细分析单个频段
    use_trim: True=8%修剪, False=完整段(与harmonic_detector_api一致)
    """
    
    try:
        if use_trim:
            # 提取段音频，去掉开头和结尾各8%
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage
            
            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration
            
            # 确保修剪后的时间段有效
            if trimmed_end_time <= trimmed_start_time:
                return None
            
            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
            actual_duration = trimmed_end_time - trimmed_start_time
        else:
            # 使用完整段（与harmonic_detector_api一致）
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            actual_duration = end_time - start_time
        
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            return None
        
        # 标准化 - 与谐波检测系统一致
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与谐波检测系统一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数 - 与谐波检测系统一致
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT - 与谐波检测系统一致
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率 - 与谐波检测系统一致
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz - 与谐波检测系统一致
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 找主频 - 与谐波检测系统一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
            fundamental_power = display_power[actual_idx]
            fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(display_power) + 1e-12)
        
        # 动态噪声分析
        noise_analysis = estimate_dynamic_noise_for_segment_detailed(display_freqs, display_power, fundamental_freq)
        
        if noise_analysis:
            # 计算信噪比
            snr_db = fundamental_power_db - noise_analysis['global_noise_floor_db']
        else:
            snr_db = 0
        
        # 谐波检测
        harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'display_freqs': display_freqs,
            'display_power': display_power,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'harmonic_count': len(harmonic_analysis) if harmonic_analysis else 0,
            'snr_db': snr_db,
            'actual_duration': actual_duration,
            'original_duration': end_time - start_time,
            'use_trim': use_trim
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_single_segment_visualization(segment_result, output_dir, filename=""):
    """
    创建单个段的详细可视化
    第一张子图：对数坐标频谱
    第二张子图：线性坐标频谱
    """

    if not segment_result:
        return None

    seg_idx = segment_result['seg_idx']
    expected_freq = segment_result['expected_freq']
    fundamental_freq = segment_result['fundamental_freq']
    freqs = segment_result['display_freqs']
    power = segment_result['display_power']
    power_db = 10 * np.log10(power + 1e-12)
    noise_analysis = segment_result['noise_analysis']
    harmonic_analysis = segment_result['harmonic_analysis']

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # 动态显示文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    trim_status = "8%修剪" if segment_result.get('use_trim', True) else "完整段"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n动态噪声阈值详细分析 ({trim_status})',
                 fontsize=14, fontweight='bold')

    # 子图1: 完整频谱和动态噪声阈值
    ax1.plot(freqs, power_db, 'b-', linewidth=1, alpha=0.7, label='频谱')

    # 标记主频
    ax1.axvline(fundamental_freq, color='red', linestyle='-', linewidth=2,
              alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')

    # 绘制噪声特征
    if noise_analysis:
        noise_floor = noise_analysis['global_noise_floor_db']
        ax1.axhline(noise_floor, color='green', linestyle='--', linewidth=2,
                  alpha=0.8, label=f'全局噪声底噪: {noise_floor:.1f}dB')

        # 绘制动态噪声阈值线
        if 'local_noise_levels' in noise_analysis and 'window_centers' in noise_analysis:
            local_noise = noise_analysis['local_noise_levels']
            window_centers = noise_analysis['window_centers']

            ax1.plot(window_centers, local_noise, color='cyan', linestyle='-',
                   linewidth=2, alpha=0.8, label='动态噪声阈值')

            ax1.fill_between(window_centers, local_noise, noise_floor,
                           alpha=0.2, color='cyan', label='噪声变化区域')

        # 标记排除的谐波区域
        if 'exclude_mask' in noise_analysis:
            exclude_mask = noise_analysis['exclude_mask']
            excluded_freqs = freqs[exclude_mask]
            excluded_powers = power_db[exclude_mask]

            if len(excluded_freqs) > 0:
                ax1.scatter(excluded_freqs, excluded_powers,
                          c='gray', s=1, alpha=0.5, label='排除区域(谐波)')

        # 计算自适应检测阈值
        noise_variation_db = noise_analysis['noise_variation_db']
        stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']

        if noise_variation_db > 10:
            base_snr_threshold = 8.0
        elif noise_variation_db > 5:
            base_snr_threshold = 10.0
        else:
            base_snr_threshold = 12.0

        if stability_score > 0.8:
            stability_adjustment = 1.0
        elif stability_score > 0.6:
            stability_adjustment = 0.0
        else:
            stability_adjustment = -1.0

        adjusted_snr_threshold = base_snr_threshold + stability_adjustment
        detection_threshold = noise_floor + adjusted_snr_threshold

        ax1.axhline(detection_threshold, color='orange', linestyle='--', linewidth=1.5,
                  alpha=0.8, label=f'谐波检测阈值: {detection_threshold:.1f}dB')

    # 标记检测到的谐波
    if harmonic_analysis:
        harmonic_freqs = [h['freq'] for h in harmonic_analysis]
        harmonic_powers = [h['power_db'] for h in harmonic_analysis]
        harmonic_orders = [h['order'] for h in harmonic_analysis]

        ax1.scatter(harmonic_freqs, harmonic_powers, c='red', s=50, alpha=0.8,
                  marker='^', label=f'谐波({len(harmonic_analysis)}个)')

        # 标注前几个谐波的次数
        for _, (freq, power, order) in enumerate(zip(harmonic_freqs[:5], harmonic_powers[:5], harmonic_orders[:5])):
            ax1.annotate(f'{order}', (freq, power), xytext=(5, 5),
                       textcoords='offset points', fontsize=8, color='red')

    # 计算第一张子图的动态噪声阈值特征（对数坐标）
    noise_features_text1 = ""
    if noise_analysis and 'local_noise_levels' in noise_analysis:
        local_noise = noise_analysis['local_noise_levels']
        window_centers = noise_analysis['window_centers']

        if len(local_noise) > 2:
            # 对数坐标下的特征计算
            log_window_centers = np.log10(window_centers)

            # 在对数空间计算差分
            diff1_log = np.diff(local_noise) / np.diff(log_window_centers)  # 对数空间的斜率
            diff2_log = np.diff(diff1_log) if len(diff1_log) > 1 else [0]  # 对数空间的曲率

            std_curvature_log = np.std(diff2_log)
            std_slope_log = np.std(diff1_log)
            noise_variation = noise_analysis['noise_variation_db']

            noise_features_text1 = f"曲率标准差: {std_curvature_log:.3f}, 斜率标准差: {std_slope_log:.3f}, 噪声变化: {noise_variation:.1f}dB"
        else:
            noise_features_text1 = "曲率标准差: 0.000, 斜率标准差: 0.000, 噪声变化: 0.0dB"

    # 设置第一张子图坐标轴
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('功率 (dB)')
    ax1.set_title(f'频谱和动态噪声阈值（对数坐标）- SNR: {segment_result["snr_db"]:.1f}dB\n{noise_features_text1}')
    ax1.set_xlim(100, 20000)
    ax1.set_xscale('log')
    ax1.set_ylim(-80, 40)
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)

    # 子图2: 频谱图（线性横坐标）
    ax2.plot(freqs, power_db, 'b-', linewidth=1, alpha=0.7, label='频谱')

    # 标记主频
    ax2.axvline(fundamental_freq, color='red', linestyle='-', linewidth=2,
              alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')

    # 绘制噪声特征
    if noise_analysis:
        noise_floor = noise_analysis['global_noise_floor_db']
        ax2.axhline(noise_floor, color='green', linestyle='--', linewidth=2,
                  alpha=0.8, label=f'全局噪声底噪: {noise_floor:.1f}dB')

        # 绘制动态噪声阈值线
        if 'local_noise_levels' in noise_analysis and 'window_centers' in noise_analysis:
            local_noise = noise_analysis['local_noise_levels']
            window_centers = noise_analysis['window_centers']

            ax2.plot(window_centers, local_noise, color='cyan', linestyle='-',
                   linewidth=2, alpha=0.8, label='动态噪声阈值')

            ax2.fill_between(window_centers, local_noise, noise_floor,
                           alpha=0.2, color='cyan', label='噪声变化区域')

        # 标记排除的谐波区域
        if 'exclude_mask' in noise_analysis:
            exclude_mask = noise_analysis['exclude_mask']
            excluded_freqs = freqs[exclude_mask]
            excluded_powers = power_db[exclude_mask]

            if len(excluded_freqs) > 0:
                ax2.scatter(excluded_freqs, excluded_powers,
                          c='gray', s=1, alpha=0.5, label='排除区域(谐波)')

        # 计算自适应检测阈值
        noise_variation_db = noise_analysis['noise_variation_db']
        stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']

        if noise_variation_db > 10:
            base_snr_threshold = 8.0
        elif noise_variation_db > 5:
            base_snr_threshold = 10.0
        else:
            base_snr_threshold = 12.0

        if stability_score > 0.8:
            stability_adjustment = 1.0
        elif stability_score > 0.6:
            stability_adjustment = 0.0
        else:
            stability_adjustment = -1.0

        adjusted_snr_threshold = base_snr_threshold + stability_adjustment
        detection_threshold = noise_floor + adjusted_snr_threshold

        ax2.axhline(detection_threshold, color='orange', linestyle='--', linewidth=1.5,
                  alpha=0.8, label=f'谐波检测阈值: {detection_threshold:.1f}dB')

    # 标记检测到的谐波
    if harmonic_analysis:
        harmonic_freqs = [h['freq'] for h in harmonic_analysis]
        harmonic_powers = [h['power_db'] for h in harmonic_analysis]
        harmonic_orders = [h['order'] for h in harmonic_analysis]

        ax2.scatter(harmonic_freqs, harmonic_powers, c='red', s=50, alpha=0.8,
                  marker='^', label=f'谐波({len(harmonic_analysis)}个)')

        # 标注前几个谐波的次数
        for _, (freq, power, order) in enumerate(zip(harmonic_freqs[:5], harmonic_powers[:5], harmonic_orders[:5])):
            ax2.annotate(f'{order}', (freq, power), xytext=(5, 5),
                       textcoords='offset points', fontsize=8, color='red')

    # 计算第二张子图的动态噪声阈值特征（线性坐标）
    noise_features_text2 = ""
    if noise_analysis and 'local_noise_levels' in noise_analysis:
        local_noise = noise_analysis['local_noise_levels']
        window_centers = noise_analysis['window_centers']

        if len(local_noise) > 2:
            # 线性坐标下的特征计算
            # 在线性空间计算差分
            diff1_linear = np.diff(local_noise) / np.diff(window_centers)  # 线性空间的斜率
            diff2_linear = np.diff(diff1_linear) if len(diff1_linear) > 1 else [0]  # 线性空间的曲率

            std_curvature_linear = np.std(diff2_linear)
            std_slope_linear = np.std(diff1_linear)

            # 计算峰值和谷值数量
            if len(local_noise) > 2:
                diff_signs = np.diff(np.sign(np.diff(local_noise)))
                num_peaks = len([x for x in diff_signs if x < 0])
                num_valleys = len([x for x in diff_signs if x > 0])
            else:
                num_peaks = 0
                num_valleys = 0

            noise_features_text2 = f"曲率标准差: {std_curvature_linear:.6f}, 斜率标准差: {std_slope_linear:.6f}, 峰值: {num_peaks}, 谷值: {num_valleys}"
        else:
            noise_features_text2 = "曲率标准差: 0.000000, 斜率标准差: 0.000000, 峰值: 0, 谷值: 0"

    # 设置第二张子图坐标轴（线性刻度）
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('功率 (dB)')
    ax2.set_title(f'频谱图（线性坐标） - SNR: {segment_result["snr_db"]:.1f}dB\n{noise_features_text2}')
    ax2.set_xlim(100, 20000)
    # 注意：这里不设置xscale('log')，保持线性刻度
    ax2.set_ylim(-80, 40)
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)

    # 添加统计信息
    trim_status = "8%修剪" if segment_result.get('use_trim', True) else "完整段"
    stats_text = f"""段{seg_idx}统计信息:
期望频率: {expected_freq:.1f}Hz
实际主频: {fundamental_freq:.1f}Hz
信噪比: {segment_result['snr_db']:.1f}dB
谐波数: {segment_result['harmonic_count']}个
实际时长: {segment_result['actual_duration']:.2f}s ({trim_status})
原始时长: {segment_result['original_duration']:.2f}s"""

    if noise_analysis:
        stats_text += f"""
噪声底噪: {noise_analysis['global_noise_floor_db']:.1f}dB
噪声变化: {noise_analysis['noise_variation_db']:.1f}dB"""

    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment(args):
    """
    处理单个段的包装函数，用于多进程
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 处理段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析单个段
        segment_result = analyze_single_segment_detailed(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_result:
            # 生成可视化
            viz_path = create_single_segment_visualization(segment_result, output_dir, filename)

            if viz_path:
                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'snr_db': segment_result['snr_db'],
                    'harmonic_count': segment_result['harmonic_count'],
                    'expected_freq': expected_freq
                }
                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 SNR: {segment_result['snr_db']:.1f}dB, 谐波: {segment_result['harmonic_count']}个")
                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def analyze_audio_segments_parallel(audio_path, output_dir, use_trim=True, num_processes=None):
    """
    并行分析音频文件的93段
    """

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        if num_processes is None:
            num_processes = min(cpu_count(), len(step_boundaries), 8)  # 最多8个进程

        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, use_trim,
                   output_dir, audio_path, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行处理...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        return {
            'successful_results': successful_results,
            'failed_results': failed_results,
            'processing_time': processing_time,
            'total_segments': len(step_boundaries)
        }

    except Exception as e:
        print(f"❌ 并行处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    主函数 - 支持命令行参数的音频段分析
    """

    parser = argparse.ArgumentParser(description='对音频文件进行93段动态噪声阈值可视化')
    parser.add_argument('audio_path', nargs='?', help='音频文件路径')
    parser.add_argument('-o', '--output', help='输出目录路径')
    parser.add_argument('--no-trim', action='store_true', help='不进行8%修剪，使用完整段(与harmonic_detector_api一致)')
    parser.add_argument('-j', '--jobs', type=int, help='并行进程数 (默认: min(CPU核心数, 8))')
    parser.add_argument('--no-parallel', action='store_true', help='禁用多进程，使用单进程处理')

    args = parser.parse_args()

    # 如果没有提供音频路径，显示帮助信息
    if not args.audio_path:
        print("❌ 请指定音频文件路径")
        print("\n用法示例:")
        print("  python visualize_audio_segments.py audio.wav")
        print("  python visualize_audio_segments.py audio.wav -o output_dir")
        print("  python visualize_audio_segments.py audio.wav --no-trim")
        return

    # 检查文件是否存在
    if not os.path.exists(args.audio_path):
        print(f"❌ 音频文件不存在: {args.audio_path}")
        return

    # 获取文件名（不含扩展名）
    base_filename = os.path.splitext(os.path.basename(args.audio_path))[0]

    print("🎯 音频文件93段动态噪声阈值可视化")
    print("📝 每段去掉开头和结尾8%后进行计算" if not args.no_trim else "📝 使用完整段进行计算(与harmonic_detector_api一致)")
    print("="*80)

    # 创建输出目录
    if args.output:
        output_dir = args.output
    else:
        # 创建安全的文件名
        safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
        output_dir = f"{safe_filename}_segments_analysis"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    target_file = args.audio_path
    use_trim = not args.no_trim

    # 处理模式选择
    if args.no_parallel:
        print("🔧 使用单进程模式")
        # 单进程处理（原始方式）
        try:
            # 获取频段分割
            print("🔍 进行频段分割...")
            step_boundaries, freq_table, _ = split_freq_steps_optimized(
                target_file, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )

            print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

            # 加载音频
            print("🔊 加载音频文件...")
            y, sr = librosa.load(target_file, sr=48000)
            if sr != 48000:
                y = librosa.resample(y, orig_sr=sr, target_sr=48000)

            print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

            # 单进程分析所有段
            print(f"\n📊 开始单进程分析{len(step_boundaries)}段...")
            start_time = time.time()
            successful_count = 0
            failed_count = 0

            for seg_idx in range(len(step_boundaries)):
                start_time_seg, end_time_seg = step_boundaries[seg_idx]
                expected_freq = freq_table[seg_idx]

                print(f"[{seg_idx+1}/{len(step_boundaries)}] 分析段{seg_idx}: {expected_freq:.1f}Hz")

                # 分析单个段
                segment_result = analyze_single_segment_detailed(
                    seg_idx, start_time_seg, end_time_seg, expected_freq, y, sr, use_trim
                )

                if segment_result:
                    # 生成可视化
                    viz_path = create_single_segment_visualization(segment_result, output_dir, target_file)

                    if viz_path:
                        successful_count += 1
                        print(f"  ✅ 成功生成: {os.path.basename(viz_path)}")
                        print(f"  📊 SNR: {segment_result['snr_db']:.1f}dB, 谐波: {segment_result['harmonic_count']}个")
                    else:
                        failed_count += 1
                        print(f"  ❌ 可视化失败")
                else:
                    failed_count += 1
                    print(f"  ❌ 分析失败")

            end_time = time.time()
            processing_time = end_time - start_time

            # 生成汇总统计
            print("\n" + "="*80)
            print(f"📊 {base_filename}文件93段分析完成统计 (单进程):")
            print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
            print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
            print(f"  ✅ 成功: {successful_count}个段")
            print(f"  ❌ 失败: {failed_count}个段")
            print(f"  📁 输出目录: {output_dir}")
            print(f"  📊 生成图片: {successful_count}张")
            print(f"  🔧 处理模式: {'8%修剪' if use_trim else '完整段(与API一致)'}")

        except Exception as e:
            print(f"❌ 单进程处理失败: {e}")
            import traceback
            traceback.print_exc()
            return
    else:
        print("🚀 使用多进程模式")
        # 多进程处理
        result = analyze_audio_segments_parallel(
            target_file, output_dir, use_trim, args.jobs
        )

        if not result:
            return

        successful_results = result['successful_results']
        failed_results = result['failed_results']
        processing_time = result['processing_time']
        total_segments = result['total_segments']

        successful_count = len(successful_results)
        failed_count = len(failed_results)

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件93段分析完成统计 (多进程):")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/total_segments:.1f}秒")
        print(f"  ✅ 成功: {successful_count}个段")
        print(f"  ❌ 失败: {failed_count}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {successful_count}张")
        print(f"  🔧 处理模式: {'8%修剪' if use_trim else '完整段(与API一致)'}")

    # 通用输出信息
    if successful_count > 0:
        print(f"\n📈 每张图片包含:")
        print(f"  🔸 完整频谱 (100Hz-20kHz)")
        print(f"  🔸 动态噪声阈值曲线")
        print(f"  🔸 谐波检测结果")
        print(f"  🔸 噪声特征统计")
        print(f"  🔸 曲率和斜率标准差")

    print("="*80)
    print(f"🎯 {base_filename}文件93段动态噪声阈值可视化完成！")

if __name__ == "__main__":
    main()
