import os
import numpy as np
import librosa
import pandas as pd
from scipy.signal import windows, butter, filtfilt
from scipy.stats import skew, kurtosis
from freq_split import split_freq_steps
from librosa.feature import spectral_flatness, spectral_centroid, spectral_bandwidth, spectral_rolloff
import warnings
warnings.filterwarnings('ignore')

class OptimizedFeatureExtractor:
    """
    优化的特征提取器，专门针对正负样本分类
    删除无效特征，增加分帧检测，提高特征区分度
    """
    
    def __init__(self, 
                 frame_length=2048,
                 hop_length=512,
                 n_frames_per_segment=5,  # 每个频段分帧数
                 freq_bands=[(20, 200), (200, 2000), (2000, 8000), (8000, 20000)]):
        self.frame_length = frame_length
        self.hop_length = hop_length
        self.n_frames_per_segment = n_frames_per_segment
        self.freq_bands = freq_bands
        
    def extract_frame_features(self, y_seg, sr, f0):
        """
        对音频段进行分帧特征提取
        """
        # 将音频段分成多个帧
        frame_size = len(y_seg) // self.n_frames_per_segment
        frame_features = []
        
        for i in range(self.n_frames_per_segment):
            start_idx = i * frame_size
            end_idx = min((i + 1) * frame_size, len(y_seg))
            frame = y_seg[start_idx:end_idx]
            
            if len(frame) < 256:  # 帧太短，跳过
                continue
                
            # 提取单帧特征
            frame_feat = self._extract_single_frame_features(frame, sr, f0)
            frame_features.append(frame_feat)
        
        if not frame_features:
            return {}
            
        # 统计帧间特征
        frame_stats = self._compute_frame_statistics(frame_features)
        return frame_stats
    
    def _extract_single_frame_features(self, frame, sr, f0):
        """
        提取单帧的核心特征
        """
        features = {}
        
        # STFT分析
        S = np.abs(librosa.stft(frame, n_fft=self.frame_length, hop_length=self.hop_length))**2
        freqs = librosa.fft_frequencies(sr=sr, n_fft=self.frame_length)
        
        if S.size == 0:
            return features
            
        # 1. 基础频谱特征
        spec_mean = S.mean(axis=1)
        features['spectral_energy'] = np.sum(spec_mean)
        features['spectral_peak'] = np.max(spec_mean)
        features['spectral_flatness'] = spectral_flatness(S=S).mean()
        
        # 2. 频带能量分布
        total_energy = np.sum(S)
        for i, (low_freq, high_freq) in enumerate(self.freq_bands):
            band_mask = (freqs >= low_freq) & (freqs <= high_freq)
            band_energy = np.sum(S[band_mask]) / (total_energy + 1e-12)
            features[f'band_{i}_energy_ratio'] = band_energy
        
        # 3. 主频相关特征
        peak_idx = np.argmax(spec_mean)
        peak_freq = freqs[peak_idx]
        features['peak_freq_deviation'] = abs(peak_freq - f0) / f0 if f0 > 0 else 0
        
        # 主频带外能量（关键特征）
        main_bw = min(100, f0 * 0.1) if f0 > 0 else 100
        main_mask = (freqs >= peak_freq - main_bw) & (freqs <= peak_freq + main_bw)
        out_band_energy = np.sum(S[~main_mask]) / (total_energy + 1e-12)
        features['out_band_energy_ratio'] = out_band_energy
        
        # 4. 噪声特征
        # 高频噪声检测
        high_freq_mask = freqs > 8000
        if np.any(high_freq_mask):
            high_freq_energy = np.sum(S[high_freq_mask]) / (total_energy + 1e-12)
            features['high_freq_noise_ratio'] = high_freq_energy
            
            # 高频段的不规律性
            high_freq_spec = S[high_freq_mask, :]
            if high_freq_spec.size > 0:
                high_freq_var = np.var(high_freq_spec.mean(axis=0))
                features['high_freq_irregularity'] = high_freq_var
        
        # 5. 窄带干扰检测（简化版）
        # 检测异常突出的频率峰
        median_energy = np.median(spec_mean)
        std_energy = np.std(spec_mean)
        threshold = median_energy + 3 * std_energy
        narrow_peaks = np.sum(spec_mean > threshold)
        features['narrow_peak_count'] = narrow_peaks
        
        return features
    
    def _compute_frame_statistics(self, frame_features):
        """
        计算帧间统计特征
        """
        if not frame_features:
            return {}
            
        stats = {}
        
        # 获取所有特征名
        all_keys = set()
        for feat in frame_features:
            all_keys.update(feat.keys())
        
        # 计算每个特征的统计量
        for key in all_keys:
            values = [feat.get(key, 0) for feat in frame_features]
            values = np.array(values)
            
            if len(values) > 0:
                stats[f'{key}_mean'] = np.mean(values)
                stats[f'{key}_std'] = np.std(values)
                stats[f'{key}_max'] = np.max(values)
                stats[f'{key}_min'] = np.min(values)
                
                # 只对有变化的特征计算偏度和峰度
                if np.std(values) > 1e-6:
                    stats[f'{key}_skew'] = skew(values)
                    stats[f'{key}_kurtosis'] = kurtosis(values)
        
        return stats
    
    def extract_enhanced_thd_features(self, y_seg, sr, f0, n_harmonics=5):
        """
        增强的THD特征提取
        """
        features = {}
        
        # 使用更大的FFT窗口提高频率分辨率
        n_fft = min(8192, len(y_seg))
        x = y_seg * windows.hann(len(y_seg))
        x = np.pad(x, (0, max(0, n_fft - len(x))))
        X = np.fft.rfft(x[:n_fft], n=n_fft)
        freqs = np.fft.rfftfreq(n_fft, 1/sr)
        mag = np.abs(X)
        
        # 基频功率
        idx0 = np.argmin(np.abs(freqs - f0))
        fund_pow = mag[idx0]**2
        
        # 谐波功率
        harmonic_powers = []
        for k in range(2, n_harmonics + 1):
            harm_freq = k * f0
            if harm_freq < sr/2:
                harm_idx = np.argmin(np.abs(freqs - harm_freq))
                harm_pow = mag[harm_idx]**2
                harmonic_powers.append(harm_pow)
        
        total_harm_pow = sum(harmonic_powers)
        total_pow = np.sum(mag**2)
        noise_pow = total_pow - (fund_pow + total_harm_pow)
        
        # THD和THD+N
        features['thd'] = np.sqrt(total_harm_pow) / (np.sqrt(fund_pow) + 1e-12)
        features['thdn'] = np.sqrt(total_harm_pow + noise_pow) / (np.sqrt(fund_pow) + 1e-12)
        
        # 谐波分布特征
        if harmonic_powers:
            features['harmonic_energy_ratio'] = total_harm_pow / (total_pow + 1e-12)
            features['harmonic_irregularity'] = np.std(harmonic_powers) / (np.mean(harmonic_powers) + 1e-12)
        
        # 噪声功率比
        features['noise_power_ratio'] = noise_pow / (total_pow + 1e-12)
        
        return features
    
    def detect_leakage_noise(self, y_seg, sr, low_freq_threshold=500):
        """
        检测低频漏气噪声
        """
        features = {}
        
        # 低通滤波提取低频成分
        nyquist = sr / 2
        low_cutoff = low_freq_threshold / nyquist
        
        if low_cutoff < 1.0:
            b, a = butter(4, low_cutoff, btype='low')
            low_freq_signal = filtfilt(b, a, y_seg)
            
            # 低频能量比例
            low_freq_energy = np.sum(low_freq_signal**2)
            total_energy = np.sum(y_seg**2)
            features['low_freq_energy_ratio'] = low_freq_energy / (total_energy + 1e-12)
            
            # 低频信号的不规律性（漏气噪声通常不规律）
            # 使用短时能量的变化率
            frame_length = min(1024, len(low_freq_signal) // 10)
            if frame_length > 0:
                n_frames = len(low_freq_signal) // frame_length
                frame_energies = []
                for i in range(n_frames):
                    start = i * frame_length
                    end = start + frame_length
                    frame_energy = np.sum(low_freq_signal[start:end]**2)
                    frame_energies.append(frame_energy)
                
                if len(frame_energies) > 1:
                    features['low_freq_energy_variation'] = np.std(frame_energies) / (np.mean(frame_energies) + 1e-12)
        
        return features
    
    def detect_vertical_lines(self, y_seg, sr, f0):
        """
        检测频谱中的竖线干扰
        竖线干扰特点：某时间点很多非主频和谐波的频点能量突然变高
        """
        features = {}

        # 使用较长的窗口进行STFT以获得更好的频率分辨率
        n_fft = min(4096, len(y_seg))
        S = np.abs(librosa.stft(y_seg, n_fft=n_fft, hop_length=self.hop_length))**2
        freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)

        if S.shape[1] < 3:  # 时间帧太少
            return features

        # 识别主频和谐波频率（需要排除的频率）
        harmonics = []
        for h in range(1, 6):  # 前5个谐波
            harm_freq = h * f0
            if harm_freq < freqs[-1]:
                # 谐波附近的频率范围（±50Hz或±5%）
                harm_bandwidth = max(50, f0 * 0.05)
                harm_mask = np.abs(freqs - harm_freq) <= harm_bandwidth
                harmonics.extend(np.where(harm_mask)[0])

        # 去重并排序
        harmonic_indices = sorted(list(set(harmonics)))

        # 创建非主频非谐波的频率掩码
        non_harmonic_mask = np.ones(len(freqs), dtype=bool)
        if harmonic_indices:
            non_harmonic_mask[harmonic_indices] = False

        # 提取非主频非谐波的频谱
        S_non_harmonic = S[non_harmonic_mask, :]

        if S_non_harmonic.size == 0:
            return features

        # 计算每个时间帧的基线能量（中位数）
        baseline_energy = np.median(S_non_harmonic, axis=0)

        # 检测每个时间帧的异常能量突增
        interference_times = []
        affected_freq_counts = []
        threshold_ratio = 3
        min_affected_freqs = 5

        for t in range(S.shape[1]):
            frame_energy = S_non_harmonic[:, t]
            frame_baseline = baseline_energy[t]

            # 检测能量突增的频率点
            energy_threshold = frame_baseline * threshold_ratio
            affected_freqs = np.sum(frame_energy > energy_threshold)

            # 如果受影响的频率点足够多，认为是竖线干扰
            if affected_freqs >= min_affected_freqs:
                interference_times.append(t)
                affected_freq_counts.append(affected_freqs)

        # 计算竖线干扰特征
        features['vertical_line_count'] = len(interference_times)
        features['vertical_interference_ratio'] = len(interference_times) / S.shape[1] if S.shape[1] > 0 else 0

        if affected_freq_counts:
            features['avg_affected_freq_count'] = np.mean(affected_freq_counts)
            features['max_affected_freq_count'] = np.max(affected_freq_counts)
            features['vertical_line_intensity'] = np.mean(affected_freq_counts) / np.sum(non_harmonic_mask)
        else:
            features['avg_affected_freq_count'] = 0
            features['max_affected_freq_count'] = 0
            features['vertical_line_intensity'] = 0

        return features
    
    def extract_segment_features(self, y_seg, sr, f0):
        """
        提取单个频段的完整特征
        """
        features = {}
        
        if len(y_seg) < 256:
            return features
        
        # 1. 分帧特征
        frame_features = self.extract_frame_features(y_seg, sr, f0)
        features.update(frame_features)
        
        # 2. 增强THD特征
        thd_features = self.extract_enhanced_thd_features(y_seg, sr, f0)
        features.update(thd_features)
        
        # 3. 漏气噪声检测
        leakage_features = self.detect_leakage_noise(y_seg, sr)
        features.update(leakage_features)
        
        # 4. 竖线干扰检测
        vertical_features = self.detect_vertical_lines(y_seg, sr, f0)
        features.update(vertical_features)
        
        # 5. 基础信息
        features['frequency'] = f0
        features['segment_length'] = len(y_seg) / sr
        
        return features

def extract_features_from_audio(audio_path, 
                               start_freq=100, stop_freq=20000, octave=12,
                               min_cycles=10, min_duration=156, fs=48000,
                               energy_threshold_db=-45, min_start_time=0.2):
    """
    从音频文件提取优化特征
    """
    extractor = OptimizedFeatureExtractor()
    
    # 分割频段
    step_bounds, freq_table = split_freq_steps(
        audio_path,
        start_freq=start_freq, stop_freq=stop_freq,
        octave=octave, fs=fs,
        min_cycles=min_cycles, min_duration=min_duration,
        energy_threshold_db=energy_threshold_db, 
        min_start_time=min_start_time,
        plot=False
    )
    
    # 加载音频
    y, sr = librosa.load(audio_path, sr=None)
    
    # 提取每个频段的特征
    all_features = []
    for i, (t0, t1) in enumerate(step_bounds):
        if i >= len(freq_table):
            break
            
        # 提取音频段（去除首尾10%）
        L = t1 - t0
        seg_start = t0 + 0.1 * L
        seg_end = t1 - 0.1 * L
        y_seg = y[int(seg_start*sr):int(seg_end*sr)]
        f0 = freq_table[i]
        
        # 提取特征
        features = extractor.extract_segment_features(y_seg, sr, f0)
        features['segment_id'] = i
        features['time_start'] = seg_start
        features['time_end'] = seg_end
        
        all_features.append(features)
    
    return pd.DataFrame(all_features)

def process_dataset(dataset_dir="dataset", output_dir="optimized_features"):
    """
    批量处理数据集中的正负样本
    """
    os.makedirs(output_dir, exist_ok=True)

    pos_dir = os.path.join(dataset_dir, "pos")
    neg_dir = os.path.join(dataset_dir, "neg")

    all_features = []

    # 处理正样本
    print("[INFO] 处理正样本...")
    if os.path.exists(pos_dir):
        for file in os.listdir(pos_dir):
            if file.lower().endswith('.wav'):
                file_path = os.path.join(pos_dir, file)
                print(f"  处理: {file}")
                try:
                    # 根据文件名判断参数
                    min_dur = 156 if '156' in file else 153
                    features_df = extract_features_from_audio(
                        file_path,
                        min_duration=min_dur,
                        energy_threshold_db=-45
                    )
                    features_df['label'] = 1  # 正样本标签
                    features_df['filename'] = file
                    all_features.append(features_df)
                except Exception as e:
                    print(f"    错误: {e}")

    # 处理负样本
    print("[INFO] 处理负样本...")
    if os.path.exists(neg_dir):
        for file in os.listdir(neg_dir):
            if file.lower().endswith('.wav'):
                file_path = os.path.join(neg_dir, file)
                print(f"  处理: {file}")
                try:
                    # 根据文件名判断参数
                    min_dur = 156 if '156' in file else 153
                    features_df = extract_features_from_audio(
                        file_path,
                        min_duration=min_dur,
                        energy_threshold_db=-45
                    )
                    features_df['label'] = 0  # 负样本标签
                    features_df['filename'] = file
                    all_features.append(features_df)
                except Exception as e:
                    print(f"    错误: {e}")

    # 合并所有特征
    if all_features:
        combined_df = pd.concat(all_features, ignore_index=True)

        # 保存特征
        features_path = os.path.join(output_dir, "all_features.csv")
        combined_df.to_csv(features_path, index=False)
        print(f"[INFO] 特征已保存到: {features_path}")
        print(f"[INFO] 总样本数: {len(combined_df)}")
        print(f"[INFO] 正样本: {(combined_df['label'] == 1).sum()}")
        print(f"[INFO] 负样本: {(combined_df['label'] == 0).sum()}")
        print(f"[INFO] 特征维度: {len(combined_df.columns)}")

        return combined_df
    else:
        print("[ERROR] 没有成功提取任何特征")
        return None

if __name__ == "__main__":
    # 批量处理数据集
    combined_df = process_dataset()

    if combined_df is not None:
        print("\n[INFO] 特征统计:")
        print(combined_df.describe())

        # 显示部分特征列
        feature_cols = [col for col in combined_df.columns
                       if col not in ['label', 'filename', 'segment_id', 'time_start', 'time_end', 'frequency']]
        print(f"\n[INFO] 提取的特征类型 ({len(feature_cols)}个):")
        for col in sorted(feature_cols)[:20]:  # 显示前20个
            print(f"  {col}")
        if len(feature_cols) > 20:
            print(f"  ... 还有 {len(feature_cols) - 20} 个特征")
