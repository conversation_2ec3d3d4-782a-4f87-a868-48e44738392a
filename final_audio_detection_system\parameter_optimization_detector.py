#!/usr/bin/env python3
"""
参数优化检测器
Parameter Optimization Detector
测试不同阈值和连续点参数，找到最佳区分效果
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

class ParameterOptimizationDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 测试参数范围 - 扩展到更小的阈值
        self.test_params = {
            'amplitude_thresholds': [-50, -55, -60, -65, -70, -75, -80, -85],  # 更小的阈值
            'min_continuous_points': [3, 4, 5, 6, 7, 8, 10],                  # 不同连续点要求
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.2
        }
        
        print(f"参数优化检测器初始化完成")
        print(f"将测试{len(self.test_params['amplitude_thresholds'])}个阈值 × {len(self.test_params['min_continuous_points'])}个连续点参数")
    
    def analyze_single_file(self, audio_path, amplitude_threshold_db, min_continuous_points):
        """分析单个文件在特定参数下的表现"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            if len(y) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.test_params['frequency_range'][0]) & \
                       (frequencies <= self.test_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # freq_split分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path, **self.freq_split_params, plot=False
            )
            
            if len(step_boundaries) == 0:
                return None
            
            # 分析每个频段
            anomalous_segments = 0
            max_continuous_points = 0
            total_continuous_groups = 0
            
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                segment_result = self._analyze_segment_with_params(
                    Zxx, frequencies, times, seg_start_time, seg_end_time,
                    amplitude_threshold_db, min_continuous_points
                )
                
                if segment_result['has_anomaly']:
                    anomalous_segments += 1
                    max_continuous_points = max(max_continuous_points, segment_result['max_continuous_points'])
                    total_continuous_groups += segment_result['continuous_groups']
            
            return {
                'total_segments': len(step_boundaries),
                'anomalous_segments': anomalous_segments,
                'max_continuous_points': max_continuous_points,
                'total_continuous_groups': total_continuous_groups,
                'anomaly_ratio': anomalous_segments / len(step_boundaries)
            }
            
        except Exception as e:
            print(f"分析失败 {os.path.basename(audio_path)}: {e}")
            return None
    
    def _analyze_segment_with_params(self, Zxx, frequencies, times, seg_start_time, seg_end_time, 
                                   amplitude_threshold_db, min_continuous_points):
        """使用指定参数分析频段"""
        result = {
            'has_anomaly': False,
            'max_continuous_points': 0,
            'continuous_groups': 0
        }
        
        # 找到时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.test_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            return result
        
        # 分析每个时间片
        segment_spectrum = Zxx[:, seg_core_start:seg_core_end]
        
        for t_idx in range(segment_spectrum.shape[1]):
            time_spectrum = segment_spectrum[:, t_idx]
            amplitude_db = 20 * np.log10(np.abs(time_spectrum) + 1e-12)
            
            # 阈值检测
            above_threshold = amplitude_db > amplitude_threshold_db
            
            # 查找连续频点组
            continuous_groups = self._find_continuous_groups(above_threshold)
            
            # 检查连续点要求
            for group in continuous_groups:
                if len(group) >= min_continuous_points:
                    result['has_anomaly'] = True
                    result['max_continuous_points'] = max(result['max_continuous_points'], len(group))
                    result['continuous_groups'] += 1
        
        return result
    
    def _find_continuous_groups(self, above_threshold_mask):
        """查找连续的频点组"""
        groups = []
        current_group = []
        
        for i, is_above in enumerate(above_threshold_mask):
            if is_above:
                current_group.append(i)
            else:
                if len(current_group) > 0:
                    groups.append(current_group)
                    current_group = []
        
        if len(current_group) > 0:
            groups.append(current_group)
        
        return groups
    
    def auto_detect_threshold_range(self, sample_files):
        """自动检测合适的阈值范围"""
        print("\n自动检测阈值范围...")

        all_amplitudes = []

        for sample_file in sample_files[:5]:  # 分析前5个样本
            if not os.path.exists(sample_file):
                continue

            try:
                y, sr = librosa.load(sample_file, sr=self.sample_rate)
                if len(y) == 0:
                    continue

                # 标准化
                if np.max(np.abs(y)) > 0:
                    y = y / np.max(np.abs(y)) * 0.9

                # STFT分析
                frequencies, times, Zxx = stft(
                    y, sr,
                    nperseg=self.stft_params['nperseg'],
                    noverlap=self.stft_params['noverlap'],
                    window=self.stft_params['window'],
                    nfft=self.stft_params['nfft']
                )

                # 转换为dB
                amplitude_db = 20 * np.log10(np.abs(Zxx) + 1e-12)
                all_amplitudes.extend(amplitude_db.flatten())

            except Exception as e:
                print(f"分析样本失败 {os.path.basename(sample_file)}: {e}")

        if all_amplitudes:
            all_amplitudes = np.array(all_amplitudes)
            percentiles = [1, 5, 10, 20, 30, 40, 50]

            print(f"幅度分布分析 (基于{len(all_amplitudes)}个数据点):")
            for p in percentiles:
                value = np.percentile(all_amplitudes, p)
                print(f"  {p}%分位数: {value:.1f}dB")

            # 建议阈值范围
            suggested_min = np.percentile(all_amplitudes, 5) - 5
            suggested_max = np.percentile(all_amplitudes, 20) - 5

            print(f"\n建议阈值范围: {suggested_min:.1f}dB 到 {suggested_max:.1f}dB")

            # 更新测试阈值
            if suggested_min < -50:
                step = 5
                new_thresholds = list(range(int(suggested_min), int(suggested_max) + step, step))
                self.test_params['amplitude_thresholds'] = new_thresholds
                print(f"更新测试阈值: {new_thresholds}")

    def test_parameter_combinations(self):
        """测试所有参数组合"""
        print("\n" + "="*80)
        print("参数优化测试")
        print("="*80)
        
        # 定义样本分类
        problematic_samples = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav"
        ]
        
        other_negative_samples = [
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        # 收集正样本
        positive_samples = []
        pos_dir = "../test20250717/pos"
        if os.path.exists(pos_dir):
            for root, dirs, files in os.walk(pos_dir):
                for file in files:
                    if file.endswith('.wav'):
                        positive_samples.append(os.path.join(root, file))
        
        print(f"问题样本: {len(problematic_samples)}个")
        print(f"其他负样本: {len(other_negative_samples)}个")
        print(f"正样本: {len(positive_samples)}个")

        # 自动检测阈值范围
        all_samples = problematic_samples + other_negative_samples + positive_samples[:5]
        self.auto_detect_threshold_range(all_samples)
        
        all_results = []
        
        # 测试每个参数组合
        for threshold in self.test_params['amplitude_thresholds']:
            for min_points in self.test_params['min_continuous_points']:
                print(f"\n测试参数: 阈值={threshold}dB, 最少连续点={min_points}")
                
                # 分析所有样本
                param_result = {
                    'threshold': threshold,
                    'min_continuous_points': min_points,
                    'problematic_results': [],
                    'other_negative_results': [],
                    'positive_results': []
                }
                
                # 分析问题样本
                for sample in problematic_samples:
                    if os.path.exists(sample):
                        result = self.analyze_single_file(sample, threshold, min_points)
                        if result:
                            result['filename'] = os.path.basename(sample)
                            param_result['problematic_results'].append(result)
                
                # 分析其他负样本
                for sample in other_negative_samples:
                    if os.path.exists(sample):
                        result = self.analyze_single_file(sample, threshold, min_points)
                        if result:
                            result['filename'] = os.path.basename(sample)
                            param_result['other_negative_results'].append(result)
                
                # 分析正样本（只分析前10个以节省时间）
                for sample in positive_samples[:10]:
                    if os.path.exists(sample):
                        result = self.analyze_single_file(sample, threshold, min_points)
                        if result:
                            result['filename'] = os.path.basename(sample)
                            param_result['positive_results'].append(result)
                
                # 计算区分效果
                discrimination_score = self._calculate_discrimination_score(param_result)
                param_result['discrimination_score'] = discrimination_score
                
                print(f"  区分效果分数: {discrimination_score:.3f}")
                
                all_results.append(param_result)
        
        # 找到最佳参数
        best_result = max(all_results, key=lambda x: x['discrimination_score'])
        
        print(f"\n" + "="*60)
        print(f"最佳参数组合:")
        print(f"  阈值: {best_result['threshold']}dB")
        print(f"  最少连续点: {best_result['min_continuous_points']}")
        print(f"  区分效果分数: {best_result['discrimination_score']:.3f}")
        
        # 显示最佳参数下的详细结果
        self._show_detailed_results(best_result)
        
        # 保存所有结果
        self._save_optimization_results(all_results)
        
        return all_results, best_result
    
    def _calculate_discrimination_score(self, param_result):
        """计算区分效果分数"""
        problematic = param_result['problematic_results']
        other_negative = param_result['other_negative_results']
        positive = param_result['positive_results']
        
        if not problematic or not (other_negative or positive):
            return 0.0
        
        # 计算各组的平均异常比例
        prob_avg = np.mean([r['anomaly_ratio'] for r in problematic])
        other_neg_avg = np.mean([r['anomaly_ratio'] for r in other_negative]) if other_negative else 0
        pos_avg = np.mean([r['anomaly_ratio'] for r in positive]) if positive else 0
        
        # 计算区分度：问题样本应该有更高的异常比例
        discrimination_score = prob_avg - max(other_neg_avg, pos_avg)
        
        return max(0, discrimination_score)  # 确保非负
    
    def _show_detailed_results(self, best_result):
        """显示最佳参数下的详细结果"""
        print(f"\n最佳参数下的详细结果:")
        print(f"问题样本:")
        for result in best_result['problematic_results']:
            print(f"  {result['filename']}: {result['anomalous_segments']}/{result['total_segments']} "
                  f"({result['anomaly_ratio']:.1%}) 异常频段")
        
        print(f"\n其他负样本:")
        for result in best_result['other_negative_results']:
            print(f"  {result['filename']}: {result['anomalous_segments']}/{result['total_segments']} "
                  f"({result['anomaly_ratio']:.1%}) 异常频段")
        
        print(f"\n正样本 (前10个):")
        for result in best_result['positive_results']:
            print(f"  {result['filename']}: {result['anomalous_segments']}/{result['total_segments']} "
                  f"({result['anomaly_ratio']:.1%}) 异常频段")
    
    def _save_optimization_results(self, all_results):
        """保存优化结果"""
        # 创建汇总表
        summary_data = []
        for result in all_results:
            prob_avg = np.mean([r['anomaly_ratio'] for r in result['problematic_results']]) if result['problematic_results'] else 0
            other_neg_avg = np.mean([r['anomaly_ratio'] for r in result['other_negative_results']]) if result['other_negative_results'] else 0
            pos_avg = np.mean([r['anomaly_ratio'] for r in result['positive_results']]) if result['positive_results'] else 0
            
            summary_data.append({
                'threshold_db': result['threshold'],
                'min_continuous_points': result['min_continuous_points'],
                'problematic_avg_ratio': prob_avg,
                'other_negative_avg_ratio': other_neg_avg,
                'positive_avg_ratio': pos_avg,
                'discrimination_score': result['discrimination_score']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('parameter_optimization_summary.csv', index=False)
        print(f"\n参数优化结果已保存: parameter_optimization_summary.csv")

def main():
    """主函数"""
    detector = ParameterOptimizationDetector()
    all_results, best_result = detector.test_parameter_combinations()
    return detector, all_results, best_result

if __name__ == "__main__":
    detector, all_results, best_result = main()
