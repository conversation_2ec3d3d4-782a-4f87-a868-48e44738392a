#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量THD+N分析脚本
对指定文件夹中的所有音频文件进行THD+N双方法分析
"""

import os
import sys
import time
import glob
from pathlib import Path
import shutil

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入THD+N分析模块
from adaptive_fundamental_removal import main as thd_analysis_main

def find_audio_files(folder_path):
    """查找文件夹中的所有音频文件"""
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.aiff', '*.m4a']
    audio_files = []
    
    folder_path = Path(folder_path)
    if not folder_path.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in audio_extensions:
        files = list(folder_path.glob(f"**/{ext}"))  # 递归搜索
        audio_files.extend(files)
    
    return sorted(audio_files)

def create_batch_output_dir():
    """创建批量处理输出目录"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_dir = f"批量THD+N分析结果_{timestamp}"
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    return output_dir

def process_audio_file(audio_file, batch_output_dir):
    """处理单个音频文件"""
    print(f"\n🎵 开始处理: {audio_file.name}")
    print(f"📂 文件路径: {audio_file}")
    
    try:
        # 创建该文件的输出子目录
        file_stem = audio_file.stem
        file_output_dir = os.path.join(batch_output_dir, file_stem)
        
        # 调用THD+N分析
        success = thd_analysis_main(str(audio_file), file_output_dir)
        
        if success:
            print(f"✅ {audio_file.name} 处理完成")
            return True
        else:
            print(f"❌ {audio_file.name} 处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理 {audio_file.name} 时发生错误: {str(e)}")
        return False

def create_batch_summary(batch_output_dir, results):
    """创建批量处理汇总报告"""
    summary_file = os.path.join(batch_output_dir, "批量处理汇总报告.txt")
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("批量THD+N分析汇总报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {len(results)}\n")
        f.write(f"成功处理: {sum(1 for r in results if r['success'])}\n")
        f.write(f"处理失败: {sum(1 for r in results if not r['success'])}\n\n")
        
        f.write("详细结果:\n")
        f.write("-" * 50 + "\n")
        
        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            f.write(f"{i:2d}. {result['file']} - {status}\n")
            if result['error']:
                f.write(f"    错误信息: {result['error']}\n")
        
        f.write("\n" + "=" * 50 + "\n")
        f.write("批量处理完成\n")
    
    print(f"📄 汇总报告已保存: {summary_file}")

def main():
    """主函数"""
    print("🚀 批量THD+N分析工具启动")
    print("=" * 60)
    
    # 定义要处理的文件夹
    target_folders = ["test20250717", "待定", "test20250722"]
    
    # 创建批量输出目录
    batch_output_dir = create_batch_output_dir()
    
    # 收集所有音频文件
    all_audio_files = []
    for folder in target_folders:
        print(f"\n📂 扫描文件夹: {folder}")
        audio_files = find_audio_files(folder)
        
        if audio_files:
            print(f"   找到 {len(audio_files)} 个音频文件")
            for file in audio_files:
                all_audio_files.append({
                    'file': file,
                    'folder': folder
                })
        else:
            print(f"   未找到音频文件")
    
    if not all_audio_files:
        print("❌ 未找到任何音频文件，程序退出")
        return
    
    print(f"\n📊 总共找到 {len(all_audio_files)} 个音频文件")
    print("开始批量处理...")
    
    # 处理所有文件
    results = []
    start_time = time.time()
    
    for i, audio_info in enumerate(all_audio_files, 1):
        audio_file = audio_info['file']
        folder = audio_info['folder']
        
        print(f"\n[{i}/{len(all_audio_files)}] 处理来自 {folder} 的文件")
        
        try:
            success = process_audio_file(audio_file, batch_output_dir)
            results.append({
                'file': f"{folder}/{audio_file.name}",
                'success': success,
                'error': None
            })
        except Exception as e:
            results.append({
                'file': f"{folder}/{audio_file.name}",
                'success': False,
                'error': str(e)
            })
    
    # 计算总耗时
    total_time = time.time() - start_time
    
    # 创建汇总报告
    create_batch_summary(batch_output_dir, results)
    
    # 打印最终统计
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    
    print("\n" + "=" * 60)
    print("🎉 批量处理完成!")
    print(f"📊 处理统计:")
    print(f"   总文件数: {len(results)}")
    print(f"   成功处理: {successful}")
    print(f"   处理失败: {failed}")
    print(f"   总耗时: {total_time:.1f}秒")
    print(f"📁 输出目录: {batch_output_dir}")
    print("=" * 60)

if __name__ == "__main__":
    main()
