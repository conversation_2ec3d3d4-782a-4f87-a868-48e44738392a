#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版低音戳洞前5段可视化
解决图片显示问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib后端和字体
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def fixed_low_freq_visualization():
    """修正版低频段可视化"""
    print("🎯 修正版低音戳洞前5段可视化")
    print("="*50)
    
    # 两个样本文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    normal_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav'
    
    if not os.path.exists(hole_file) or not os.path.exists(normal_file):
        print("❌ 文件不存在")
        return
    
    # 分析两个样本
    hole_data = analyze_fixed(hole_file, "Low_Freq_Hole")
    normal_data = analyze_fixed(normal_file, "Normal_Sample")
    
    if hole_data and normal_data:
        create_fixed_visualization(hole_data, normal_data)

def analyze_fixed(audio_path, sample_name):
    """修正版分析"""
    try:
        print(f"\nAnalyzing {sample_name}...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前5段
        for seg_idx in range(min(5, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析
            fft_size = 16384  # 16k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power_db = 20 * np.log10(magnitude + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            # 找主频
            search_range = (expected_freq - 3, expected_freq + 3)
            search_mask = (display_freqs >= search_range[0]) & (display_freqs <= search_range[1])
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power_db[search_mask]
                max_idx = search_indices[np.argmax(search_powers)]
                main_freq = display_freqs[max_idx]
                main_power = display_power_db[max_idx]
            else:
                main_freq = expected_freq
                main_power = -60
            
            # 简单谐波检测
            harmonics = []
            noise_floor = np.percentile(display_power_db, 10)  # 估计噪声底噪
            
            for order in range(2, 21):  # 2-20次谐波
                harmonic_freq = main_freq * order
                if harmonic_freq >= 20000:
                    break
                
                # 在谐波频率附近搜索
                harm_range = (harmonic_freq - 5, harmonic_freq + 5)
                harm_mask = (display_freqs >= harm_range[0]) & (display_freqs <= harm_range[1])
                
                if np.any(harm_mask):
                    harm_indices = np.where(harm_mask)[0]
                    harm_powers = display_power_db[harm_mask]
                    harm_max_idx = harm_indices[np.argmax(harm_powers)]
                    harm_freq_actual = display_freqs[harm_max_idx]
                    harm_power = display_power_db[harm_max_idx]
                    
                    # SNR检查
                    snr = harm_power - noise_floor
                    if snr > 10:  # SNR > 10dB
                        harmonics.append({
                            'order': order,
                            'freq': harm_freq_actual,
                            'power': harm_power,
                            'snr': snr
                        })
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': display_freqs,
                'power_db': display_power_db,
                'main_freq': main_freq,
                'main_power': main_power,
                'harmonics': harmonics,
                'noise_floor': noise_floor
            })
            
            print(f"  Segment {seg_idx}: {expected_freq:.0f}Hz -> {main_freq:.1f}Hz ({main_power:.1f}dB), {len(harmonics)} harmonics")
        
        return {
            'sample_name': sample_name,
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"Analysis failed: {e}")
        return None

def create_fixed_visualization(hole_data, normal_data):
    """创建修正版可视化"""
    print(f"\n🎨 Creating fixed visualization...")
    
    # 创建图表 (5行1列)
    fig, axes = plt.subplots(5, 1, figsize=(16, 20))
    fig.suptitle('Low Frequency Hole vs Normal Sample Comparison (0-20kHz)', fontsize=16, fontweight='bold')
    
    hole_segments = hole_data['segments']
    normal_segments = normal_data['segments']
    
    min_segments = min(len(hole_segments), len(normal_segments))
    
    for i in range(min_segments):
        ax = axes[i]
        
        hole_seg = hole_segments[i]
        normal_seg = normal_segments[i]
        
        # 绘制频谱 - 使用线性频率轴
        ax.plot(hole_seg['freqs'], hole_seg['power_db'], 'r-', linewidth=0.8, alpha=0.8, 
               label=f'{hole_data["sample_name"]} Spectrum')
        ax.plot(normal_seg['freqs'], normal_seg['power_db'], 'b-', linewidth=0.8, alpha=0.8, 
               label=f'{normal_data["sample_name"]} Spectrum')
        
        # 标记噪声底噪
        ax.axhline(y=hole_seg['noise_floor'], color='red', linestyle='--', alpha=0.5, 
                  label=f'{hole_data["sample_name"]} Noise Floor')
        ax.axhline(y=normal_seg['noise_floor'], color='blue', linestyle='--', alpha=0.5,
                  label=f'{normal_data["sample_name"]} Noise Floor')
        
        # 标记主频
        ax.plot(hole_seg['main_freq'], hole_seg['main_power'], 'ro', markersize=10, 
               label=f'{hole_data["sample_name"]} Fundamental {hole_seg["main_freq"]:.1f}Hz')
        ax.plot(normal_seg['main_freq'], normal_seg['main_power'], 'bo', markersize=10, 
               label=f'{normal_data["sample_name"]} Fundamental {normal_seg["main_freq"]:.1f}Hz')
        
        # 标记谐波 - 只显示前8个
        for j, harmonic in enumerate(hole_seg['harmonics'][:8]):
            ax.plot(harmonic['freq'], harmonic['power'], 's', color='darkred', 
                   markersize=6, alpha=0.8)
            if j < 5:  # 只标注前5个
                ax.text(harmonic['freq'], harmonic['power'] + 3, f'{harmonic["order"]}', 
                       ha='center', va='bottom', fontsize=8, color='darkred')
        
        for j, harmonic in enumerate(normal_seg['harmonics'][:8]):
            ax.plot(harmonic['freq'], harmonic['power'], '^', color='darkblue', 
                   markersize=6, alpha=0.8)
            if j < 5:  # 只标注前5个
                ax.text(harmonic['freq'], harmonic['power'] - 5, f'{harmonic["order"]}', 
                       ha='center', va='top', fontsize=8, color='darkblue')
        
        # 设置图表属性
        expected_freq = hole_seg['expected_freq']
        ax.set_title(f'Segment {hole_seg["seg_idx"]} ({expected_freq:.0f}Hz) - Full Spectrum Comparison', 
                    fontweight='bold', fontsize=12)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power (dB)')
        
        # 设置x轴范围和刻度
        ax.set_xlim(0, 20000)
        ax.set_xticks([0, 1000, 2000, 5000, 10000, 15000, 20000])
        ax.set_xticklabels(['0', '1k', '2k', '5k', '10k', '15k', '20k'])
        
        # 设置y轴范围
        all_power = np.concatenate([hole_seg['power_db'], normal_seg['power_db']])
        y_min = np.percentile(all_power, 1) - 5
        y_max = np.percentile(all_power, 99) + 5
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8, loc='upper right')
        ax.grid(True, alpha=0.3)
        
        # 添加信息文本
        info_text = f"Comparison Info:\n"
        info_text += f"{hole_data['sample_name']}: {hole_seg['main_freq']:.1f}Hz, {len(hole_seg['harmonics'])} harmonics\n"
        info_text += f"{normal_data['sample_name']}: {normal_seg['main_freq']:.1f}Hz, {len(normal_seg['harmonics'])} harmonics\n"
        info_text += f"Harmonic Difference: {len(hole_seg['harmonics']) - len(normal_seg['harmonics']):+d}"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'fixed_low_freq_comparison.png'
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ Fixed visualization saved: {filename}")
    plt.close()
    
    # 打印详细统计
    print(f"\n📊 Detailed Statistics:")
    print(f"{'='*60}")
    
    for i in range(min_segments):
        hole_seg = hole_segments[i]
        normal_seg = normal_segments[i]
        
        print(f"\nSegment {i} ({hole_seg['expected_freq']:.0f}Hz):")
        print(f"  {hole_data['sample_name']}:")
        print(f"    Fundamental: {hole_seg['main_freq']:.2f}Hz ({hole_seg['main_power']:.1f}dB)")
        print(f"    Harmonics: {len(hole_seg['harmonics'])} detected")
        print(f"    Noise Floor: {hole_seg['noise_floor']:.1f}dB")
        
        print(f"  {normal_data['sample_name']}:")
        print(f"    Fundamental: {normal_seg['main_freq']:.2f}Hz ({normal_seg['main_power']:.1f}dB)")
        print(f"    Harmonics: {len(normal_seg['harmonics'])} detected")
        print(f"    Noise Floor: {normal_seg['noise_floor']:.1f}dB")
        
        print(f"  Differences:")
        freq_diff = hole_seg['main_freq'] - normal_seg['main_freq']
        power_diff = hole_seg['main_power'] - normal_seg['main_power']
        harmonic_diff = len(hole_seg['harmonics']) - len(normal_seg['harmonics'])
        noise_diff = hole_seg['noise_floor'] - normal_seg['noise_floor']
        
        print(f"    Frequency: {freq_diff:+.2f}Hz")
        print(f"    Power: {power_diff:+.1f}dB")
        print(f"    Harmonics: {harmonic_diff:+d}")
        print(f"    Noise Floor: {noise_diff:+.1f}dB")

if __name__ == "__main__":
    fixed_low_freq_visualization()
