#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
找出导致异常值的正常样本文件
"""

import os
import pandas as pd
import numpy as np

def find_problematic_normal_sample():
    """找出导致异常值的正常样本文件"""
    print("🔍 找出导致异常值的正常样本文件")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 正常样本概览:")
    print(f"   正常样本记录数: {len(normal_data)}")
    print(f"   正常样本文件数: {len(normal_data['filename'].unique())}")
    
    # 分析每个特征的异常值
    features = ['true_noise_floor_median', 'true_noise_floor_mean']
    
    for feature in features:
        print(f"\n🎯 分析 {feature} 的异常值:")
        print("-" * 50)
        
        # 找出每个频段的异常值
        segments = sorted(normal_data['segment_idx'].unique())
        anomalous_files = {}
        
        for seg_idx in segments:
            seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
            
            if len(seg_data) > 0:
                values = seg_data[feature].values
                mean_val = np.mean(values)
                std_val = np.std(values)
                
                # 找出超过均值+2σ的异常值
                threshold = mean_val + 2 * std_val
                anomalous_mask = values > threshold
                
                if np.any(anomalous_mask):
                    anomalous_indices = np.where(anomalous_mask)[0]
                    anomalous_data = seg_data.iloc[anomalous_indices]
                    
                    for _, row in anomalous_data.iterrows():
                        filename = row['filename']
                        value = row[feature]
                        
                        if filename not in anomalous_files:
                            anomalous_files[filename] = []
                        
                        anomalous_files[filename].append({
                            'segment': seg_idx,
                            'frequency': row['expected_freq'],
                            'value': value,
                            'mean': mean_val,
                            'std': std_val,
                            'z_score': (value - mean_val) / std_val
                        })
        
        # 统计每个文件的异常频段数
        file_anomaly_counts = {}
        for filename, anomalies in anomalous_files.items():
            file_anomaly_counts[filename] = len(anomalies)
        
        # 排序并显示
        sorted_files = sorted(file_anomaly_counts.items(), key=lambda x: x[1], reverse=True)
        
        print(f"   异常文件统计 (超过均值+2σ):")
        print(f"   {'文件名':>30} {'异常频段数':>10} {'异常比例':>8}")
        print("-" * 55)
        
        for filename, count in sorted_files[:10]:  # 显示前10个最异常的文件
            percentage = count / len(segments) * 100
            print(f"   {filename:>30} {count:>10} {percentage:>7.1f}%")
        
        # 详细分析最异常的文件
        if sorted_files:
            most_anomalous_file = sorted_files[0][0]
            print(f"\n   📊 最异常文件详细分析: {most_anomalous_file}")
            
            file_anomalies = anomalous_files[most_anomalous_file]
            print(f"   异常频段数: {len(file_anomalies)}/{len(segments)} ({len(file_anomalies)/len(segments)*100:.1f}%)")
            
            print(f"   前10个最异常的频段:")
            sorted_anomalies = sorted(file_anomalies, key=lambda x: x['z_score'], reverse=True)
            
            for i, anomaly in enumerate(sorted_anomalies[:10]):
                print(f"     {i+1:2d}. 频段{anomaly['segment']:2d} ({anomaly['frequency']:6.1f}Hz): "
                      f"值={anomaly['value']:8.3f}, Z-score={anomaly['z_score']:6.2f}")
    
    # 检查是否有文件在多个特征上都异常
    print(f"\n🔍 检查在多个特征上都异常的文件:")
    print("-" * 50)
    
    multi_feature_anomalies = {}
    
    for feature in features:
        segments = sorted(normal_data['segment_idx'].unique())
        
        for seg_idx in segments:
            seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
            
            if len(seg_data) > 0:
                values = seg_data[feature].values
                mean_val = np.mean(values)
                std_val = np.std(values)
                threshold = mean_val + 2 * std_val
                
                anomalous_mask = values > threshold
                if np.any(anomalous_mask):
                    anomalous_data = seg_data.iloc[np.where(anomalous_mask)[0]]
                    
                    for _, row in anomalous_data.iterrows():
                        filename = row['filename']
                        
                        if filename not in multi_feature_anomalies:
                            multi_feature_anomalies[filename] = set()
                        
                        multi_feature_anomalies[filename].add(feature)
    
    # 找出在多个特征上都异常的文件
    multi_anomalous_files = {filename: features_set 
                           for filename, features_set in multi_feature_anomalies.items() 
                           if len(features_set) > 1}
    
    if multi_anomalous_files:
        print(f"   在多个特征上都异常的文件:")
        for filename, features_set in multi_anomalous_files.items():
            print(f"     {filename}: {', '.join(features_set)}")
    else:
        print(f"   没有文件在多个特征上都异常")
    
    # 检查这些异常文件的基本信息
    print(f"\n📋 异常文件基本信息检查:")
    print("-" * 50)
    
    # 获取所有异常文件
    all_anomalous_files = set()
    for feature in features:
        segments = sorted(normal_data['segment_idx'].unique())
        
        for seg_idx in segments:
            seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
            
            if len(seg_data) > 0:
                values = seg_data[feature].values
                mean_val = np.mean(values)
                std_val = np.std(values)
                threshold = mean_val + 2 * std_val
                
                anomalous_mask = values > threshold
                if np.any(anomalous_mask):
                    anomalous_data = seg_data.iloc[np.where(anomalous_mask)[0]]
                    for _, row in anomalous_data.iterrows():
                        all_anomalous_files.add(row['filename'])
    
    print(f"   总异常文件数: {len(all_anomalous_files)}")
    print(f"   异常文件列表:")
    
    for filename in sorted(all_anomalous_files):
        file_data = normal_data[normal_data['filename'] == filename]
        segment_count = len(file_data)
        
        print(f"     {filename} (记录数: {segment_count})")
        
        # 检查该文件的特征值范围
        for feature in features:
            values = file_data[feature].values
            if len(values) > 0:
                print(f"       {feature}: 范围 [{np.min(values):.3f}, {np.max(values):.3f}]")
    
    # 建议解决方案
    print(f"\n💡 建议解决方案:")
    print("-" * 50)
    print(f"   1. 检查异常文件是否确实是正常样本")
    print(f"   2. 如果确实是正常样本，检查特征计算是否有问题")
    print(f"   3. 考虑使用稳健统计方法 (如分位数) 而不是均值±标准差")
    print(f"   4. 可以暂时排除异常文件重新计算绝对范围")
    
    return all_anomalous_files

if __name__ == "__main__":
    anomalous_files = find_problematic_normal_sample()
    print(f"\n✅ 异常文件识别完成！")
