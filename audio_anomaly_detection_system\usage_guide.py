"""
优化特征提取和分类系统使用指南
=====================================

本指南展示如何使用优化后的特征提取和分类系统进行音频异响检测
"""

import os
import numpy as np
import pandas as pd
from optimized_feature_extractor import extract_features_from_audio, process_dataset
from improved_vertical_detection import detect_vertical_line_interference_v2
import matplotlib.pyplot as plt

def quick_start_single_file():
    """
    快速开始：分析单个音频文件
    """
    print("=" * 60)
    print("1. 单文件分析示例")
    print("=" * 60)
    
    # 选择一个音频文件进行分析
    audio_file = "dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
    
    if not os.path.exists(audio_file):
        print(f"[ERROR] 文件不存在: {audio_file}")
        print("请将音频文件路径替换为您的实际文件路径")
        return
    
    print(f"[INFO] 分析文件: {os.path.basename(audio_file)}")
    
    # 提取特征
    try:
        # 根据文件名判断参数
        min_duration = 156 if '156' in audio_file else 153
        
        features_df = extract_features_from_audio(
            audio_file,
            min_duration=min_duration,
            energy_threshold_db=-45
        )
        
        print(f"[SUCCESS] 成功提取 {len(features_df)} 个频段的特征")
        print(f"[INFO] 特征维度: {len(features_df.columns)}")
        
        # 显示关键特征统计
        key_features = [
            'vertical_line_count', 'vertical_interference_ratio',
            'low_freq_energy_ratio', 'thd', 'thdn',
            'noise_power_ratio'
        ]
        
        print("\n[INFO] 关键特征统计:")
        for feat in key_features:
            if feat in features_df.columns:
                values = features_df[feat].dropna()
                if len(values) > 0:
                    print(f"  {feat}: 平均={values.mean():.3f}, 最大={values.max():.3f}")
        
        # 保存结果
        output_file = f"single_analysis_{os.path.splitext(os.path.basename(audio_file))[0]}.csv"
        features_df.to_csv(output_file, index=False)
        print(f"\n[INFO] 特征已保存到: {output_file}")
        
        return features_df
        
    except Exception as e:
        print(f"[ERROR] 特征提取失败: {e}")
        return None

def batch_process_dataset():
    """
    批量处理数据集
    """
    print("\n" + "=" * 60)
    print("2. 批量处理数据集")
    print("=" * 60)
    
    # 检查数据集目录
    if not os.path.exists("dataset"):
        print("[ERROR] dataset目录不存在")
        print("请确保您的数据集按以下结构组织:")
        print("dataset/")
        print("  ├── pos/  (正样本)")
        print("  └── neg/  (负样本)")
        return None
    
    print("[INFO] 开始批量处理数据集...")
    
    try:
        # 批量处理
        combined_df = process_dataset(
            dataset_dir="dataset",
            output_dir="batch_features"
        )
        
        if combined_df is not None:
            print(f"\n[SUCCESS] 批量处理完成!")
            print(f"[INFO] 总样本数: {len(combined_df)}")
            print(f"[INFO] 正样本: {(combined_df['label'] == 1).sum()}")
            print(f"[INFO] 负样本: {(combined_df['label'] == 0).sum()}")
            
            # 分析正负样本差异
            analyze_pos_neg_differences(combined_df)
            
            return combined_df
        else:
            print("[ERROR] 批量处理失败")
            return None
            
    except Exception as e:
        print(f"[ERROR] 批量处理出错: {e}")
        return None

def analyze_pos_neg_differences(df):
    """
    分析正负样本的特征差异
    """
    print("\n[INFO] 正负样本特征对比:")
    
    # 关键特征对比
    key_features = [
        'vertical_line_count', 'vertical_interference_ratio',
        'low_freq_energy_ratio', 'thd', 'thdn',
        'noise_power_ratio', 'avg_affected_freq_count'
    ]
    
    pos_data = df[df['label'] == 1]
    neg_data = df[df['label'] == 0]
    
    print(f"{'特征名':<25} {'正样本均值':<12} {'负样本均值':<12} {'差异倍数':<10}")
    print("-" * 65)
    
    for feat in key_features:
        if feat in df.columns:
            pos_mean = pos_data[feat].mean()
            neg_mean = neg_data[feat].mean()
            ratio = neg_mean / (pos_mean + 1e-12)
            
            print(f"{feat:<25} {pos_mean:<12.3f} {neg_mean:<12.3f} {ratio:<10.2f}")

def train_simple_classifier(df):
    """
    训练简单分类器
    """
    print("\n" + "=" * 60)
    print("3. 训练分类器")
    print("=" * 60)
    
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import classification_report, confusion_matrix
    
    # 选择特征
    feature_cols = [col for col in df.columns 
                   if col not in ['label', 'filename', 'segment_id', 'time_start', 
                                 'time_end', 'frequency', 'segment_length']]
    
    # 只保留数值特征
    numeric_features = []
    for col in feature_cols:
        try:
            pd.to_numeric(df[col], errors='raise')
            numeric_features.append(col)
        except:
            continue
    
    X = df[numeric_features].fillna(0)
    y = df['label']
    
    print(f"[INFO] 使用 {len(numeric_features)} 个特征训练分类器")
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 训练随机森林
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train_scaled, y_train)
    
    # 预测和评估
    y_pred = rf.predict(X_test_scaled)
    
    print("\n[INFO] 分类器性能:")
    print(classification_report(y_test, y_pred, target_names=['负样本', '正样本']))
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': numeric_features,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n[INFO] 最重要的10个特征:")
    for i, row in feature_importance.head(10).iterrows():
        print(f"  {row['feature']}: {row['importance']:.4f}")
    
    return rf, scaler, numeric_features

def predict_new_audio(model, scaler, feature_names, audio_path):
    """
    对新音频进行预测
    """
    print("\n" + "=" * 60)
    print("4. 预测新音频")
    print("=" * 60)
    
    print(f"[INFO] 预测文件: {os.path.basename(audio_path)}")
    
    try:
        # 提取特征
        min_duration = 156 if '156' in audio_path else 153
        features_df = extract_features_from_audio(
            audio_path,
            min_duration=min_duration,
            energy_threshold_db=-45
        )
        
        # 准备特征
        X_new = features_df[feature_names].fillna(0)
        X_new_scaled = scaler.transform(X_new)
        
        # 预测
        predictions = model.predict(X_new_scaled)
        probabilities = model.predict_proba(X_new_scaled)
        
        # 统计结果
        pos_segments = np.sum(predictions == 1)
        neg_segments = np.sum(predictions == 0)
        total_segments = len(predictions)
        
        avg_pos_prob = np.mean(probabilities[:, 1])
        
        print(f"\n[INFO] 预测结果:")
        print(f"  总频段数: {total_segments}")
        print(f"  正常频段: {pos_segments} ({pos_segments/total_segments*100:.1f}%)")
        print(f"  异常频段: {neg_segments} ({neg_segments/total_segments*100:.1f}%)")
        print(f"  平均正常概率: {avg_pos_prob:.3f}")
        
        # 整体判断
        if avg_pos_prob > 0.7:
            overall_result = "正常"
        elif avg_pos_prob > 0.3:
            overall_result = "可疑"
        else:
            overall_result = "异常"
        
        print(f"  整体判断: {overall_result}")
        
        return predictions, probabilities
        
    except Exception as e:
        print(f"[ERROR] 预测失败: {e}")
        return None, None

def complete_workflow_example():
    """
    完整工作流程示例
    """
    print("🎯 音频异响检测完整工作流程")
    print("=" * 80)
    
    # 步骤1: 单文件分析
    single_result = quick_start_single_file()
    
    # 步骤2: 批量处理数据集
    dataset_result = batch_process_dataset()
    
    if dataset_result is not None:
        # 步骤3: 训练分类器
        model, scaler, feature_names = train_simple_classifier(dataset_result)
        
        # 步骤4: 预测新音频（使用测试文件）
        test_files = [
            "dataset/pos/录音_步进扫频_100Hz至20000Hz_20250714_152023_156.wav",
            "dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                predict_new_audio(model, scaler, feature_names, test_file)
    
    print("\n🎉 完整工作流程演示完成!")

def usage_tips():
    """
    使用技巧和注意事项
    """
    print("\n" + "=" * 60)
    print("💡 使用技巧和注意事项")
    print("=" * 60)
    
    tips = [
        "1. 数据准备:",
        "   - 确保音频文件为WAV格式",
        "   - 正样本放在dataset/pos/目录",
        "   - 负样本放在dataset/neg/目录",
        "",
        "2. 参数调整:",
        "   - min_duration: 根据文件名自动判断(153或156)",
        "   - energy_threshold_db: 默认-45dB，可根据噪声水平调整",
        "   - 竖线干扰检测阈值: threshold_ratio=3-5",
        "",
        "3. 特征选择:",
        "   - 使用feature_analysis.py分析特征重要性",
        "   - 删除低重要性和高相关性特征",
        "   - 重点关注竖线干扰、THD、低频噪声特征",
        "",
        "4. 分类器优化:",
        "   - 可尝试不同算法(RF, SVM, LR)",
        "   - 使用交叉验证评估性能",
        "   - 根据数据不平衡情况调整权重",
        "",
        "5. 实际应用:",
        "   - 建议先在小数据集上验证",
        "   - 定期重新训练模型",
        "   - 保存训练好的模型和预处理器"
    ]
    
    for tip in tips:
        print(tip)

if __name__ == "__main__":
    # 运行完整示例
    complete_workflow_example()
    
    # 显示使用技巧
    usage_tips()
