#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化sd1_1第43段竖线检测
"""

import os
import sys
import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_segment_43_detection():
    """可视化第43段竖线检测"""
    print("🔍 可视化sd1_1第43段竖线检测")
    print("="*60)
    
    # 测试文件
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    if not os.path.exists(audio_path):
        print(f"❌ 文件不存在: {audio_path}")
        return
    
    try:
        # 1. 获取频段分割信息
        print("📊 1. 获取频段分割信息")
        print("-" * 50)
        
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path,
            start_freq=100,
            stop_freq=20000,
            octave=12,
            min_cycles=10,
            min_duration=153,
            fs=48000,
            search_window_start=0.1,
            search_window_end=1.5,
            correlation_length=1.0,
            plot=False,
            debug=False
        )
        
        print(f"✅ 频段分割完成: {len(step_boundaries)}个频段")
        print(f"   开始时间: {alignment_info.get('start_offset', 0):.3f}s")
        print(f"   相关性: {alignment_info.get('correlation_score', 0):.3f}")
        
        # 检查第43段是否存在
        if len(step_boundaries) <= 43:
            print(f"❌ 第43段不存在，总共只有{len(step_boundaries)}个频段")
            return
        
        # 获取第43段信息
        seg_start_time, seg_end_time = step_boundaries[43]
        expected_freq = freq_table[43]
        
        print(f"\n📊 第43段信息:")
        print(f"   频段索引: 43")
        print(f"   时间范围: {seg_start_time:.3f}s - {seg_end_time:.3f}s")
        print(f"   时长: {seg_end_time - seg_start_time:.3f}s")
        print(f"   期望频率: {expected_freq:.1f}Hz")
        
        # 2. 加载和分析音频
        print(f"\n📊 2. 音频分析")
        print("-" * 50)
        
        y, sr = librosa.load(audio_path, sr=48000)
        print(f"   音频长度: {len(y)/sr:.2f}s")
        print(f"   采样率: {sr}Hz")
        
        # 标准化
        if np.max(np.abs(y)) > 0:
            y = y / np.max(np.abs(y)) * 0.9
        
        # 3. STFT分析
        print(f"\n📊 3. STFT频谱分析")
        print("-" * 50)
        
        # STFT参数
        nperseg = 2048
        noverlap = nperseg // 2
        nfft = 2048
        
        frequencies, times, Zxx = stft(
            y, sr, 
            nperseg=nperseg,
            noverlap=noverlap,
            window='hann',
            nfft=nfft
        )
        
        # 频率范围筛选
        freq_mask = (frequencies >= 100) & (frequencies <= 20000)
        frequencies = frequencies[freq_mask]
        Zxx = Zxx[freq_mask, :]
        
        # 计算功率谱
        power_spectrum = np.abs(Zxx) ** 2
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        print(f"   频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
        print(f"   时间分辨率: {times[1]-times[0]:.4f}s")
        print(f"   频率分辨率: {frequencies[1]-frequencies[0]:.1f}Hz")
        
        # 4. 提取第43段数据
        print(f"\n📊 4. 提取第43段数据")
        print("-" * 50)
        
        # 找到对应的时间索引
        time_mask = (times >= seg_start_time) & (times <= seg_end_time)
        segment_times = times[time_mask]
        segment_power = power_db[:, time_mask]
        
        if segment_power.shape[1] == 0:
            print(f"❌ 第43段没有数据")
            return
        
        print(f"   段内时间片数: {segment_power.shape[1]}")
        print(f"   段内时间范围: {segment_times[0]:.3f}s - {segment_times[-1]:.3f}s")
        
        # 5. 竖线检测分析
        print(f"\n📊 5. 竖线检测分析")
        print("-" * 50)
        
        # 计算总能量
        total_energy = np.sum(segment_power, axis=0)
        energy_mean = np.mean(total_energy)
        energy_std = np.std(total_energy)
        
        print(f"   能量统计:")
        print(f"     平均值: {energy_mean:.1f}")
        print(f"     标准差: {energy_std:.1f}")
        print(f"     变异系数: {energy_std/energy_mean:.3f}")
        print(f"     范围: {np.min(total_energy):.1f} - {np.max(total_energy):.1f}")
        
        # 寻找峰值
        # 使用相对阈值
        min_height = energy_mean + 0.5 * energy_std
        min_prominence = 0.3 * energy_std
        
        peaks, properties = find_peaks(total_energy, 
                                     height=min_height,
                                     distance=2,
                                     prominence=min_prominence)
        
        print(f"\n   峰值检测:")
        print(f"     检测阈值: {min_height:.1f}")
        print(f"     突出度阈值: {min_prominence:.1f}")
        print(f"     检测到峰值: {len(peaks)}个")
        
        # 分析每个峰值
        vertical_lines = []
        for i, peak_idx in enumerate(peaks):
            if peak_idx < len(segment_times):
                peak_time = segment_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = segment_power[:, peak_idx]
                
                # 分析该时刻的频谱是否为竖线
                threshold = np.percentile(peak_power_spectrum, 70)
                high_energy_mask = peak_power_spectrum > threshold
                high_energy_indices = np.where(high_energy_mask)[0]
                
                if len(high_energy_indices) >= 5:  # 至少5个频率点
                    freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                    freq_ratio = len(high_energy_indices) / len(frequencies)
                    line_strength = np.mean(peak_power_spectrum[high_energy_indices]) / (np.mean(peak_power_spectrum) + 1e-12)
                    
                    line_info = {
                        'peak_idx': i,
                        'time': peak_time,
                        'energy': peak_energy,
                        'freq_span': freq_span,
                        'freq_ratio': freq_ratio,
                        'line_strength': line_strength,
                        'high_energy_count': len(high_energy_indices)
                    }
                    
                    print(f"     峰值{i+1}: 时间={peak_time:.3f}s, 能量={peak_energy:.1f}")
                    print(f"              频率跨度={freq_span:.1f}Hz, 频率比例={freq_ratio:.3f}")
                    print(f"              线强度={line_strength:.3f}, 高能量点={len(high_energy_indices)}个")
                    
                    # 判断是否为竖线
                    if freq_span >= 1000 and freq_ratio >= 0.1 and line_strength >= 2.0:
                        vertical_lines.append(line_info)
                        print(f"              ✅ 判定为竖线")
                    else:
                        print(f"              ❌ 不是竖线")
        
        print(f"\n   竖线检测结果: {len(vertical_lines)}条竖线")
        
        # 6. 生成可视化
        print(f"\n📊 6. 生成可视化")
        print("-" * 50)
        
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle(f'sd1_1.wav 第43段竖线检测可视化\n'
                    f'时间: {seg_start_time:.3f}s-{seg_end_time:.3f}s, 频率: {expected_freq:.1f}Hz', 
                    fontsize=14, fontweight='bold')
        
        # 子图1: 频谱图
        ax1 = axes[0]
        im1 = ax1.imshow(segment_power, aspect='auto', origin='lower', 
                        extent=[segment_times[0], segment_times[-1], 
                               frequencies[0], frequencies[-1]],
                        cmap='viridis')
        ax1.set_title(f'第43段频谱图 ({expected_freq:.1f}Hz)')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('频率 (Hz)')
        plt.colorbar(im1, ax=ax1, label='功率 (dB)')
        
        # 标记竖线
        for line in vertical_lines:
            ax1.axvline(x=line['time'], color='red', linewidth=2, alpha=0.8)
            ax1.text(line['time'], frequencies[-1]*0.9, f"竖线{line['peak_idx']+1}", 
                    rotation=90, ha='center', va='top', color='red', fontweight='bold')
        
        # 子图2: 总能量曲线
        ax2 = axes[1]
        ax2.plot(segment_times, total_energy, 'b-', linewidth=1.5, label='总能量')
        ax2.axhline(y=energy_mean, color='green', linestyle='--', alpha=0.7, label=f'平均值 ({energy_mean:.1f})')
        ax2.axhline(y=min_height, color='orange', linestyle='--', alpha=0.7, label=f'峰值阈值 ({min_height:.1f})')
        
        # 标记峰值
        if len(peaks) > 0:
            peak_times = segment_times[peaks]
            peak_energies = total_energy[peaks]
            ax2.plot(peak_times, peak_energies, 'ro', markersize=8, label=f'检测峰值 ({len(peaks)}个)')
            
            # 标注峰值
            for i, (t, e) in enumerate(zip(peak_times, peak_energies)):
                ax2.annotate(f'峰{i+1}', xy=(t, e), xytext=(5, 10), 
                           textcoords='offset points', fontsize=9,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 标记竖线
        for line in vertical_lines:
            ax2.axvline(x=line['time'], color='red', linewidth=2, alpha=0.8)
        
        ax2.set_title('总能量变化')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('总能量')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 频率分布分析
        ax3 = axes[2]
        if len(vertical_lines) > 0:
            # 显示第一个竖线的频率分布
            line = vertical_lines[0]
            peak_idx = peaks[line['peak_idx']]
            peak_spectrum = segment_power[:, peak_idx]
            
            ax3.plot(frequencies, peak_spectrum, 'r-', linewidth=2, label=f'竖线时刻频谱')
            ax3.axhline(y=np.percentile(peak_spectrum, 70), color='orange', 
                       linestyle='--', alpha=0.7, label='70%阈值')
            ax3.set_title(f'竖线频率分布 (时间: {line["time"]:.3f}s)')
        else:
            # 显示平均频谱
            avg_spectrum = np.mean(segment_power, axis=1)
            ax3.plot(frequencies, avg_spectrum, 'b-', linewidth=2, label='平均频谱')
            ax3.set_title('平均频率分布')
        
        ax3.set_xlabel('频率 (Hz)')
        ax3.set_ylabel('功率 (dB)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('segment_43_vertical_line_detection.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 可视化完成")
        print(f"📊 图表已保存: segment_43_vertical_line_detection.png")
        
        # 7. 总结
        print(f"\n📊 7. 检测总结")
        print("-" * 50)
        print(f"   第43段基本信息:")
        print(f"     时间范围: {seg_start_time:.3f}s - {seg_end_time:.3f}s")
        print(f"     期望频率: {expected_freq:.1f}Hz")
        print(f"     时长: {seg_end_time - seg_start_time:.3f}s")
        print(f"   ")
        print(f"   能量分析:")
        print(f"     平均能量: {energy_mean:.1f}")
        print(f"     能量标准差: {energy_std:.1f}")
        print(f"     变异系数: {energy_std/energy_mean:.3f}")
        print(f"   ")
        print(f"   检测结果:")
        print(f"     检测到峰值: {len(peaks)}个")
        print(f"     识别竖线: {len(vertical_lines)}条")
        
        if len(vertical_lines) > 0:
            print(f"     ✅ 第43段存在竖线异常")
        else:
            print(f"     ✅ 第43段正常，无竖线异常")
        
    except Exception as e:
        print(f"❌ 可视化失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    visualize_segment_43_detection()
