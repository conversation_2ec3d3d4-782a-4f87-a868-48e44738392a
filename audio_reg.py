import os
import torch
import torchaudio
import random
import numpy as np
from torch import nn
from torch.utils.data import Dataset, DataLoader
from torchaudio.transforms import MelSpectrogram, AmplitudeToDB


# ---------------------------
# 配置参数
# ---------------------------
class Config:
    sample_rate = 16000
    n_fft = 400
    win_length = 400
    hop_length = 160
    n_mels = 64
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    use_specaugment = True
    freq_mask_param = 8
    time_mask_param = 10
    batch_size = 16
    epochs = 30
    lr = 1e-3

config = Config()

# ---------------------------
# 数据增强（SpecAugment）
# ---------------------------
class SpecAugment(nn.Module):
    def __init__(self, freq_mask_param=8, time_mask_param=10):
        super().__init__()
        self.freq_mask = torchaudio.transforms.FrequencyMasking(freq_mask_param)
        self.time_mask = torchaudio.transforms.TimeMasking(time_mask_param)

    def forward(self, x):
        x = self.freq_mask(x)
        x = self.time_mask(x)
        return x

# ---------------------------
# 数据集定义
# ---------------------------
class AudioDataset(Dataset):
    def __init__(self, file_list, labels, augment=False):
        self.file_list = file_list
        self.labels = labels
        self.augment = augment
        self.melspec = MelSpectrogram(
            sample_rate=config.sample_rate,
            n_fft=config.n_fft,
            win_length=config.win_length,
            hop_length=config.hop_length,
            n_mels=config.n_mels
        )
        self.db = AmplitudeToDB()
        self.specaugment = SpecAugment(config.freq_mask_param, config.time_mask_param)

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        filepath = self.file_list[idx]
        label = torch.tensor([self.labels[idx]], dtype=torch.float)

        waveform, sr = torchaudio.load(filepath)
        if sr != config.sample_rate:
            resampler = torchaudio.transforms.Resample(sr, config.sample_rate)
            waveform = resampler(waveform)

        mel = self.melspec(waveform)
        mel_db = self.db(mel)

        if self.augment and config.use_specaugment:
            mel_db = self.specaugment(mel_db)

        mel_db = (mel_db - mel_db.mean()) / (mel_db.std() + 1e-6)

        return mel_db.unsqueeze(0), label

# ---------------------------
# 模型定义
# ---------------------------
class CNNAudioClassifier(nn.Module):
    def __init__(self):
        super().__init__()
        self.cnn = nn.Sequential(
            nn.Conv2d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d((2, 2)),

            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveMaxPool2d((1, 1))
        )
        self.mlp = nn.Sequential(
            nn.Flatten(),
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        x = self.cnn(x)
        x = self.mlp(x)
        return x

# ---------------------------
# 训练函数
# ---------------------------
def train(model, dataloader, optimizer, loss_fn):
    model.train()
    total_loss = 0
    for x, y in dataloader:
        x, y = x.to(config.device), y.to(config.device)
        pred = model(x)
        loss = loss_fn(pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(dataloader)

# ---------------------------
# 验证函数
# ---------------------------
def evaluate(model, dataloader):
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for x, y in dataloader:
            x, y = x.to(config.device), y.to(config.device)
            pred = model(x)
            preds = (pred > 0.5).float()
            correct += (preds == y).sum().item()
            total += y.size(0)
    return correct / total

# ---------------------------
# 主训练流程
# ---------------------------
def main(train_files, train_labels, val_files, val_labels):
    train_set = AudioDataset(train_files, train_labels, augment=True)
    val_set = AudioDataset(val_files, val_labels, augment=False)

    train_loader = DataLoader(train_set, batch_size=config.batch_size, shuffle=True)
    val_loader = DataLoader(val_set, batch_size=config.batch_size)

    model = CNNAudioClassifier().to(config.device)
    optimizer = torch.optim.Adam(model.parameters(), lr=config.lr)
    loss_fn = nn.BCELoss()

    for epoch in range(config.epochs):
        train_loss = train(model, train_loader, optimizer, loss_fn)
        val_acc = evaluate(model, val_loader)
        print(f"Epoch {epoch+1} - Train Loss: {train_loss:.4f}, Val Acc: {val_acc:.4f}")

    return model


if __name__ == "__main__":
    main()