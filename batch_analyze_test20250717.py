#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量分析test20250717文件夹下的所有音频文件
使用参数: --mel-scale --show-diff --workers 8 --freq-range 100 24000
"""

import os
import glob
import subprocess
import time
from pathlib import Path

def find_all_wav_files(root_dir):
    """
    递归查找所有wav文件
    """
    wav_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                full_path = os.path.join(root, file)
                wav_files.append(full_path)
    return wav_files

def get_relative_path_info(file_path, root_dir):
    """
    获取文件的相对路径信息，用于创建输出目录结构
    """
    rel_path = os.path.relpath(file_path, root_dir)
    dir_parts = os.path.dirname(rel_path).split(os.sep) if os.path.dirname(rel_path) else []
    filename = os.path.splitext(os.path.basename(file_path))[0]
    return dir_parts, filename

def create_output_structure(output_base, dir_parts, filename):
    """
    创建输出目录结构
    """
    # 构建输出路径: 输出根目录/子目录/音频文件名_梅尔频谱分析
    if dir_parts:
        subdir_path = os.path.join(output_base, *dir_parts)
    else:
        subdir_path = output_base
    
    # 确保子目录存在
    os.makedirs(subdir_path, exist_ok=True)
    
    # 音频文件的分析结果目录
    audio_output_dir = os.path.join(subdir_path, f"{filename}_梅尔频谱分析")
    return audio_output_dir

def run_analysis(wav_file, output_dir):
    """
    运行单个音频文件的分析
    """
    cmd = [
        'python', 'universal_spectrum_analyzer.py',
        wav_file,
        '--mel-scale',
        '--show-diff', 
        '--workers', '8',
        '--freq-range', '100', '24000'
    ]
    
    print(f"🔄 分析: {os.path.basename(wav_file)}")
    print(f"📁 输出: {output_dir}")
    print(f"⚙️  命令: {' '.join(cmd)}")
    
    try:
        # 临时修改输出目录名称
        original_name = os.path.splitext(os.path.basename(wav_file))[0]
        temp_output = f"{original_name}_梅尔频谱分析"
        
        # 运行分析
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            # 移动结果到目标目录
            if os.path.exists(temp_output):
                if os.path.exists(output_dir):
                    import shutil
                    shutil.rmtree(output_dir)
                os.rename(temp_output, output_dir)
                print(f"✅ 完成: {os.path.basename(wav_file)}")
                return True
            else:
                print(f"❌ 输出目录不存在: {temp_output}")
                return False
        else:
            print(f"❌ 分析失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 执行错误: {str(e)}")
        return False

def main():
    """
    主函数：批量分析test20250717下的所有音频文件
    """
    print("🎯 批量分析test20250717音频文件")
    print("="*70)
    
    # 输入和输出目录
    input_root = "test20250717"
    output_root = "test20250717_梅尔频谱批量分析"
    
    # 检查输入目录
    if not os.path.exists(input_root):
        print(f"❌ 输入目录不存在: {input_root}")
        return
    
    # 创建输出根目录
    os.makedirs(output_root, exist_ok=True)
    print(f"📁 输出根目录: {output_root}")
    
    # 查找所有wav文件
    print("🔍 搜索音频文件...")
    wav_files = find_all_wav_files(input_root)
    
    if not wav_files:
        print("❌ 未找到任何wav文件")
        return
    
    print(f"✅ 找到 {len(wav_files)} 个音频文件")
    
    # 按目录分组显示
    file_groups = {}
    for wav_file in wav_files:
        dir_parts, filename = get_relative_path_info(wav_file, input_root)
        group_key = "/".join(dir_parts) if dir_parts else "根目录"
        if group_key not in file_groups:
            file_groups[group_key] = []
        file_groups[group_key].append(filename + ".wav")
    
    print("\n📊 文件分布:")
    for group, files in file_groups.items():
        print(f"  📂 {group}: {len(files)} 个文件")
        for file in files[:3]:  # 显示前3个文件
            print(f"    - {file}")
        if len(files) > 3:
            print(f"    ... 还有 {len(files)-3} 个文件")
    
    # 开始批量处理
    print(f"\n🚀 开始批量分析...")
    print(f"⚙️  分析参数: --mel-scale --show-diff --workers 8 --freq-range 100 24000")
    
    start_time = time.time()
    successful_count = 0
    failed_count = 0
    
    for i, wav_file in enumerate(wav_files, 1):
        print(f"\n[{i}/{len(wav_files)}] " + "="*50)
        
        # 获取输出目录
        dir_parts, filename = get_relative_path_info(wav_file, input_root)
        output_dir = create_output_structure(output_root, dir_parts, filename)
        
        # 运行分析
        if run_analysis(wav_file, output_dir):
            successful_count += 1
        else:
            failed_count += 1
        
        # 显示进度
        elapsed = time.time() - start_time
        avg_time = elapsed / i
        remaining = (len(wav_files) - i) * avg_time
        print(f"⏱️  进度: {i}/{len(wav_files)}, 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
    
    # 完成统计
    total_time = time.time() - start_time
    print(f"\n" + "="*70)
    print(f"✅ 批量分析完成!")
    print(f"📊 统计结果:")
    print(f"  总文件数: {len(wav_files)}")
    print(f"  成功分析: {successful_count}")
    print(f"  失败: {failed_count}")
    print(f"  成功率: {successful_count/len(wav_files)*100:.1f}%")
    print(f"  总耗时: {total_time:.1f}秒")
    print(f"  平均每文件: {total_time/len(wav_files):.1f}秒")
    print(f"📁 所有结果保存在: {output_root}")
    
    # 显示输出目录结构
    print(f"\n📂 输出目录结构:")
    for root, dirs, files in os.walk(output_root):
        level = root.replace(output_root, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for dir_name in dirs[:3]:  # 只显示前3个目录
            print(f"{subindent}{dir_name}/")
        if len(dirs) > 3:
            print(f"{subindent}... 还有 {len(dirs)-3} 个目录")

if __name__ == "__main__":
    main()
