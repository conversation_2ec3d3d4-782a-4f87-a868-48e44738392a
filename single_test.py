import librosa
import librosa.display
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import kurtosis
import os


# ========= 参数 ==========
AUDIO_PATH = r'test20250708\no1.wav'
SR = 48000
N_FFT = 1024
HOP_LENGTH = 256
WIN_LENGTH = 1024
FMIN = 20

# ========= 加载音频 ==========
y, sr = librosa.load(AUDIO_PATH, sr=SR)
frames = range(0, len(y), HOP_LENGTH)
t = librosa.frames_to_time(np.arange(len(frames)), sr=sr, hop_length=HOP_LENGTH)

# ========= STFT ==========
S = librosa.stft(y, n_fft=N_FFT, hop_length=HOP_LENGTH, win_length=WIN_LENGTH)
S_db = librosa.amplitude_to_db(np.abs(S), ref=np.max)
mag = np.abs(S)

# ========= 频谱平坦度 ==========
flatness = librosa.feature.spectral_flatness(S=mag)[0]

# ========= 频谱峭度 ==========
def spectral_kurtosis(S):
    S_norm = S / (np.sum(S, axis=0, keepdims=True) + 1e-8)
    return kurtosis(S_norm, axis=0, fisher=False)

kurt = spectral_kurtosis(mag)

# ========= 短时 THD+N ==========
def estimate_thdn(S, margin=2):
    """
    动态检测主频，每帧计算THD+N
    S: 幅值谱 (F, T)
    margin: 主频bin左右扩展
    """
    F, T = S.shape
    thdn = []
    for i in range(T):
        spec = S[:, i]
        f0_bin = np.argmax(spec)  # 动态检测主频
        low = max(0, f0_bin - margin)
        high = min(F, f0_bin + margin + 1)
        energy_main = np.sum(spec[low:high] ** 2)
        energy_total = np.sum(spec ** 2)
        thdn_i = 1.0 - (energy_main / (energy_total + 1e-8))
        thdn.append(thdn_i)
    return np.array(thdn)

thdn = estimate_thdn(mag, margin=2)  # 30 bin ≈ 375 Hz at 16kHz

# ========= 能量 ==========
rms_energy = librosa.feature.rms(y=y, frame_length=WIN_LENGTH, hop_length=HOP_LENGTH)[0]

# ========= 可视化 ==========
fig, ax = plt.subplots(5, 1, figsize=(12, 16), sharex=True)

# 时频谱
img = librosa.display.specshow(S_db, sr=sr, hop_length=HOP_LENGTH,
                               x_axis='time', y_axis='log', ax=ax[0])
ax[0].set_title('Spectrogram (dB)')

# THD+N
t_thdn = librosa.frames_to_time(np.arange(len(thdn)), sr=sr, hop_length=HOP_LENGTH)
ax[1].plot(t_thdn, thdn)
ax[1].set_title('Short-Time THD+N (approx)')
ax[1].set_ylabel('Ratio')

# Spectral Flatness
t_flatness = librosa.frames_to_time(np.arange(len(flatness)), sr=sr, hop_length=HOP_LENGTH)
ax[2].plot(t_flatness, flatness)
ax[2].set_title('Spectral Flatness')
ax[2].set_ylabel('Flatness')

# Spectral Kurtosis
t_kurt = librosa.frames_to_time(np.arange(len(kurt)), sr=sr, hop_length=HOP_LENGTH)
ax[3].plot(t_kurt, kurt)
ax[3].set_title('Spectral Kurtosis')
ax[3].set_ylabel('Kurtosis')

# RMS 能量
t_rms = librosa.frames_to_time(np.arange(len(rms_energy)), sr=sr, hop_length=HOP_LENGTH)
ax[4].plot(t_rms, rms_energy)
ax[4].set_title('RMS Energy Envelope')
ax[4].set_ylabel('Energy')
ax[4].set_xlabel('Time (s)')

plt.tight_layout()

# 按输入文件名保存图片
audio_name = os.path.splitext(os.path.basename(AUDIO_PATH))[0]
img_name = f"features_{audio_name}.png"
plt.savefig(img_name, dpi=150)
plt.show()