#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量谐波异常检测器
对指定目录下的所有音频文件进行谐波异常检测
"""

import os
import sys
import time
import json
from datetime import datetime
import multiprocessing as mp
from multiprocessing import Pool

# 添加当前目录到路径
sys.path.append('.')
from harmonic_anomaly_detector import HarmonicAnomalyDetector

def batch_detect_directory(target_dir, output_dir=None, num_processes=None):
    """批量检测目录下的所有音频文件"""
    
    print("🎯 批量谐波异常检测")
    print("="*50)
    
    if not os.path.exists(target_dir):
        print(f"❌ 目标目录不存在: {target_dir}")
        return
    
    # 创建输出目录
    if output_dir is None:
        output_dir = f"detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    # 查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(target_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"📊 找到{len(wav_files)}个wav文件")
    
    if len(wav_files) == 0:
        print("❌ 未找到wav文件")
        return
    
    # 准备多进程参数
    if num_processes is None:
        num_processes = min(mp.cpu_count(), len(wav_files))
    
    print(f"🚀 使用{num_processes}个进程并行检测...")
    
    # 准备参数
    process_args = [(wav_file, output_dir) for wav_file in wav_files]
    
    # 开始批量检测
    start_time = time.time()
    
    with Pool(processes=num_processes) as pool:
        results = pool.map(detect_single_file_worker, process_args)
    
    # 统计结果
    successful_results = [r for r in results if r is not None]
    failed_count = len(results) - len(successful_results)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n✅ 批量检测完成!")
    print(f"  成功: {len(successful_results)}个文件")
    print(f"  失败: {failed_count}个文件")
    print(f"  总耗时: {processing_time:.1f}秒")
    print(f"  平均每文件: {processing_time/len(wav_files):.1f}秒")
    
    # 生成汇总报告
    create_batch_summary(successful_results, output_dir, target_dir)
    
    return successful_results

def detect_single_file_worker(args):
    """单文件检测工作函数 - 用于多进程"""
    wav_file, output_dir = args
    
    try:
        print(f"  检测: {os.path.basename(wav_file)}")
        
        # 创建检测器
        detector = HarmonicAnomalyDetector()
        
        # 生成输出文件名
        safe_filename = ''.join(c for c in os.path.basename(wav_file) if c.isalnum() or c in '_-').replace('.wav', '')
        
        viz_path = os.path.join(output_dir, f"detection_{safe_filename}.png")
        report_path = os.path.join(output_dir, f"report_{safe_filename}.json")
        
        # 分析音频
        analysis_result = detector.analyze_audio_file(wav_file)
        if not analysis_result:
            return None
        
        # 异常检测
        detection_result = detector.detect_anomaly(analysis_result)
        if not detection_result:
            return None
        
        # 保存可视化
        detector.create_detection_visualization(detection_result, viz_path)
        
        # 保存报告
        detector.save_detection_report(detection_result, report_path)
        
        return {
            'filename': os.path.basename(wav_file),
            'filepath': wav_file,
            'is_anomaly': detection_result['is_anomaly'],
            'anomaly_ratio': detection_result['anomaly_ratio'],
            'anomaly_segments_count': detection_result['anomaly_segments_count'],
            'total_segments': detection_result['total_segments'],
            'total_harmonics': detection_result['total_harmonics'],
            'viz_path': viz_path,
            'report_path': report_path
        }
        
    except Exception as e:
        print(f"  ❌ 检测失败: {os.path.basename(wav_file)} - {e}")
        return None

def create_batch_summary(results, output_dir, target_dir):
    """创建批量检测汇总报告"""
    
    if not results:
        return
    
    # 统计数据
    total_files = len(results)
    anomaly_files = [r for r in results if r['is_anomaly']]
    normal_files = [r for r in results if not r['is_anomaly']]
    
    anomaly_count = len(anomaly_files)
    normal_count = len(normal_files)
    anomaly_rate = anomaly_count / total_files if total_files > 0 else 0
    
    # 创建汇总报告
    summary = {
        'batch_detection_summary': {
            'detection_time': datetime.now().isoformat(),
            'target_directory': target_dir,
            'output_directory': output_dir,
            'total_files': total_files,
            'normal_files': normal_count,
            'anomaly_files': anomaly_count,
            'anomaly_rate': anomaly_rate
        },
        'statistics': {
            'total_harmonics': {
                'min': min([r['total_harmonics'] for r in results]),
                'max': max([r['total_harmonics'] for r in results]),
                'mean': sum([r['total_harmonics'] for r in results]) / total_files,
                'anomaly_mean': sum([r['total_harmonics'] for r in anomaly_files]) / len(anomaly_files) if anomaly_files else 0,
                'normal_mean': sum([r['total_harmonics'] for r in normal_files]) / len(normal_files) if normal_files else 0
            },
            'anomaly_ratio': {
                'min': min([r['anomaly_ratio'] for r in results]),
                'max': max([r['anomaly_ratio'] for r in results]),
                'mean': sum([r['anomaly_ratio'] for r in results]) / total_files,
                'anomaly_mean': sum([r['anomaly_ratio'] for r in anomaly_files]) / len(anomaly_files) if anomaly_files else 0,
                'normal_mean': sum([r['anomaly_ratio'] for r in normal_files]) / len(normal_files) if normal_files else 0
            }
        },
        'anomaly_files_list': [
            {
                'filename': r['filename'],
                'anomaly_ratio': r['anomaly_ratio'],
                'anomaly_segments_count': r['anomaly_segments_count'],
                'total_harmonics': r['total_harmonics']
            }
            for r in anomaly_files
        ],
        'normal_files_list': [
            {
                'filename': r['filename'],
                'anomaly_ratio': r['anomaly_ratio'],
                'total_harmonics': r['total_harmonics']
            }
            for r in normal_files
        ]
    }
    
    # 保存汇总报告
    summary_path = os.path.join(output_dir, 'batch_detection_summary.json')
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 创建简单的文本报告
    text_report_path = os.path.join(output_dir, 'batch_detection_summary.txt')
    with open(text_report_path, 'w', encoding='utf-8') as f:
        f.write("批量谐波异常检测汇总报告\n")
        f.write("="*50 + "\n\n")
        
        f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"目标目录: {target_dir}\n")
        f.write(f"输出目录: {output_dir}\n\n")
        
        f.write(f"检测结果统计:\n")
        f.write(f"  总文件数: {total_files}\n")
        f.write(f"  正常文件: {normal_count}个 ({normal_count/total_files*100:.1f}%)\n")
        f.write(f"  异常文件: {anomaly_count}个 ({anomaly_count/total_files*100:.1f}%)\n\n")
        
        if anomaly_files:
            f.write(f"异常文件列表:\n")
            f.write("-" * 30 + "\n")
            for r in anomaly_files:
                f.write(f"🔴 {r['filename']}\n")
                f.write(f"   异常段比例: {r['anomaly_ratio']:.1%}\n")
                f.write(f"   异常段数: {r['anomaly_segments_count']}/{r['total_segments']}\n")
                f.write(f"   总谐波数: {r['total_harmonics']}\n\n")
        
        f.write(f"统计信息:\n")
        f.write(f"  总谐波数范围: {summary['statistics']['total_harmonics']['min']}-{summary['statistics']['total_harmonics']['max']}\n")
        f.write(f"  平均总谐波数: {summary['statistics']['total_harmonics']['mean']:.1f}\n")
        f.write(f"  异常文件平均谐波数: {summary['statistics']['total_harmonics']['anomaly_mean']:.1f}\n")
        f.write(f"  正常文件平均谐波数: {summary['statistics']['total_harmonics']['normal_mean']:.1f}\n")
    
    print(f"📄 汇总报告已保存:")
    print(f"  JSON: {summary_path}")
    print(f"  文本: {text_report_path}")
    
    # 打印简要统计
    print(f"\n📊 检测结果统计:")
    print(f"  正常文件: {normal_count}个 ({normal_count/total_files*100:.1f}%)")
    print(f"  异常文件: {anomaly_count}个 ({anomaly_count/total_files*100:.1f}%)")
    
    if anomaly_files:
        print(f"\n🔴 异常文件:")
        for r in anomaly_files[:10]:  # 只显示前10个
            print(f"  {r['filename']} - 异常比例: {r['anomaly_ratio']:.1%}")
        if len(anomaly_files) > 10:
            print(f"  ... 还有{len(anomaly_files)-10}个异常文件")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量谐波异常检测')
    parser.add_argument('target_dir', help='目标目录路径')
    parser.add_argument('--output', '-o', help='输出目录路径')
    parser.add_argument('--processes', '-p', type=int, help='并行进程数')
    
    args = parser.parse_args()
    
    # 执行批量检测
    batch_detect_directory(args.target_dir, args.output, args.processes)

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认设置
    if len(sys.argv) == 1:
        # 默认检测test20250717目录
        target_dir = '../test20250717'
        if os.path.exists(target_dir):
            batch_detect_directory(target_dir)
        else:
            print("请指定目标目录路径")
            print("用法: python batch_harmonic_detector.py <目标目录>")
    else:
        main()
