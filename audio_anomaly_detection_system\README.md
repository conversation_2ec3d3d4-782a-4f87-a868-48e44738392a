# 音频异响检测系统 (Audio Anomaly Detection System)

## 🎯 项目简介

这是一个基于机器学习的音频异响检测系统，专门用于检测步进扫频音频中的异响问题。系统能够自动识别：

- **竖线干扰**：频谱图中的垂直线条干扰
- **低频漏气噪声**：低频段的异常能量泄漏
- **总谐波失真**：THD/THD+N指标异常
- **其他音频异常**：频谱异常、能量分布异常等

## 📁 项目结构

```
audio_anomaly_detection_system/
├── README.md                          # 项目说明文档
├── README_使用指南.md                   # 详细使用指南
├── requirements.txt                    # 依赖包列表
├── 
├── 核心模块/
├── easy_detector.py                   # 🌟 简易检测器（主要接口）
├── optimized_feature_extractor.py    # 优化的特征提取器
├── improved_vertical_detection.py    # 改进的竖线干扰检测
├── feature_analysis.py               # 特征有效性分析
├── optimized_classifier.py           # 优化的分类器对比
├── 
├── 工具和示例/
├── usage_guide.py                    # 完整使用指南和示例
├── test_vertical_detection.py        # 竖线干扰检测测试
├── freq_split.py                     # 频段分割工具（依赖）
├── 
├── 模型文件/
└── audio_anomaly_detector.pkl        # 预训练模型（如果存在）
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖包
pip install -r requirements.txt
```

### 2. 最简单的使用方式

```python
from easy_detector import AudioAnomalyDetector

# 创建检测器
detector = AudioAnomalyDetector()

# 如果有预训练模型，直接加载
detector.load_model("audio_anomaly_detector.pkl")

# 检测单个文件
result = detector.detect_anomaly("your_audio.wav")
print(f"检测结果: {result['overall_status']}")
print(f"正常概率: {result['normal_probability']:.3f}")

# 批量检测
results = detector.batch_detect("audio_folder", "results.csv")
```

### 3. 直接运行演示

```bash
python easy_detector.py
```

## 📋 详细功能

### 🔧 核心模块说明

1. **easy_detector.py** - 主要接口
   - `AudioAnomalyDetector` 类：简易检测器
   - 支持训练、预测、批量处理
   - 自动特征选择和模型保存

2. **optimized_feature_extractor.py** - 特征提取
   - `OptimizedFeatureExtractor` 类：优化的特征提取器
   - 分帧检测、THD分析、竖线干扰检测
   - 支持批量处理数据集

3. **improved_vertical_detection.py** - 竖线干扰检测
   - `detect_vertical_line_interference_v2` 函数：改进的检测算法
   - 基于非主频非谐波频点的能量突增检测
   - 可调节的检测阈值

4. **feature_analysis.py** - 特征分析
   - 特征重要性分析
   - 冗余特征识别
   - 分类性能评估

### 🎯 使用场景

#### 场景1：检测单个音频文件
```python
detector = AudioAnomalyDetector()
detector.load_model("audio_anomaly_detector.pkl")
result = detector.detect_anomaly("test.wav")
```

#### 场景2：训练自定义模型
```python
# 准备数据集：dataset/pos/ 和 dataset/neg/
detector = AudioAnomalyDetector()
detector.train_from_dataset("dataset")
```

#### 场景3：批量检测
```python
results = detector.batch_detect("audio_folder", "results.csv")
```

#### 场景4：特征分析
```python
from feature_analysis import analyze_feature_effectiveness
analyze_feature_effectiveness()
```

## 📊 系统性能

- **准确率**: ~68%
- **异常检测精度**: 70%
- **特征优化**: 从83个特征优化到10个核心特征
- **计算效率**: 提升约75%
- **检测能力**: 能有效区分正负样本

## 🔍 检测结果解释

### 状态分类
- **正常**: 音频质量良好，无明显异响
- **可疑**: 存在轻微异常，建议进一步检查  
- **异常**: 检测到明显异响问题

### 置信度
- **高**: 检测结果可靠性高
- **中**: 检测结果有一定可靠性

### 关键指标
- `normal_probability`: 正常概率 (0-1)
- `normal_segments`: 正常频段数量
- `anomaly_segments`: 异常频段数量

## ⚙️ 配置参数

### 特征提取参数
```python
extract_features_from_audio(
    audio_path,
    min_duration=156,        # 最小持续时间(ms)
    energy_threshold_db=-45, # 能量阈值(dB)
    start_freq=100,          # 起始频率(Hz)
    stop_freq=20000          # 结束频率(Hz)
)
```

### 竖线干扰检测参数
```python
detect_vertical_line_interference_v2(
    S, freqs, f0,
    threshold_ratio=3,      # 能量突增阈值倍数
    min_affected_freqs=10   # 最小受影响频率数
)
```

## 🛠️ 依赖要求

- Python >= 3.7
- numpy
- pandas
- librosa
- scikit-learn
- matplotlib
- seaborn
- scipy

## 📝 使用注意事项

1. **音频格式**: 目前只支持WAV格式
2. **采样率**: 建议使用48kHz或96kHz
3. **文件命名**: 包含"156"的文件使用156ms参数，其他使用153ms
4. **数据集**: 训练时确保正负样本相对平衡
5. **模型更新**: 建议定期重新训练模型

## 🔧 故障排除

### 常见问题
1. **导入错误**: 确保所有依赖包已安装
2. **文件路径**: 检查音频文件路径是否正确
3. **模型加载**: 确保模型文件存在且完整
4. **内存不足**: 大文件可能需要更多内存

### 获取帮助
- 查看 `README_使用指南.md` 获取详细说明
- 运行 `usage_guide.py` 查看完整示例
- 检查控制台输出的错误信息

## 📈 更新日志

### v1.0 (当前版本)
- ✅ 实现优化的特征提取系统
- ✅ 修正竖线干扰检测逻辑
- ✅ 完成特征有效性分析
- ✅ 提供简易检测器接口
- ✅ 支持批量处理和模型保存

## 🎉 快速测试

```bash
# 运行完整演示
python usage_guide.py

# 运行简易检测器
python easy_detector.py

# 测试竖线干扰检测
python improved_vertical_detection.py
```

---

**开发者**: AI Assistant  
**版本**: 1.0  
**更新时间**: 2025-01-17
