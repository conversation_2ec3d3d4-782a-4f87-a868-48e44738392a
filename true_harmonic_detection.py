#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的谐波检测算法
严格区分真实谐波和噪声中的随机峰值
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks, peak_prominences
import librosa

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def true_harmonic_detection():
    """真正的谐波检测演示"""
    print("🔧 真正的谐波检测算法")
    print("="*70)
    print("核心原理:")
    print("1. 谐波是由信号非线性失真产生的，不是噪声")
    print("2. 真实谐波必须显著高于局部噪声底噪")
    print("3. 谐波功率应该与基频功率成比例关系")
    print("4. 谐波应该有清晰的峰值特征，不是噪声起伏")
    print("5. 使用信噪比阈值严格筛选谐波")
    print("="*70)
    
    # 生成对比测试信号
    test_cases = [
        {
            'name': '纯净信号+少量谐波',
            'fundamental': 1000,
            'harmonics': [0.3, 0.1, 0.05],  # 明显的谐波
            'noise_level': 0.01  # 很低的噪声
        },
        {
            'name': '中等失真信号',
            'fundamental': 1000, 
            'harmonics': [0.2, 0.08, 0.03, 0.01],  # 中等谐波
            'noise_level': 0.03  # 中等噪声
        },
        {
            'name': '高噪声信号',
            'fundamental': 1000,
            'harmonics': [0.15, 0.05],  # 较弱谐波
            'noise_level': 0.08  # 高噪声，可能掩盖弱谐波
        },
        {
            'name': '几乎无谐波信号',
            'fundamental': 1000,
            'harmonics': [],  # 无明显谐波
            'noise_level': 0.05  # 中等噪声
        }
    ]
    
    for case in test_cases:
        print(f"\n🎯 测试: {case['name']}")
        print("-" * 50)
        
        # 生成测试信号
        signal_data = generate_controlled_test_signal(case)
        
        # 真正的谐波检测
        harmonic_analysis = true_harmonic_analysis(signal_data)
        
        # 可视化结果
        visualize_true_harmonic_detection(signal_data, harmonic_analysis, case['name'])

def generate_controlled_test_signal(test_case):
    """生成可控的测试信号"""
    
    fs = 48000
    duration = 1.0
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    fundamental_freq = test_case['fundamental']
    harmonics = test_case['harmonics']
    noise_level = test_case['noise_level']
    
    # 基频信号
    signal = np.sin(2 * np.pi * fundamental_freq * t)
    
    # 添加指定的谐波 (这些是真实的谐波)
    true_harmonics = []
    for i, amp in enumerate(harmonics):
        harmonic_order = i + 2
        harmonic_freq = fundamental_freq * harmonic_order
        
        if harmonic_freq < fs / 2:
            signal += amp * np.sin(2 * np.pi * harmonic_freq * t)
            true_harmonics.append({
                'order': harmonic_order,
                'freq': harmonic_freq,
                'amplitude': amp
            })
    
    # 添加白噪声
    noise = noise_level * np.random.randn(len(t))
    signal += noise
    
    print(f"   生成信号参数:")
    print(f"     基频: {fundamental_freq}Hz")
    print(f"     真实谐波: {len(true_harmonics)}个")
    for h in true_harmonics:
        print(f"       {h['order']}次谐波: {h['freq']}Hz, 幅度{h['amplitude']:.3f}")
    print(f"     噪声水平: {noise_level:.3f}")
    
    return {
        'signal': signal,
        'fs': fs,
        'fundamental_freq': fundamental_freq,
        'true_harmonics': true_harmonics,
        'noise_level': noise_level,
        'duration': duration,
        'time': t
    }

def true_harmonic_analysis(signal_data):
    """真正的谐波分析"""
    
    signal = signal_data['signal']
    fs = signal_data['fs']
    fundamental_freq = signal_data['fundamental_freq']
    true_harmonics = signal_data['true_harmonics']
    
    # 高分辨率FFT
    fft_size = max(32768, len(signal))  # 更高分辨率
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    # 只分析正频率
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    power_db = 10 * np.log10(positive_power + 1e-12)
    
    freq_resolution = positive_freqs[1] - positive_freqs[0]
    print(f"   频率分辨率: {freq_resolution:.3f} Hz")
    
    # 1. 找到主频并估计其功率
    fundamental_analysis = analyze_fundamental_with_snr(positive_freqs, positive_power, fundamental_freq)
    
    # 2. 估计噪声底噪水平
    noise_floor_analysis = estimate_noise_floor(positive_freqs, positive_power, fundamental_freq)
    
    # 3. 使用严格的SNR阈值检测谐波
    detected_harmonics = detect_harmonics_with_snr_threshold(
        positive_freqs, positive_power, fundamental_freq, 
        fundamental_analysis, noise_floor_analysis
    )
    
    # 4. 验证检测结果与真实谐波的对比
    validation_results = validate_against_ground_truth(detected_harmonics, true_harmonics)
    
    return {
        'freqs': positive_freqs,
        'power': positive_power,
        'power_db': power_db,
        'fundamental_analysis': fundamental_analysis,
        'noise_floor_analysis': noise_floor_analysis,
        'detected_harmonics': detected_harmonics,
        'validation_results': validation_results,
        'true_harmonics': true_harmonics,
        'freq_resolution': freq_resolution
    }

def analyze_fundamental_with_snr(freqs, power, expected_freq):
    """分析主频并计算其信噪比"""
    
    # 主频搜索带宽
    bandwidth = 3.0  # 固定±3Hz，足够精确
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    if len(search_powers) == 0:
        return None
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    # 估计主频附近的局部噪声水平
    # 排除主频±10Hz范围，计算周围的噪声
    noise_estimation_mask = (freqs >= expected_freq - 50) & (freqs <= expected_freq + 50) & \
                           ~((freqs >= expected_freq - 10) & (freqs <= expected_freq + 10))
    
    if np.any(noise_estimation_mask):
        local_noise_powers = power[noise_estimation_mask]
        local_noise_floor = np.median(local_noise_powers)  # 使用中位数更稳定
        local_noise_floor_db = 10 * np.log10(local_noise_floor + 1e-12)
        
        # 计算主频的局部SNR
        local_snr_db = fundamental_power_db - local_noise_floor_db
    else:
        local_noise_floor_db = -120
        local_snr_db = 120
    
    print(f"   主频分析:")
    print(f"     频率: {fundamental_freq:.2f}Hz (期望: {expected_freq}Hz)")
    print(f"     功率: {fundamental_power_db:.1f}dB")
    print(f"     局部噪声底噪: {local_noise_floor_db:.1f}dB")
    print(f"     局部SNR: {local_snr_db:.1f}dB")
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'local_noise_floor_db': local_noise_floor_db,
        'local_snr_db': local_snr_db,
        'index': actual_idx
    }

def estimate_noise_floor(freqs, power, fundamental_freq):
    """估计全局噪声底噪水平"""
    
    # 排除可能的信号频率 (基频和前几个谐波)
    excluded_ranges = []
    
    # 排除基频±20Hz
    excluded_ranges.append((fundamental_freq - 20, fundamental_freq + 20))
    
    # 排除前10个谐波位置±20Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 20, harmonic_freq + 20))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    # 计算噪声统计
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        
        # 使用多种统计量估计噪声底噪
        noise_floor_median = np.median(noise_powers)
        noise_floor_mean = np.mean(noise_powers)
        noise_floor_10th = np.percentile(noise_powers, 10)  # 最低10%
        noise_floor_25th = np.percentile(noise_powers, 25)  # 最低25%
        
        # 选择保守的噪声底噪估计 (25th percentile)
        noise_floor = noise_floor_25th
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        
        print(f"   全局噪声底噪分析:")
        print(f"     噪声频点数: {np.sum(noise_mask)}")
        print(f"     噪声底噪(25th): {noise_floor_db:.1f}dB")
        print(f"     噪声底噪(median): {10 * np.log10(noise_floor_median + 1e-12):.1f}dB")
        print(f"     噪声底噪(10th): {10 * np.log10(noise_floor_10th + 1e-12):.1f}dB")
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_floor_median_db': 10 * np.log10(noise_floor_median + 1e-12),
            'noise_floor_10th_db': 10 * np.log10(noise_floor_10th + 1e-12),
            'noise_sample_count': np.sum(noise_mask)
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0
        }

def detect_harmonics_with_snr_threshold(freqs, power, fundamental_freq, 
                                       fundamental_analysis, noise_floor_analysis):
    """使用严格的SNR阈值检测谐波"""
    
    if not fundamental_analysis or not noise_floor_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_floor_analysis['noise_floor_db']
    
    # 严格的SNR阈值
    MIN_HARMONIC_SNR_DB = 15.0  # 谐波必须比噪声底噪高15dB
    MIN_RELATIVE_POWER_DB = -40.0  # 谐波功率相对于基频的最小值
    
    print(f"\n   谐波检测 (SNR阈值: {MIN_HARMONIC_SNR_DB}dB):")
    
    for order in range(2, 11):  # 只检测前9个谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= freqs[-1]:
            print(f"     {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超出频谱范围")
            break
        
        # 搜索谐波
        bandwidth = 5.0  # 固定±5Hz搜索带宽
        search_mask = (freqs >= expected_harmonic_freq - bandwidth) & \
                     (freqs <= expected_harmonic_freq + bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR (相对于全局噪声底噪)
        harmonic_snr_db = harmonic_power_db - noise_floor_db
        
        # 计算相对于基频的功率比
        relative_power_db = harmonic_power_db - fundamental_analysis['power_db']
        
        # 严格的谐波判断条件
        is_valid_harmonic = (
            harmonic_snr_db >= MIN_HARMONIC_SNR_DB and  # SNR足够高
            relative_power_db >= MIN_RELATIVE_POWER_DB and  # 相对功率合理
            abs(actual_freq - expected_harmonic_freq) <= bandwidth * 0.5  # 频率精度
        )
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': harmonic_snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': actual_freq - expected_harmonic_freq
            })
            
            print(f"     ✅ {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"SNR={harmonic_snr_db:.1f}dB, 相对基频{relative_power_db:+.1f}dB")
        else:
            reasons = []
            if harmonic_snr_db < MIN_HARMONIC_SNR_DB:
                reasons.append(f"SNR不足({harmonic_snr_db:.1f}dB)")
            if relative_power_db < MIN_RELATIVE_POWER_DB:
                reasons.append(f"功率过低({relative_power_db:.1f}dB)")
            if abs(actual_freq - expected_harmonic_freq) > bandwidth * 0.5:
                reasons.append(f"频率误差过大({actual_freq - expected_harmonic_freq:.1f}Hz)")
            
            print(f"     ❌ {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"SNR={harmonic_snr_db:.1f}dB - {', '.join(reasons)}")
    
    print(f"\n   检测结果: {len(detected_harmonics)} 个真实谐波")
    
    return detected_harmonics

def validate_against_ground_truth(detected_harmonics, true_harmonics):
    """验证检测结果与真实谐波的对比"""
    
    print(f"\n   检测准确性验证:")
    print(f"     真实谐波数: {len(true_harmonics)}")
    print(f"     检测到谐波数: {len(detected_harmonics)}")
    
    # 匹配检测结果与真实谐波
    true_positive = 0
    false_positive = 0
    false_negative = 0
    
    detected_orders = {h['order'] for h in detected_harmonics}
    true_orders = {h['order'] for h in true_harmonics}
    
    # 计算混淆矩阵
    true_positive = len(detected_orders & true_orders)
    false_positive = len(detected_orders - true_orders)
    false_negative = len(true_orders - detected_orders)
    
    # 计算性能指标
    precision = true_positive / (true_positive + false_positive) if (true_positive + false_positive) > 0 else 0
    recall = true_positive / (true_positive + false_negative) if (true_positive + false_negative) > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"     正确检测 (TP): {true_positive}")
    print(f"     误报 (FP): {false_positive}")
    print(f"     漏检 (FN): {false_negative}")
    print(f"     精确率: {precision:.3f}")
    print(f"     召回率: {recall:.3f}")
    print(f"     F1分数: {f1_score:.3f}")
    
    # 详细对比
    if false_positive > 0:
        fp_orders = detected_orders - true_orders
        print(f"     误报谐波: {sorted(fp_orders)}次")
    
    if false_negative > 0:
        fn_orders = true_orders - detected_orders
        print(f"     漏检谐波: {sorted(fn_orders)}次")
    
    return {
        'true_positive': true_positive,
        'false_positive': false_positive,
        'false_negative': false_negative,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score
    }

def visualize_true_harmonic_detection(signal_data, harmonic_analysis, test_name):
    """可视化真正的谐波检测结果"""
    
    freqs = harmonic_analysis['freqs']
    power_db = harmonic_analysis['power_db']
    fundamental_analysis = harmonic_analysis['fundamental_analysis']
    noise_floor_analysis = harmonic_analysis['noise_floor_analysis']
    detected_harmonics = harmonic_analysis['detected_harmonics']
    true_harmonics = harmonic_analysis['true_harmonics']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 上图: 时域信号
    t = signal_data['time']
    signal = signal_data['signal']
    ax1.plot(t[:2000], signal[:2000], 'b-', linewidth=1)
    ax1.set_title(f'{test_name} - 时域信号', fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('幅度')
    ax1.grid(True, alpha=0.3)
    
    # 下图: 频域谐波检测
    ax2.plot(freqs, power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
    
    # 标记噪声底噪线
    if noise_floor_analysis:
        noise_floor_db = noise_floor_analysis['noise_floor_db']
        ax2.axhline(y=noise_floor_db, color='gray', linestyle='--', alpha=0.8, 
                   label=f'噪声底噪 {noise_floor_db:.1f}dB')
        
        # SNR阈值线
        snr_threshold_db = noise_floor_db + 15.0
        ax2.axhline(y=snr_threshold_db, color='orange', linestyle='--', alpha=0.8,
                   label=f'谐波SNR阈值 {snr_threshold_db:.1f}dB')
    
    # 标记主频
    if fundamental_analysis:
        fund_freq = fundamental_analysis['freq']
        fund_power_db = fundamental_analysis['power_db']
        ax2.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
                label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
    
    # 标记真实谐波位置 (理论位置)
    for harmonic in true_harmonics:
        ax2.axvline(x=harmonic['freq'], color='lightgreen', linestyle=':', alpha=0.6)
        ax2.text(harmonic['freq'], ax2.get_ylim()[1] - 5, f"真实{harmonic['order']}次", 
                rotation=90, ha='center', va='top', fontsize=8, color='green')
    
    # 标记检测到的谐波
    harmonic_colors = ['red', 'orange', 'green', 'purple', 'brown', 'pink', 'gray', 'olive']
    
    for i, harmonic in enumerate(detected_harmonics):
        color = harmonic_colors[i % len(harmonic_colors)]
        freq = harmonic['freq']
        power_db = harmonic['power_db']
        order = harmonic['order']
        snr_db = harmonic['snr_db']
        
        ax2.plot(freq, power_db, 'o', color=color, markersize=10,
                label=f'检测{order}次谐波 {freq:.0f}Hz (SNR={snr_db:.1f}dB)')
        
        # 标注
        ax2.annotate(f'检测{order}次\nSNR={snr_db:.1f}dB', 
                    xy=(freq, power_db), xytext=(freq, power_db + 8),
                    ha='center', va='bottom', fontsize=8, color=color,
                    arrowprops=dict(arrowstyle='->', color=color, lw=1))
    
    # 设置图表属性
    ax2.set_title(f'{test_name} - 真实谐波检测 (SNR阈值过滤)', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('功率 (dB)')
    ax2.set_xlim(0, 12000)  # 只显示0-12kHz
    ax2.set_ylim(np.min(power_db) - 5, np.max(power_db) + 20)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'true_harmonic_detection_{test_name.replace(" ", "_").replace("+", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 真实谐波检测可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    true_harmonic_detection()
