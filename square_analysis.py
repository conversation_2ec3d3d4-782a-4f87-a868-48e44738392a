import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import IPython.display as ipd
import soundfile as sf
import librosa


def auto_correlate(x):
    x = np.asarray(x)
    # y = np.asarray(y)
    N = x.shape[0]
    acf1 = np.correlate(x, x, mode='full')
    acf1 = acf1[N-1:]
    acf1 = acf1 / np.arange(N, 0, -1)
    acf1 = acf1 / acf1[0]
    return acf1

def cross_correlate(x, y):
    N = min(x.shape[0], y.shape[0])
    x = np.asarray(x[:N])
    y = np.asarray(y[:N])
    acf1 = np.correlate(x, y, mode='full')
    acf1 = acf1[:N]
    print(f'x: {x.shape}, y: {y.shape}, acf1: {acf1.shape}')
    # acf1 = acf1 / np.arange(N, 0, -1)
    return acf1

def normalize(x):
    return x / np.max(np.abs(x))

def find_delay_v1(x, delay_const, fs, threshold=0.1):
    for i in range(delay_const*fs, len(x)):
        if not i:
            continue
        if np.abs(x[i] - x[i-1]) > threshold:
            return i
    return None

def find_delay_v2(x, delay_const, fs, threshold=0.1):
    for i in range(delay_const * fs, len(x)):
        if not i or i == len(x)-1:
            continue
        if np.abs(x[i] - x[i-1]) > threshold and (x[i] - x[i-1])*(x[i+1] - x[i]) > 0:
            return i
    return None

def find_delay_v3(x, delay_const, fs):
    # noise_mean = np.mean(x[:delay_const * fs])
    # noise_var = np.var(x[:delay_const * fs])
    noise_diff = np.diff(x[:delay_const * fs])
    noise_diff_max = np.max(np.abs(noise_diff))
    for i in range(delay_const * fs, len(x)):
        if not i or i == len(x)-1:
            continue
        if np.abs(x[i] - x[i-1]) > noise_diff_max and (x[i] - x[i-1])*(x[i+1] - x[i]) > 0:
            return i
    return None

def find_delay_v4(x, delay_const, fs, threshold=0.1):
    # noise_mean = np.mean(x[:delay_const * fs])
    # noise_var = np.var(x[:delay_const * fs])
    noise_diff = np.diff(x[:delay_const * fs])
    noise_diff_max = np.max(np.abs(noise_diff))
    for i in range(delay_const * fs, len(x)):
        if not i or i == len(x)-1:
            continue
        if np.abs(x[i]) > threshold and (x[i] - x[i-1])*(x[i+1] - x[i]) > 0:
            return i
    return None

def find_delay_v5(x, delay_const, fs, threshold_1st=0.1, threshold_2nd=0.01):
    pos = 0
    for i in range(delay_const * fs, len(x)):
        if not i or i == len(x)-1:
            continue
        if np.abs(x[i]) > threshold_1st and (x[i] - x[i-1]) * (x[i+1] - x[i]) > 0:
            pos = i
            break
    if not pos:
        return pos

    for i in range(pos-1, -1, -1):
        if (abs(x[i]) < threshold_2nd and x[i] * x[pos] >= 0) or x[i] * x[pos] < 0:
            return i + 1
    return pos

def find_delay_v6(x, y):
    N = min(x.shape[0], y.shape[0])
    x = np.asarray(x[:N])
    y = np.asarray(y[:N])
    acf1 = np.correlate(x, y, mode='full')
    acf1 = acf1[:N]
    print(f'x: {x.shape}, y: {y.shape}, acf1: {acf1.shape}')
    # acf1 = acf1 / np.arange(N, 0, -1)
    return acf1


fs = 96000
duration = 1
f = 1000
delay = 1
t = np.arange(0, duration, 1/fs)
# square_wave = signal.square(2 * np.pi * f * t)
# square_wave = np.concatenate((np.zeros(int(delay * fs)), square_wave))
# plt.figure(figsize=(20, 6))
# plt.plot(square_wave)
# plt.xlabel('Time')
# plt.ylabel('Amplitude')
# plt.title('Square Wave Graph')
# plt.grid(True)
# plt.show()

# v_path = f'test20241023\sq_f1000-d1-dl1-fs96000_v1.wav'
# i_path = f'test20241023\sq_f1000-d1-dl1-fs96000_i1.wav'
# mic_path = f'test20241023\sq_f1000-d1-dl1-fs96000_mic1.wav'

ground_truth_path = 'gen_square/sq_half_f100-d0.005-dl1-fs96000.wav'
v_path = 'test20241101/sq_half_f100-d0.005-dl1-fs96000_v1.wav'
i_path = 'test20241101/sq_half_f100-d0.005-dl1-fs96000_i1.wav'
mic_path = 'test20241101/sq_half_f100-d0.005-dl1-fs96000_mic1.wav'
disp_path = 'test20241101/sq_half_f100-d0.005-dl1-fs96000_disp1.wav'

signal_class = {0:'v', 1:'i', 2:'mic', 3:'disp'}
g, _ = librosa.load(ground_truth_path, sr=fs)
v, _ = librosa.load(v_path, sr=fs)
i, _ = librosa.load(i_path, sr=fs)
mic, _ = librosa.load(mic_path, sr=fs)
disp, _ = librosa.load(disp_path, sr=fs)

for i, x in enumerate([v, i, mic, disp]):
    x = normalize(x)
    # plt.figure(figsize=(20, 12))
    # plt.plot(x, marker='.')
    # plt.title(f'{signal_class[i]} Signal')
    # plt.grid(True)
    # plt.show()
    onset1 = find_delay_v1(x, delay, fs)
    if onset1:
        print(f'{signal_class[i]}v1 onset sample: {onset1}, time: {onset1/fs}')
    
    onset2 = find_delay_v2(x, delay, fs)
    if onset2:
        print(f'{signal_class[i]}v2 onset sample: {onset2}, time: {onset2/fs}')

    # onset3 = find_delay_v3(x, delay, fs)
    # if onset3:
    #     print(f'{signal_class[i]}v3 onset sample: {onset3}, time: {onset3/fs}')

    onset4 = find_delay_v4(x, delay, fs)
    if onset4:
        print(f'{signal_class[i]}v4 onset sample: {onset4}, time: {onset4/fs}')
        
    onset5 = find_delay_v5(x, delay, fs)
    if onset5:
        print(f'{signal_class[i]}v5 onset sample: {onset5}, time: {onset5/fs}')


    # plt.figure(figsize=(20, 12))
    # plt.subplot(2, 1, 1)
    # plt.plot(x[:])
    # plt.title(f'{signal_class[i]} Signal')
    # plt.subplot(2, 1, 2)
    # diff = np.diff(x)
    # diff = np.pad(diff, (1, 0), 'constant')
    # diff = np.abs(diff)
    # diff[diff < 0.1] = 0
    # print(np.where(diff > 0)[0])
    # plt.plot(diff, marker='.')
    # plt.grid(True)
    # # c = auto_correlate(x)
    # # plt.plot(c)
    # # plt.subplot(3, 1, 3)

    # # d = cross_correlate(square_wave, x)
    # # d = normalize(d)
    # # match_index = np.argmax(d)
    # # delta_samples = x.shape[0] - match_index
    # # delta_t = delta_samples/fs
    # # print(np.argmax(d), delta_samples, delta_t)
    # # plt.plot(d)
    # plt.show()

