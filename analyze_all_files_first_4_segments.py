#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析test20250717目录下所有wav文件的前4段谐波和噪声特征
使用与谐波检测系统完全一致的方法和参数
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import glob

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_for_segment_with_details(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声 - 与原始方法完全一致，但返回详细数据用于可视化
    """
    
    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features,
        # 额外返回用于可视化的数据
        'local_noise_levels': smoothed_noise,
        'window_centers': window_centers,
        'raw_noise_levels': local_noise_levels,
        'exclude_mask': exclude_mask
    }

def analyze_single_segment_detailed(audio_path, seg_idx, start_time, end_time, expected_freq, y, sr):
    """
    详细分析单个频段 - 使用与谐波检测系统完全相同的方法
    """
    
    try:
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            return None
        
        # 标准化 - 与谐波检测系统一致
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与谐波检测系统一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数 - 与谐波检测系统一致
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT - 与谐波检测系统一致
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率 - 与谐波检测系统一致
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz - 与谐波检测系统一致
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 找主频 - 与谐波检测系统一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
            fundamental_power = display_power[actual_idx]
            fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(display_power) + 1e-12)
        
        # 动态噪声分析
        noise_analysis = estimate_dynamic_noise_for_segment_with_details(display_freqs, display_power, fundamental_freq)
        
        if noise_analysis:
            # 计算信噪比
            snr_db = fundamental_power_db - noise_analysis['global_noise_floor_db']
        else:
            snr_db = 0
        
        # 谐波检测
        harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'display_freqs': display_freqs,
            'display_power': display_power,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'harmonic_count': len(harmonic_analysis) if harmonic_analysis else 0,
            'snr_db': snr_db
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_segment_visualization(segment_results, filename, output_dir):
    """创建前4段的详细可视化"""
    
    if not segment_results:
        print("❌ 无有效分析结果")
        return None
    
    # 创建2x2子图布局
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    
    # 提取文件名（不含路径和扩展名）
    base_filename = os.path.splitext(os.path.basename(filename))[0]
    
    fig.suptitle(f'{base_filename}\n前4段谐波和噪声特征分析', 
                 fontsize=16, fontweight='bold')
    
    for i, result in enumerate(segment_results):
        if i >= 4:  # 只显示前4段
            break
        
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 提取数据
        freqs = result['display_freqs']
        power = result['display_power']
        power_db = 10 * np.log10(power + 1e-12)
        
        fundamental_freq = result['fundamental_freq']
        noise_analysis = result['noise_analysis']
        harmonic_analysis = result['harmonic_analysis']
        
        # 绘制频谱
        ax.plot(freqs, power_db, 'b-', linewidth=1, alpha=0.7, label='频谱')
        
        # 标记主频
        ax.axvline(fundamental_freq, color='red', linestyle='-', linewidth=2, 
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')
        
        # 绘制噪声特征
        if noise_analysis:
            noise_floor = noise_analysis['global_noise_floor_db']
            ax.axhline(noise_floor, color='green', linestyle='--', linewidth=2, 
                      alpha=0.8, label=f'全局噪声底噪: {noise_floor:.1f}dB')
            
            # 绘制动态噪声阈值线
            if 'local_noise_levels' in noise_analysis and 'window_centers' in noise_analysis:
                local_noise = noise_analysis['local_noise_levels']
                window_centers = noise_analysis['window_centers']
                
                ax.plot(window_centers, local_noise, color='cyan', linestyle='-', 
                       linewidth=2, alpha=0.8, label='动态噪声阈值')
                
                ax.fill_between(window_centers, local_noise, noise_floor, 
                               alpha=0.2, color='cyan', label='噪声变化区域')
            
            # 标记排除的谐波区域
            if 'exclude_mask' in noise_analysis:
                exclude_mask = noise_analysis['exclude_mask']
                excluded_freqs = freqs[exclude_mask]
                excluded_powers = power_db[exclude_mask]
                
                freq_range_mask = (excluded_freqs >= 0) & (excluded_freqs <= min(5000, freqs[-1]))
                if np.any(freq_range_mask):
                    display_excluded_freqs = excluded_freqs[freq_range_mask]
                    display_excluded_powers = excluded_powers[freq_range_mask]
                    
                    ax.scatter(display_excluded_freqs, display_excluded_powers, 
                              c='gray', s=1, alpha=0.5, label='排除区域(谐波)')
            
            # 计算自适应检测阈值
            noise_variation_db = noise_analysis['noise_variation_db']
            stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
            
            if noise_variation_db > 10:
                base_snr_threshold = 8.0
            elif noise_variation_db > 5:
                base_snr_threshold = 10.0
            else:
                base_snr_threshold = 12.0
            
            if stability_score > 0.8:
                stability_adjustment = 1.0
            elif stability_score > 0.6:
                stability_adjustment = 0.0
            else:
                stability_adjustment = -1.0
            
            adjusted_snr_threshold = base_snr_threshold + stability_adjustment
            detection_threshold = noise_floor + adjusted_snr_threshold
            
            ax.axhline(detection_threshold, color='orange', linestyle='--', linewidth=1.5, 
                      alpha=0.8, label=f'谐波检测阈值: {detection_threshold:.1f}dB')
        
        # 标记检测到的谐波
        if harmonic_analysis:
            harmonic_freqs = [h['freq'] for h in harmonic_analysis]
            harmonic_powers = [h['power_db'] for h in harmonic_analysis]
            harmonic_orders = [h['order'] for h in harmonic_analysis]
            
            ax.scatter(harmonic_freqs, harmonic_powers, c='red', s=50, alpha=0.8, 
                      marker='^', label=f'谐波({len(harmonic_analysis)}个)')
            
            # 标注前几个谐波的次数
            for j, (freq, power, order) in enumerate(zip(harmonic_freqs[:5], harmonic_powers[:5], harmonic_orders[:5])):
                ax.annotate(f'{order}', (freq, power), xytext=(5, 5), 
                           textcoords='offset points', fontsize=8, color='red')
        
        # 设置坐标轴
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_title(f'段{result["seg_idx"]} - {result["expected_freq"]:.0f}Hz\n'
                    f'SNR: {result["snr_db"]:.1f}dB, 谐波数: {result["harmonic_count"]}个')
        
        # 设置频率范围 (统一显示100-20000Hz)
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')  # 使用对数坐标更好地显示宽频范围

        # 设置功率范围 (统一显示-80到40dB)
        ax.set_ylim(-80, 40)
        
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)
    
    plt.tight_layout()
    
    # 保存图片
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_path = os.path.join(output_dir, f"{safe_filename}_first_4_segments.png")
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def analyze_file(audio_path, output_dir):
    """分析单个音频文件的前4段"""

    filename = os.path.basename(audio_path)
    print(f"📁 分析文件: {filename}")

    try:
        # 获取频段分割 - 与谐波检测系统一致
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        # 加载音频 - 与谐波检测系统一致
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        # 分析前4段
        segment_results = []

        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            result = analyze_single_segment_detailed(
                audio_path, seg_idx, start_time, end_time, expected_freq, y, sr
            )

            if result:
                segment_results.append(result)

        if segment_results:
            # 生成可视化
            viz_path = create_segment_visualization(segment_results, filename, output_dir)

            # 打印汇总信息
            print(f"  ✅ 成功分析{len(segment_results)}段")
            print(f"  📊 前4段汇总:")
            for result in segment_results:
                noise_floor = result['noise_analysis']['global_noise_floor_db'] if result['noise_analysis'] else 0
                print(f"    段{result['seg_idx']}: {result['expected_freq']:.0f}Hz, "
                      f"SNR:{result['snr_db']:.1f}dB, 谐波:{result['harmonic_count']}个, "
                      f"噪声:{noise_floor:.1f}dB")

            return viz_path
        else:
            print(f"  ❌ 分析失败")
            return None

    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def main():
    """主函数 - 分析test20250717目录下所有wav文件"""

    print("🎯 分析test20250717目录下所有wav文件的前4段")
    print("="*60)
    print("使用与谐波检测系统完全一致的方法和参数")
    print()

    # 创建输出目录
    output_dir = "first_4_segments_analysis"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 查找所有wav文件
    test_dir = "test20250717"
    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return

    # 递归查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))

    if not wav_files:
        print(f"❌ 在{test_dir}目录下未找到wav文件")
        return

    print(f"🔍 找到{len(wav_files)}个wav文件:")
    for i, wav_file in enumerate(wav_files, 1):
        rel_path = os.path.relpath(wav_file, test_dir)
        print(f"  {i:2d}. {rel_path}")
    print()

    # 分析所有文件
    successful_count = 0
    failed_count = 0

    for i, wav_file in enumerate(wav_files, 1):
        print(f"[{i}/{len(wav_files)}] ", end="")

        viz_path = analyze_file(wav_file, output_dir)

        if viz_path:
            successful_count += 1
            print(f"  📊 可视化已保存: {os.path.basename(viz_path)}")
        else:
            failed_count += 1

        print()

    # 统计结果
    print("="*60)
    print(f"📊 分析完成统计:")
    print(f"  ✅ 成功: {successful_count}个文件")
    print(f"  ❌ 失败: {failed_count}个文件")
    print(f"  📁 输出目录: {output_dir}")
    print(f"  📊 生成图片: {successful_count}张")

if __name__ == "__main__":
    main()
