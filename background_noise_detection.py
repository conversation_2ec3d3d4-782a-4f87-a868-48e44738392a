#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门检测全频背景噪声的特征
基于频谱图观察到的明显噪声差异
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def extract_background_noise_features():
    """提取全频背景噪声特征"""
    print("🔍 提取全频背景噪声特征")
    print("="*70)
    print("基于频谱图观察：噪声样本在低频段有明显的全频背景噪声")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_results = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取背景噪声特征
                bg_features = extract_file_background_noise(audio_path, filename)
                
                if bg_features:
                    bg_features['filename'] = filename
                    bg_features['label'] = true_label
                    bg_features['is_target'] = filename in target_files
                    all_results.append(bg_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 保存结果
    df.to_csv('background_noise_features.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 背景噪声特征已保存: background_noise_features.csv")
    
    # 分析结果
    analyze_background_noise_separation(df, target_files)
    
    return df

def extract_file_background_noise(audio_path, filename):
    """提取单个文件的背景噪声特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 聚焦前10个频段 (低频段，100-200Hz左右)
        target_segments = list(range(min(10, len(step_boundaries))))
        
        bg_features = {}
        
        # 1. 全频背景噪声水平
        bg_features.update(extract_background_noise_level(y, sr, step_boundaries, target_segments, freq_table))
        
        # 2. 频谱密度特征
        bg_features.update(extract_spectral_density_features(y, sr, step_boundaries, target_segments))
        
        # 3. 噪声覆盖度特征
        bg_features.update(extract_noise_coverage_features(y, sr, step_boundaries, target_segments, freq_table))
        
        # 4. 背景噪声一致性特征
        bg_features.update(extract_noise_consistency_features(y, sr, step_boundaries, target_segments))
        
        return bg_features
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_background_noise_level(y, sr, step_boundaries, target_segments, freq_table):
    """提取背景噪声水平特征"""
    features = {}
    
    try:
        all_background_levels = []
        all_signal_to_background_ratios = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries) or seg_idx >= len(freq_table):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # STFT分析
            f, t, Zxx = stft(segment_audio, sr, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 找到期望频率的索引
            expected_freq_idx = np.argmin(np.abs(f - expected_freq))
            
            # 计算每个时间帧的背景噪声水平
            for frame_idx in range(power_spectrum.shape[1]):
                frame_power_db = power_db[:, frame_idx]
                
                # 信号功率 (期望频率附近)
                signal_band_start = max(0, expected_freq_idx - 5)
                signal_band_end = min(len(f), expected_freq_idx + 5)
                signal_power = np.max(frame_power_db[signal_band_start:signal_band_end])
                
                # 背景噪声功率 (排除信号频率)
                background_mask = np.ones(len(f), dtype=bool)
                background_mask[signal_band_start:signal_band_end] = False
                
                if np.any(background_mask):
                    background_power = np.mean(frame_power_db[background_mask])
                    background_power_median = np.median(frame_power_db[background_mask])
                    background_power_max = np.max(frame_power_db[background_mask])
                    
                    all_background_levels.append(background_power)
                    
                    # 信号与背景比
                    signal_to_background = signal_power - background_power
                    all_signal_to_background_ratios.append(signal_to_background)
        
        # 统计特征
        if all_background_levels:
            features['background_noise_mean'] = np.mean(all_background_levels)
            features['background_noise_std'] = np.std(all_background_levels)
            features['background_noise_max'] = np.max(all_background_levels)
            features['background_noise_min'] = np.min(all_background_levels)
            features['background_noise_median'] = np.median(all_background_levels)
            features['background_noise_p90'] = np.percentile(all_background_levels, 90)
            features['background_noise_p95'] = np.percentile(all_background_levels, 95)
        else:
            for key in ['background_noise_mean', 'background_noise_std', 'background_noise_max', 
                       'background_noise_min', 'background_noise_median', 'background_noise_p90', 'background_noise_p95']:
                features[key] = 0
        
        if all_signal_to_background_ratios:
            features['signal_to_background_mean'] = np.mean(all_signal_to_background_ratios)
            features['signal_to_background_std'] = np.std(all_signal_to_background_ratios)
            features['signal_to_background_min'] = np.min(all_signal_to_background_ratios)
        else:
            features['signal_to_background_mean'] = 0
            features['signal_to_background_std'] = 0
            features['signal_to_background_min'] = 0
            
    except Exception as e:
        # 设置默认值
        default_keys = ['background_noise_mean', 'background_noise_std', 'background_noise_max', 
                       'background_noise_min', 'background_noise_median', 'background_noise_p90', 'background_noise_p95',
                       'signal_to_background_mean', 'signal_to_background_std', 'signal_to_background_min']
        for key in default_keys:
            features[key] = 0
    
    return features

def extract_spectral_density_features(y, sr, step_boundaries, target_segments):
    """提取频谱密度特征"""
    features = {}
    
    try:
        all_spectral_densities = []
        all_spectral_entropies = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 使用Welch方法计算功率谱密度
            freqs, psd = welch(segment_audio, sr, nperseg=1024)
            
            # 聚焦低频段 (100-1000Hz)
            low_freq_mask = (freqs >= 100) & (freqs <= 1000)
            if np.any(low_freq_mask):
                low_freq_psd = psd[low_freq_mask]
                
                # 频谱密度统计
                spectral_density_mean = np.mean(low_freq_psd)
                spectral_density_std = np.std(low_freq_psd)
                spectral_density_max = np.max(low_freq_psd)
                
                all_spectral_densities.extend([spectral_density_mean, spectral_density_std, spectral_density_max])
                
                # 频谱熵 (衡量频谱分布的均匀性)
                psd_normalized = low_freq_psd / (np.sum(low_freq_psd) + 1e-12)
                psd_normalized = psd_normalized + 1e-12
                spectral_entropy = -np.sum(psd_normalized * np.log2(psd_normalized))
                all_spectral_entropies.append(spectral_entropy)
        
        # 统计特征
        if all_spectral_densities:
            features['spectral_density_mean'] = np.mean(all_spectral_densities)
            features['spectral_density_std'] = np.std(all_spectral_densities)
            features['spectral_density_max'] = np.max(all_spectral_densities)
        else:
            features['spectral_density_mean'] = 0
            features['spectral_density_std'] = 0
            features['spectral_density_max'] = 0
        
        if all_spectral_entropies:
            features['spectral_entropy_mean'] = np.mean(all_spectral_entropies)
            features['spectral_entropy_std'] = np.std(all_spectral_entropies)
            features['spectral_entropy_max'] = np.max(all_spectral_entropies)
        else:
            features['spectral_entropy_mean'] = 0
            features['spectral_entropy_std'] = 0
            features['spectral_entropy_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['spectral_density_mean', 'spectral_density_std', 'spectral_density_max',
                   'spectral_entropy_mean', 'spectral_entropy_std', 'spectral_entropy_max']:
            features[key] = 0
    
    return features

def extract_noise_coverage_features(y, sr, step_boundaries, target_segments, freq_table):
    """提取噪声覆盖度特征"""
    features = {}
    
    try:
        all_noise_coverage_ratios = []
        all_noise_bandwidth_ratios = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries) or seg_idx >= len(freq_table):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # STFT分析
            f, t, Zxx = stft(segment_audio, sr, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx) ** 2
            
            # 计算每个时间帧的噪声覆盖度
            for frame_idx in range(power_spectrum.shape[1]):
                frame_power = power_spectrum[:, frame_idx]
                
                # 找到期望频率的峰值
                expected_freq_idx = np.argmin(np.abs(f - expected_freq))
                signal_power = frame_power[expected_freq_idx]
                
                # 噪声阈值 (信号功率的一定比例)
                noise_threshold = signal_power * 0.1  # 10%的信号功率作为噪声阈值
                
                # 噪声覆盖度 = 超过噪声阈值的频率点数 / 总频率点数
                noise_mask = frame_power > noise_threshold
                noise_coverage_ratio = np.sum(noise_mask) / len(frame_power)
                all_noise_coverage_ratios.append(noise_coverage_ratio)
                
                # 噪声带宽比例
                if np.any(noise_mask):
                    noise_freqs = f[noise_mask]
                    noise_bandwidth = np.max(noise_freqs) - np.min(noise_freqs)
                    total_bandwidth = np.max(f) - np.min(f)
                    noise_bandwidth_ratio = noise_bandwidth / total_bandwidth
                    all_noise_bandwidth_ratios.append(noise_bandwidth_ratio)
        
        # 统计特征
        if all_noise_coverage_ratios:
            features['noise_coverage_mean'] = np.mean(all_noise_coverage_ratios)
            features['noise_coverage_std'] = np.std(all_noise_coverage_ratios)
            features['noise_coverage_max'] = np.max(all_noise_coverage_ratios)
            features['noise_coverage_p95'] = np.percentile(all_noise_coverage_ratios, 95)
        else:
            features['noise_coverage_mean'] = 0
            features['noise_coverage_std'] = 0
            features['noise_coverage_max'] = 0
            features['noise_coverage_p95'] = 0
        
        if all_noise_bandwidth_ratios:
            features['noise_bandwidth_ratio_mean'] = np.mean(all_noise_bandwidth_ratios)
            features['noise_bandwidth_ratio_std'] = np.std(all_noise_bandwidth_ratios)
            features['noise_bandwidth_ratio_max'] = np.max(all_noise_bandwidth_ratios)
        else:
            features['noise_bandwidth_ratio_mean'] = 0
            features['noise_bandwidth_ratio_std'] = 0
            features['noise_bandwidth_ratio_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['noise_coverage_mean', 'noise_coverage_std', 'noise_coverage_max', 'noise_coverage_p95',
                   'noise_bandwidth_ratio_mean', 'noise_bandwidth_ratio_std', 'noise_bandwidth_ratio_max']:
            features[key] = 0
    
    return features

def extract_noise_consistency_features(y, sr, step_boundaries, target_segments):
    """提取背景噪声一致性特征"""
    features = {}
    
    try:
        segment_noise_levels = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 计算该频段的平均噪声水平
            segment_rms = np.sqrt(np.mean(segment_audio**2))
            segment_noise_levels.append(segment_rms)
        
        # 噪声一致性特征
        if len(segment_noise_levels) > 1:
            # 噪声水平的变异系数
            noise_mean = np.mean(segment_noise_levels)
            noise_std = np.std(segment_noise_levels)
            features['noise_consistency_cv'] = noise_std / (noise_mean + 1e-12)
            
            # 噪声水平的范围
            features['noise_level_range'] = np.max(segment_noise_levels) - np.min(segment_noise_levels)
            
            # 噪声水平的相对标准差
            features['noise_level_rel_std'] = noise_std / (noise_mean + 1e-12)
        else:
            features['noise_consistency_cv'] = 0
            features['noise_level_range'] = 0
            features['noise_level_rel_std'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['noise_consistency_cv', 'noise_level_range', 'noise_level_rel_std']:
            features[key] = 0
    
    return features

def analyze_background_noise_separation(df, target_files):
    """分析背景噪声特征的分离能力"""
    print(f"\n🔍 分析背景噪声特征的分离能力")
    print("="*70)
    
    # 获取特征列
    feature_cols = [col for col in df.columns if col not in ['filename', 'label', 'is_target']]
    print(f"📊 背景噪声特征数: {len(feature_cols)}")
    
    # 分析目标样本与其他样本的分离
    target_data = df[df['is_target'] == True]
    other_data = df[df['is_target'] == False]
    
    print(f"📊 目标样本: {len(target_data)}个")
    print(f"📊 其他样本: {len(other_data)}个")
    
    separable_features = []
    
    for feature in feature_cols:
        target_values = target_data[feature].dropna()
        other_values = other_data[feature].dropna()
        
        if len(target_values) == 0 or len(other_values) == 0:
            continue
        
        target_min, target_max = np.min(target_values), np.max(target_values)
        other_min, other_max = np.min(other_values), np.max(other_values)
        
        # 检查完全分离
        if target_max < other_min:
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
        elif target_min > other_max:
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
    
    # 排序并显示结果
    separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
    
    print(f"\n✅ 找到 {len(separable_features)} 个可完全分离的背景噪声特征:")
    
    for i, feature_info in enumerate(separable_features[:10]):
        print(f"  {i+1:2d}. {feature_info['feature']}")
        print(f"      分离类型: {feature_info['separation_type']}")
        print(f"      分离间隙: {feature_info['separation_gap']:.6f}")
        print(f"      目标范围: [{feature_info['target_range'][0]:.6f}, {feature_info['target_range'][1]:.6f}]")
        print(f"      其他范围: [{feature_info['other_range'][0]:.6f}, {feature_info['other_range'][1]:.6f}]")
        print(f"      目标均值: {feature_info['target_mean']:.6f}")
        print(f"      其他均值: {feature_info['other_mean']:.6f}")
    
    # 可视化最佳特征
    if len(separable_features) > 0:
        visualize_background_noise_separation(df, separable_features[:3], target_files)
    
    return separable_features

def visualize_background_noise_separation(df, top_features, target_files):
    """可视化背景噪声分离特征"""
    print(f"\n🎨 生成背景噪声分离可视化...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('全频背景噪声特征分离分析', fontsize=16, fontweight='bold')
    
    # 1. 最佳特征分布对比
    if len(top_features) > 0:
        ax1 = axes[0, 0]
        feature1 = top_features[0]['feature']
        
        target_data = df[df['is_target'] == True]
        other_data = df[df['is_target'] == False]
        
        target_values = target_data[feature1].dropna()
        other_values = other_data[feature1].dropna()
        
        if len(target_values) > 0 and len(other_values) > 0:
            ax1.hist(other_values, bins=30, alpha=0.6, color='blue', 
                    label=f'其他样本 (n={len(other_values)})', density=True)
            ax1.hist(target_values, bins=30, alpha=0.8, color='red', 
                    label=f'噪声样本 (n={len(target_values)})', density=True)
            
            # 标记分离区域
            target_min, target_max = np.min(target_values), np.max(target_values)
            other_min, other_max = np.min(other_values), np.max(other_values)
            
            ax1.axvspan(other_min, other_max, alpha=0.2, color='blue', label='其他样本范围')
            ax1.axvspan(target_min, target_max, alpha=0.2, color='red', label='噪声样本范围')
            
            ax1.set_title(f'最佳背景噪声特征: {feature1}')
            ax1.set_xlabel('特征值')
            ax1.set_ylabel('密度')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 显示分离信息
            if target_max < other_min:
                separation_gap = other_min - target_max
                ax1.text(0.5, 0.95, f'完全分离: 噪声样本在下方\n分离间隙: {separation_gap:.6f}', 
                        transform=ax1.transAxes, ha='center', va='top',
                        bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
            elif target_min > other_max:
                separation_gap = target_min - other_max
                ax1.text(0.5, 0.95, f'完全分离: 噪声样本在上方\n分离间隙: {separation_gap:.6f}', 
                        transform=ax1.transAxes, ha='center', va='top',
                        bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    # 2. 特征重要性
    ax2 = axes[0, 1]
    
    if len(top_features) >= 3:
        feature_names = [f['feature'] for f in top_features[:3]]
        gaps = [f['separation_gap'] for f in top_features[:3]]
        
        bars = ax2.bar(range(len(feature_names)), gaps, color=['red', 'orange', 'yellow'])
        ax2.set_title('背景噪声特征分离能力')
        ax2.set_xlabel('特征')
        ax2.set_ylabel('分离间隙')
        ax2.set_xticks(range(len(feature_names)))
        ax2.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, gap in zip(bars, gaps):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{gap:.4f}', ha='center', va='bottom', fontsize=8)
    
    # 3. 样本分类散点图
    ax3 = axes[1, 0]
    
    if len(top_features) >= 2:
        feature1 = top_features[0]['feature']
        feature2 = top_features[1]['feature']
        
        target_data = df[df['is_target'] == True]
        other_data = df[df['is_target'] == False]
        
        ax3.scatter(other_data[feature1], other_data[feature2], 
                   alpha=0.6, color='blue', label='其他样本', s=30)
        ax3.scatter(target_data[feature1], target_data[feature2], 
                   alpha=0.8, color='red', label='噪声样本', s=50, marker='s')
        
        ax3.set_title(f'二维特征分离: {feature1} vs {feature2}')
        ax3.set_xlabel(feature1[:20] + '...' if len(feature1) > 20 else feature1)
        ax3.set_ylabel(feature2[:20] + '...' if len(feature2) > 20 else feature2)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 4. 特征类型分布
    ax4 = axes[1, 1]
    
    feature_types = {
        'background_noise': 0,
        'spectral_density': 0,
        'noise_coverage': 0,
        'noise_consistency': 0,
        'signal_to_background': 0
    }
    
    for feature_info in top_features:
        feature_name = feature_info['feature']
        if 'background_noise' in feature_name:
            feature_types['background_noise'] += 1
        elif 'spectral_density' in feature_name or 'spectral_entropy' in feature_name:
            feature_types['spectral_density'] += 1
        elif 'noise_coverage' in feature_name or 'noise_bandwidth' in feature_name:
            feature_types['noise_coverage'] += 1
        elif 'noise_consistency' in feature_name or 'noise_level' in feature_name:
            feature_types['noise_consistency'] += 1
        elif 'signal_to_background' in feature_name:
            feature_types['signal_to_background'] += 1
    
    type_names = list(feature_types.keys())
    type_counts = list(feature_types.values())
    
    colors = ['red', 'orange', 'yellow', 'green', 'blue']
    wedges, texts, autotexts = ax4.pie(type_counts, labels=type_names, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax4.set_title('有效背景噪声特征类型分布')
    
    plt.tight_layout()
    plt.savefig('background_noise_separation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: background_noise_separation_analysis.png")

if __name__ == "__main__":
    df = extract_background_noise_features()
    print(f"\n✅ 全频背景噪声特征分析完成！")
