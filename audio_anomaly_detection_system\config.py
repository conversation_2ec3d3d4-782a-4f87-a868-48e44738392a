"""
音频异响检测系统配置文件
Configuration file for Audio Anomaly Detection System
"""

# 系统版本信息
VERSION = "1.0.0"
AUTHOR = "AI Assistant"
UPDATE_DATE = "2025-01-17"

# 默认参数配置
DEFAULT_CONFIG = {
    # 特征提取参数
    "feature_extraction": {
        "min_duration": 156,           # 最小持续时间(ms)
        "energy_threshold_db": -45,    # 能量阈值(dB)
        "start_freq": 100,             # 起始频率(Hz)
        "stop_freq": 20000,            # 结束频率(Hz)
        "frame_length": 2048,          # 帧长度
        "hop_length": 512,             # 跳跃长度
        "n_frames_per_segment": 5,     # 每段帧数
    },
    
    # 竖线干扰检测参数
    "vertical_detection": {
        "threshold_ratio": 3,          # 能量突增阈值倍数
        "min_affected_freqs": 10,      # 最小受影响频率数
        "harmonic_range": 7,           # 谐波检测范围
        "harmonic_bandwidth_hz": 100,  # 谐波带宽(Hz)
        "harmonic_bandwidth_ratio": 0.1, # 谐波带宽比例
    },
    
    # 分类器参数
    "classifier": {
        "test_size": 0.3,              # 测试集比例
        "random_state": 42,            # 随机种子
        "n_estimators": 100,           # 随机森林树数量
        "class_weight": "balanced",    # 类别权重
        "cv_folds": 5,                 # 交叉验证折数
    },
    
    # 检测阈值
    "detection_thresholds": {
        "normal_high": 0.7,            # 正常(高置信度)
        "normal_medium": 0.5,          # 正常(中置信度)
        "suspicious": 0.3,             # 可疑
        # < 0.3 为异常
    },
    
    # 文件路径
    "paths": {
        "model_file": "audio_anomaly_detector.pkl",
        "dataset_dir": "dataset",
        "pos_dir": "dataset/pos",
        "neg_dir": "dataset/neg",
        "output_dir": "output",
        "features_dir": "features",
    }
}

# 核心特征列表（基于重要性分析）
CORE_FEATURES = [
    'vertical_line_count',
    'vertical_interference_ratio', 
    'avg_affected_freq_count',
    'low_freq_energy_ratio',
    'low_freq_energy_variation',
    'thd',
    'thdn',
    'noise_power_ratio',
    'spectral_energy_mean',
    'spectral_energy_kurtosis'
]

# 频带定义
FREQUENCY_BANDS = [
    (20, 200),      # 低频段
    (200, 2000),    # 中低频段
    (2000, 8000),   # 中高频段
    (8000, 20000)   # 高频段
]

# 支持的音频格式
SUPPORTED_FORMATS = ['.wav']

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "[%(levelname)s] %(message)s",
    "enable_file_log": False,
    "log_file": "audio_detection.log"
}

def get_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()

def update_config(config_dict):
    """更新配置"""
    config = get_config()
    
    def deep_update(base_dict, update_dict):
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    deep_update(config, config_dict)
    return config

def get_feature_config():
    """获取特征提取配置"""
    return DEFAULT_CONFIG["feature_extraction"].copy()

def get_detection_config():
    """获取检测配置"""
    return DEFAULT_CONFIG["vertical_detection"].copy()

def get_classifier_config():
    """获取分类器配置"""
    return DEFAULT_CONFIG["classifier"].copy()

# 示例：自定义配置
CUSTOM_CONFIG_EXAMPLE = {
    "feature_extraction": {
        "min_duration": 153,           # 修改最小持续时间
        "energy_threshold_db": -40,    # 修改能量阈值
    },
    "vertical_detection": {
        "threshold_ratio": 4,          # 提高检测阈值
        "min_affected_freqs": 15,      # 提高最小频率数
    },
    "detection_thresholds": {
        "normal_high": 0.8,            # 提高正常阈值
        "suspicious": 0.4,             # 提高可疑阈值
    }
}

if __name__ == "__main__":
    # 配置测试
    print("默认配置:")
    import json
    print(json.dumps(DEFAULT_CONFIG, indent=2, ensure_ascii=False))
    
    print("\n核心特征:")
    for i, feature in enumerate(CORE_FEATURES, 1):
        print(f"{i:2d}. {feature}")
    
    print(f"\n系统版本: {VERSION}")
    print(f"更新日期: {UPDATE_DATE}")
