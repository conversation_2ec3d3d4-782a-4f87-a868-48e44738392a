#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化音频异常检测系统
结合竖线检测和最有效的非重复特征
"""

import os
import sys
import numpy as np
import pandas as pd
import librosa
from scipy.signal import stft, find_peaks
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

class OptimizedAudioDetectionSystem:
    def __init__(self):
        self.fs = 48000
        
        # 选择的最有效特征（避免功能重复）
        self.selected_features = {
            # 谐波失真特征 - 选择最全面的
            'harmonic': ['thd', 'harmonic_3_ratio'],
            
            # 时域特征 - 选择最有区分力的
            'time_domain': ['td_skewness', 'td_kurtosis'],
            
            # 频域特征 - 选择最核心的
            'frequency_domain': ['fd_total_energy', 'fd_expected_freq_ratio'],
            
            # 频谱特征 - 选择最有效的
            'spectral': ['spec_bandwidth_mean', 'spec_max'],
            
            # 统计特征 - 选择最稳定的
            'statistical': ['stat_p50', 'stat_iqr'],
            
            # 能量特征 - 选择最代表性的
            'energy': ['energy_max', 'energy_kurtosis']
        }
        
        # 所有选择的特征列表
        self.all_features = []
        for category in self.selected_features.values():
            self.all_features.extend(category)
        
        print(f"🔧 优化检测系统初始化")
        print(f"📊 选择特征: {len(self.all_features)}个")
        for category, features in self.selected_features.items():
            print(f"  {category}: {features}")
        
        # 加载预训练的阈值数据
        self.load_thresholds()
    
    def load_thresholds(self):
        """加载阈值数据"""
        try:
            # 尝试加载之前分析的阈值数据
            if os.path.exists('segment_feature_thresholds.csv'):
                self.threshold_df = pd.read_csv('segment_feature_thresholds.csv')
                print(f"✅ 加载阈值数据: {len(self.threshold_df)}个频段")
            else:
                print("⚠️ 未找到阈值数据，将使用默认阈值")
                self.threshold_df = None
        except Exception as e:
            print(f"❌ 加载阈值数据失败: {e}")
            self.threshold_df = None
    
    def extract_segment_features(self, audio_path, segment_idx, start_time, end_time, expected_freq):
        """提取单个频段的特征"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            # 提取频段音频
            start_sample = int(start_time * self.fs)
            end_sample = int(end_time * self.fs)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            features = {}
            
            # 1. 竖线检测特征
            features.update(self._extract_vertical_line_features(segment_audio))
            
            # 2. 谐波失真特征
            features.update(self._extract_harmonic_features(segment_audio, expected_freq))
            
            # 3. 时域特征
            features.update(self._extract_time_domain_features(segment_audio))
            
            # 4. 频域特征
            features.update(self._extract_frequency_domain_features(segment_audio, expected_freq))
            
            # 5. 频谱特征
            features.update(self._extract_spectral_features(segment_audio))
            
            # 6. 统计特征
            features.update(self._extract_statistical_features(segment_audio))
            
            # 7. 能量特征
            features.update(self._extract_energy_features(segment_audio))
            
            return features
            
        except Exception as e:
            print(f"提取频段{segment_idx}特征失败: {e}")
            return None
    
    def _extract_vertical_line_features(self, audio):
        """竖线检测特征（使用之前的算法）"""
        features = {}
        
        try:
            # STFT分析
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            
            # 频率范围筛选
            freq_mask = (f >= 100) & (f <= 20000)
            f_filtered = f[freq_mask]
            Zxx_filtered = Zxx[freq_mask, :]
            
            power_spectrum = np.abs(Zxx_filtered) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 总能量变化
            total_energy = np.sum(power_db, axis=0)
            energy_mean = np.mean(total_energy)
            energy_std = np.std(total_energy)
            
            # 峰值检测
            min_height = energy_mean + 0.5 * energy_std
            min_prominence = 0.3 * energy_std
            
            peaks, properties = find_peaks(total_energy, 
                                         height=min_height,
                                         distance=2,
                                         prominence=min_prominence)
            
            # 竖线检测
            vertical_lines = 0
            max_line_strength = 0
            
            for peak_idx in peaks:
                if peak_idx < len(t):
                    peak_spectrum = power_db[:, peak_idx]
                    threshold = np.percentile(peak_spectrum, 70)
                    high_energy_mask = peak_spectrum > threshold
                    high_energy_indices = np.where(high_energy_mask)[0]
                    
                    if len(high_energy_indices) > 0:
                        # 频率跨度
                        freq_span = f_filtered[high_energy_indices[-1]] - f_filtered[high_energy_indices[0]]
                        
                        # 频率比例
                        freq_ratio = len(high_energy_indices) / len(f_filtered)
                        
                        # 线强度
                        high_energy_power = peak_spectrum[high_energy_indices]
                        background_power = np.delete(peak_spectrum, high_energy_indices)
                        if len(background_power) > 0:
                            line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                        else:
                            line_strength = 1.0
                        
                        max_line_strength = max(max_line_strength, line_strength)
                        
                        # 竖线判定
                        if line_strength >= 2.0 and freq_span >= 1000 and freq_ratio >= 0.1:
                            vertical_lines += 1
            
            features['vl_count'] = vertical_lines
            features['vl_max_strength'] = max_line_strength
            features['vl_peak_count'] = len(peaks)
            features['vl_energy_cv'] = energy_std / (abs(energy_mean) + 1e-12)
            
        except Exception as e:
            features.update({
                'vl_count': 0,
                'vl_max_strength': 0,
                'vl_peak_count': 0,
                'vl_energy_cv': 0
            })
        
        return features
    
    def _extract_harmonic_features(self, audio, expected_freq):
        """谐波失真特征"""
        features = {}
        
        try:
            # FFT分析
            fft = np.fft.fft(audio)
            freqs = np.fft.fftfreq(len(audio), 1/self.fs)
            magnitude = np.abs(fft)
            
            # 只考虑正频率
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            # 基频能量
            fundamental_tolerance = 20
            fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
            if np.any(fundamental_mask):
                fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
            else:
                fundamental_energy = 0
            
            # 3次谐波能量
            harmonic_3_freq = expected_freq * 3
            if harmonic_3_freq < self.fs / 2:
                harmonic_3_mask = np.abs(positive_freqs - harmonic_3_freq) <= fundamental_tolerance
                if np.any(harmonic_3_mask):
                    harmonic_3_energy = np.max(positive_magnitude[harmonic_3_mask])**2
                else:
                    harmonic_3_energy = 0
            else:
                harmonic_3_energy = 0
            
            # 所有谐波能量
            harmonic_energies = []
            for harmonic in range(2, 6):
                harmonic_freq = expected_freq * harmonic
                if harmonic_freq < self.fs / 2:
                    harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                    if np.any(harmonic_mask):
                        harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                        harmonic_energies.append(harmonic_energy)
                    else:
                        harmonic_energies.append(0)
            
            # THD计算
            total_harmonic_energy = sum(harmonic_energies)
            if fundamental_energy > 0:
                features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
                features['harmonic_3_ratio'] = harmonic_3_energy / fundamental_energy
            else:
                features['thd'] = 0
                features['harmonic_3_ratio'] = 0
            
        except Exception as e:
            features.update({
                'thd': 0,
                'harmonic_3_ratio': 0
            })
        
        return features
    
    def _extract_time_domain_features(self, audio):
        """时域特征"""
        features = {}
        
        try:
            features['td_skewness'] = stats.skew(audio)
            features['td_kurtosis'] = stats.kurtosis(audio)
        except Exception as e:
            features.update({
                'td_skewness': 0,
                'td_kurtosis': 0
            })
        
        return features
    
    def _extract_frequency_domain_features(self, audio, expected_freq):
        """频域特征"""
        features = {}
        
        try:
            # FFT分析
            fft = np.fft.fft(audio)
            freqs = np.fft.fftfreq(len(audio), 1/self.fs)
            magnitude = np.abs(fft)
            
            # 只考虑正频率
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            features['fd_total_energy'] = np.sum(positive_magnitude**2)
            
            # 期望频率附近的能量比例
            freq_tolerance = 50
            freq_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
            if np.any(freq_mask):
                expected_freq_energy = np.sum(positive_magnitude[freq_mask]**2)
                features['fd_expected_freq_ratio'] = expected_freq_energy / features['fd_total_energy']
            else:
                features['fd_expected_freq_ratio'] = 0
            
        except Exception as e:
            features.update({
                'fd_total_energy': 0,
                'fd_expected_freq_ratio': 0
            })
        
        return features
    
    def _extract_spectral_features(self, audio):
        """频谱特征"""
        features = {}
        
        try:
            # STFT分析
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            magnitude = np.abs(Zxx)
            power = magnitude**2
            
            features['spec_max'] = np.max(magnitude)
            
            # 频谱带宽
            freq_weighted_sum = np.sum(f[:, np.newaxis] * power, axis=0)
            total_power = np.sum(power, axis=0)
            spectral_centroid = freq_weighted_sum / (total_power + 1e-12)
            
            freq_diff = f[:, np.newaxis] - spectral_centroid[np.newaxis, :]
            spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * power, axis=0) / (total_power + 1e-12))
            features['spec_bandwidth_mean'] = np.mean(spectral_bandwidth)
            
        except Exception as e:
            features.update({
                'spec_max': 0,
                'spec_bandwidth_mean': 0
            })
        
        return features
    
    def _extract_statistical_features(self, audio):
        """统计特征"""
        features = {}
        
        try:
            features['stat_p50'] = np.percentile(audio, 50)  # 中位数
            features['stat_iqr'] = np.percentile(audio, 75) - np.percentile(audio, 25)  # 四分位距
        except Exception as e:
            features.update({
                'stat_p50': 0,
                'stat_iqr': 0
            })
        
        return features
    
    def _extract_energy_features(self, audio):
        """能量特征"""
        features = {}
        
        try:
            # 短时能量
            frame_length = 1024
            hop_length = 512
            frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
            frame_energy = np.sum(frames**2, axis=0)
            
            features['energy_max'] = np.max(frame_energy)
            features['energy_kurtosis'] = stats.kurtosis(frame_energy) if len(frame_energy) > 0 else 0
            
        except Exception as e:
            features.update({
                'energy_max': 0,
                'energy_kurtosis': 0
            })
        
        return features
    
    def detect_audio_file(self, audio_path, debug=False):
        """检测单个音频文件"""
        try:
            if debug:
                print(f"🔍 检测文件: {os.path.basename(audio_path)}")
            
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            segment_results = []
            anomaly_segments = 0
            vertical_line_segments = 0
            total_segments = len(step_boundaries)
            
            # 检测每个频段
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                # 提取特征
                features = self.extract_segment_features(audio_path, seg_idx, start_time, end_time, expected_freq)
                
                if features is None:
                    continue
                
                # 1. 竖线检测
                vl_anomaly = features['vl_count'] > 0 or features['vl_max_strength'] >= 2.0
                if vl_anomaly:
                    vertical_line_segments += 1
                
                # 2. 其他特征检测
                other_anomaly = self._detect_other_anomalies(features, seg_idx)
                
                # 3. 综合判定
                is_segment_anomaly = vl_anomaly or other_anomaly
                
                if is_segment_anomaly:
                    anomaly_segments += 1
                
                segment_result = {
                    'segment_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'start_time': start_time,
                    'end_time': end_time,
                    'vl_anomaly': vl_anomaly,
                    'other_anomaly': other_anomaly,
                    'is_anomaly': is_segment_anomaly,
                    'features': features
                }
                
                segment_results.append(segment_result)
                
                if debug and is_segment_anomaly:
                    print(f"  频段{seg_idx:2d} ({expected_freq:6.1f}Hz): "
                          f"{'竖线' if vl_anomaly else ''}{'其他' if other_anomaly else ''}")
            
            # 文件级判定
            if total_segments > 0:
                file_anomaly_ratio = anomaly_segments / total_segments
                vl_ratio = vertical_line_segments / total_segments

                # 判定逻辑（更敏感的阈值）：
                # 1. 如果有竖线异常且比例>2%，判定为异常
                # 2. 如果总异常比例>8%，判定为异常
                predicted_label = 'neg' if (vl_ratio >= 0.02 or file_anomaly_ratio >= 0.08) else 'pos'
            else:
                file_anomaly_ratio = 0
                vl_ratio = 0
                predicted_label = 'unknown'
            
            result = {
                'status': 'success',
                'predicted_label': predicted_label,
                'file_anomaly_ratio': file_anomaly_ratio,
                'vertical_line_ratio': vl_ratio,
                'anomaly_segments': anomaly_segments,
                'vertical_line_segments': vertical_line_segments,
                'total_segments': total_segments,
                'segment_results': segment_results
            }
            
            if debug:
                print(f"  结果: {predicted_label}")
                print(f"  异常频段: {anomaly_segments}/{total_segments} ({file_anomaly_ratio:.3f})")
                print(f"  竖线频段: {vertical_line_segments}/{total_segments} ({vl_ratio:.3f})")
            
            return result
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _detect_other_anomalies(self, features, segment_idx):
        """检测其他特征异常"""
        try:
            # 使用更敏感的阈值判定
            anomaly_votes = 0
            total_votes = 0

            # 谐波失真检测 - 降低阈值
            if features['thd'] > 0.02:  # THD > 2%
                anomaly_votes += 1
            total_votes += 1

            if features['harmonic_3_ratio'] > 0.001:  # 3次谐波比例 > 0.1%
                anomaly_votes += 1
            total_votes += 1

            # 时域特征检测 - 降低阈值
            if abs(features['td_skewness']) > 1.0:  # 偏度异常
                anomaly_votes += 1
            total_votes += 1

            if abs(features['td_kurtosis']) > 2.0:  # 峰度异常
                anomaly_votes += 1
            total_votes += 1

            # 频域特征检测 - 新增
            if features['fd_expected_freq_ratio'] < 0.5:  # 期望频率能量比例过低
                anomaly_votes += 1
            total_votes += 1

            # 频谱特征检测 - 降低阈值
            if features['spec_bandwidth_mean'] > 500:  # 频谱带宽过宽
                anomaly_votes += 1
            total_votes += 1

            # 统计特征检测 - 新增
            if abs(features['stat_p50']) > 0.1:  # 中位数偏离零点
                anomaly_votes += 1
            total_votes += 1

            if features['stat_iqr'] > 0.2:  # 四分位距过大
                anomaly_votes += 1
            total_votes += 1

            # 能量特征检测 - 降低阈值
            if abs(features['energy_kurtosis']) > 1.5:  # 能量分布异常
                anomaly_votes += 1
            total_votes += 1

            # 如果超过30%的特征异常，判定为异常（降低阈值）
            return anomaly_votes / total_votes >= 0.3

        except Exception as e:
            return False

def test_optimized_detection_system():
    """测试优化检测系统"""
    print("🔍 测试优化音频异常检测系统")
    print("="*70)
    
    detector = OptimizedAudioDetectionSystem()
    
    # 测试文件
    test_files = [
        r"../test20250717/pos/完美/ok1.wav",
        r"../test20250717/neg/喇叭eva没贴.wav",
        r"../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav"
    ]
    
    for audio_path in test_files:
        if os.path.exists(audio_path):
            print(f"\n📊 测试文件: {os.path.basename(audio_path)}")
            result = detector.detect_audio_file(audio_path, debug=True)
            print(f"状态: {result['status']}")
        else:
            print(f"❌ 文件不存在: {audio_path}")

def validate_optimized_system():
    """验证优化检测系统"""
    print("🔍 验证优化音频异常检测系统")
    print("="*70)

    detector = OptimizedAudioDetectionSystem()

    # 定义测试文件夹
    test_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }

    validation_results = []

    # 测试所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue

            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)

            print(f"\n📁 测试文件夹: {folder_name} ({true_label}) - {len(wav_files)}个文件")
            print("-" * 60)

            for i, audio_path in enumerate(wav_files):
                filename = os.path.basename(audio_path)

                print(f"  🎵 [{i+1}/{len(wav_files)}] {filename}")

                # 检测
                result = detector.detect_audio_file(audio_path, debug=False)

                if result['status'] == 'success':
                    predicted_label = result['predicted_label']
                    file_anomaly_ratio = result['file_anomaly_ratio']
                    vl_ratio = result['vertical_line_ratio']
                    anomaly_segments = result['anomaly_segments']
                    vl_segments = result['vertical_line_segments']
                    total_segments = result['total_segments']

                    # 判定正确性
                    is_correct = predicted_label == true_label
                    correctness_icon = "✅" if is_correct else "❌"

                    # 显示结果
                    print(f"     {correctness_icon} 预测: {predicted_label}, 实际: {true_label}")
                    print(f"        异常频段: {anomaly_segments}/{total_segments} ({file_anomaly_ratio:.3f})")
                    print(f"        竖线频段: {vl_segments}/{total_segments} ({vl_ratio:.3f})")

                    # 保存结果
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'is_correct': is_correct,
                        'file_anomaly_ratio': file_anomaly_ratio,
                        'vertical_line_ratio': vl_ratio,
                        'anomaly_segments': anomaly_segments,
                        'vertical_line_segments': vl_segments,
                        'total_segments': total_segments,
                        'status': 'success'
                    })

                else:
                    print(f"     ❌ 检测失败: {result['error']}")
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': 'failed',
                        'is_correct': False,
                        'file_anomaly_ratio': 0,
                        'vertical_line_ratio': 0,
                        'anomaly_segments': 0,
                        'vertical_line_segments': 0,
                        'total_segments': 0,
                        'status': 'failed',
                        'error': result['error']
                    })

    # 生成验证报告
    generate_validation_report(validation_results)

    return validation_results

def generate_validation_report(results):
    """生成验证报告"""
    print(f"\n" + "="*70)
    print(f"📊 优化检测系统验证报告")
    print("="*70)

    df = pd.DataFrame(results)

    # 总体统计
    total_files = len(df)
    success_files = len(df[df['status'] == 'success'])
    failed_files = total_files - success_files

    print(f"\n📊 总体统计:")
    print(f"  总文件数: {total_files}")
    print(f"  成功检测: {success_files} ({success_files/total_files*100:.1f}%)")
    print(f"  检测失败: {failed_files} ({failed_files/total_files*100:.1f}%)")

    # 准确率统计
    success_data = df[df['status'] == 'success']

    if len(success_data) > 0:
        correct_predictions = success_data[success_data['is_correct'] == True]
        accuracy = len(correct_predictions) / len(success_data)

        print(f"\n📊 准确率统计:")
        print(f"  成功样本数: {len(success_data)}")
        print(f"  正确预测: {len(correct_predictions)}")
        print(f"  总体准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")

        # 按类别统计
        for label in ['pos', 'neg']:
            label_samples = success_data[success_data['true_label'] == label]
            if len(label_samples) > 0:
                label_correct = label_samples[label_samples['is_correct'] == True]
                label_accuracy = len(label_correct) / len(label_samples)
                print(f"  {label}样本准确率: {label_accuracy:.3f} ({len(label_correct)}/{len(label_samples)})")

    # 按文件夹统计
    print(f"\n📊 按文件夹统计:")
    print("-" * 50)

    for folder in df['folder'].unique():
        folder_data = df[df['folder'] == folder]
        folder_success = folder_data[folder_data['status'] == 'success']

        if len(folder_success) > 0:
            true_label = folder_data['true_label'].iloc[0]
            folder_correct = folder_success[folder_success['is_correct'] == True]
            folder_accuracy = len(folder_correct) / len(folder_success)

            print(f"  {folder} ({true_label}): {len(folder_success)}/{len(folder_data)}个成功, "
                  f"准确率: {folder_accuracy:.3f}")

    # 异常检测统计
    print(f"\n📊 异常检测统计:")
    print("-" * 50)

    if len(success_data) > 0:
        print(f"  平均异常频段比例: {success_data['file_anomaly_ratio'].mean():.3f}")
        print(f"  平均竖线频段比例: {success_data['vertical_line_ratio'].mean():.3f}")

        # 按预测标签统计
        for pred_label in ['pos', 'neg']:
            pred_data = success_data[success_data['predicted_label'] == pred_label]
            if len(pred_data) > 0:
                avg_anomaly = pred_data['file_anomaly_ratio'].mean()
                avg_vl = pred_data['vertical_line_ratio'].mean()
                print(f"  预测为{pred_label}的平均异常比例: {avg_anomaly:.3f} (竖线: {avg_vl:.3f})")

    # 错误案例分析
    error_cases = success_data[success_data['is_correct'] == False]

    if len(error_cases) > 0:
        print(f"\n📊 错误案例分析:")
        print("-" * 50)

        for _, case in error_cases.iterrows():
            print(f"  ❌ {case['filename']}")
            print(f"     真实: {case['true_label']}, 预测: {case['predicted_label']}")
            print(f"     异常比例: {case['file_anomaly_ratio']:.3f}, 竖线比例: {case['vertical_line_ratio']:.3f}")

    # 保存验证结果
    df.to_csv('optimized_detection_validation.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 验证结果已保存: optimized_detection_validation.csv")

if __name__ == "__main__":
    validate_optimized_system()
