#!/usr/bin/env python3
"""
调试freq_split失败的原因
Debug freq_split failures
"""

import numpy as np
import librosa
import pandas as pd
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
    from chirp import gen_freq_step
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

def debug_freq_split_failure(audio_path):
    """调试freq_split失败的原因"""
    print(f"\n调试文件: {os.path.basename(audio_path)}")
    print("="*60)
    
    try:
        # 检查文件是否存在
        if not os.path.exists(audio_path):
            print(f"❌ 文件不存在: {audio_path}")
            return False
        
        # 加载音频基本信息
        y, sr = librosa.load(audio_path, sr=None)
        print(f"✅ 音频加载成功:")
        print(f"   采样率: {sr} Hz")
        print(f"   时长: {len(y)/sr:.2f} 秒")
        print(f"   样本数: {len(y)}")
        print(f"   最大幅度: {np.max(np.abs(y)):.4f}")
        
        # 检查是否为扫频文件
        filename = os.path.basename(audio_path).lower()
        is_sweep_file = any(keyword in filename for keyword in ['扫频', 'sweep', 'chirp', '100hz', '20000hz'])
        print(f"   扫频文件判断: {'是' if is_sweep_file else '否'}")
        
        if not is_sweep_file:
            print(f"⚠️  非扫频文件，freq_split不适用")
            return False
        
        # 尝试生成理论频点
        try:
            _, _, freq_ssample_dict = gen_freq_step(
                start_freq=100, stop_freq=20000, 
                min_cycles=10, min_duration=156, 
                octave=12, fs=48000, A=1
            )
            freq_table = list(freq_ssample_dict.keys())
            print(f"✅ 理论频点生成成功: {len(freq_table)}个频点")
            print(f"   频率范围: {freq_table[0]:.1f} - {freq_table[-1]:.1f} Hz")
        except Exception as e:
            print(f"❌ 理论频点生成失败: {e}")
            return False
        
        # 检查能量阈值
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms_db = librosa.amplitude_to_db(rms, ref=np.max)
        frame_times = librosa.frames_to_time(np.arange(len(rms_db)), sr=sr, hop_length=512)
        
        print(f"✅ 能量分析:")
        print(f"   RMS范围: {np.min(rms_db):.1f} - {np.max(rms_db):.1f} dB")
        print(f"   平均RMS: {np.mean(rms_db):.1f} dB")
        
        # 测试不同的能量阈值
        energy_thresholds = [-30, -35, -40, -45, -50]
        min_start_time = 0.2
        
        for threshold in energy_thresholds:
            search_idx = np.where(frame_times >= min_start_time)[0]
            if len(search_idx) == 0:
                valid_idx = 0
            else:
                above_thr = np.where(rms_db[search_idx] > threshold)[0]
                if len(above_thr) == 0:
                    valid_idx = search_idx[0]
                else:
                    valid_idx = search_idx[above_thr[0]]
            start_offset = frame_times[valid_idx]
            print(f"   阈值 {threshold}dB: 起点 {start_offset:.3f}s")
        
        # 尝试freq_split
        try:
            step_boundaries, freq_table_result = split_freq_steps(
                audio_path,
                start_freq=100, stop_freq=20000, octave=12, fs=48000,
                min_cycles=10, min_duration=156,
                energy_threshold_db=-45, min_start_time=0.2,
                plot=False
            )
            
            if len(step_boundaries) > 0:
                print(f"✅ freq_split成功: {len(step_boundaries)}个频段")
                print(f"   时间范围: {step_boundaries[0][0]:.3f} - {step_boundaries[-1][1]:.3f}s")
                return True
            else:
                print(f"❌ freq_split返回空结果")
                return False
                
        except Exception as e:
            print(f"❌ freq_split执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_files():
    """测试所有文件的freq_split"""
    print("="*80)
    print("调试所有文件的freq_split状态")
    print("="*80)
    
    test_files = [
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav",
        "../test20250717/neg/主板隔音eva取消.wav",
        "../test20250717/neg/主板隔音eva取消_1.wav",
        "../test20250717/neg/喇叭eva没贴.wav",
        "../test20250717/neg/喇叭eva没贴_1.wav"
    ]
    
    results = []
    
    for file_path in test_files:
        success = debug_freq_split_failure(file_path)
        results.append({
            'filename': os.path.basename(file_path),
            'freq_split_success': success,
            'file_exists': os.path.exists(file_path)
        })
    
    # 总结结果
    print("\n" + "="*60)
    print("freq_split调试总结:")
    print("="*60)
    
    for result in results:
        status = "✅ 成功" if result['freq_split_success'] else "❌ 失败"
        exists = "存在" if result['file_exists'] else "不存在"
        print(f"{result['filename']}: {status} (文件{exists})")
    
    # 分析失败原因
    failed_files = [r for r in results if not r['freq_split_success'] and r['file_exists']]
    success_files = [r for r in results if r['freq_split_success']]
    
    print(f"\n统计:")
    print(f"  成功: {len(success_files)}个")
    print(f"  失败: {len(failed_files)}个")
    
    if failed_files:
        print(f"\n失败文件分析:")
        for result in failed_files:
            filename = result['filename']
            if 'eva' in filename.lower():
                print(f"  {filename}: 非扫频文件，freq_split不适用")
            else:
                print(f"  {filename}: 扫频文件但freq_split失败，需要进一步调试")
    
    return results

def test_freq_split_with_different_params():
    """测试不同参数下的freq_split"""
    print("\n" + "="*60)
    print("测试不同参数下的freq_split")
    print("="*60)
    
    # 选择一个扫频文件进行测试
    test_file = "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    # 测试不同的参数组合
    param_combinations = [
        {'energy_threshold_db': -30, 'min_start_time': 0.2},
        {'energy_threshold_db': -35, 'min_start_time': 0.2},
        {'energy_threshold_db': -40, 'min_start_time': 0.2},
        {'energy_threshold_db': -45, 'min_start_time': 0.2},
        {'energy_threshold_db': -50, 'min_start_time': 0.2},
        {'energy_threshold_db': -45, 'min_start_time': 0.1},
        {'energy_threshold_db': -45, 'min_start_time': 0.0},
    ]
    
    print(f"测试文件: {os.path.basename(test_file)}")
    
    for i, params in enumerate(param_combinations):
        print(f"\n参数组合 {i+1}: {params}")
        try:
            step_boundaries, freq_table = split_freq_steps(
                test_file,
                start_freq=100, stop_freq=20000, octave=12, fs=48000,
                min_cycles=10, min_duration=156,
                plot=False,
                **params
            )
            
            if len(step_boundaries) > 0:
                print(f"  ✅ 成功: {len(step_boundaries)}个频段")
                print(f"  时间范围: {step_boundaries[0][0]:.3f} - {step_boundaries[-1][1]:.3f}s")
            else:
                print(f"  ❌ 返回空结果")
                
        except Exception as e:
            print(f"  ❌ 执行失败: {e}")

def main():
    """主函数"""
    # 测试所有文件
    results = test_all_files()
    
    # 测试不同参数
    test_freq_split_with_different_params()
    
    return results

if __name__ == "__main__":
    results = main()
