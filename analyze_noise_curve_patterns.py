#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析动态噪声阈值曲线模式，分离特定文件
基于噪声曲线特征识别低音戳洞、153632、155101等文件
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import json

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_for_analysis(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声 - 返回详细分析数据
    """
    
    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    # 计算额外的曲线特征用于分类
    curve_features = analyze_noise_curve_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features,
        'local_noise_levels': smoothed_noise,
        'window_centers': window_centers,
        'raw_noise_levels': local_noise_levels,
        'exclude_mask': exclude_mask,
        'curve_features': curve_features
    }

def analyze_noise_curve_features(noise_levels, window_centers):
    """
    分析噪声曲线的特征用于分类
    """
    
    if len(noise_levels) < 5:
        return {}
    
    # 基本统计特征
    mean_noise = np.mean(noise_levels)
    std_noise = np.std(noise_levels)
    min_noise = np.min(noise_levels)
    max_noise = np.max(noise_levels)
    range_noise = max_noise - min_noise
    
    # 曲线形状特征
    # 1. 计算一阶差分（斜率变化）
    diff1 = np.diff(noise_levels)
    mean_slope = np.mean(diff1)
    std_slope = np.std(diff1)
    
    # 2. 计算二阶差分（曲率变化）
    if len(diff1) > 1:
        diff2 = np.diff(diff1)
        mean_curvature = np.mean(diff2)
        std_curvature = np.std(diff2)
    else:
        mean_curvature = 0
        std_curvature = 0
    
    # 3. 频域特征分析
    # 低频段(100-1000Hz)、中频段(1000-5000Hz)、高频段(5000Hz+)的噪声水平
    freq_array = np.array(window_centers)
    noise_array = np.array(noise_levels)
    
    low_freq_mask = freq_array <= 1000
    mid_freq_mask = (freq_array > 1000) & (freq_array <= 5000)
    high_freq_mask = freq_array > 5000
    
    low_freq_noise = np.mean(noise_array[low_freq_mask]) if np.any(low_freq_mask) else mean_noise
    mid_freq_noise = np.mean(noise_array[mid_freq_mask]) if np.any(mid_freq_mask) else mean_noise
    high_freq_noise = np.mean(noise_array[high_freq_mask]) if np.any(high_freq_mask) else mean_noise
    
    # 4. 趋势分析
    # 使用线性拟合分析整体趋势
    if len(window_centers) > 2:
        log_freqs = np.log10(np.array(window_centers))
        trend_coeff = np.polyfit(log_freqs, noise_levels, 1)[0]  # 斜率
    else:
        trend_coeff = 0
    
    # 5. 峰值和谷值分析
    # 寻找局部极值
    peaks = []
    valleys = []
    for i in range(1, len(noise_levels) - 1):
        if noise_levels[i] > noise_levels[i-1] and noise_levels[i] > noise_levels[i+1]:
            peaks.append((window_centers[i], noise_levels[i]))
        elif noise_levels[i] < noise_levels[i-1] and noise_levels[i] < noise_levels[i+1]:
            valleys.append((window_centers[i], noise_levels[i]))
    
    return {
        # 基本统计
        'mean_noise': mean_noise,
        'std_noise': std_noise,
        'min_noise': min_noise,
        'max_noise': max_noise,
        'range_noise': range_noise,
        
        # 曲线形状
        'mean_slope': mean_slope,
        'std_slope': std_slope,
        'mean_curvature': mean_curvature,
        'std_curvature': std_curvature,
        
        # 频域特征
        'low_freq_noise': low_freq_noise,
        'mid_freq_noise': mid_freq_noise,
        'high_freq_noise': high_freq_noise,
        'low_mid_diff': low_freq_noise - mid_freq_noise,
        'mid_high_diff': mid_freq_noise - high_freq_noise,
        
        # 趋势特征
        'trend_coefficient': trend_coeff,
        
        # 极值特征
        'num_peaks': len(peaks),
        'num_valleys': len(valleys),
        'peak_valley_ratio': len(peaks) / max(len(valleys), 1)
    }

def analyze_single_file_noise_pattern(audio_path):
    """
    分析单个文件的噪声模式
    """
    
    filename = os.path.basename(audio_path)
    print(f"📁 分析文件: {filename}")
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 分析前4段的噪声模式
        noise_patterns = []
        
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            # 提取段音频
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_analysis(display_freqs, display_power, fundamental_freq)
            
            if noise_analysis:
                noise_patterns.append({
                    'seg_idx': seg_idx,
                    'freq': expected_freq,
                    'noise_analysis': noise_analysis
                })
        
        return {
            'filename': filename,
            'noise_patterns': noise_patterns
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def classify_noise_pattern(noise_patterns, filename=""):
    """
    基于真正有区分度的特征进行二分类
    主要区分特征：曲率标准差、斜率标准差、峰谷值数量
    """

    if not noise_patterns:
        return "unknown"

    # 提取所有段的特征
    all_features = []
    for pattern in noise_patterns:
        if 'curve_features' in pattern['noise_analysis']:
            all_features.append(pattern['noise_analysis']['curve_features'])

    if not all_features:
        return "unknown"

    # 计算平均特征
    avg_features = {}
    for key in all_features[0].keys():
        values = [f[key] for f in all_features if key in f]
        if values:
            avg_features[key] = np.mean(values)

    # 提取关键区分特征
    std_curvature = avg_features.get('std_curvature', 0)      # 区分度: 6.991
    std_slope = avg_features.get('std_slope', 0)              # 区分度: 4.764
    num_peaks = avg_features.get('num_peaks', 0)              # 区分度: 2.009
    num_valleys = avg_features.get('num_valleys', 0)          # 区分度: 2.062

    # 基于统计分析的阈值
    # 目标异常文件: std_curvature=0.462±0.039, std_slope=0.747±0.086, num_peaks=111.6±5.7, num_valleys=111.0±5.3
    # 正常文件: std_curvature=0.111±0.012, std_slope=0.253±0.018, num_peaks=92.2±3.9, num_valleys=91.9±4.0

    abnormal_score = 0

    # 规则1: 曲率标准差 (最强区分特征)
    if std_curvature > 0.3:  # 阈值设在两组均值之间
        abnormal_score += 4  # 最高权重
    elif std_curvature > 0.2:
        abnormal_score += 2

    # 规则2: 斜率标准差 (第二强区分特征)
    if std_slope > 0.5:  # 阈值设在两组均值之间
        abnormal_score += 3
    elif std_slope > 0.35:
        abnormal_score += 1

    # 规则3: 峰值数量
    if num_peaks > 105:  # 阈值设在两组均值之间
        abnormal_score += 2
    elif num_peaks > 100:
        abnormal_score += 1

    # 规则4: 谷值数量
    if num_valleys > 105:  # 阈值设在两组均值之间
        abnormal_score += 2
    elif num_valleys > 100:
        abnormal_score += 1

    # 分类决策
    # 基于真实特征区分度设置阈值
    is_abnormal = abnormal_score >= 6  # 需要多个强特征同时满足

    # 确定具体的异常类型（基于特征模式）
    specific_type = "normal"
    if is_abnormal:
        # 基于曲率和斜率特征的细分
        if std_curvature > 0.45 and std_slope > 0.8:
            specific_type = "high_complexity_pattern"  # 高复杂度模式
        elif std_curvature > 0.35 and std_slope > 0.6:
            specific_type = "medium_complexity_pattern"  # 中等复杂度模式
        else:
            specific_type = "low_complexity_abnormal"  # 低复杂度异常

    return {
        'classification': 'abnormal' if is_abnormal else 'normal',
        'specific_type': specific_type,
        'abnormal_score': abnormal_score,
        'features': {
            'std_curvature': std_curvature,
            'std_slope': std_slope,
            'num_peaks': num_peaks,
            'num_valleys': num_valleys
        },
        'feature_checks': {
            'high_curvature_std': std_curvature > 0.3,
            'high_slope_std': std_slope > 0.5,
            'many_peaks': num_peaks > 105,
            'many_valleys': num_valleys > 105
        }
    }

def create_noise_pattern_comparison(target_files_data, output_dir):
    """
    创建目标文件的噪声模式对比图
    """

    if not target_files_data:
        print("❌ 无目标文件数据")
        return

    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('目标文件动态噪声阈值曲线对比分析', fontsize=16, fontweight='bold')

    colors = ['red', 'blue', 'green', 'orange']

    for i, (filename, data) in enumerate(target_files_data.items()):
        if i >= 4:  # 最多显示4个文件
            break

        row = i // 2
        col = i % 2
        ax = axes[row, col]

        # 绘制前4段的噪声曲线
        for j, pattern in enumerate(data['noise_patterns'][:4]):
            noise_analysis = pattern['noise_analysis']

            if 'local_noise_levels' in noise_analysis and 'window_centers' in noise_analysis:
                window_centers = noise_analysis['window_centers']
                local_noise = noise_analysis['local_noise_levels']

                ax.plot(window_centers, local_noise,
                       color=colors[j % len(colors)], linewidth=2, alpha=0.8,
                       label=f'段{pattern["seg_idx"]} ({pattern["freq"]:.0f}Hz)')

                # 标记全局噪声底噪
                noise_floor = noise_analysis['global_noise_floor_db']
                ax.axhline(noise_floor, color=colors[j % len(colors)],
                          linestyle='--', alpha=0.5)

        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('噪声水平 (dB)')
        ax.set_title(f'{filename}\n分类: {data.get("classification", {}).get("classification", "unknown")}')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, -20)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

    plt.tight_layout()

    # 保存对比图
    output_path = os.path.join(output_dir, "target_files_noise_pattern_comparison.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def main():
    """
    主函数 - 分析目标文件的噪声模式
    """

    print("🎯 分析目标文件的动态噪声阈值曲线模式")
    print("="*60)

    # 目标文件列表
    target_patterns = [
        "低音戳洞",
        "153632",
        "155101"
    ]

    # 查找目标文件
    target_files = []
    test_dir = "test20250717"

    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return

    # 递归查找所有wav文件
    all_wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                all_wav_files.append(os.path.join(root, file))

    # 筛选目标文件
    for pattern in target_patterns:
        for wav_file in all_wav_files:
            if pattern in os.path.basename(wav_file):
                target_files.append(wav_file)
                break

    print(f"🔍 找到{len(target_files)}个目标文件:")
    for i, target_file in enumerate(target_files, 1):
        rel_path = os.path.relpath(target_file, test_dir)
        print(f"  {i}. {rel_path}")
    print()

    # 创建输出目录
    output_dir = "noise_pattern_analysis"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 分析每个目标文件
    target_files_data = {}

    for i, target_file in enumerate(target_files, 1):
        print(f"[{i}/{len(target_files)}] ", end="")

        result = analyze_single_file_noise_pattern(target_file)

        if result:
            # 进行分类
            classification = classify_noise_pattern(result['noise_patterns'], result['filename'])
            result['classification'] = classification

            target_files_data[result['filename']] = result

            print(f"  ✅ 分析完成")
            print(f"  📊 分类结果: {classification.get('classification', 'unknown')}")
            if 'features' in classification:
                features = classification['features']
                print(f"  📈 关键特征: 曲率std={features['std_curvature']:.3f}, "
                      f"斜率std={features['std_slope']:.3f}, 峰值={features['num_peaks']:.0f}")
        else:
            print(f"  ❌ 分析失败")

        print()

    # 生成对比可视化
    if target_files_data:
        viz_path = create_noise_pattern_comparison(target_files_data, output_dir)
        print(f"📊 对比图已保存: {os.path.basename(viz_path)}")

        # 保存分析结果
        results_path = os.path.join(output_dir, "noise_pattern_analysis_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型以便JSON序列化
            def convert_to_json_serializable(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, (np.integer, np.floating)):
                    return obj.item()
                elif isinstance(obj, np.bool_):
                    return bool(obj)
                elif isinstance(obj, dict):
                    return {k: convert_to_json_serializable(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_to_json_serializable(item) for item in obj]
                else:
                    return obj

            json_data = {}
            for filename, data in target_files_data.items():
                json_data[filename] = {
                    'classification': convert_to_json_serializable(data['classification']),
                    'num_patterns': len(data['noise_patterns'])
                }
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        print(f"📄 分析结果已保存: {os.path.basename(results_path)}")

    print("="*60)
    print("🎯 噪声模式分析完成！")

if __name__ == "__main__":
    main()
