#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的噪声检测算法
检测频谱曲线波动大且与动态噪声阈值差异很大的地方
主频和谐波区域不算噪声，即使在阈值线以上
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_threshold_detailed(freqs, power, fundamental_freq):
    """
    估计动态噪声阈值 - 与harmonic_detection_system完全一致，返回详细信息
    """
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    raw_noise_levels = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            raw_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None, None, None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    return smoothed_noise, window_centers, exclude_mask, raw_noise_levels

def method1_threshold_fluctuation_analysis(smoothed_noise, window_centers):
    """
    方法1: 动态阈值波动分析
    检测动态噪声阈值曲线本身的波动特性
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 10:
        return False, [], "动态阈值数据不足"
    
    # 计算阈值曲线的波动特征
    threshold_diff1 = np.diff(smoothed_noise)  # 一阶差分
    threshold_diff2 = np.diff(threshold_diff1)  # 二阶差分（曲率）
    
    # 计算波动统计
    fluctuation_std = np.std(threshold_diff1)
    curvature_std = np.std(threshold_diff2)
    
    # 检测异常波动点
    fluctuation_threshold = np.mean(np.abs(threshold_diff1)) + 2 * np.std(threshold_diff1)
    
    fluctuation_points = []
    for i, (diff, freq) in enumerate(zip(threshold_diff1, window_centers[1:])):
        if abs(diff) > fluctuation_threshold:
            # 找到对应的阈值
            threshold_value = smoothed_noise[i+1]
            fluctuation_points.append((freq, threshold_value))
    
    has_noise = len(fluctuation_points) > 0
    
    return has_noise, fluctuation_points, f"波动标准差: {fluctuation_std:.2f}dB, 曲率标准差: {curvature_std:.3f}, 检测到{len(fluctuation_points)}个波动点"

def method2_high_frequency_threshold_analysis(smoothed_noise, window_centers):
    """
    方法2: 高频阈值异常分析
    检测高频部分动态阈值的异常升高
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 10:
        return False, [], "动态阈值数据不足"
    
    # 分析高频部分（>5000Hz）
    high_freq_mask = np.array(window_centers) > 5000
    
    if not np.any(high_freq_mask):
        return False, [], "无高频数据"
    
    high_freq_centers = np.array(window_centers)[high_freq_mask]
    high_freq_thresholds = np.array(smoothed_noise)[high_freq_mask]
    
    # 计算高频阈值的统计特征
    high_freq_mean = np.mean(high_freq_thresholds)
    high_freq_std = np.std(high_freq_thresholds)
    
    # 检测异常升高的点
    anomaly_threshold = high_freq_mean + 1.5 * high_freq_std
    anomaly_mask = high_freq_thresholds > anomaly_threshold
    
    anomaly_points = [(freq, threshold) for freq, threshold in zip(high_freq_centers[anomaly_mask], high_freq_thresholds[anomaly_mask])]
    
    has_noise = len(anomaly_points) > 0
    
    return has_noise, anomaly_points, f"高频均值: {high_freq_mean:.1f}dB, 异常阈值: {anomaly_threshold:.1f}dB, 检测到{len(anomaly_points)}个异常点"

def method3_threshold_gradient_analysis(smoothed_noise, window_centers):
    """
    方法3: 阈值梯度分析
    检测动态阈值的急剧变化
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 10:
        return False, [], "动态阈值数据不足"
    
    # 计算频率间隔（对数空间）
    log_freqs = np.log10(window_centers)
    freq_intervals = np.diff(log_freqs)
    
    # 计算阈值梯度（dB/decade）
    threshold_gradients = np.diff(smoothed_noise) / freq_intervals
    
    # 检测异常梯度
    gradient_std = np.std(threshold_gradients)
    gradient_threshold = 3 * gradient_std  # 3σ准则
    
    steep_gradient_points = []
    for i, (gradient, freq) in enumerate(zip(threshold_gradients, window_centers[1:])):
        if abs(gradient) > gradient_threshold:
            threshold_value = smoothed_noise[i+1]
            steep_gradient_points.append((freq, threshold_value))
    
    has_noise = len(steep_gradient_points) > 0
    
    return has_noise, steep_gradient_points, f"梯度标准差: {gradient_std:.1f}dB/decade, 检测到{len(steep_gradient_points)}个陡峭梯度点"

def method4_threshold_smoothness_deviation(smoothed_noise, window_centers):
    """
    方法4: 阈值平滑度偏差分析
    检测偏离平滑趋势的阈值点
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 15:
        return False, [], "动态阈值数据不足"
    
    # 使用更强的平滑来获得趋势线
    if len(smoothed_noise) > 20:
        try:
            trend_line = savgol_filter(smoothed_noise, min(len(smoothed_noise)//2, 21), 3)
        except:
            trend_line = smoothed_noise
    else:
        trend_line = smoothed_noise
    
    # 计算偏差
    deviations = np.array(smoothed_noise) - trend_line
    deviation_std = np.std(deviations)
    
    # 检测显著偏差
    deviation_threshold = 2 * deviation_std
    significant_deviations = []
    
    for i, (deviation, freq, threshold) in enumerate(zip(deviations, window_centers, smoothed_noise)):
        if abs(deviation) > deviation_threshold:
            significant_deviations.append((freq, threshold))
    
    has_noise = len(significant_deviations) > 0
    
    return has_noise, significant_deviations, f"偏差标准差: {deviation_std:.2f}dB, 检测到{len(significant_deviations)}个显著偏差点"

def method5_local_threshold_variance(smoothed_noise, window_centers):
    """
    方法5: 局部阈值方差分析
    检测局部区域内阈值的高方差
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 20:
        return False, [], "动态阈值数据不足"
    
    local_window = 10  # 局部窗口大小
    high_variance_points = []
    
    for i in range(len(smoothed_noise) - local_window):
        local_thresholds = smoothed_noise[i:i+local_window]
        local_variance = np.var(local_thresholds)
        
        # 如果局部方差过大，认为存在噪声
        if local_variance > 4.0:  # 方差阈值
            center_idx = i + local_window // 2
            if center_idx < len(window_centers):
                freq = window_centers[center_idx]
                threshold = smoothed_noise[center_idx]
                high_variance_points.append((freq, threshold))
    
    # 去重
    high_variance_points = list(set(high_variance_points))
    has_noise = len(high_variance_points) > 0
    
    return has_noise, high_variance_points, f"检测到{len(high_variance_points)}个高方差区域"

def method6_threshold_frequency_correlation(smoothed_noise, window_centers):
    """
    方法6: 阈值频率相关性分析
    检测阈值与频率的异常相关性
    """
    
    if smoothed_noise is None or len(smoothed_noise) < 10:
        return False, [], "动态阈值数据不足"
    
    # 计算阈值与对数频率的相关性
    log_freqs = np.log10(window_centers)
    
    # 分段分析相关性
    segment_size = max(10, len(smoothed_noise) // 5)
    anomaly_points = []
    
    for i in range(0, len(smoothed_noise) - segment_size, segment_size // 2):
        end_idx = min(i + segment_size, len(smoothed_noise))
        
        segment_freqs = log_freqs[i:end_idx]
        segment_thresholds = smoothed_noise[i:end_idx]
        
        # 计算相关系数
        if len(segment_freqs) > 3:
            correlation = np.corrcoef(segment_freqs, segment_thresholds)[0, 1]
            
            # 检测异常相关性（过高或过低）
            if abs(correlation) > 0.8:  # 相关性阈值
                for j in range(i, end_idx):
                    if j < len(window_centers):
                        freq = window_centers[j]
                        threshold = smoothed_noise[j]
                        anomaly_points.append((freq, threshold))
    
    # 去重
    anomaly_points = list(set(anomaly_points))
    has_noise = len(anomaly_points) > 0
    
    return has_noise, anomaly_points, f"检测到{len(anomaly_points)}个异常相关性点"

def analyze_segment_for_threshold_noise(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的动态阈值波动噪声
    """

    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            return None

        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 高分辨率FFT
        fft_size = 131072
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]

        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window

        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2

        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]

        # 限制到20kHz
        freq_mask = positive_freqs <= 20000
        freqs = positive_freqs[freq_mask]
        power_linear = positive_power[freq_mask]
        power_db = 10 * np.log10(power_linear + 1e-12)

        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_linear[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_power_db = power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(power_linear) + 1e-12)

        # 估计动态噪声阈值（详细版本）
        smoothed_noise, window_centers, exclude_mask, raw_noise_levels = estimate_dynamic_noise_threshold_detailed(freqs, power_linear, fundamental_freq)

        if smoothed_noise is None:
            return None

        # 使用harmonic_detection_system检测谐波
        noise_analysis = {
            'global_noise_floor_db': np.mean(smoothed_noise),
            'noise_variation_db': np.max(smoothed_noise) - np.min(smoothed_noise),
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }

        harmonic_analysis = detect_harmonics_for_segment(freqs, power_linear, fundamental_freq, noise_analysis)

        # 应用6种动态阈值波动检测方法
        methods = [
            ("阈值波动分析", method1_threshold_fluctuation_analysis),
            ("高频阈值异常", method2_high_frequency_threshold_analysis),
            ("阈值梯度分析", method3_threshold_gradient_analysis),
            ("阈值平滑度偏差", method4_threshold_smoothness_deviation),
            ("局部阈值方差", method5_local_threshold_variance),
            ("阈值频率相关性", method6_threshold_frequency_correlation)
        ]

        results = {}
        for method_name, method_func in methods:
            has_noise, detected_points, info = method_func(smoothed_noise, window_centers)

            results[method_name] = {
                'has_noise': has_noise,
                'detected_points': detected_points,
                'info': info
            }

        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'power_db': power_db,
            'exclude_mask': exclude_mask,
            'smoothed_noise': smoothed_noise,
            'window_centers': window_centers,
            'raw_noise_levels': raw_noise_levels,
            'harmonic_analysis': harmonic_analysis,
            'results': results
        }

    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_threshold_noise_visualization(segment_data, output_dir, filename=""):
    """
    创建动态阈值波动噪声检测可视化
    """

    if not segment_data:
        return None

    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    fundamental_power_db = segment_data['fundamental_power_db']
    freqs = segment_data['freqs']
    power_db = segment_data['power_db']
    exclude_mask = segment_data['exclude_mask']
    smoothed_noise = segment_data['smoothed_noise']
    window_centers = segment_data['window_centers']
    raw_noise_levels = segment_data['raw_noise_levels']
    harmonic_analysis = segment_data['harmonic_analysis']
    results = segment_data['results']

    # 创建图形 - 2x3布局显示6种方法
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n动态阈值波动噪声检测 (主频: {fundamental_power_db:.1f}dB)',
                 fontsize=16, fontweight='bold')

    method_names = list(results.keys())
    colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown']

    for i, (method_name, color) in enumerate(zip(method_names, colors)):
        ax = axes[i]
        result = results[method_name]

        # 绘制基础频谱（浅灰色）
        ax.plot(freqs, power_db, 'lightgray', linewidth=1, alpha=0.5, label='频谱')

        # 绘制动态噪声阈值（重点显示）
        ax.plot(window_centers, smoothed_noise, 'cyan', linewidth=3, alpha=0.9, label='动态噪声阈值')

        # 绘制原始噪声估计（虚线）
        ax.plot(window_centers, raw_noise_levels, 'cyan', linewidth=1, alpha=0.5, linestyle='--', label='原始噪声估计')

        # 标记主频（但不算噪声）
        ax.axvline(fundamental_freq, color='black', linestyle='-', linewidth=2,
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz (非噪声)')

        # 标记检测到的谐波（但不算噪声）
        if harmonic_analysis:
            harmonic_freqs = [h['freq'] for h in harmonic_analysis]
            harmonic_powers = [h['power_db'] for h in harmonic_analysis]
            ax.scatter(harmonic_freqs, harmonic_powers, c='blue', s=20, alpha=0.6,
                      marker='^', label=f'谐波({len(harmonic_analysis)}个,非噪声)')

        # 标记排除区域（信号区域，非噪声）
        excluded_freqs = freqs[exclude_mask]
        excluded_powers = power_db[exclude_mask]
        if len(excluded_freqs) > 0:
            ax.scatter(excluded_freqs, excluded_powers, c='lightblue', s=1,
                      alpha=0.3, label='信号区域(非噪声)')

        # 标记检测到的阈值波动噪声点
        detected_points = result['detected_points']
        if detected_points:
            det_freqs, det_powers = zip(*detected_points)
            ax.scatter(det_freqs, det_powers, c=color, s=60, alpha=0.9,
                      marker='x', linewidth=3, label=f'阈值波动噪声({len(detected_points)}个)')

            # 标注一些检测点的功率值
            for j, (freq, power) in enumerate(detected_points[:3]):  # 只标注前3个
                ax.annotate(f'{power:.0f}', (freq, power), xytext=(5, 5),
                           textcoords='offset points', fontsize=8, color=color, weight='bold')

        # 设置标题和标签
        has_noise = result['has_noise']
        noise_status = "检测到阈值波动噪声" if has_noise else "阈值平滑无噪声"
        ax.set_title(f'{method_name} - {noise_status}\n{result["info"]}', fontsize=10)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, 40)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

        # 设置边框颜色表示检测结果
        for spine in ax.spines.values():
            spine.set_color(color if has_noise else 'gray')
            spine.set_linewidth(3 if has_noise else 1)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"threshold_noise_detection_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment_threshold_noise(args):
    """
    处理单个段的动态阈值波动噪声检测（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 检测段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_for_threshold_noise(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_threshold_noise_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 统计检测结果
                results = segment_data['results']
                noise_count = sum(1 for r in results.values() if r['has_noise'])

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_power_db': segment_data['fundamental_power_db'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'threshold_variation': np.max(segment_data['smoothed_noise']) - np.min(segment_data['smoothed_noise']),
                    'noise_methods': noise_count,
                    'total_methods': len(results),
                    'results': results
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 阈值波动噪声: {noise_count}/{len(results)}种方法, 阈值变化: {result['threshold_variation']:.1f}dB")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析低频戳洞文件的动态阈值波动噪声
    """

    print("🎯 低频戳洞文件动态阈值波动噪声检测")
    print("📝 检测动态噪声阈值波动较大、频率较高的部分")
    print("📝 主频和谐波区域不算噪声，即使在阈值线以上")
    print("="*80)

    # 查找低频戳洞文件
    target_file = None
    test_dirs = ["test20250717", "test20250722"]

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, _, files in os.walk(test_dir):
                for file in files:
                    if "低音戳洞" in file and file.lower().endswith('.wav'):
                        target_file = os.path.join(root, file)
                        break
                if target_file:
                    break
        if target_file:
            break

    if not target_file:
        print("❌ 未找到低频戳洞文件")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_threshold_noise_detection"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行动态阈值波动噪声检测...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_threshold_noise, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 统计分析
        method_stats = {}
        method_names = ["阈值波动分析", "高频阈值异常", "阈值梯度分析",
                       "阈值平滑度偏差", "局部阈值方差", "阈值频率相关性"]

        for method_name in method_names:
            method_stats[method_name] = {
                'detected_segments': 0,
                'total_segments': len(successful_results)
            }

        for result in successful_results:
            for method_name in method_names:
                if result['results'][method_name]['has_noise']:
                    method_stats[method_name]['detected_segments'] += 1

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件动态阈值波动噪声检测完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        print(f"\n🔍 各方法检测结果统计:")
        for method_name, stats in method_stats.items():
            detected = stats['detected_segments']
            total = stats['total_segments']
            percentage = (detected / total * 100) if total > 0 else 0
            print(f"  📈 {method_name}: {detected}/{total} ({percentage:.1f}%)")

        # 综合分析
        multi_method_segments = []
        for result in successful_results:
            noise_count = result['noise_methods']
            if noise_count >= 3:  # 3种以上方法检测到噪声
                multi_method_segments.append((result['seg_idx'], result['expected_freq'],
                                            noise_count, result['threshold_variation']))

        print(f"\n🎯 重点关注段（≥3种方法检测到阈值波动噪声）:")
        if multi_method_segments:
            for seg_idx, freq, count, variation in multi_method_segments:
                print(f"  ⚠️  段{seg_idx} ({freq:.1f}Hz): {count}/6种方法, 阈值变化{variation:.1f}dB")
        else:
            print("  ✅ 无段被多种方法同时检测为有阈值波动噪声")

        # 阈值变化统计
        threshold_variations = [r['threshold_variation'] for r in successful_results]

        print(f"\n📊 动态阈值特征统计:")
        print(f"  平均阈值变化: {np.mean(threshold_variations):.1f}dB")
        print(f"  阈值变化范围: {np.min(threshold_variations):.1f} ~ {np.max(threshold_variations):.1f}dB")
        print(f"  高变化段数(>10dB): {sum(1 for v in threshold_variations if v > 10)}个")

        print("="*80)
        print("🎯 动态阈值波动噪声检测分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
