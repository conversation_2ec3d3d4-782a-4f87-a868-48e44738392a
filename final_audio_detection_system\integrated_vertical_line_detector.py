#!/usr/bin/env python3
"""
集成竖线异常检测器
Integrated Vertical Line Anomaly Detector
将基于峰值的竖线检测方法集成到完整的音频异常检测系统中
对每个freq_split分割的频段进行竖线检测
"""

import numpy as np
import librosa
import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class IntegratedVerticalLineDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 竖线检测参数 - 调整为更严格的检测
        self.detection_params = {
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.3,              # 排除频段边界30% - 增加
            'min_peak_prominence_ratio': 0.3,       # 最小峰值突出度比例 - 增加
            'min_peak_height_ratio': 0.4,           # 最小峰值高度比例 - 增加
            'min_frequency_span': 1000,             # 最小频率跨度(Hz) - 增加
            'min_line_strength': 0.5,               # 最小竖线强度 - 增加
            'min_anomalous_segments': 10            # 最少异常频段数 - 增加
        }
        
        print(f"集成竖线异常检测器初始化完成")
        print(f"检测策略: freq_split频段分割 → 逐频段峰值检测 → 竖线确认")
    
    def detect_integrated_anomalies(self, audio_path):
        """集成的音频异常检测"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 使用freq_split进行频段分割并检测
            analysis = self._analyze_all_segments(
                audio_path, power_db, frequencies, times
            )
            
            # 基于竖线检测结果判断异常
            anomaly_score, anomaly_detected = self._integrated_decision(analysis)
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'analysis': analysis,
                'detection_method': 'integrated_vertical_line',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_all_segments(self, audio_path, power_db, frequencies, times):
        """分析所有频段的竖线异常"""
        analysis = {
            'freq_split_success': False,
            'total_segments': 0,
            'anomalous_segments': 0,
            'segment_details': [],
            'total_vertical_lines': 0,
            'max_line_strength': 0,
            'total_line_strength': 0,
            'segment_anomaly_ratio': 0
        }
        
        try:
            # 使用优化的频段分割
            from freq_split_optimized import split_freq_steps_optimized
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path,
                start_freq=100,
                stop_freq=20000,
                octave=12,
                min_cycles=10,
                min_duration=153,
                fs=48000,
                search_window_start=0.1,
                search_window_end=1.5,
                correlation_length=1.0,
                plot=False,
                debug=False
            )
            
            if len(step_boundaries) == 0:
                return analysis
            
            analysis['freq_split_success'] = True
            analysis['total_segments'] = len(step_boundaries)
            
            print(f"{os.path.basename(audio_path)}: 分析{len(step_boundaries)}个频段的竖线异常")
            
            # 分析每个频段
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                segment_result = self._analyze_single_segment(
                    power_db, frequencies, times, seg_start_time, seg_end_time, 
                    seg_idx, freq_table[seg_idx] if seg_idx < len(freq_table) else None
                )
                
                analysis['segment_details'].append(segment_result)
                
                if segment_result['has_vertical_lines']:
                    analysis['anomalous_segments'] += 1
                    analysis['total_vertical_lines'] += segment_result['line_count']
                    analysis['max_line_strength'] = max(
                        analysis['max_line_strength'],
                        segment_result['max_line_strength']
                    )
                    analysis['total_line_strength'] += segment_result['total_line_strength']
            
            # 计算异常频段比例
            if analysis['total_segments'] > 0:
                analysis['segment_anomaly_ratio'] = analysis['anomalous_segments'] / analysis['total_segments']
            
            print(f"  异常频段: {analysis['anomalous_segments']}/{analysis['total_segments']} "
                  f"({analysis['segment_anomaly_ratio']:.1%})")
            print(f"  总竖线数: {analysis['total_vertical_lines']}")
            print(f"  最大竖线强度: {analysis['max_line_strength']:.3f}")
            
        except Exception as e:
            print(f"频段分析失败: {e}")
        
        return analysis
    
    def _analyze_single_segment(self, power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq):
        """分析单个频段的竖线异常"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'expected_frequency': expected_freq,
            'has_vertical_lines': False,
            'line_count': 0,
            'max_line_strength': 0,
            'total_line_strength': 0,
            'vertical_lines': [],
            'energy_range': None
        }
        
        # 找到频段对应的时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除频段边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            return result
        
        # 提取频段核心部分的数据
        segment_power = power_db[:, seg_core_start:seg_core_end]
        segment_times = times[seg_core_start:seg_core_end]
        
        # 使用峰值检测方法检测竖线
        vertical_lines = self._detect_vertical_lines_in_segment(
            segment_power, frequencies, segment_times
        )
        
        # 更新结果
        if vertical_lines:
            result['has_vertical_lines'] = True
            result['line_count'] = len(vertical_lines)
            result['vertical_lines'] = vertical_lines
            result['max_line_strength'] = max(line['line_strength'] for line in vertical_lines)
            result['total_line_strength'] = sum(line['line_strength'] for line in vertical_lines)
        
        # 记录能量范围
        result['energy_range'] = (np.min(segment_power), np.max(segment_power))
        
        return result
    
    def _detect_vertical_lines_in_segment(self, segment_power, frequencies, segment_times):
        """在单个频段中检测竖线"""
        vertical_lines = []
        
        if segment_power.shape[1] < 3:  # 时间片太少
            return vertical_lines
        
        # 计算时间维度总能量
        total_energy = np.sum(segment_power, axis=0)
        
        if len(total_energy) == 0:
            return vertical_lines
        
        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        
        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        
        if energy_range < 100:  # 能量变化太小
            return vertical_lines
        
        min_height = np.min(smoothed_energy) + energy_range * self.detection_params['min_peak_height_ratio']
        min_prominence = energy_range * self.detection_params['min_peak_prominence_ratio']
        
        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy, 
                                     height=min_height,
                                     distance=2,
                                     prominence=min_prominence)
        
        # 分析每个峰值
        for peak_idx in peaks:
            if peak_idx < len(segment_times):
                peak_time = segment_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = segment_power[:, peak_idx]
                
                # 分析该时刻的频谱是否为竖线
                line_analysis = self._analyze_spectrum_for_line(
                    peak_power_spectrum, frequencies, peak_time, peak_energy
                )
                
                if line_analysis and line_analysis['line_strength'] >= self.detection_params['min_line_strength']:
                    vertical_lines.append(line_analysis)
        
        return vertical_lines
    
    def _analyze_spectrum_for_line(self, power_spectrum, frequencies, peak_time, peak_energy):
        """分析频谱是否为竖线"""
        # 计算功率分布统计
        power_mean = np.mean(power_spectrum)
        power_std = np.std(power_spectrum)
        power_median = np.median(power_spectrum)
        
        # 使用多种方法确定高能量频率点
        methods = {
            'percentile_90': np.percentile(power_spectrum, 90),
            'mean_plus_std': power_mean + 1.0 * power_std,
            'median_plus_range': power_median + (np.max(power_spectrum) - power_median) * 0.3
        }
        
        best_method = None
        best_score = 0
        
        for method_name, threshold in methods.items():
            high_energy_mask = power_spectrum > threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) >= 3:
                # 计算频率跨度
                freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                freq_ratio = len(high_energy_indices) / len(frequencies)
                
                # 计算连续性分数
                continuous_segments = self._find_continuous_segments(high_energy_indices)
                continuity_score = sum(len(seg) for seg in continuous_segments) / len(high_energy_indices) if high_energy_indices.size > 0 else 0
                
                # 综合评分
                span_score = min(1.0, freq_span / 5000.0)
                ratio_score = min(1.0, freq_ratio / 0.3)
                total_score = span_score * ratio_score * continuity_score
                
                if (total_score > best_score and 
                    freq_span >= self.detection_params['min_frequency_span']):
                    best_score = total_score
                    best_method = {
                        'name': method_name,
                        'threshold': threshold,
                        'high_energy_indices': high_energy_indices,
                        'freq_span': freq_span,
                        'freq_ratio': freq_ratio,
                        'total_score': total_score
                    }
        
        if best_method and best_score > 0.05:
            return {
                'time': peak_time,
                'peak_energy': peak_energy,
                'frequency_span': best_method['freq_span'],
                'frequency_ratio': best_method['freq_ratio'],
                'line_strength': best_score,
                'detection_method': f"segment_{best_method['name']}",
                'threshold': best_method['threshold'],
                'max_power': np.max(power_spectrum[best_method['high_energy_indices']]),
                'mean_power': np.mean(power_spectrum[best_method['high_energy_indices']])
            }
        
        return None
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _integrated_decision(self, analysis):
        """基于集成竖线检测结果判断异常"""
        if not analysis['freq_split_success']:
            return 0.0, False
        
        anomalous_segments = analysis['anomalous_segments']
        total_segments = analysis['total_segments']
        total_vertical_lines = analysis['total_vertical_lines']
        max_line_strength = analysis['max_line_strength']
        segment_anomaly_ratio = analysis['segment_anomaly_ratio']
        
        if anomalous_segments == 0:
            return 0.0, False
        
        # 计算异常分数
        segment_score = min(1.0, segment_anomaly_ratio * 2.0)  # 异常频段比例分数
        line_count_score = min(1.0, total_vertical_lines / 20.0)  # 竖线数量分数
        strength_score = min(1.0, max_line_strength * 2.0)  # 最大强度分数
        
        # 加权计算最终分数
        anomaly_score = (
            segment_score * 0.4 +
            line_count_score * 0.3 +
            strength_score * 0.3
        )
        
        # 判断是否异常
        anomaly_detected = (
            anomalous_segments >= self.detection_params['min_anomalous_segments'] and
            anomaly_score > 0.3
        )
        
        return anomaly_score, anomaly_detected
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'analysis': {},
            'detection_method': 'integrated_vertical_line',
            'error': True,
            'error_message': error_msg
        }
    
    def test_all_samples(self):
        """测试所有样本"""
        print("\n" + "="*80)
        print("集成竖线异常检测 - 完整音频文件分析")
        print("="*80)
        
        # 定义样本分类
        problematic_samples = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav"
        ]
        
        other_negative_samples = [
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        # 收集正样本
        positive_samples = []
        pos_dir = "../test20250717/pos"
        if os.path.exists(pos_dir):
            for root, dirs, files in os.walk(pos_dir):
                for file in files:
                    if file.endswith('.wav'):
                        positive_samples.append(os.path.join(root, file))
        
        all_samples = problematic_samples + other_negative_samples + positive_samples[:5]
        
        print(f"问题样本: {len(problematic_samples)}个")
        print(f"其他负样本: {len(other_negative_samples)}个")
        print(f"正样本: {len(positive_samples[:5])}个")
        
        results = []
        
        for sample in all_samples:
            if os.path.exists(sample):
                print(f"\n测试文件: {os.path.basename(sample)}")
                print("-" * 50)
                
                result = self.detect_integrated_anomalies(sample)
                result['filename'] = os.path.basename(sample)
                
                # 标记样本类型
                if sample in problematic_samples:
                    result['sample_type'] = 'problematic'
                elif sample in other_negative_samples:
                    result['sample_type'] = 'other_negative'
                else:
                    result['sample_type'] = 'positive'
                
                results.append(result)
                
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    
                    analysis = result['analysis']
                    print(f"异常频段: {analysis.get('anomalous_segments', 0)}/{analysis.get('total_segments', 0)} "
                          f"({analysis.get('segment_anomaly_ratio', 0):.1%})")
                    print(f"总竖线数: {analysis.get('total_vertical_lines', 0)}")
                    print(f"最大竖线强度: {analysis.get('max_line_strength', 0):.3f}")
                else:
                    print(f"处理失败: {result['error_message']}")
        
        # 分析结果
        self._analyze_classification_results(results)
        
        return results
    
    def _analyze_classification_results(self, results):
        """分析分类结果"""
        print(f"\n" + "="*60)
        print(f"集成竖线检测分类结果分析:")
        print("="*60)
        
        # 按类型分组
        problematic = [r for r in results if r.get('sample_type') == 'problematic' and not r['error']]
        other_negative = [r for r in results if r.get('sample_type') == 'other_negative' and not r['error']]
        positive = [r for r in results if r.get('sample_type') == 'positive' and not r['error']]
        
        # 计算各组统计
        def calc_group_stats(group, name):
            if not group:
                print(f"{name}: 无有效样本")
                return
            
            anomaly_scores = [r['anomaly_score'] for r in group]
            detection_rate = sum(1 for r in group if r['anomaly_detected']) / len(group) * 100
            avg_segments = np.mean([r['analysis'].get('anomalous_segments', 0) for r in group])
            avg_lines = np.mean([r['analysis'].get('total_vertical_lines', 0) for r in group])
            
            print(f"{name}:")
            print(f"  检出率: {detection_rate:.1f}%")
            print(f"  平均异常分数: {np.mean(anomaly_scores):.3f} ± {np.std(anomaly_scores):.3f}")
            print(f"  平均异常频段: {avg_segments:.1f}")
            print(f"  平均竖线数: {avg_lines:.1f}")
            
            for r in group:
                status = "异常" if r['anomaly_detected'] else "正常"
                lines = r['analysis'].get('total_vertical_lines', 0)
                print(f"    {r['filename']}: {status} (分数:{r['anomaly_score']:.3f}, 竖线:{lines})")
        
        calc_group_stats(problematic, "问题样本")
        calc_group_stats(other_negative, "其他负样本")
        calc_group_stats(positive, "正样本")

def main():
    """主函数"""
    detector = IntegratedVerticalLineDetector()
    results = detector.test_all_samples()
    
    # 保存结果
    results_data = []
    for result in results:
        if not result['error']:
            analysis = result['analysis']
            row = {
                'filename': result['filename'],
                'sample_type': result.get('sample_type', 'unknown'),
                'anomaly_detected': result['anomaly_detected'],
                'confidence': result['confidence'],
                'anomaly_score': result['anomaly_score'],
                'total_segments': analysis.get('total_segments', 0),
                'anomalous_segments': analysis.get('anomalous_segments', 0),
                'segment_anomaly_ratio': analysis.get('segment_anomaly_ratio', 0),
                'total_vertical_lines': analysis.get('total_vertical_lines', 0),
                'max_line_strength': analysis.get('max_line_strength', 0),
                'error': result['error']
            }
            results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('integrated_vertical_line_detection_results.csv', index=False)
    
    print(f"\n集成竖线检测结果已保存: integrated_vertical_line_detection_results.csv")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
