import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import classification_report
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

def analyze_feature_effectiveness(features_csv_path="optimized_features/all_features.csv", output_dir="feature_analysis"):
    """
    分析特征对正负样本分类的有效性
    """
    os.makedirs(output_dir, exist_ok=True)

    # 加载特征数据
    if os.path.exists(features_csv_path):
        print(f"[INFO] 加载特征文件: {features_csv_path}")
        combined_df = pd.read_csv(features_csv_path)

        print(f"[INFO] 数据集信息:")
        print(f"  总样本数: {len(combined_df)}")
        print(f"  正样本: {(combined_df['label'] == 1).sum()}")
        print(f"  负样本: {(combined_df['label'] == 0).sum()}")
        print(f"  特征维度: {len(combined_df.columns)}")

        # 分析特征有效性
        analyze_features(combined_df, output_dir)
    else:
        print(f"[ERROR] 特征文件不存在: {features_csv_path}")
        print("[INFO] 请先运行 optimized_feature_extractor.py 提取特征")

def analyze_features(df, output_dir):
    """
    分析特征的有效性和重要性
    """
    # 排除非数值列
    exclude_cols = ['label', 'freq', 'mid_time', 'anomaly_types', 'peak_freq', 'filename',
                   'segment_id', 'time_start', 'time_end', 'frequency', 'segment_length']
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    # 进一步过滤，只保留数值列
    numeric_cols = []
    for col in feature_cols:
        try:
            pd.to_numeric(df[col], errors='raise')
            numeric_cols.append(col)
        except:
            print(f"[WARN] 跳过非数值列: {col}")

    feature_cols = numeric_cols
    X = df[feature_cols].fillna(0)
    y = df['label']
    
    print(f"[INFO] 分析 {len(feature_cols)} 个特征的有效性")
    
    # 1. 特征分布分析
    plt.figure(figsize=(20, 15))
    n_cols = 4
    n_rows = (len(feature_cols) + n_cols - 1) // n_cols
    
    for i, col in enumerate(feature_cols[:16]):  # 只显示前16个特征
        plt.subplot(n_rows, n_cols, i + 1)
        pos_data = df[df['label'] == 1][col].dropna()
        neg_data = df[df['label'] == 0][col].dropna()
        
        plt.hist(pos_data, alpha=0.7, label='Positive', bins=20, color='green')
        plt.hist(neg_data, alpha=0.7, label='Negative', bins=20, color='red')
        plt.title(f'{col}')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "feature_distributions.png"), dpi=150)
    plt.close()
    
    # 2. 特征重要性分析（使用随机森林）
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': feature_cols,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n[INFO] 特征重要性排序（前10）:")
    print(feature_importance.head(10))
    
    # 可视化特征重要性
    plt.figure(figsize=(12, 8))
    top_features = feature_importance.head(15)
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Feature Importance')
    plt.title('Top 15 Feature Importance (Random Forest)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "feature_importance.png"), dpi=150)
    plt.close()
    
    # 3. 特征相关性分析
    correlation_matrix = X.corr()
    
    plt.figure(figsize=(15, 12))
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm', center=0)
    plt.title('Feature Correlation Matrix')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "feature_correlation.png"), dpi=150)
    plt.close()
    
    # 4. 识别高相关性特征（可能冗余）
    high_corr_pairs = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_val = abs(correlation_matrix.iloc[i, j])
            if corr_val > 0.8:  # 高相关性阈值
                high_corr_pairs.append((
                    correlation_matrix.columns[i],
                    correlation_matrix.columns[j],
                    corr_val
                ))
    
    print(f"\n[INFO] 发现 {len(high_corr_pairs)} 对高相关性特征（相关系数 > 0.8）:")
    for feat1, feat2, corr in high_corr_pairs:
        print(f"  {feat1} <-> {feat2}: {corr:.3f}")
    
    # 5. 分类性能评估
    y_pred = rf.predict(X_test)
    print(f"\n[INFO] 随机森林分类性能:")
    print(classification_report(y_test, y_pred))
    
    # 6. PCA分析
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    pca = PCA()
    X_pca = pca.fit_transform(X_scaled)
    
    # 解释方差比例
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(range(1, len(pca.explained_variance_ratio_) + 1), 
             np.cumsum(pca.explained_variance_ratio_), 'bo-')
    plt.xlabel('Number of Components')
    plt.ylabel('Cumulative Explained Variance Ratio')
    plt.title('PCA Explained Variance')
    plt.grid(True)
    
    # PCA散点图（前两个主成分）
    plt.subplot(1, 2, 2)
    pos_mask = y == 1
    neg_mask = y == 0
    plt.scatter(X_pca[pos_mask, 0], X_pca[pos_mask, 1], c='green', alpha=0.6, label='Positive')
    plt.scatter(X_pca[neg_mask, 0], X_pca[neg_mask, 1], c='red', alpha=0.6, label='Negative')
    plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
    plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
    plt.title('PCA Visualization')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "pca_analysis.png"), dpi=150)
    plt.close()
    
    # 7. 生成特征有效性报告
    report = generate_feature_report(feature_importance, high_corr_pairs, pca, feature_cols)
    
    with open(os.path.join(output_dir, "feature_effectiveness_report.txt"), 'w', encoding='utf-8') as f:
        f.write(report)
    
    return feature_importance, high_corr_pairs

def generate_feature_report(feature_importance, high_corr_pairs, pca, feature_cols):
    """
    生成特征有效性分析报告
    """
    report = "特征有效性分析报告\n"
    report += "=" * 50 + "\n\n"
    
    # 最重要的特征
    report += "1. 最重要的特征（前10）:\n"
    for i, row in feature_importance.head(10).iterrows():
        report += f"   {row['feature']}: {row['importance']:.4f}\n"
    
    # 最不重要的特征（可能需要删除）
    report += "\n2. 最不重要的特征（后10，建议删除）:\n"
    for i, row in feature_importance.tail(10).iterrows():
        report += f"   {row['feature']}: {row['importance']:.4f}\n"
    
    # 高相关性特征
    report += f"\n3. 高相关性特征对（{len(high_corr_pairs)} 对）:\n"
    for feat1, feat2, corr in high_corr_pairs:
        report += f"   {feat1} <-> {feat2}: {corr:.3f}\n"
    
    # PCA分析
    report += f"\n4. PCA分析:\n"
    report += f"   前5个主成分解释方差比例: {pca.explained_variance_ratio_[:5]}\n"
    report += f"   前10个主成分累计解释方差: {np.sum(pca.explained_variance_ratio_[:10]):.3f}\n"
    
    # 建议
    report += "\n5. 优化建议:\n"
    low_importance_features = feature_importance[feature_importance['importance'] < 0.01]['feature'].tolist()
    report += f"   - 删除低重要性特征（{len(low_importance_features)}个）: {low_importance_features}\n"
    
    redundant_features = set()
    for feat1, feat2, corr in high_corr_pairs:
        # 保留重要性更高的特征
        imp1 = feature_importance[feature_importance['feature'] == feat1]['importance'].iloc[0]
        imp2 = feature_importance[feature_importance['feature'] == feat2]['importance'].iloc[0]
        if imp1 < imp2:
            redundant_features.add(feat1)
        else:
            redundant_features.add(feat2)
    
    report += f"   - 删除冗余特征（{len(redundant_features)}个）: {list(redundant_features)}\n"
    
    return report

if __name__ == "__main__":
    analyze_feature_effectiveness()
