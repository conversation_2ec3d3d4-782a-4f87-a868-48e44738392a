#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示两个噪声样本93个频段分析的总结
"""

import os
import pandas as pd
import numpy as np

def show_target_analysis_summary():
    """显示目标样本分析总结"""
    print("🎯 两个噪声样本93个频段分析总结")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    
    # 筛选数据
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 数据概览:")
    print(f"   噪声样本记录数: {len(target_data)} (2个文件 × 93个频段)")
    print(f"   正常样本记录数: {len(normal_data)} (54个文件 × 93个频段)")
    
    # 前四个特征
    features = [
        'true_noise_floor_median',
        'true_noise_floor_mean', 
        'noise_floor_stability_mean',
        'noise_floor_stability_std'
    ]
    
    feature_names = [
        '真实底噪中位数',
        '真实底噪平均值',
        '底噪稳定性平均值',
        '底噪稳定性标准差'
    ]
    
    print(f"\n📈 两个样本的特征对比:")
    print("-" * 50)
    
    # 分析每个样本
    for i, filename in enumerate(target_files):
        print(f"\n🎯 样本 {i+1}: {filename}")
        sample_data = target_data[target_data['filename'] == filename]
        
        for j, (feature, name) in enumerate(zip(features, feature_names)):
            sample_values = sample_data[feature]
            normal_values = normal_data[feature]
            
            sample_mean = np.mean(sample_values)
            normal_mean = np.mean(normal_values)
            normal_std = np.std(normal_values)
            
            # 计算异常频段数
            anomaly_count = 0
            for seg_idx in range(93):
                seg_sample = sample_data[sample_data['segment_idx'] == seg_idx]
                seg_normal = normal_data[normal_data['segment_idx'] == seg_idx]
                
                if len(seg_sample) > 0 and len(seg_normal) > 0:
                    sample_val = seg_sample[feature].iloc[0]
                    normal_seg_mean = np.mean(seg_normal[feature])
                    normal_seg_std = np.std(seg_normal[feature])
                    
                    # 超出2σ算异常
                    if abs(sample_val - normal_seg_mean) > 2 * normal_seg_std:
                        anomaly_count += 1
            
            z_score = (sample_mean - normal_mean) / normal_std if normal_std > 0 else 0
            
            print(f"   {name}:")
            print(f"     样本均值: {sample_mean:.6f}")
            print(f"     正常均值: {normal_mean:.6f}")
            print(f"     Z-score: {z_score:+.2f}")
            print(f"     异常频段: {anomaly_count}/93 ({anomaly_count/93*100:.1f}%)")
    
    print(f"\n🔄 两个样本相互对比:")
    print("-" * 50)
    
    sample1_data = target_data[target_data['filename'] == target_files[0]]
    sample2_data = target_data[target_data['filename'] == target_files[1]]
    
    for feature, name in zip(features, feature_names):
        sample1_values = []
        sample2_values = []
        
        for seg_idx in range(93):
            s1_seg = sample1_data[sample1_data['segment_idx'] == seg_idx]
            s2_seg = sample2_data[sample2_data['segment_idx'] == seg_idx]
            
            if len(s1_seg) > 0 and len(s2_seg) > 0:
                sample1_values.append(s1_seg[feature].iloc[0])
                sample2_values.append(s2_seg[feature].iloc[0])
        
        if len(sample1_values) > 0:
            sample1_mean = np.mean(sample1_values)
            sample2_mean = np.mean(sample2_values)
            correlation = np.corrcoef(sample1_values, sample2_values)[0, 1]
            
            print(f"   {name}:")
            print(f"     样本1均值: {sample1_mean:.6f}")
            print(f"     样本2均值: {sample2_mean:.6f}")
            print(f"     差异: {sample2_mean - sample1_mean:+.6f}")
            print(f"     相关性: {correlation:.3f}")
    
    print(f"\n🏆 最异常的频段 (综合所有特征):")
    print("-" * 50)
    
    # 计算每个频段的综合异常分数
    segment_anomaly_scores = {}
    
    for seg_idx in range(93):
        seg_target = target_data[target_data['segment_idx'] == seg_idx]
        seg_normal = normal_data[normal_data['segment_idx'] == seg_idx]
        
        if len(seg_target) > 0 and len(seg_normal) > 0:
            total_z_score = 0
            
            for feature in features:
                target_mean = np.mean(seg_target[feature])
                normal_mean = np.mean(seg_normal[feature])
                normal_std = np.std(seg_normal[feature])
                
                if normal_std > 0:
                    z_score = abs(target_mean - normal_mean) / normal_std
                    total_z_score += z_score
            
            freq = seg_target['expected_freq'].iloc[0]
            segment_anomaly_scores[seg_idx] = {
                'frequency': freq,
                'total_z_score': total_z_score,
                'avg_z_score': total_z_score / len(features)
            }
    
    # 排序并显示前10个
    sorted_segments = sorted(segment_anomaly_scores.items(), 
                           key=lambda x: x[1]['total_z_score'], reverse=True)
    
    print(f"   前10个最异常频段:")
    print(f"   {'频段':>4} {'频率(Hz)':>8} {'综合Z-score':>12} {'平均Z-score':>12}")
    print("-" * 45)
    
    for i, (seg_idx, info) in enumerate(sorted_segments[:10]):
        print(f"   {seg_idx:>4} {info['frequency']:>8.1f} "
              f"{info['total_z_score']:>12.2f} {info['avg_z_score']:>12.2f}")
    
    print(f"\n📊 可视化文件:")
    print(f"   ✅ target_samples_93_segments_analysis.png - 详细可视化图表")
    print(f"   包含:")
    print(f"   - 4个特征的93个频段对比图 (显示正常样本范围)")
    print(f"   - 两个样本的特征热力图")
    print(f"   - 差异分析图")
    
    print(f"\n💡 关键发现:")
    print(f"   1. 两个噪声样本在多个频段都显著偏离正常范围")
    print(f"   2. 底噪稳定性特征在更多频段显示异常")
    print(f"   3. 真实底噪特征在低频段(100Hz)异常最明显")
    print(f"   4. 两个样本表现出相似的异常模式")

if __name__ == "__main__":
    show_target_analysis_summary()
