#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排除异常文件重新计算绝对范围
"""

import os
import pandas as pd
import numpy as np

def recalculate_without_anomalous_files():
    """排除异常文件重新计算绝对范围"""
    print("🔧 排除异常文件重新计算绝对范围")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 最异常的文件
    most_anomalous_file = '录音_步进扫频_100Hz至20000Hz_20250714_155101.wav'
    
    # 原始数据
    target_data = df[df['is_target'] == True]
    normal_data_original = df[df['is_target'] == False]
    
    # 排除最异常文件后的正常数据
    normal_data_cleaned = normal_data_original[normal_data_original['filename'] != most_anomalous_file]
    
    print(f"📊 数据对比:")
    print(f"   原始正常样本记录数: {len(normal_data_original)}")
    print(f"   清理后正常样本记录数: {len(normal_data_cleaned)}")
    print(f"   排除的文件: {most_anomalous_file}")
    print(f"   噪声样本记录数: {len(target_data)}")
    
    # 对比关键频段的分离效果
    key_segments = [0, 1, 2, 10, 20]  # 关键频段
    features = ['true_noise_floor_median', 'true_noise_floor_mean']
    
    for feature in features:
        print(f"\n🎯 {feature} 分离效果对比:")
        print("-" * 60)
        print(f"{'频段':>4} {'频率(Hz)':>8} {'原始分离':>10} {'清理后分离':>12} {'改善效果':>10}")
        print("-" * 60)
        
        for seg_idx in key_segments:
            freq = df[df['segment_idx'] == seg_idx]['expected_freq'].iloc[0]
            
            # 原始分离效果
            original_separation = calculate_separation(normal_data_original, target_data, seg_idx, feature)
            
            # 清理后分离效果
            cleaned_separation = calculate_separation(normal_data_cleaned, target_data, seg_idx, feature)
            
            # 改善效果
            if original_separation['separable'] and cleaned_separation['separable']:
                improvement = cleaned_separation['gap'] - original_separation['gap']
            elif not original_separation['separable'] and cleaned_separation['separable']:
                improvement = cleaned_separation['gap']
            else:
                improvement = 0
            
            original_status = f"{original_separation['gap']:.3f}" if original_separation['separable'] else "重叠"
            cleaned_status = f"{cleaned_separation['gap']:.3f}" if cleaned_separation['separable'] else "重叠"
            improvement_status = f"+{improvement:.3f}" if improvement > 0 else f"{improvement:.3f}"
            
            print(f"{seg_idx:>4} {freq:>8.1f} {original_status:>10} {cleaned_status:>12} {improvement_status:>10}")
    
    # 统计所有频段的分离改善
    print(f"\n📊 所有93个频段分离改善统计:")
    print("-" * 50)
    
    for feature in features:
        print(f"\n   {feature}:")
        
        original_separable_count = 0
        cleaned_separable_count = 0
        improved_count = 0
        
        segments = sorted(df['segment_idx'].unique())
        
        for seg_idx in segments:
            original_sep = calculate_separation(normal_data_original, target_data, seg_idx, feature)
            cleaned_sep = calculate_separation(normal_data_cleaned, target_data, seg_idx, feature)
            
            if original_sep['separable']:
                original_separable_count += 1
            
            if cleaned_sep['separable']:
                cleaned_separable_count += 1
            
            # 检查是否有改善
            if not original_sep['separable'] and cleaned_sep['separable']:
                improved_count += 1
            elif original_sep['separable'] and cleaned_sep['separable'] and cleaned_sep['gap'] > original_sep['gap']:
                improved_count += 1
        
        print(f"     原始可分离频段: {original_separable_count}/{len(segments)} ({original_separable_count/len(segments)*100:.1f}%)")
        print(f"     清理后可分离频段: {cleaned_separable_count}/{len(segments)} ({cleaned_separable_count/len(segments)*100:.1f}%)")
        print(f"     改善的频段数: {improved_count}")
        print(f"     改善幅度: +{cleaned_separable_count - original_separable_count} 个可分离频段")
    
    # 详细分析100Hz频段
    print(f"\n🔍 100Hz频段详细分析:")
    print("-" * 50)
    
    analyze_segment_detailed(normal_data_original, normal_data_cleaned, target_data, 0, 'true_noise_floor_median')

def calculate_separation(normal_data, target_data, segment_idx, feature):
    """计算分离效果"""
    normal_seg = normal_data[normal_data['segment_idx'] == segment_idx]
    target_seg = target_data[target_data['segment_idx'] == segment_idx]
    
    if len(normal_seg) == 0 or len(target_seg) == 0:
        return {'separable': False, 'gap': 0}
    
    normal_values = normal_seg[feature].dropna().values
    target_values = target_seg[feature].dropna().values
    
    if len(normal_values) == 0 or len(target_values) == 0:
        return {'separable': False, 'gap': 0}
    
    normal_min = np.min(normal_values)
    normal_max = np.max(normal_values)
    target_min = np.min(target_values)
    target_max = np.max(target_values)
    
    # 检查完全分离
    if target_max < normal_min:
        gap = normal_min - target_max
        return {'separable': True, 'gap': gap, 'type': 'target_below'}
    elif target_min > normal_max:
        gap = target_min - normal_max
        return {'separable': True, 'gap': gap, 'type': 'target_above'}
    else:
        return {'separable': False, 'gap': 0, 'type': 'overlap'}

def analyze_segment_detailed(normal_original, normal_cleaned, target_data, segment_idx, feature):
    """详细分析特定频段"""
    
    freq = normal_original[normal_original['segment_idx'] == segment_idx]['expected_freq'].iloc[0]
    print(f"   频段{segment_idx} ({freq:.1f}Hz) - {feature}:")
    
    # 原始数据
    normal_orig_seg = normal_original[normal_original['segment_idx'] == segment_idx]
    normal_orig_values = normal_orig_seg[feature].dropna().values
    
    # 清理后数据
    normal_clean_seg = normal_cleaned[normal_cleaned['segment_idx'] == segment_idx]
    normal_clean_values = normal_clean_seg[feature].dropna().values
    
    # 目标数据
    target_seg = target_data[target_data['segment_idx'] == segment_idx]
    target_values = target_seg[feature].dropna().values
    
    print(f"     原始正常样本: 数量={len(normal_orig_values)}, 范围=[{np.min(normal_orig_values):.3f}, {np.max(normal_orig_values):.3f}]")
    print(f"     清理后正常样本: 数量={len(normal_clean_values)}, 范围=[{np.min(normal_clean_values):.3f}, {np.max(normal_clean_values):.3f}]")
    print(f"     噪声样本: 数量={len(target_values)}, 范围=[{np.min(target_values):.3f}, {np.max(target_values):.3f}]")
    
    # 计算分离效果
    original_sep = calculate_separation(normal_original, target_data, segment_idx, feature)
    cleaned_sep = calculate_separation(normal_cleaned, target_data, segment_idx, feature)
    
    print(f"     原始分离: {'可分离' if original_sep['separable'] else '重叠'}")
    if original_sep['separable']:
        print(f"       分离间隙: {original_sep['gap']:.6f}")
        print(f"       分离类型: {original_sep['type']}")
    
    print(f"     清理后分离: {'可分离' if cleaned_sep['separable'] else '重叠'}")
    if cleaned_sep['separable']:
        print(f"       分离间隙: {cleaned_sep['gap']:.6f}")
        print(f"       分离类型: {cleaned_sep['type']}")
    
    # 找出被排除的异常值
    most_anomalous_file = '录音_步进扫频_100Hz至20000Hz_20250714_155101.wav'
    anomalous_seg = normal_original[(normal_original['segment_idx'] == segment_idx) & 
                                   (normal_original['filename'] == most_anomalous_file)]
    
    if len(anomalous_seg) > 0:
        anomalous_value = anomalous_seg[feature].iloc[0]
        print(f"     被排除的异常值: {anomalous_value:.6f}")
        print(f"     异常值与正常范围的差距: {anomalous_value - np.max(normal_clean_values):.6f}")

if __name__ == "__main__":
    recalculate_without_anomalous_files()
