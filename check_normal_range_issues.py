#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查正常范围计算的问题
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def check_normal_range_issues():
    """检查正常范围计算的问题"""
    print("🔍 检查正常范围计算的问题")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    
    # 筛选数据
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 数据概览:")
    print(f"   总记录数: {len(df)}")
    print(f"   噪声样本记录数: {len(target_data)}")
    print(f"   正常样本记录数: {len(normal_data)}")
    print(f"   噪声样本文件数: {len(target_data['filename'].unique())}")
    print(f"   正常样本文件数: {len(normal_data['filename'].unique())}")
    
    # 检查数据完整性
    print(f"\n🔍 数据完整性检查:")
    segments = sorted(df['segment_idx'].unique())
    print(f"   频段数: {len(segments)}")
    print(f"   频段范围: {min(segments)} - {max(segments)}")
    
    # 检查每个频段的样本数
    print(f"\n📊 每个频段的样本数检查:")
    for sample_type, data in [('正常样本', normal_data), ('噪声样本', target_data)]:
        print(f"   {sample_type}:")
        segment_counts = data.groupby('segment_idx').size()
        print(f"     最小样本数: {segment_counts.min()}")
        print(f"     最大样本数: {segment_counts.max()}")
        print(f"     平均样本数: {segment_counts.mean():.1f}")
        
        # 检查是否有缺失频段
        missing_segments = set(segments) - set(segment_counts.index)
        if missing_segments:
            print(f"     ❌ 缺失频段: {sorted(missing_segments)}")
        else:
            print(f"     ✅ 所有频段都有数据")
    
    # 检查特征值的合理性
    print(f"\n🔍 特征值合理性检查:")
    features = ['true_noise_floor_median', 'true_noise_floor_mean', 
               'noise_floor_stability_mean', 'noise_floor_stability_std']
    
    for feature in features:
        print(f"\n   {feature}:")
        
        # 正常样本统计
        normal_values = normal_data[feature].dropna()
        target_values = target_data[feature].dropna()
        
        print(f"     正常样本: 数量={len(normal_values)}, 范围=[{normal_values.min():.3f}, {normal_values.max():.3f}]")
        print(f"     噪声样本: 数量={len(target_values)}, 范围=[{target_values.min():.3f}, {target_values.max():.3f}]")
        
        # 检查异常值
        if 'noise_floor' in feature:
            # 底噪特征应该是负值
            if normal_values.max() > 0:
                print(f"     ⚠️  警告: 正常样本有正值 (最大值: {normal_values.max():.3f})")
            if target_values.max() > 0:
                print(f"     ⚠️  警告: 噪声样本有正值 (最大值: {target_values.max():.3f})")
        
        elif 'stability' in feature:
            # 稳定性特征应该是非负值
            if normal_values.min() < 0:
                print(f"     ⚠️  警告: 正常样本有负值 (最小值: {normal_values.min():.3f})")
            if target_values.min() < 0:
                print(f"     ⚠️  警告: 噪声样本有负值 (最小值: {target_values.min():.3f})")
    
    # 详细检查100Hz频段
    print(f"\n🔍 详细检查100Hz频段 (频段0):")
    check_segment_details(df, 0)
    
    # 检查范围计算逻辑
    print(f"\n🔍 检查范围计算逻辑:")
    check_range_calculation_logic(normal_data, target_data)

def check_segment_details(df, segment_idx):
    """详细检查特定频段"""
    seg_data = df[df['segment_idx'] == segment_idx]
    normal_seg = seg_data[seg_data['is_target'] == False]
    target_seg = seg_data[seg_data['is_target'] == True]
    
    if len(seg_data) == 0:
        print(f"   ❌ 频段{segment_idx}无数据")
        return
    
    freq = seg_data['expected_freq'].iloc[0]
    print(f"   频段{segment_idx} ({freq:.1f}Hz):")
    print(f"     正常样本数: {len(normal_seg)}")
    print(f"     噪声样本数: {len(target_seg)}")
    
    # 检查每个特征
    features = ['true_noise_floor_median', 'true_noise_floor_mean', 
               'noise_floor_stability_mean', 'noise_floor_stability_std']
    
    for feature in features:
        normal_values = normal_seg[feature].dropna()
        target_values = target_seg[feature].dropna()
        
        if len(normal_values) > 0 and len(target_values) > 0:
            normal_mean = np.mean(normal_values)
            normal_std = np.std(normal_values)
            normal_min = np.min(normal_values)
            normal_max = np.max(normal_values)
            
            target_mean = np.mean(target_values)
            target_min = np.min(target_values)
            target_max = np.max(target_values)
            
            print(f"     {feature}:")
            print(f"       正常: 均值={normal_mean:.3f}, 标准差={normal_std:.3f}, 范围=[{normal_min:.3f}, {normal_max:.3f}]")
            print(f"       噪声: 均值={target_mean:.3f}, 范围=[{target_min:.3f}, {target_max:.3f}]")
            
            # 检查噪声样本是否在正常范围内
            sigma1_lower = normal_mean - normal_std
            sigma1_upper = normal_mean + normal_std
            sigma2_lower = normal_mean - 2*normal_std
            sigma2_upper = normal_mean + 2*normal_std
            
            print(f"       正常±1σ范围: [{sigma1_lower:.3f}, {sigma1_upper:.3f}]")
            print(f"       正常±2σ范围: [{sigma2_lower:.3f}, {sigma2_upper:.3f}]")
            
            # 检查噪声样本位置
            for i, target_val in enumerate(target_values):
                if target_val < normal_min or target_val > normal_max:
                    status = "超出绝对范围"
                elif target_val < sigma2_lower or target_val > sigma2_upper:
                    status = "超出±2σ"
                elif target_val < sigma1_lower or target_val > sigma1_upper:
                    status = "超出±1σ"
                else:
                    status = "在正常范围内"
                
                print(f"       噪声样本{i+1}: {target_val:.3f} → {status}")

def check_range_calculation_logic(normal_data, target_data):
    """检查范围计算逻辑"""
    
    # 选择一个特征进行详细检查
    feature = 'true_noise_floor_median'
    segment_idx = 0
    
    print(f"   以{feature}在频段{segment_idx}为例:")
    
    # 获取数据
    normal_seg = normal_data[normal_data['segment_idx'] == segment_idx]
    target_seg = target_data[target_data['segment_idx'] == segment_idx]
    
    normal_values = normal_seg[feature].values
    target_values = target_seg[feature].values
    
    print(f"     正常样本原始数据 (前10个): {normal_values[:10]}")
    print(f"     噪声样本原始数据: {target_values}")
    
    # 计算统计量
    normal_mean = np.mean(normal_values)
    normal_std = np.std(normal_values, ddof=1)
    normal_min = np.min(normal_values)
    normal_max = np.max(normal_values)
    normal_p25 = np.percentile(normal_values, 25)
    normal_p75 = np.percentile(normal_values, 75)
    
    print(f"     正常样本统计:")
    print(f"       均值: {normal_mean:.6f}")
    print(f"       标准差: {normal_std:.6f}")
    print(f"       最小值: {normal_min:.6f}")
    print(f"       最大值: {normal_max:.6f}")
    print(f"       25%分位数: {normal_p25:.6f}")
    print(f"       75%分位数: {normal_p75:.6f}")
    
    # 计算范围
    ranges = {
        'absolute': [normal_min, normal_max],
        'iqr': [normal_p25, normal_p75],
        'sigma1': [normal_mean - normal_std, normal_mean + normal_std],
        'sigma2': [normal_mean - 2*normal_std, normal_mean + 2*normal_std]
    }
    
    print(f"     计算的范围:")
    for range_name, (lower, upper) in ranges.items():
        print(f"       {range_name}: [{lower:.6f}, {upper:.6f}] (宽度: {upper-lower:.6f})")
    
    # 检查范围的合理性
    print(f"     范围合理性检查:")
    
    # 1. 检查±2σ范围是否比绝对范围更宽
    if ranges['sigma2'][0] < ranges['absolute'][0] or ranges['sigma2'][1] > ranges['absolute'][1]:
        print(f"       ⚠️  警告: ±2σ范围超出了绝对范围")
        print(f"         这可能表明数据不符合正态分布")
    
    # 2. 检查范围的包含关系
    if ranges['iqr'][0] < ranges['sigma1'][0] or ranges['iqr'][1] > ranges['sigma1'][1]:
        print(f"       ℹ️  信息: IQR范围与±1σ范围不完全重叠")
        print(f"         这是正常的，因为分位数和正态分布假设不同")
    
    # 3. 检查噪声样本的位置
    print(f"     噪声样本位置分析:")
    for i, target_val in enumerate(target_values):
        print(f"       噪声样本{i+1}: {target_val:.6f}")
        
        # 检查在哪个范围内
        in_ranges = []
        for range_name, (lower, upper) in ranges.items():
            if lower <= target_val <= upper:
                in_ranges.append(range_name)
        
        if in_ranges:
            print(f"         在范围内: {', '.join(in_ranges)}")
        else:
            print(f"         ❌ 超出所有范围!")
            
            # 计算最近的范围边界
            distances = []
            for range_name, (lower, upper) in ranges.items():
                if target_val < lower:
                    distances.append((range_name, 'lower', lower - target_val))
                elif target_val > upper:
                    distances.append((range_name, 'upper', target_val - upper))
            
            if distances:
                closest = min(distances, key=lambda x: x[2])
                print(f"         最近边界: {closest[0]}的{closest[1]}边界，距离{closest[2]:.6f}")

def visualize_range_issues():
    """可视化范围问题"""
    print(f"\n🎨 生成范围问题可视化...")
    
    # 这里可以添加可视化代码
    pass

if __name__ == "__main__":
    check_normal_range_issues()
