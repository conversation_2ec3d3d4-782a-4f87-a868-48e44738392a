#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量生成93段汇总图脚本
只生成汇总可视化图表，不进行完整的THD+N分析
"""

import os
import sys
import time
import glob
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from adaptive_fundamental_removal import analyze_segment_adaptive_removal
from freq_split_optimized import split_freq_steps_optimized
import soundfile as sf

# 设置matplotlib字体
import matplotlib.font_manager as fm
available_fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = []
common_chinese = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
for font in common_chinese:
    if font in available_fonts:
        chinese_fonts.append(font)

plt.rcParams.update({
    'font.sans-serif': chinese_fonts + ['DejaVu Sans', 'Arial', 'sans-serif'],
    'axes.unicode_minus': False,
    'font.family': 'sans-serif',
    'font.size': 10,
})

def find_audio_files(folder_path):
    """查找文件夹中的所有音频文件"""
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.aiff', '*.m4a']
    audio_files = []
    
    folder_path = Path(folder_path)
    if not folder_path.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in audio_extensions:
        files = list(folder_path.glob(f"**/{ext}"))  # 递归搜索
        audio_files.extend(files)
    
    return sorted(audio_files)

def create_summary_visualization_only(results, output_file):
    """创建93段结果汇总可视化 - 三张图竖向排列"""
    
    # 提取数据
    segments = [r['segment_idx'] for r in results]
    method1_thd_n = [r['thd_n_method1'] for r in results]
    method2_thd_n = [r['thd_n_method2'] for r in results]
    scaling_factors = [r['scaling_factor'] for r in results]
    
    # 创建三张图竖向排列
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 18))
    
    # 1. 方法一THD+N分布
    ax1.bar(segments, method1_thd_n, alpha=0.8, color='red', 
           label='Method 1 (Time Domain Power Subtraction)')
    
    ax1.set_xlabel('Segment Number', fontsize=12)
    ax1.set_ylabel('THD+N (%)', fontsize=12)
    # 检查是否有正值，决定是否使用对数坐标
    if any(val > 0 for val in method1_thd_n):
        ax1.set_yscale('log')
    ax1.set_title('Method 1: THD+N Distribution (93 Segments)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.legend()
    ax1.set_xlim(0, len(segments) + 1)
    
    # 添加统计信息
    method1_avg = np.mean(method1_thd_n)
    method1_min = np.min(method1_thd_n)
    method1_max = np.max(method1_thd_n)
    ax1.text(0.02, 0.98, f'Avg: {method1_avg:.3f}%\nMin: {method1_min:.3f}%\nMax: {method1_max:.3f}%', 
             transform=ax1.transAxes, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # 2. 方法二THD+N分布
    ax2.bar(segments, method2_thd_n, alpha=0.8, color='blue', 
           label='Method 2 (Fundamental Bandwidth Exclusion, ≥100Hz)')
    
    ax2.set_xlabel('Segment Number', fontsize=12)
    ax2.set_ylabel('THD+N (%)', fontsize=12)
    ax2.set_yscale('log')
    ax2.set_title('Method 2: THD+N Distribution (93 Segments, Power ≥100Hz)', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.legend()
    ax2.set_xlim(0, len(segments) + 1)
    
    # 添加统计信息
    method2_avg = np.mean(method2_thd_n)
    method2_min = np.min(method2_thd_n)
    method2_max = np.max(method2_thd_n)
    ax2.text(0.02, 0.98, f'Avg: {method2_avg:.3f}%\nMin: {method2_min:.3f}%\nMax: {method2_max:.3f}%', 
             transform=ax2.transAxes, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 3. 参考音频缩放系数分布
    ax3.bar(segments, scaling_factors, alpha=0.8, color='green', 
           label='Reference Audio Scaling Factor')
    
    ax3.set_xlabel('Segment Number', fontsize=12)
    ax3.set_ylabel('Scaling Factor', fontsize=12)
    ax3.set_title('Reference Audio Scaling Factor Distribution (93 Segments)', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.legend()
    ax3.set_xlim(0, len(segments) + 1)
    
    # 添加统计信息
    scaling_avg = np.mean(scaling_factors)
    scaling_min = np.min(scaling_factors)
    scaling_max = np.max(scaling_factors)
    ax3.text(0.02, 0.98, f'Avg: {scaling_avg:.6f}\nMin: {scaling_min:.6f}\nMax: {scaling_max:.6f}', 
             transform=ax3.transAxes, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 汇总图已保存: {output_file}")

def process_single_audio(audio_file, output_dir):
    """处理单个音频文件，只生成汇总图"""
    print(f"\n🎵 处理: {audio_file.name}")

    try:
        # 读取音频文件
        test_audio, sr = sf.read(str(audio_file))
        if test_audio.ndim > 1:
            test_audio = test_audio[:, 0]  # 取第一声道

        print(f"📊 音频信息: {len(test_audio)} 采样点, 采样率 {sr}Hz")

        # 使用freq_split_optimized进行频段切割
        print("🔍 进行频段切割...")
        step_boundaries, frequency_points = split_freq_steps_optimized(str(audio_file), plot=False)
        
        if not step_boundaries:
            print("❌ 频段切割失败")
            return False
        
        print(f"✅ 检测到 {len(step_boundaries)} 个频段")
        
        # 生成参考信号
        print("🎼 生成参考信号...")
        ref_audio = []
        for freq in frequency_points:
            duration = 1.0  # 每个频段1秒
            t = np.linspace(0, duration, int(sr * duration), endpoint=False)
            ref_segment = np.sin(2 * np.pi * freq * t)
            ref_audio.extend(ref_segment)
        ref_audio = np.array(ref_audio)
        
        # 分析所有频段
        print("🔬 开始分析...")
        results = []
        
        for i in range(len(step_boundaries)):
            test_start_time, test_end_time = step_boundaries[i]
            expected_freq = frequency_points[i]
            
            # 提取测试信号段
            test_start_sample = int(test_start_time * sr)
            test_end_sample = int(test_end_time * sr)
            test_segment = test_audio[test_start_sample:test_end_sample]
            
            # 提取参考信号段
            ref_start_sample = i * int(sr * 1.0)
            ref_end_sample = (i + 1) * int(sr * 1.0)
            ref_segment = ref_audio[ref_start_sample:ref_end_sample]
            
            # 分析该频段
            success, result = analyze_segment_adaptive_removal((
                i + 1, test_start_time, test_end_time,
                test_start_time, test_end_time, expected_freq,
                test_audio, ref_audio, sr, output_dir
            ))
            
            if success and result:
                results.append(result)
                print(f"  ✅ 频段 {i+1}: {expected_freq:.1f}Hz")
            else:
                print(f"  ❌ 频段 {i+1}: {expected_freq:.1f}Hz 分析失败")
        
        if not results:
            print("❌ 没有成功分析的频段")
            return False
        
        # 生成汇总图
        file_stem = audio_file.stem
        summary_image = os.path.join(output_dir, f"{file_stem}_93段汇总图.png")
        create_summary_visualization_only(results, summary_image)
        
        print(f"✅ {audio_file.name} 处理完成，共分析 {len(results)} 个频段")
        return True
        
    except Exception as e:
        print(f"❌ 处理 {audio_file.name} 时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 批量生成93段汇总图工具")
    print("=" * 60)
    
    # 定义要处理的文件夹
    target_folders = ["test20250717", "待定", "test20250722"]
    
    # 创建输出目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    batch_output_dir = f"批量汇总图_{timestamp}"
    os.makedirs(batch_output_dir, exist_ok=True)
    print(f"📁 输出目录: {batch_output_dir}")
    
    # 收集所有音频文件
    all_audio_files = []
    for folder in target_folders:
        print(f"\n📂 扫描文件夹: {folder}")
        audio_files = find_audio_files(folder)
        
        if audio_files:
            print(f"   找到 {len(audio_files)} 个音频文件")
            all_audio_files.extend(audio_files)
        else:
            print(f"   未找到音频文件")
    
    if not all_audio_files:
        print("❌ 未找到任何音频文件")
        return
    
    print(f"\n📊 总共找到 {len(all_audio_files)} 个音频文件")
    
    # 处理所有文件
    successful = 0
    failed = 0
    start_time = time.time()
    
    for i, audio_file in enumerate(all_audio_files, 1):
        print(f"\n[{i}/{len(all_audio_files)}]")
        
        try:
            success = process_single_audio(audio_file, batch_output_dir)
            if success:
                successful += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
            failed += 1
    
    # 总结
    total_time = time.time() - start_time
    print("\n" + "=" * 60)
    print("🎉 批量处理完成!")
    print(f"📊 处理统计:")
    print(f"   总文件数: {len(all_audio_files)}")
    print(f"   成功处理: {successful}")
    print(f"   处理失败: {failed}")
    print(f"   总耗时: {total_time:.1f}秒")
    print(f"📁 输出目录: {batch_output_dir}")
    print("=" * 60)

if __name__ == "__main__":
    main()
