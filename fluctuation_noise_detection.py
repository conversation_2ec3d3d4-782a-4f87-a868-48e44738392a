#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频谱抖动噪声检测脚本
检测频谱后面深蓝色抖动幅度很大的部分
去除开头结尾8%，不加窗
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Deja<PERSON>u Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_spectrum_fluctuation(audio_segment, sr):
    """
    计算频谱并分析抖动幅度
    """
    
    # 标准化
    if np.max(np.abs(audio_segment)) > 0:
        audio_segment = audio_segment / np.max(np.abs(audio_segment))
    
    # 高分辨率FFT，不加窗
    fft_size = 131072
    if len(audio_segment) < fft_size:
        audio_segment = np.pad(audio_segment, (0, fft_size - len(audio_segment)), 'constant')
    else:
        audio_segment = audio_segment[:fft_size]
    
    # 直接FFT，不加窗
    fft_result = np.fft.fft(audio_segment)
    
    # 只取正频率部分
    positive_fft = fft_result[:fft_size//2]
    positive_freqs = np.fft.fftfreq(fft_size, 1/sr)[:fft_size//2]
    
    # 计算功率谱
    power_spectrum = np.abs(positive_fft) ** 2
    power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)
    
    return positive_freqs, power_spectrum_db

def analyze_fluctuation_in_frequency_bands(freqs, power_db, fundamental_freq):
    """
    分析不同频段的抖动幅度
    """
    
    # 定义频段
    bands = [
        ("低频", 100, 500),
        ("中低频", 500, 2000),
        ("中频", 2000, 5000),
        ("中高频", 5000, 10000),
        ("高频", 10000, 20000)
    ]
    
    fluctuation_results = {}
    
    for band_name, low_freq, high_freq in bands:
        # 选择频段
        band_mask = (freqs >= low_freq) & (freqs <= high_freq)
        
        if not np.any(band_mask):
            continue
            
        band_freqs = freqs[band_mask]
        band_powers = power_db[band_mask]
        
        if len(band_powers) < 10:
            continue
        
        # 排除主频和谐波区域
        exclude_mask = np.zeros(len(band_powers), dtype=bool)
        
        # 排除主频±10Hz
        if low_freq <= fundamental_freq <= high_freq:
            main_exclude = (band_freqs >= fundamental_freq - 10) & (band_freqs <= fundamental_freq + 10)
            exclude_mask |= main_exclude
        
        # 排除谐波±10Hz
        for order in range(2, 11):
            harmonic_freq = fundamental_freq * order
            if low_freq <= harmonic_freq <= high_freq:
                harm_exclude = (band_freqs >= harmonic_freq - 10) & (band_freqs <= harmonic_freq + 10)
                exclude_mask |= harm_exclude
        
        # 分析非谐波区域的抖动
        noise_mask = ~exclude_mask
        if np.sum(noise_mask) < 5:
            continue
            
        noise_powers = band_powers[noise_mask]
        noise_freqs = band_freqs[noise_mask]
        
        # 计算抖动特征
        # 1. 标准差（整体抖动）
        fluctuation_std = np.std(noise_powers)
        
        # 2. 相邻点差分的标准差（局部抖动）
        if len(noise_powers) > 1:
            diff_powers = np.diff(noise_powers)
            local_fluctuation = np.std(diff_powers)
        else:
            local_fluctuation = 0
        
        # 3. 峰谷差（最大抖动幅度）
        peak_to_valley = np.max(noise_powers) - np.min(noise_powers)
        
        # 4. 抖动密度（单位频率内的抖动次数）
        if len(diff_powers) > 0:
            # 计算变化方向的次数
            sign_changes = np.sum(np.diff(np.sign(diff_powers)) != 0)
            freq_span = noise_freqs[-1] - noise_freqs[0] if len(noise_freqs) > 1 else 1
            fluctuation_density = sign_changes / freq_span * 1000  # 每kHz的变化次数
        else:
            fluctuation_density = 0
        
        fluctuation_results[band_name] = {
            'freq_range': (low_freq, high_freq),
            'fluctuation_std': fluctuation_std,
            'local_fluctuation': local_fluctuation,
            'peak_to_valley': peak_to_valley,
            'fluctuation_density': fluctuation_density,
            'noise_points': len(noise_powers),
            'mean_power': np.mean(noise_powers),
            'noise_freqs': noise_freqs,
            'noise_powers': noise_powers
        }
    
    return fluctuation_results

def analyze_segment_fluctuation(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的频谱抖动
    """
    
    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage
            
            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration
            
            if trimmed_end_time <= trimmed_start_time:
                print(f"  ⚠️  段{seg_idx}: 修剪后时长不足")
                return None
            
            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
        
        # 检查边界
        if start_sample >= len(y) or end_sample > len(y) or start_sample >= end_sample:
            print(f"  ⚠️  段{seg_idx}: 时间边界超出音频范围")
            return None
        
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"  ⚠️  段{seg_idx}: 音频段为空")
            return None
        
        if len(segment_audio) < 1024:
            print(f"  ⚠️  段{seg_idx}: 音频段太短")
            return None
        
        # 计算频谱
        freqs, power_db = calculate_spectrum_fluctuation(segment_audio, sr)
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = 10**(power_db[search_mask]/10)
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_power_db = power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = np.max(power_db)
        
        # 使用harmonic_detection_system检测谐波
        power_linear = 10**(power_db/10)
        noise_analysis = {
            'global_noise_floor_db': np.percentile(power_db, 25),
            'noise_variation_db': np.max(power_db) - np.min(power_db),
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }
        
        harmonic_analysis = detect_harmonics_for_segment(freqs, power_linear, fundamental_freq, noise_analysis)
        
        # 分析频段抖动
        fluctuation_results = analyze_fluctuation_in_frequency_bands(freqs, power_db, fundamental_freq)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'power_db': power_db,
            'harmonic_analysis': harmonic_analysis,
            'fluctuation_results': fluctuation_results
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None
