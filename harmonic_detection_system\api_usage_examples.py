#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
谐波异常检测器API使用示例
"""

import os
import sys
from harmonic_detector_api import HarmonicDetectorAPI, detect_audio_harmonic_anomaly

def example_basic_usage():
    """基本使用示例"""
    print("🎯 基本使用示例")
    print("="*50)
    
    # 方法1: 使用便捷函数
    audio_path = "../test20250722/鼓膜破裂（复测1.1).wav"
    
    if os.path.exists(audio_path):
        try:
            result = detect_audio_harmonic_anomaly(audio_path)
            print(f"便捷函数检测结果: {result} ({'正常' if result == 1 else '异常'})")
        except Exception as e:
            print(f"检测失败: {e}")
    else:
        print(f"文件不存在: {audio_path}")

def example_class_usage():
    """类使用示例"""
    print("\n🎯 类使用示例")
    print("="*50)
    
    # 创建检测器实例
    detector = HarmonicDetectorAPI(anomaly_threshold=2)
    
    # 测试文件列表
    test_files = [
        "../test20250717/neg/主板隔音eva取消.wav",
        "../test20250717/pos/sd卡/sd1.wav",
        "../test20250717/neg/喇叭eva没贴.wav"
    ]
    
    for audio_path in test_files:
        if os.path.exists(audio_path):
            try:
                # 简单检测
                result = detector.detect(audio_path)
                filename = os.path.basename(audio_path)
                status = "正常" if result == 1 else "异常"
                print(f"📁 {filename}: {result} ({status})")
                
            except Exception as e:
                print(f"❌ {os.path.basename(audio_path)}: 检测失败 - {e}")
        else:
            print(f"❌ 文件不存在: {audio_path}")

def example_detailed_usage():
    """详细信息使用示例"""
    print("\n🎯 详细信息使用示例")
    print("="*50)
    
    # 创建检测器实例
    detector = HarmonicDetectorAPI(anomaly_threshold=2)
    
    audio_path = "../test20250717/neg/主板隔音eva取消.wav"
    
    if os.path.exists(audio_path):
        try:
            # 获取详细检测结果
            details = detector.detect_with_details(audio_path)
            
            print(f"文件: {os.path.basename(audio_path)}")
            print(f"检测结果: {details['result']} ({'正常' if details['result'] == 1 else '异常'})")
            print(f"异常段个数: {details['anomaly_count']}个")
            print(f"异常段阈值: >={details['threshold']}个")
            print(f"总段数: {details['total_segments']}个")
            print(f"异常段比例: {details['anomaly_ratio']:.1%}")
            
            if details['anomaly_segments']:
                print(f"异常段详情:")
                for seg in details['anomaly_segments'][:5]:  # 只显示前5个
                    print(f"  段{seg['seg_idx']} ({seg['expected_freq']:.0f}Hz): "
                          f"{seg['harmonic_count']}个 > {seg['threshold']}个 "
                          f"(超出{seg['excess']}个)")
                if len(details['anomaly_segments']) > 5:
                    print(f"  ... 还有{len(details['anomaly_segments'])-5}个异常段")
            
        except Exception as e:
            print(f"检测失败: {e}")
    else:
        print(f"文件不存在: {audio_path}")

def example_custom_threshold():
    """自定义阈值示例"""
    print("\n🎯 自定义阈值示例")
    print("="*50)
    
    audio_path = "../test20250717/neg/主板隔音eva取消.wav"
    
    if os.path.exists(audio_path):
        # 测试不同阈值
        thresholds = [1, 2, 3, 5]
        
        for threshold in thresholds:
            try:
                detector = HarmonicDetectorAPI(anomaly_threshold=threshold)
                result = detector.detect(audio_path)
                status = "正常" if result == 1 else "异常"
                print(f"阈值={threshold}: {result} ({status})")
                
            except Exception as e:
                print(f"阈值={threshold}: 检测失败 - {e}")
    else:
        print(f"文件不存在: {audio_path}")

def example_batch_detection():
    """批量检测示例"""
    print("\n🎯 批量检测示例")
    print("="*50)
    
    # 创建检测器
    detector = HarmonicDetectorAPI(anomaly_threshold=2)
    
    # 扫描目录
    test_dir = "../test20250717/pos/sd卡"
    
    if os.path.exists(test_dir):
        wav_files = []
        for file in os.listdir(test_dir):
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(test_dir, file))
        
        print(f"检测目录: {test_dir}")
        print(f"找到{len(wav_files)}个wav文件")
        print()
        
        normal_count = 0
        anomaly_count = 0
        
        for wav_file in sorted(wav_files)[:5]:  # 只检测前5个
            try:
                result = detector.detect(wav_file)
                filename = os.path.basename(wav_file)
                status = "正常" if result == 1 else "异常"
                icon = "🟢" if result == 1 else "🔴"
                
                print(f"{icon} {filename}: {status}")
                
                if result == 1:
                    normal_count += 1
                else:
                    anomaly_count += 1
                    
            except Exception as e:
                print(f"❌ {os.path.basename(wav_file)}: 检测失败")
        
        print()
        print(f"统计: {normal_count}个正常, {anomaly_count}个异常")
    else:
        print(f"目录不存在: {test_dir}")

def example_error_handling():
    """错误处理示例"""
    print("\n🎯 错误处理示例")
    print("="*50)
    
    detector = HarmonicDetectorAPI()
    
    # 测试各种错误情况
    test_cases = [
        ("不存在的文件.wav", "文件不存在"),
        ("../README.md", "非音频文件"),
    ]
    
    for file_path, description in test_cases:
        try:
            result = detector.detect(file_path)
            print(f"✅ {description}: {result}")
        except FileNotFoundError as e:
            print(f"❌ {description}: 文件不存在")
        except ValueError as e:
            print(f"❌ {description}: 文件格式错误")
        except Exception as e:
            print(f"❌ {description}: 其他错误 - {str(e)[:50]}...")

def main():
    """主函数 - 运行所有示例"""
    print("🎯 谐波异常检测器API使用示例")
    print("="*60)
    
    # 运行所有示例
    example_basic_usage()
    # example_class_usage()
    # example_detailed_usage()
    # example_custom_threshold()
    # example_batch_detection()
    # example_error_handling()
    
    print("\n✅ 所有示例运行完成!")

if __name__ == "__main__":
    main()
