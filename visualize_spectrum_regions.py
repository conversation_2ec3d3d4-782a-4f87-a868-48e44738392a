#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在频谱图上可视化主频、谐波、噪声区域
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_spectrum_regions():
    """在频谱图上可视化主频、谐波、噪声区域"""
    print("🎨 在频谱图上可视化主频、谐波、噪声区域")
    print("="*70)
    
    # 生成多个测试信号进行演示
    test_cases = [
        {'freq': 1000, 'name': '1kHz信号'},
        {'freq': 500, 'name': '500Hz信号'},
        {'freq': 5000, 'name': '5kHz信号'},
        {'freq': 15000, 'name': '15kHz信号'}
    ]
    
    for case in test_cases:
        print(f"\n🎯 分析 {case['name']} ({case['freq']}Hz)")
        print("-" * 40)
        
        # 生成信号
        signal_data = generate_test_signal(case['freq'])
        
        # 分析频谱
        spectrum_analysis = analyze_spectrum_regions(signal_data)
        
        # 可视化
        create_spectrum_visualization(signal_data, spectrum_analysis, case['name'])

def generate_test_signal(fundamental_freq):
    """生成测试信号"""
    
    fs = 48000
    duration = 1.0
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    # 基频信号
    signal = np.sin(2 * np.pi * fundamental_freq * t)
    
    # 添加谐波 (幅度递减)
    harmonic_amplitudes = [0.4, 0.2, 0.1, 0.05, 0.02]
    for i, amp in enumerate(harmonic_amplitudes):
        harmonic_order = i + 2
        harmonic_freq = fundamental_freq * harmonic_order
        
        if harmonic_freq < fs / 2:
            signal += amp * np.sin(2 * np.pi * harmonic_freq * t)
    
    # 添加白噪声
    noise_level = 0.05
    signal += noise_level * np.random.randn(len(t))
    
    # 添加一些有色噪声 (模拟干扰)
    interference_freqs = [fundamental_freq * 1.5, fundamental_freq * 2.7]
    for interference_freq in interference_freqs:
        if interference_freq < fs / 2:
            interference_amp = 0.03
            signal += interference_amp * np.sin(2 * np.pi * interference_freq * t + 
                                              np.random.randn(len(t)) * 0.1)
    
    return {
        'signal': signal,
        'fs': fs,
        'fundamental_freq': fundamental_freq,
        'duration': duration,
        'time': t
    }

def analyze_spectrum_regions(signal_data):
    """分析频谱区域"""
    
    signal = signal_data['signal']
    fs = signal_data['fs']
    fundamental_freq = signal_data['fundamental_freq']
    
    # 高分辨率FFT
    fft_size = max(16384, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    # 只分析正频率
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    power_db = 10 * np.log10(positive_power + 1e-12)
    
    # 1. 找到主频区域
    fundamental_region = find_fundamental_region(positive_freqs, positive_power, fundamental_freq)
    
    # 2. 找到谐波区域
    harmonic_regions = find_harmonic_regions(positive_freqs, positive_power, fundamental_freq, fs)
    
    # 3. 确定噪声区域
    noise_regions = determine_noise_regions(positive_freqs, fundamental_region, harmonic_regions)
    
    return {
        'freqs': positive_freqs,
        'power_db': power_db,
        'fundamental_region': fundamental_region,
        'harmonic_regions': harmonic_regions,
        'noise_regions': noise_regions,
        'freq_resolution': positive_freqs[1] - positive_freqs[0]
    }

def calculate_adaptive_bandwidth(frequency):
    """计算自适应带宽"""
    if frequency <= 200:
        return 2.0
    elif frequency <= 500:
        return 3.0
    elif frequency <= 1000:
        return 5.0
    elif frequency <= 2000:
        return 8.0
    elif frequency <= 5000:
        return 12.0
    elif frequency <= 10000:
        return 20.0
    else:
        return 30.0

def find_fundamental_region(freqs, power, fundamental_freq):
    """找到主频区域"""
    
    bandwidth = calculate_adaptive_bandwidth(fundamental_freq)
    
    # 搜索范围
    search_start = fundamental_freq - bandwidth
    search_end = fundamental_freq + bandwidth
    search_mask = (freqs >= search_start) & (freqs <= search_end)
    
    if not np.any(search_mask):
        return None
    
    # 找到最大功率点
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    actual_freq = freqs[actual_idx]
    actual_power = power[actual_idx]
    
    return {
        'center_freq': actual_freq,
        'power': actual_power,
        'power_db': 10 * np.log10(actual_power + 1e-12),
        'bandwidth': bandwidth,
        'freq_start': search_start,
        'freq_end': search_end,
        'freq_indices': search_indices,
        'expected_freq': fundamental_freq,
        'freq_error': actual_freq - fundamental_freq
    }

def find_harmonic_regions(freqs, power, fundamental_freq, fs):
    """找到谐波区域"""
    
    harmonics = []
    nyquist_freq = fs / 2
    max_freq_in_spectrum = freqs[-1]
    
    for order in range(2, 20):
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            break
        if expected_harmonic_freq > max_freq_in_spectrum:
            break
        
        # 计算该谐波的带宽
        harmonic_bandwidth = calculate_adaptive_bandwidth(expected_harmonic_freq)
        
        # 搜索范围
        search_start = expected_harmonic_freq - harmonic_bandwidth
        search_end = expected_harmonic_freq + harmonic_bandwidth
        search_mask = (freqs >= search_start) & (freqs <= search_end)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            
            actual_freq = freqs[actual_idx]
            actual_power = power[actual_idx]
            actual_power_db = 10 * np.log10(actual_power + 1e-12)
            
            # 功率阈值检查
            if actual_power_db > -60:  # 只保留显著的谐波
                harmonics.append({
                    'order': order,
                    'center_freq': actual_freq,
                    'power': actual_power,
                    'power_db': actual_power_db,
                    'bandwidth': harmonic_bandwidth,
                    'freq_start': search_start,
                    'freq_end': search_end,
                    'freq_indices': search_indices,
                    'expected_freq': expected_harmonic_freq,
                    'freq_error': actual_freq - expected_harmonic_freq
                })
    
    return harmonics

def determine_noise_regions(freqs, fundamental_region, harmonic_regions):
    """确定噪声区域"""
    
    # 创建噪声掩码 (初始全为True)
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频区域
    if fundamental_region:
        fund_mask = (freqs >= fundamental_region['freq_start']) & (freqs <= fundamental_region['freq_end'])
        noise_mask &= ~fund_mask
    
    # 排除所有谐波区域
    for harmonic in harmonic_regions:
        harm_mask = (freqs >= harmonic['freq_start']) & (freqs <= harmonic['freq_end'])
        noise_mask &= ~harm_mask
    
    # 将连续的噪声区域分组
    noise_regions = []
    if np.any(noise_mask):
        noise_indices = np.where(noise_mask)[0]
        
        # 找到连续区域的边界
        diff_indices = np.diff(noise_indices)
        break_points = np.where(diff_indices > 1)[0]
        
        start_idx = 0
        for break_point in break_points:
            end_idx = break_point
            region_indices = noise_indices[start_idx:end_idx+1]
            
            if len(region_indices) > 0:
                noise_regions.append({
                    'freq_start': freqs[region_indices[0]],
                    'freq_end': freqs[region_indices[-1]],
                    'freq_indices': region_indices,
                    'freq_count': len(region_indices)
                })
            
            start_idx = end_idx + 1
        
        # 处理最后一个区域
        if start_idx < len(noise_indices):
            region_indices = noise_indices[start_idx:]
            if len(region_indices) > 0:
                noise_regions.append({
                    'freq_start': freqs[region_indices[0]],
                    'freq_end': freqs[region_indices[-1]],
                    'freq_indices': region_indices,
                    'freq_count': len(region_indices)
                })
    
    return noise_regions

def create_spectrum_visualization(signal_data, spectrum_analysis, signal_name):
    """创建频谱可视化"""
    
    freqs = spectrum_analysis['freqs']
    power_db = spectrum_analysis['power_db']
    fundamental_region = spectrum_analysis['fundamental_region']
    harmonic_regions = spectrum_analysis['harmonic_regions']
    noise_regions = spectrum_analysis['noise_regions']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 上图: 时域信号
    t = signal_data['time']
    signal = signal_data['signal']
    ax1.plot(t[:2000], signal[:2000], 'b-', linewidth=1)
    ax1.set_title(f'{signal_name} - 时域信号 (前2000个采样点)', fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('幅度')
    ax1.grid(True, alpha=0.3)
    
    # 下图: 频域分析
    ax2.plot(freqs, power_db, 'k-', linewidth=0.8, alpha=0.7, label='完整频谱')
    
    # 可视化主频区域
    if fundamental_region:
        fund_start = fundamental_region['freq_start']
        fund_end = fundamental_region['freq_end']
        fund_center = fundamental_region['center_freq']
        fund_power = fundamental_region['power_db']
        
        # 主频区域背景
        ax2.axvspan(fund_start, fund_end, alpha=0.3, color='red', label='主频区域')
        
        # 主频峰值标记
        ax2.plot(fund_center, fund_power, 'ro', markersize=10, 
                label=f'主频 {fund_center:.1f}Hz ({fund_power:.1f}dB)')
        
        # 主频标注
        ax2.annotate(f'主频\n{fund_center:.1f}Hz\n{fund_power:.1f}dB', 
                    xy=(fund_center, fund_power), xytext=(fund_center, fund_power + 15),
                    ha='center', va='bottom', fontweight='bold', color='red',
                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 可视化谐波区域
    harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, harmonic in enumerate(harmonic_regions[:8]):  # 只显示前8个谐波
        harm_start = harmonic['freq_start']
        harm_end = harmonic['freq_end']
        harm_center = harmonic['center_freq']
        harm_power = harmonic['power_db']
        harm_order = harmonic['order']
        
        color = harmonic_colors[i % len(harmonic_colors)]
        
        # 谐波区域背景
        ax2.axvspan(harm_start, harm_end, alpha=0.2, color=color)
        
        # 谐波峰值标记
        ax2.plot(harm_center, harm_power, 'o', color=color, markersize=8,
                label=f'{harm_order}次谐波 {harm_center:.0f}Hz ({harm_power:.1f}dB)')
        
        # 谐波标注 (只标注前4个主要谐波)
        if i < 4:
            ax2.annotate(f'{harm_order}次\n{harm_center:.0f}Hz', 
                        xy=(harm_center, harm_power), xytext=(harm_center, harm_power + 10),
                        ha='center', va='bottom', fontsize=8, color=color,
                        arrowprops=dict(arrowstyle='->', color=color, lw=1))
    
    # 可视化噪声区域
    for i, noise_region in enumerate(noise_regions):
        noise_start = noise_region['freq_start']
        noise_end = noise_region['freq_end']
        
        # 只显示较大的噪声区域
        if noise_end - noise_start > 50:  # 只显示宽度>50Hz的噪声区域
            ax2.axvspan(noise_start, noise_end, alpha=0.1, color='blue', 
                       label='噪声区域' if i == 0 else "")
    
    # 设置图表属性
    ax2.set_title(f'{signal_name} - 频域分析 (主频/谐波/噪声区域标识)', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('功率 (dB)')
    
    # 设置显示范围
    fundamental_freq = signal_data['fundamental_freq']
    if fundamental_freq <= 1000:
        ax2.set_xlim(0, 5000)
    elif fundamental_freq <= 5000:
        ax2.set_xlim(0, 20000)
    else:
        ax2.set_xlim(0, 24000)
    
    ax2.set_ylim(np.min(power_db) - 5, np.max(power_db) + 20)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'spectrum_regions_{signal_name.replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 可视化已保存: {filename}")
    plt.close()
    
    # 显示分析结果
    display_analysis_summary(fundamental_region, harmonic_regions, noise_regions, signal_name)

def display_analysis_summary(fundamental_region, harmonic_regions, noise_regions, signal_name):
    """显示分析摘要"""
    
    print(f"   📊 {signal_name} 频谱区域分析摘要:")
    
    # 主频信息
    if fundamental_region:
        print(f"     🔴 主频区域:")
        print(f"       频率: {fundamental_region['center_freq']:.2f}Hz "
              f"(期望: {fundamental_region['expected_freq']:.1f}Hz)")
        print(f"       功率: {fundamental_region['power_db']:.2f}dB")
        print(f"       带宽: ±{fundamental_region['bandwidth']:.1f}Hz")
        print(f"       频率误差: {fundamental_region['freq_error']:.2f}Hz")
    
    # 谐波信息
    if harmonic_regions:
        print(f"     🟠 谐波区域 (共{len(harmonic_regions)}个):")
        for i, harmonic in enumerate(harmonic_regions[:5]):  # 只显示前5个
            print(f"       {harmonic['order']}次谐波: {harmonic['center_freq']:.1f}Hz, "
                  f"{harmonic['power_db']:.1f}dB, 带宽±{harmonic['bandwidth']:.1f}Hz")
        if len(harmonic_regions) > 5:
            print(f"       ... 还有 {len(harmonic_regions) - 5} 个谐波")
    else:
        print(f"     ❌ 未检测到明显谐波")
    
    # 噪声信息
    if noise_regions:
        total_noise_bandwidth = sum(r['freq_end'] - r['freq_start'] for r in noise_regions)
        print(f"     🔵 噪声区域 (共{len(noise_regions)}个区域):")
        print(f"       总噪声带宽: {total_noise_bandwidth:.1f}Hz")
        
        # 显示主要噪声区域
        large_noise_regions = [r for r in noise_regions if r['freq_end'] - r['freq_start'] > 100]
        if large_noise_regions:
            print(f"       主要噪声区域:")
            for region in large_noise_regions[:3]:
                print(f"         {region['freq_start']:.0f}-{region['freq_end']:.0f}Hz "
                      f"(宽度: {region['freq_end'] - region['freq_start']:.0f}Hz)")

if __name__ == "__main__":
    visualize_spectrum_regions()
