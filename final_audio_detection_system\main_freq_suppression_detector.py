#!/usr/bin/env python3
"""
主频抑制异常检测器
Main Frequency Suppression Anomaly Detector
去除主频和高能量区域，检测剩余的异常能量
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

class MainFreqSuppressionDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.2,
            'main_freq_suppress_db': 20,        # 主频抑制强度(dB)
            'high_energy_percentile': 90,       # 高能量区域百分位
            'residual_threshold_auto': True,    # 自动检测残留能量阈值
            'min_continuous_points': 3,
            'min_anomalous_segments': 5
        }
        
        print(f"主频抑制异常检测器初始化完成")
        print(f"检测逻辑: 去除主频和高能量区域 → 检测残留异常能量")
    
    def detect_with_main_freq_suppression(self, audio_path):
        """使用主频抑制检测异常"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 使用freq_split分析
            analysis = self._analyze_with_suppression(
                audio_path, Zxx, frequencies, times
            )
            
            # 判断异常
            anomaly_score, anomaly_detected = self._suppression_decision(analysis)
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'analysis': analysis,
                'detection_method': 'main_freq_suppression',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_with_suppression(self, audio_path, Zxx, frequencies, times):
        """使用主频抑制分析"""
        analysis = {
            'freq_split_success': False,
            'total_segments': 0,
            'anomalous_segments': 0,
            'segment_details': [],
            'auto_threshold': None,
            'max_residual_energy': 0,
            'total_residual_points': 0
        }
        
        try:
            # freq_split分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path, **self.freq_split_params, plot=False
            )
            
            if len(step_boundaries) == 0:
                return analysis
            
            analysis['freq_split_success'] = True
            analysis['total_segments'] = len(step_boundaries)
            
            # 自动检测残留能量阈值
            auto_threshold = self._auto_detect_residual_threshold(Zxx, step_boundaries, times)
            analysis['auto_threshold'] = auto_threshold
            
            print(f"{os.path.basename(audio_path)}: 自动检测阈值 = {auto_threshold:.1f}dB")
            
            # 分析每个频段
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                segment_result = self._analyze_segment_suppression(
                    Zxx, frequencies, times, seg_start_time, seg_end_time, 
                    seg_idx, freq_table[seg_idx] if seg_idx < len(freq_table) else None,
                    auto_threshold
                )
                
                analysis['segment_details'].append(segment_result)
                
                if segment_result['has_anomaly']:
                    analysis['anomalous_segments'] += 1
                    analysis['max_residual_energy'] = max(
                        analysis['max_residual_energy'],
                        segment_result['max_residual_energy']
                    )
                    analysis['total_residual_points'] += segment_result['residual_points']
            
            print(f"  异常频段: {analysis['anomalous_segments']}/{analysis['total_segments']}")
            print(f"  最大残留能量: {analysis['max_residual_energy']:.1f}dB")
            
        except Exception as e:
            print(f"主频抑制分析失败: {e}")
        
        return analysis
    
    def _auto_detect_residual_threshold(self, Zxx, step_boundaries, times):
        """自动检测残留能量阈值"""
        all_residual_energies = []
        
        # 分析几个频段来估计正常的残留能量水平
        sample_segments = step_boundaries[:min(10, len(step_boundaries))]
        
        for seg_start_time, seg_end_time in sample_segments:
            # 找到时间范围
            seg_start_idx = np.argmin(np.abs(times - seg_start_time))
            seg_end_idx = np.argmin(np.abs(times - seg_end_time))
            
            # 排除边界
            edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
            seg_core_start = seg_start_idx + edge_exclude
            seg_core_end = seg_end_idx - edge_exclude
            
            if seg_core_end <= seg_core_start:
                continue
            
            # 提取频段数据
            segment_spectrum = Zxx[:, seg_core_start:seg_core_end]
            
            for t_idx in range(segment_spectrum.shape[1]):
                time_spectrum = segment_spectrum[:, t_idx]
                
                # 去除主频和高能量区域
                residual_spectrum = self._suppress_main_frequencies(time_spectrum)
                
                # 转换为dB
                residual_db = 20 * np.log10(np.abs(residual_spectrum) + 1e-12)
                all_residual_energies.extend(residual_db)
        
        if all_residual_energies:
            all_residual_energies = np.array(all_residual_energies)
            # 使用95%分位数作为阈值，检测异常高的残留能量
            threshold = np.percentile(all_residual_energies, 95)
            return threshold
        else:
            return -60  # 默认阈值
    
    def _suppress_main_frequencies(self, spectrum):
        """抑制主频和高能量区域"""
        # 计算功率谱
        power_spectrum = np.abs(spectrum) ** 2

        # 找到主频（最大能量点）
        main_freq_idx = np.argmax(power_spectrum)

        # 找到高能量区域（90%分位数以上）
        high_energy_threshold = np.percentile(power_spectrum, self.detection_params['high_energy_percentile'])

        # 创建抑制后的频谱 - 只保留低能量部分
        suppressed_spectrum = spectrum.copy()

        # 方法1: 完全去除高能量区域，只保留低能量部分
        high_energy_mask = power_spectrum > high_energy_threshold
        suppressed_spectrum[high_energy_mask] = 0  # 完全置零而非衰减

        return suppressed_spectrum
    
    def _analyze_segment_suppression(self, Zxx, frequencies, times, seg_start_time, seg_end_time, 
                                   seg_idx, expected_freq, residual_threshold):
        """分析单个频段的主频抑制后异常"""
        result = {
            'segment_index': seg_idx,
            'expected_frequency': expected_freq,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'has_anomaly': False,
            'max_residual_energy': -np.inf,
            'residual_points': 0,
            'anomaly_details': []
        }
        
        # 找到时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            return result
        
        # 分析每个时间片
        segment_spectrum = Zxx[:, seg_core_start:seg_core_end]
        
        for t_idx in range(segment_spectrum.shape[1]):
            time_spectrum = segment_spectrum[:, t_idx]
            
            # 抑制主频和高能量区域
            residual_spectrum = self._suppress_main_frequencies(time_spectrum)
            
            # 转换为dB
            residual_db = 20 * np.log10(np.abs(residual_spectrum) + 1e-12)
            
            # 检测高于阈值的残留能量
            # 只考虑非零的残留能量
            non_zero_mask = np.abs(residual_spectrum) > 1e-12
            above_threshold = (residual_db > residual_threshold) & non_zero_mask
            
            # 查找连续的异常点
            continuous_groups = self._find_continuous_groups(above_threshold)
            
            for group in continuous_groups:
                if len(group) >= self.detection_params['min_continuous_points']:
                    result['has_anomaly'] = True
                    result['residual_points'] += len(group)
                    
                    max_energy = np.max(residual_db[group])
                    result['max_residual_energy'] = max(result['max_residual_energy'], max_energy)
                    
                    # 记录异常详情
                    freq_start = frequencies[group[0]]
                    freq_end = frequencies[group[-1]]
                    
                    result['anomaly_details'].append({
                        'time_idx': t_idx,
                        'frequency_range': (freq_start, freq_end),
                        'continuous_points': len(group),
                        'max_residual_energy': max_energy
                    })
        
        return result
    
    def _find_continuous_groups(self, above_threshold_mask):
        """查找连续的频点组"""
        groups = []
        current_group = []
        
        for i, is_above in enumerate(above_threshold_mask):
            if is_above:
                current_group.append(i)
            else:
                if len(current_group) > 0:
                    groups.append(current_group)
                    current_group = []
        
        if len(current_group) > 0:
            groups.append(current_group)
        
        return groups
    
    def _suppression_decision(self, analysis):
        """基于主频抑制结果判断异常"""
        if not analysis['freq_split_success']:
            return 0.0, False
        
        anomalous_segments = analysis['anomalous_segments']
        total_segments = analysis['total_segments']
        max_residual_energy = analysis['max_residual_energy']
        total_residual_points = analysis['total_residual_points']
        
        if anomalous_segments == 0:
            return 0.0, False
        
        # 计算异常分数
        segment_ratio = anomalous_segments / total_segments
        energy_score = min(1.0, (max_residual_energy + 60) / 20.0)  # 假设-60dB为基线
        points_score = min(1.0, total_residual_points / 100.0)
        
        # 加权计算最终分数
        anomaly_score = (
            segment_ratio * 0.4 +
            energy_score * 0.4 +
            points_score * 0.2
        )
        
        # 判断是否异常
        anomaly_detected = (
            anomalous_segments >= self.detection_params['min_anomalous_segments'] and
            anomaly_score > 0.3
        )
        
        return anomaly_score, anomaly_detected
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'analysis': {},
            'detection_method': 'main_freq_suppression',
            'error': True,
            'error_message': error_msg
        }
    
    def test_all_samples(self):
        """测试所有样本"""
        print("\n" + "="*80)
        print("主频抑制异常检测")
        print("="*80)
        
        # 定义样本分类
        problematic_samples = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav"
        ]
        
        other_negative_samples = [
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        # 收集正样本
        positive_samples = []
        pos_dir = "../test20250717/pos"
        if os.path.exists(pos_dir):
            for root, dirs, files in os.walk(pos_dir):
                for file in files:
                    if file.endswith('.wav'):
                        positive_samples.append(os.path.join(root, file))
        
        all_samples = problematic_samples + other_negative_samples + positive_samples[:5]
        
        print(f"问题样本: {len(problematic_samples)}个")
        print(f"其他负样本: {len(other_negative_samples)}个")
        print(f"正样本: {len(positive_samples[:5])}个")
        
        results = []
        
        for sample in all_samples:
            if os.path.exists(sample):
                print(f"\n测试文件: {os.path.basename(sample)}")
                print("-" * 50)
                
                result = self.detect_with_main_freq_suppression(sample)
                result['filename'] = os.path.basename(sample)
                
                # 标记样本类型
                if sample in problematic_samples:
                    result['sample_type'] = 'problematic'
                elif sample in other_negative_samples:
                    result['sample_type'] = 'other_negative'
                else:
                    result['sample_type'] = 'positive'
                
                results.append(result)
                
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    
                    analysis = result['analysis']
                    print(f"异常频段: {analysis.get('anomalous_segments', 0)}/{analysis.get('total_segments', 0)}")
                    print(f"自动阈值: {analysis.get('auto_threshold', 0):.1f}dB")
                    print(f"最大残留能量: {analysis.get('max_residual_energy', 0):.1f}dB")
                    print(f"残留异常点: {analysis.get('total_residual_points', 0)}")
        
        # 分析结果
        self._analyze_discrimination_results(results)
        
        return results
    
    def _analyze_discrimination_results(self, results):
        """分析区分效果"""
        print(f"\n" + "="*60)
        print(f"区分效果分析:")
        print("="*60)
        
        # 按类型分组
        problematic = [r for r in results if r.get('sample_type') == 'problematic' and not r['error']]
        other_negative = [r for r in results if r.get('sample_type') == 'other_negative' and not r['error']]
        positive = [r for r in results if r.get('sample_type') == 'positive' and not r['error']]
        
        # 计算各组统计
        def calc_stats(group, name):
            if not group:
                print(f"{name}: 无有效样本")
                return
            
            anomaly_scores = [r['anomaly_score'] for r in group]
            detection_rate = sum(1 for r in group if r['anomaly_detected']) / len(group) * 100
            
            print(f"{name}:")
            print(f"  检出率: {detection_rate:.1f}%")
            print(f"  平均异常分数: {np.mean(anomaly_scores):.3f} ± {np.std(anomaly_scores):.3f}")
            
            for r in group:
                status = "异常" if r['anomaly_detected'] else "正常"
                print(f"    {r['filename']}: {status} ({r['anomaly_score']:.3f})")
        
        calc_stats(problematic, "问题样本")
        calc_stats(other_negative, "其他负样本")
        calc_stats(positive, "正样本")

def main():
    """主函数"""
    detector = MainFreqSuppressionDetector()
    results = detector.test_all_samples()
    
    # 保存结果
    results_data = []
    for result in results:
        if not result['error']:
            analysis = result['analysis']
            row = {
                'filename': result['filename'],
                'sample_type': result.get('sample_type', 'unknown'),
                'anomaly_detected': result['anomaly_detected'],
                'confidence': result['confidence'],
                'anomaly_score': result['anomaly_score'],
                'auto_threshold': analysis.get('auto_threshold', 0),
                'anomalous_segments': analysis.get('anomalous_segments', 0),
                'total_segments': analysis.get('total_segments', 0),
                'max_residual_energy': analysis.get('max_residual_energy', 0),
                'total_residual_points': analysis.get('total_residual_points', 0),
                'error': result['error']
            }
            results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('main_freq_suppression_results.csv', index=False)
    
    print(f"\n主频抑制检测结果已保存: main_freq_suppression_results.csv")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
