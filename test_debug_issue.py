#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试调试信息问题
"""

import sys
import os
sys.path.append('.')

# 直接导入并测试
from freq_split_optimized import OptimizedFreqSplitter

def test_debug():
    print("🔍 测试调试信息问题")
    
    # 创建分割器
    splitter = OptimizedFreqSplitter()
    
    # 测试音频路径
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    print(f"测试文件: {audio_path}")
    print(f"文件存在: {os.path.exists(audio_path)}")
    
    # 调用分割函数
    try:
        result = splitter.split_freq_steps_with_alignment(
            audio_path, 
            search_window_start=0.1,
            search_window_end=1.5,
            correlation_length=1.0,
            plot=False,  # 不绘图，专注于调试信息
            debug=True
        )
        print(f"✅ 函数调用成功")
        print(f"结果类型: {type(result)}")
        if result:
            print(f"结果长度: {len(result)}")
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_debug()
