#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门为两个噪声样本生成93个频段的噪声特征可视化
重点展示这两个样本在所有频段的特征分布
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_target_samples_93_segments():
    """为两个噪声样本生成93个频段的特征可视化"""
    print("🎨 为两个噪声样本生成93个频段特征可视化")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 检查数据文件
    if os.path.exists('top4_features_93_segments.csv'):
        print("📊 加载93个频段数据...")
        df = pd.read_csv('top4_features_93_segments.csv')
    else:
        print("❌ 未找到93个频段数据文件")
        return None
    
    # 筛选目标样本数据
    target_data = df[df['is_target'] == True].copy()
    normal_data = df[df['is_target'] == False].copy()
    
    print(f"📊 数据概览:")
    print(f"   噪声样本记录数: {len(target_data)}")
    print(f"   正常样本记录数: {len(normal_data)}")
    print(f"   频段数: {len(target_data['segment_idx'].unique())}")
    
    # 前四个特征
    top4_features = [
        'true_noise_floor_median',
        'true_noise_floor_mean', 
        'noise_floor_stability_mean',
        'noise_floor_stability_std'
    ]
    
    feature_titles = [
        '真实底噪中位数 (dB)',
        '真实底噪平均值 (dB)',
        '底噪稳定性平均值',
        '底噪稳定性标准差'
    ]
    
    # 生成可视化
    create_target_samples_visualization(target_data, normal_data, top4_features, feature_titles, target_files)
    
    # 生成详细分析
    analyze_target_samples_details(target_data, normal_data, top4_features, target_files)
    
    return target_data

def create_target_samples_visualization(target_data, normal_data, features, feature_titles, target_files):
    """创建目标样本的可视化"""
    print(f"\n🎨 生成目标样本93个频段可视化...")
    
    # 准备数据
    segments = sorted(target_data['segment_idx'].unique())
    frequencies = [target_data[target_data['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    # 创建大图 - 2x3布局
    fig = plt.figure(figsize=(24, 16))
    
    # 1. 两个样本的特征对比线图 (2x2)
    for i, (feature, title) in enumerate(zip(features, feature_titles)):
        ax = plt.subplot(3, 2, i+1)
        plot_target_samples_comparison(ax, target_data, normal_data, feature, title, segments, frequencies, target_files)
    
    # 5. 两个样本的特征热力图
    ax5 = plt.subplot(3, 2, 5)
    plot_target_samples_heatmap(ax5, target_data, features, segments, frequencies, target_files)
    
    # 6. 两个样本与正常样本的差异分析
    ax6 = plt.subplot(3, 2, 6)
    plot_difference_analysis(ax6, target_data, normal_data, features, segments, frequencies)
    
    plt.suptitle('两个噪声样本93个频段特征详细分析', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('target_samples_93_segments_analysis.png', dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存: target_samples_93_segments_analysis.png")
    plt.close()

def plot_target_samples_comparison(ax, target_data, normal_data, feature, title, segments, frequencies, target_files):
    """绘制两个目标样本的特征对比 - 重点显示正常样本范围"""

    # 分别获取两个样本的数据
    sample1_data = target_data[target_data['filename'] == target_files[0]]
    sample2_data = target_data[target_data['filename'] == target_files[1]]

    # 计算正常样本的详细统计值
    normal_means = []
    normal_stds = []
    normal_mins = []
    normal_maxs = []
    normal_p25 = []
    normal_p75 = []
    sample1_values = []
    sample2_values = []

    for seg_idx in segments:
        # 正常样本统计
        normal_seg_data = normal_data[normal_data['segment_idx'] == seg_idx][feature]
        if len(normal_seg_data) > 0:
            normal_means.append(np.mean(normal_seg_data))
            normal_stds.append(np.std(normal_seg_data))
            normal_mins.append(np.min(normal_seg_data))
            normal_maxs.append(np.max(normal_seg_data))
            normal_p25.append(np.percentile(normal_seg_data, 25))
            normal_p75.append(np.percentile(normal_seg_data, 75))
        else:
            normal_means.append(np.nan)
            normal_stds.append(np.nan)
            normal_mins.append(np.nan)
            normal_maxs.append(np.nan)
            normal_p25.append(np.nan)
            normal_p75.append(np.nan)

        # 目标样本值
        sample1_seg = sample1_data[sample1_data['segment_idx'] == seg_idx][feature]
        sample2_seg = sample2_data[sample2_data['segment_idx'] == seg_idx][feature]

        sample1_values.append(sample1_seg.iloc[0] if len(sample1_seg) > 0 else np.nan)
        sample2_values.append(sample2_seg.iloc[0] if len(sample2_seg) > 0 else np.nan)

    # 转换为数组
    normal_means = np.array(normal_means)
    normal_stds = np.array(normal_stds)
    normal_mins = np.array(normal_mins)
    normal_maxs = np.array(normal_maxs)
    normal_p25 = np.array(normal_p25)
    normal_p75 = np.array(normal_p75)
    sample1_values = np.array(sample1_values)
    sample2_values = np.array(sample2_values)

    x = np.array(segments)

    # 绘制正常样本的范围区间
    # 1. 最大最小值范围 (最浅色)
    ax.fill_between(x, normal_mins, normal_maxs,
                   alpha=0.15, color='lightblue', label='正常样本 [最小值, 最大值]')

    # 2. 25%-75%分位数范围 (中等色)
    ax.fill_between(x, normal_p25, normal_p75,
                   alpha=0.3, color='blue', label='正常样本 [25%, 75%分位数]')

    # 3. 均值±1标准差范围 (较深色)
    ax.fill_between(x, normal_means - normal_stds, normal_means + normal_stds,
                   alpha=0.4, color='darkblue', label='正常样本 [均值±1σ]')

    # 4. 均值线
    ax.plot(x, normal_means, 'b-', linewidth=2, alpha=0.8, label='正常样本均值')

    # 绘制两个目标样本
    ax.plot(x, sample1_values, 'r-', linewidth=3, marker='o', markersize=4,
           label='样本1 (153632)', alpha=0.9, zorder=5)
    ax.plot(x, sample2_values, 'orange', linewidth=3, marker='s', markersize=4,
           label='样本2 (低音戳洞)', alpha=0.9, zorder=5)

    # 标记超出正常范围的点
    # 超出最大最小值范围
    extreme_mask1 = (sample1_values > normal_maxs) | (sample1_values < normal_mins)
    extreme_mask2 = (sample2_values > normal_maxs) | (sample2_values < normal_mins)

    # 超出±2σ范围
    sigma2_mask1 = (sample1_values > normal_means + 2*normal_stds) | (sample1_values < normal_means - 2*normal_stds)
    sigma2_mask2 = (sample2_values > normal_means + 2*normal_stds) | (sample2_values < normal_means - 2*normal_stds)

    if np.any(extreme_mask1):
        ax.scatter(x[extreme_mask1], sample1_values[extreme_mask1],
                  color='darkred', s=80, marker='*', zorder=6,
                  label='样本1超出正常范围', edgecolor='black', linewidth=1)

    if np.any(extreme_mask2):
        ax.scatter(x[extreme_mask2], sample2_values[extreme_mask2],
                  color='darkorange', s=80, marker='*', zorder=6,
                  label='样本2超出正常范围', edgecolor='black', linewidth=1)

    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('特征值')
    ax.legend(fontsize=7, loc='upper right')
    ax.grid(True, alpha=0.3)

    # 添加频率标签
    freq_ticks = range(0, len(segments), 20)
    ax2 = ax.twiny()
    ax2.set_xlim(ax.get_xlim())
    ax2.set_xticks([segments[i] for i in freq_ticks if i < len(segments)])
    ax2.set_xticklabels([f'{frequencies[i]:.0f}Hz' for i in freq_ticks if i < len(frequencies)],
                       rotation=45)

    # 添加统计信息
    extreme_count1 = np.sum(extreme_mask1)
    extreme_count2 = np.sum(extreme_mask2)
    sigma2_count1 = np.sum(sigma2_mask1)
    sigma2_count2 = np.sum(sigma2_mask2)
    total_segments = len(segments)

    stats_text = f'超出正常范围:\n样本1: {extreme_count1}/{total_segments} ({extreme_count1/total_segments*100:.1f}%)\n'
    stats_text += f'样本2: {extreme_count2}/{total_segments} ({extreme_count2/total_segments*100:.1f}%)\n'
    stats_text += f'超出±2σ:\n样本1: {sigma2_count1}/{total_segments} ({sigma2_count1/total_segments*100:.1f}%)\n'
    stats_text += f'样本2: {sigma2_count2}/{total_segments} ({sigma2_count2/total_segments*100:.1f}%)'

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8), fontsize=7)

def plot_target_samples_heatmap(ax, target_data, features, segments, frequencies, target_files):
    """绘制两个目标样本的特征热力图"""
    
    # 准备热力图数据
    heatmap_data = []
    
    for filename in target_files:
        sample_data = target_data[target_data['filename'] == filename]
        row_data = []
        
        for seg_idx in segments:
            seg_data = sample_data[sample_data['segment_idx'] == seg_idx]
            if len(seg_data) > 0:
                # 计算该频段的综合特征值 (标准化后求和)
                feature_values = []
                for feature in features:
                    value = seg_data[feature].iloc[0]
                    feature_values.append(value)
                
                # 简单求和作为综合指标
                combined_value = np.sum(feature_values)
                row_data.append(combined_value)
            else:
                row_data.append(0)
        
        heatmap_data.append(row_data)
    
    # 绘制热力图
    heatmap_data = np.array(heatmap_data)
    
    im = ax.imshow(heatmap_data, cmap='RdYlBu_r', aspect='auto', interpolation='nearest')
    
    # 设置标签
    ax.set_title('两个样本综合特征热力图', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('样本')
    
    # 设置y轴标签
    sample_labels = ['样本1 (153632)', '样本2 (低音戳洞)']
    ax.set_yticks(range(len(sample_labels)))
    ax.set_yticklabels(sample_labels)
    
    # 设置x轴标签
    x_ticks = range(0, len(segments), 10)
    ax.set_xticks(x_ticks)
    ax.set_xticklabels([f'Seg{segments[i]}' for i in x_ticks if i < len(segments)], rotation=45)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('综合特征值', rotation=270, labelpad=15)

def plot_difference_analysis(ax, target_data, normal_data, features, segments, frequencies):
    """绘制差异分析"""
    
    # 计算每个频段的差异程度
    difference_scores = []
    
    for seg_idx in segments:
        target_seg_data = target_data[target_data['segment_idx'] == seg_idx]
        normal_seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
        
        if len(target_seg_data) > 0 and len(normal_seg_data) > 0:
            # 计算每个特征的差异
            feature_diffs = []
            
            for feature in features:
                target_mean = np.mean(target_seg_data[feature])
                normal_mean = np.mean(normal_seg_data[feature])
                normal_std = np.std(normal_seg_data[feature])
                
                # 标准化差异 (Z-score)
                if normal_std > 0:
                    z_score = abs(target_mean - normal_mean) / normal_std
                    feature_diffs.append(z_score)
                else:
                    feature_diffs.append(0)
            
            # 综合差异分数
            combined_diff = np.mean(feature_diffs)
            difference_scores.append(combined_diff)
        else:
            difference_scores.append(0)
    
    # 绘制差异分数
    bars = ax.bar(segments, difference_scores, color='red', alpha=0.7)
    
    # 标记显著差异的频段 (Z-score > 2)
    significant_mask = np.array(difference_scores) > 2
    if np.any(significant_mask):
        significant_segments = np.array(segments)[significant_mask]
        significant_scores = np.array(difference_scores)[significant_mask]
        ax.scatter(significant_segments, significant_scores, color='darkred', s=50, marker='*', zorder=5)
    
    ax.set_title('各频段差异程度分析 (Z-score)', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('差异分数 (Z-score)')
    ax.grid(True, alpha=0.3)
    
    # 添加显著性线
    ax.axhline(y=2, color='orange', linestyle='--', alpha=0.7, label='显著差异线 (Z=2)')
    ax.axhline(y=3, color='red', linestyle='--', alpha=0.7, label='极显著差异线 (Z=3)')
    ax.legend()
    
    # 添加统计信息
    significant_count = np.sum(significant_mask)
    total_count = len(segments)
    max_diff = np.max(difference_scores) if difference_scores else 0
    
    stats_text = f'显著差异频段: {significant_count}/{total_count} ({significant_count/total_count*100:.1f}%)\n'
    stats_text += f'最大差异分数: {max_diff:.2f}'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7), fontsize=9)

def analyze_target_samples_details(target_data, normal_data, features, target_files):
    """分析目标样本的详细信息"""
    print(f"\n📊 两个噪声样本详细分析")
    print("="*70)
    
    segments = sorted(target_data['segment_idx'].unique())
    frequencies = [target_data[target_data['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    # 分析每个样本
    for i, filename in enumerate(target_files):
        print(f"\n🎯 样本 {i+1}: {filename}")
        print("-" * 50)
        
        sample_data = target_data[target_data['filename'] == filename]
        
        # 计算每个特征的异常频段
        for feature in features:
            print(f"\n📈 {feature}:")
            
            anomaly_segments = []
            
            for seg_idx in segments:
                sample_seg_data = sample_data[sample_data['segment_idx'] == seg_idx]
                normal_seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
                
                if len(sample_seg_data) > 0 and len(normal_seg_data) > 0:
                    sample_value = sample_seg_data[feature].iloc[0]
                    normal_mean = np.mean(normal_seg_data[feature])
                    normal_std = np.std(normal_seg_data[feature])
                    
                    # 检查是否异常 (超出2σ)
                    if abs(sample_value - normal_mean) > 2 * normal_std:
                        z_score = (sample_value - normal_mean) / normal_std
                        anomaly_segments.append({
                            'segment': seg_idx,
                            'frequency': frequencies[seg_idx],
                            'value': sample_value,
                            'normal_mean': normal_mean,
                            'z_score': z_score
                        })
            
            # 排序并显示
            anomaly_segments.sort(key=lambda x: abs(x['z_score']), reverse=True)
            
            print(f"   异常频段数: {len(anomaly_segments)}/{len(segments)} ({len(anomaly_segments)/len(segments)*100:.1f}%)")
            
            if len(anomaly_segments) > 0:
                print(f"   前5个最异常频段:")
                for j, seg_info in enumerate(anomaly_segments[:5]):
                    print(f"     {j+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): "
                          f"Z={seg_info['z_score']:+.2f}, 值={seg_info['value']:.3f}")
    
    # 两个样本的对比分析
    print(f"\n🔄 两个样本对比分析")
    print("-" * 50)
    
    sample1_data = target_data[target_data['filename'] == target_files[0]]
    sample2_data = target_data[target_data['filename'] == target_files[1]]
    
    for feature in features:
        print(f"\n📊 {feature}:")
        
        sample1_values = []
        sample2_values = []
        
        for seg_idx in segments:
            s1_seg = sample1_data[sample1_data['segment_idx'] == seg_idx]
            s2_seg = sample2_data[sample2_data['segment_idx'] == seg_idx]
            
            if len(s1_seg) > 0 and len(s2_seg) > 0:
                sample1_values.append(s1_seg[feature].iloc[0])
                sample2_values.append(s2_seg[feature].iloc[0])
        
        if len(sample1_values) > 0 and len(sample2_values) > 0:
            sample1_mean = np.mean(sample1_values)
            sample2_mean = np.mean(sample2_values)
            correlation = np.corrcoef(sample1_values, sample2_values)[0, 1]
            
            print(f"   样本1均值: {sample1_mean:.6f}")
            print(f"   样本2均值: {sample2_mean:.6f}")
            print(f"   差异: {sample2_mean - sample1_mean:+.6f}")
            print(f"   相关性: {correlation:.3f}")

if __name__ == "__main__":
    target_data = visualize_target_samples_93_segments()
    if target_data is not None:
        print(f"\n✅ 两个噪声样本93个频段可视化完成！")
    else:
        print(f"\n❌ 可视化失败，请先运行93个频段数据提取！")
