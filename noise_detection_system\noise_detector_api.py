#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
噪声检测器API
规范化的噪声特征分析接口
"""

import os
import sys
import numpy as np

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from noise_analysis_93segments import (
    analyze_audio_noise_93segments,
    create_noise_analysis_visualization,
    save_noise_analysis_report,
    print_noise_statistics
)

class NoiseDetectorAPI:
    """噪声检测器API类"""
    
    def __init__(self):
        """初始化噪声检测器"""
        pass
    
    def analyze(self, audio_path):
        """
        分析音频文件的噪声特征
        
        Args:
            audio_path (str): 音频文件路径
            
        Returns:
            dict: 噪声分析结果
            {
                'success': bool,           # 分析是否成功
                'filename': str,           # 文件名
                'total_segments': int,     # 总段数
                'successful_segments': int, # 成功分析段数
                'noise_summary': dict,     # 噪声特征汇总
                'frequency_analysis': dict # 频段分析结果
            }
            
        Raises:
            FileNotFoundError: 音频文件不存在
            Exception: 分析过程中的错误
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            # 执行噪声分析
            analysis_result = analyze_audio_noise_93segments(audio_path)
            
            if not analysis_result or not analysis_result['noise_results']:
                return {
                    'success': False,
                    'error': '噪声分析失败或无有效结果'
                }
            
            # 提取噪声特征
            noise_results = analysis_result['noise_results']
            
            # 计算汇总统计
            noise_floors = [r['global_noise_floor_db'] for r in noise_results]
            snr_values = [r['snr_db'] for r in noise_results]
            noise_variations = [r['noise_variation_db'] for r in noise_results]
            stability_scores = [r['noise_fluctuation_features']['fluctuation_stability_score'] for r in noise_results]
            
            # 噪声特征汇总
            noise_summary = {
                'noise_floor': {
                    'mean': float(np.mean(noise_floors)),
                    'std': float(np.std(noise_floors)),
                    'min': float(np.min(noise_floors)),
                    'max': float(np.max(noise_floors))
                },
                'snr': {
                    'mean': float(np.mean(snr_values)),
                    'std': float(np.std(snr_values)),
                    'min': float(np.min(snr_values)),
                    'max': float(np.max(snr_values)),
                    'excellent_count': int(sum(1 for snr in snr_values if snr > 20)),
                    'good_count': int(sum(1 for snr in snr_values if snr > 10))
                },
                'noise_variation': {
                    'mean': float(np.mean(noise_variations)),
                    'std': float(np.std(noise_variations)),
                    'min': float(np.min(noise_variations)),
                    'max': float(np.max(noise_variations))
                },
                'stability': {
                    'mean': float(np.mean(stability_scores)),
                    'std': float(np.std(stability_scores)),
                    'min': float(np.min(stability_scores)),
                    'max': float(np.max(stability_scores)),
                    'high_stability_count': int(sum(1 for s in stability_scores if s > 0.8)),
                    'medium_stability_count': int(sum(1 for s in stability_scores if s > 0.6))
                }
            }
            
            # 频段分析
            low_freq_results = [r for r in noise_results if r['expected_freq'] <= 500]
            mid_freq_results = [r for r in noise_results if 500 < r['expected_freq'] <= 5000]
            high_freq_results = [r for r in noise_results if r['expected_freq'] > 5000]
            
            frequency_analysis = {}
            
            if low_freq_results:
                low_noise_floors = [r['global_noise_floor_db'] for r in low_freq_results]
                low_snr = [r['snr_db'] for r in low_freq_results]
                frequency_analysis['low_freq'] = {
                    'count': len(low_freq_results),
                    'freq_range': '≤500Hz',
                    'avg_noise_floor': float(np.mean(low_noise_floors)),
                    'avg_snr': float(np.mean(low_snr))
                }
            
            if mid_freq_results:
                mid_noise_floors = [r['global_noise_floor_db'] for r in mid_freq_results]
                mid_snr = [r['snr_db'] for r in mid_freq_results]
                frequency_analysis['mid_freq'] = {
                    'count': len(mid_freq_results),
                    'freq_range': '500-5000Hz',
                    'avg_noise_floor': float(np.mean(mid_noise_floors)),
                    'avg_snr': float(np.mean(mid_snr))
                }
            
            if high_freq_results:
                high_noise_floors = [r['global_noise_floor_db'] for r in high_freq_results]
                high_snr = [r['snr_db'] for r in high_freq_results]
                frequency_analysis['high_freq'] = {
                    'count': len(high_freq_results),
                    'freq_range': '>5000Hz',
                    'avg_noise_floor': float(np.mean(high_noise_floors)),
                    'avg_snr': float(np.mean(high_snr))
                }
            
            return {
                'success': True,
                'filename': analysis_result['filename'],
                'total_segments': analysis_result['total_segments'],
                'successful_segments': analysis_result['successful_segments'],
                'noise_summary': noise_summary,
                'frequency_analysis': frequency_analysis,
                '_raw_results': analysis_result  # 原始结果，供高级用户使用
            }
            
        except Exception as e:
            raise Exception(f"噪声分析过程出错: {str(e)}")
    
    def analyze_with_visualization(self, audio_path, save_report=True):
        """
        分析音频并生成可视化
        
        Args:
            audio_path (str): 音频文件路径
            save_report (bool): 是否保存详细报告
            
        Returns:
            dict: 包含分析结果和文件路径的字典
        """
        try:
            # 执行分析
            result = self.analyze(audio_path)
            
            if not result['success']:
                return result
            
            # 获取原始分析结果
            analysis_result = result['_raw_results']
            
            # 生成可视化
            viz_path = create_noise_analysis_visualization(analysis_result)
            result['visualization_path'] = viz_path
            
            # 保存报告
            if save_report:
                report_path = save_noise_analysis_report(analysis_result)
                result['report_path'] = report_path
            
            # 移除原始结果以减少返回数据量
            del result['_raw_results']
            
            return result
            
        except Exception as e:
            raise Exception(f"分析和可视化过程出错: {str(e)}")
    
    def get_noise_quality_assessment(self, analysis_result):
        """
        获取噪声质量评估
        
        Args:
            analysis_result (dict): 分析结果
            
        Returns:
            dict: 质量评估结果
        """
        if not analysis_result['success']:
            return {'assessment': 'failed', 'reason': '分析失败'}
        
        noise_summary = analysis_result['noise_summary']
        
        # 评估标准
        assessments = []
        
        # 噪声底噪评估
        avg_noise_floor = noise_summary['noise_floor']['mean']
        if avg_noise_floor < -40:
            noise_floor_grade = 'excellent'
        elif avg_noise_floor < -30:
            noise_floor_grade = 'good'
        elif avg_noise_floor < -20:
            noise_floor_grade = 'fair'
        else:
            noise_floor_grade = 'poor'
        
        # SNR评估
        avg_snr = noise_summary['snr']['mean']
        excellent_snr_ratio = noise_summary['snr']['excellent_count'] / analysis_result['successful_segments']
        
        if avg_snr > 50 and excellent_snr_ratio > 0.9:
            snr_grade = 'excellent'
        elif avg_snr > 30 and excellent_snr_ratio > 0.8:
            snr_grade = 'good'
        elif avg_snr > 15:
            snr_grade = 'fair'
        else:
            snr_grade = 'poor'
        
        # 稳定性评估
        avg_stability = noise_summary['stability']['mean']
        if avg_stability > 0.8:
            stability_grade = 'excellent'
        elif avg_stability > 0.6:
            stability_grade = 'good'
        elif avg_stability > 0.4:
            stability_grade = 'fair'
        else:
            stability_grade = 'poor'
        
        # 综合评估
        grades = [noise_floor_grade, snr_grade, stability_grade]
        grade_scores = {'excellent': 4, 'good': 3, 'fair': 2, 'poor': 1}
        avg_score = np.mean([grade_scores[g] for g in grades])
        
        if avg_score >= 3.5:
            overall_grade = 'excellent'
        elif avg_score >= 2.5:
            overall_grade = 'good'
        elif avg_score >= 1.5:
            overall_grade = 'fair'
        else:
            overall_grade = 'poor'
        
        return {
            'overall_grade': overall_grade,
            'noise_floor_grade': noise_floor_grade,
            'snr_grade': snr_grade,
            'stability_grade': stability_grade,
            'details': {
                'avg_noise_floor_db': avg_noise_floor,
                'avg_snr_db': avg_snr,
                'avg_stability_score': avg_stability,
                'excellent_snr_ratio': excellent_snr_ratio
            }
        }

# 便捷函数
def analyze_audio_noise(audio_path, with_visualization=False, save_report=False):
    """
    便捷函数：分析音频噪声特征
    
    Args:
        audio_path (str): 音频文件路径
        with_visualization (bool): 是否生成可视化
        save_report (bool): 是否保存报告
        
    Returns:
        dict: 分析结果
    """
    detector = NoiseDetectorAPI()
    
    if with_visualization:
        return detector.analyze_with_visualization(audio_path, save_report)
    else:
        return detector.analyze(audio_path)
