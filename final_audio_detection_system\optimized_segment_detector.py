#!/usr/bin/env python3
"""
优化的频段竖线检测器
Optimized Segment Vertical Line Detector
对freq_split分割的各个时间段进行全频竖线检测，精确处理边界
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedSegmentDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 优化的检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),
            'edge_exclude_ratio': 0.25,             # 排除边界25%
            'min_peak_prominence_ratio': 0.15,      # 降低峰值突出度要求
            'min_peak_height_ratio': 0.25,          # 降低峰值高度要求
            'min_frequency_span': 500,              # 降低最小频率跨度
            'min_line_strength': 0.2,               # 降低最小竖线强度
            'peak_distance': 2,                     # 峰值间最小距离
        }
        
        print(f"优化的频段竖线检测器初始化完成")
        print(f"检测策略: freq_split时间分割 → 各时间段全频竖线检测 → 精确边界处理")
    
    def analyze_with_optimized_detection(self, audio_path, max_display_segments=16):
        """使用优化检测分析音频"""
        print(f"\n优化检测分析: {os.path.basename(audio_path)}")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            print(f"频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
            
            # 使用freq_split进行时间分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path, **self.freq_split_params, plot=False
            )
            
            if len(step_boundaries) == 0:
                print("freq_split分割失败")
                return None
            
            print(f"freq_split时间分割: {len(step_boundaries)}个时间段")
            
            # 分析所有时间段
            all_segment_results = []
            total_vertical_lines = 0
            
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                expected_freq = freq_table[seg_idx] if seg_idx < len(freq_table) else None
                
                segment_result = self._analyze_time_segment_optimized(
                    power_db, frequencies, times, seg_start_time, seg_end_time, 
                    seg_idx, expected_freq
                )
                
                all_segment_results.append(segment_result)
                
                if segment_result['analysis_success'] and segment_result['vertical_lines']:
                    total_vertical_lines += len(segment_result['vertical_lines'])
            
            # 统计结果
            anomalous_segments = sum(1 for seg in all_segment_results 
                                   if seg['analysis_success'] and seg['vertical_lines'])
            
            print(f"\n检测结果统计:")
            print(f"  总时间段数: {len(step_boundaries)}")
            print(f"  成功分析段数: {sum(1 for seg in all_segment_results if seg['analysis_success'])}")
            print(f"  异常时间段数: {anomalous_segments}")
            print(f"  总竖线数: {total_vertical_lines}")
            print(f"  异常段比例: {anomalous_segments/len(step_boundaries)*100:.1f}%")
            
            # 可视化结果
            self._create_optimized_visualization(
                power_db, frequencies, times, step_boundaries, freq_table,
                all_segment_results, audio_path, max_display_segments
            )
            
            return {
                'power_db': power_db,
                'frequencies': frequencies,
                'times': times,
                'step_boundaries': step_boundaries,
                'freq_table': freq_table,
                'segment_results': all_segment_results,
                'total_segments': len(step_boundaries),
                'anomalous_segments': anomalous_segments,
                'total_vertical_lines': total_vertical_lines
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _analyze_time_segment_optimized(self, power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq):
        """优化的时间段分析"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'expected_frequency': expected_freq,
            'duration': seg_end_time - seg_start_time,
            'analysis_success': False,
            'vertical_lines': [],
            'segment_stats': {},
            'error': None
        }
        
        # 找到时间段对应的索引范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        if seg_end_idx <= seg_start_idx:
            result['error'] = '时间段索引无效'
            return result
        
        # 精确的边界处理
        total_frames = seg_end_idx - seg_start_idx
        edge_exclude_frames = max(1, int(total_frames * self.detection_params['edge_exclude_ratio']))
        
        seg_core_start = seg_start_idx + edge_exclude_frames
        seg_core_end = seg_end_idx - edge_exclude_frames
        
        if seg_core_end <= seg_core_start:
            result['error'] = f'边界排除后无数据 (总帧数:{total_frames}, 排除:{edge_exclude_frames*2})'
            return result
        
        # 提取核心时间段的全频数据
        core_power = power_db[:, seg_core_start:seg_core_end]
        core_times = times[seg_core_start:seg_core_end]
        
        # 记录统计信息
        result['segment_stats'] = {
            'total_frames': total_frames,
            'excluded_frames': edge_exclude_frames * 2,
            'core_frames': seg_core_end - seg_core_start,
            'core_duration': core_times[-1] - core_times[0] if len(core_times) > 0 else 0,
            'power_range': (np.min(core_power), np.max(core_power)),
            'power_mean': np.mean(core_power)
        }
        
        # 在核心时间段内检测竖线
        vertical_lines = self._detect_vertical_lines_in_core_segment(
            core_power, frequencies, core_times, seg_idx
        )
        
        result['vertical_lines'] = vertical_lines
        result['analysis_success'] = True
        
        return result
    
    def _detect_vertical_lines_in_core_segment(self, core_power, frequencies, core_times, seg_idx):
        """在核心时间段内检测竖线"""
        vertical_lines = []
        
        if core_power.shape[1] < 3:  # 时间片太少
            return vertical_lines
        
        # 计算时间维度总能量
        total_energy = np.sum(core_power, axis=0)
        
        if len(total_energy) == 0:
            return vertical_lines
        
        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)
        
        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        
        if energy_range < 50:  # 降低能量变化阈值
            return vertical_lines
        
        min_height = np.min(smoothed_energy) + energy_range * self.detection_params['min_peak_height_ratio']
        min_prominence = energy_range * self.detection_params['min_peak_prominence_ratio']
        
        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy, 
                                     height=min_height,
                                     distance=self.detection_params['peak_distance'],
                                     prominence=min_prominence)
        
        # 分析每个峰值
        for peak_idx in peaks:
            if peak_idx < len(core_times):
                peak_time = core_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = core_power[:, peak_idx]
                
                # 分析该时刻的频谱是否为竖线
                line_analysis = self._analyze_peak_for_vertical_line(
                    peak_power_spectrum, frequencies, peak_time, peak_energy, seg_idx, peak_idx
                )
                
                if line_analysis and line_analysis['line_strength'] >= self.detection_params['min_line_strength']:
                    vertical_lines.append(line_analysis)
        
        return vertical_lines
    
    def _analyze_peak_for_vertical_line(self, power_spectrum, frequencies, peak_time, peak_energy, seg_idx, peak_idx):
        """分析峰值是否为竖线"""
        # 计算功率分布统计
        power_mean = np.mean(power_spectrum)
        power_std = np.std(power_spectrum)
        power_max = np.max(power_spectrum)
        power_min = np.min(power_spectrum)
        
        # 使用多种阈值方法
        thresholds = {
            'percentile_85': np.percentile(power_spectrum, 85),
            'percentile_90': np.percentile(power_spectrum, 90),
            'mean_plus_std': power_mean + 1.5 * power_std,
            'adaptive': power_min + (power_max - power_min) * 0.7
        }
        
        best_result = None
        best_score = 0
        
        for method_name, threshold in thresholds.items():
            high_energy_mask = power_spectrum > threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) < 3:
                continue
            
            # 计算频率特征
            freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
            freq_ratio = len(high_energy_indices) / len(frequencies)
            
            # 计算连续性
            continuous_segments = self._find_continuous_segments(high_energy_indices)
            if len(continuous_segments) == 0:
                continuity_score = 0
            else:
                total_continuous = sum(len(seg) for seg in continuous_segments)
                continuity_score = total_continuous / len(high_energy_indices)
            
            # 计算竖线强度
            if freq_span >= self.detection_params['min_frequency_span']:
                span_score = min(1.0, freq_span / 3000.0)
                ratio_score = min(1.0, freq_ratio / 0.2)
                line_strength = span_score * ratio_score * continuity_score
                
                if line_strength > best_score:
                    best_score = line_strength
                    best_result = {
                        'segment_index': seg_idx,
                        'peak_index': peak_idx,
                        'time': peak_time,
                        'peak_energy': peak_energy,
                        'frequency_span': freq_span,
                        'frequency_ratio': freq_ratio,
                        'line_strength': line_strength,
                        'detection_method': method_name,
                        'threshold': threshold,
                        'high_energy_indices': high_energy_indices,
                        'continuous_segments': continuous_segments,
                        'continuity_score': continuity_score,
                        'power_stats': {
                            'max': power_max,
                            'min': power_min,
                            'mean': power_mean,
                            'std': power_std
                        }
                    }
        
        return best_result
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _create_optimized_visualization(self, power_db, frequencies, times, step_boundaries, freq_table,
                                      all_segment_results, audio_path, max_display_segments):
        """创建优化的可视化"""
        print(f"\n生成优化可视化...")
        
        # 选择要显示的时间段
        successful_segments = [seg for seg in all_segment_results if seg['analysis_success']]
        anomalous_segments = [seg for seg in successful_segments if seg['vertical_lines']]
        
        # 优先显示有异常的时间段，然后是正常的
        display_segments = anomalous_segments[:max_display_segments//2] + \
                          [seg for seg in successful_segments if not seg['vertical_lines']][:max_display_segments//2]
        
        if len(display_segments) > max_display_segments:
            display_segments = display_segments[:max_display_segments]
        
        print(f"显示 {len(display_segments)} 个时间段 (异常:{len([s for s in display_segments if s['vertical_lines']])}, "
              f"正常:{len([s for s in display_segments if not s['vertical_lines']])})")
        
        # 创建可视化
        cols = min(4, len(display_segments))
        rows = (len(display_segments) + cols - 1) // cols + 1
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        fig.suptitle(f'优化频段竖线检测: {os.path.basename(audio_path)}\n'
                    f'总段数:{len(all_segment_results)}, 异常段数:{len(anomalous_segments)}, '
                    f'总竖线:{sum(len(seg["vertical_lines"]) for seg in all_segment_results)}', fontsize=14)
        
        # 确保axes是二维数组
        if rows == 1:
            axes = axes.reshape(1, -1) if len(display_segments) > 1 else np.array([[axes]])
        elif cols == 1:
            axes = axes.reshape(-1, 1)
        
        # 第一行：完整频谱图
        ax_main = plt.subplot2grid((rows, cols), (0, 0), colspan=cols)
        im = ax_main.imshow(power_db, aspect='auto', origin='lower', 
                           extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                           cmap='viridis')
        
        # 标记时间段分割和竖线
        for seg in all_segment_results:
            if seg['analysis_success']:
                color = 'red' if seg['vertical_lines'] else 'white'
                alpha = 0.8 if seg['vertical_lines'] else 0.3
                linewidth = 2 if seg['vertical_lines'] else 1
                
                ax_main.axvline(x=seg['start_time'], color=color, alpha=alpha, linewidth=linewidth)
                ax_main.axvline(x=seg['end_time'], color=color, alpha=alpha, linewidth=linewidth)
                
                # 标记竖线
                for line in seg['vertical_lines']:
                    ax_main.axvline(x=line['time'], color='yellow', linewidth=3, alpha=0.9)
        
        ax_main.set_title('完整频谱 + 时间段分割 + 竖线检测结果')
        ax_main.set_xlabel('时间 (s)')
        ax_main.set_ylabel('频率 (Hz)')
        plt.colorbar(im, ax=ax_main, label='功率 (dB)')
        
        # 显示各个时间段的详细分析
        for idx, segment in enumerate(display_segments):
            row = (idx // cols) + 1
            col = idx % cols
            
            if row < rows:
                ax = axes[row, col] if rows > 1 else axes[0, col]
                self._plot_segment_detailed(ax, segment, power_db, frequencies, times)
        
        # 隐藏多余的子图
        for idx in range(len(display_segments), rows * cols - cols):
            row = (idx // cols) + 1
            col = idx % cols
            if row < rows:
                axes[row, col].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'optimized_segment_detection_{os.path.splitext(os.path.basename(audio_path))[0]}.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  优化可视化结果已保存: {output_filename}")
        
        plt.show()
    
    def _plot_segment_detailed(self, ax, segment, power_db, frequencies, times):
        """绘制时间段的详细分析"""
        if not segment['analysis_success']:
            ax.text(0.5, 0.5, f"段 {segment['segment_index']}\n{segment.get('error', '分析失败')}", 
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            ax.set_title(f'段 {segment["segment_index"]} (失败)')
            return
        
        # 提取时间段数据
        seg_start_idx = np.argmin(np.abs(times - segment['start_time']))
        seg_end_idx = np.argmin(np.abs(times - segment['end_time']))
        
        # 边界处理
        stats = segment['segment_stats']
        edge_exclude = stats['excluded_frames'] // 2
        core_start = seg_start_idx + edge_exclude
        core_end = seg_end_idx - edge_exclude
        
        if core_end > core_start:
            # 显示时间段的频谱
            segment_power = power_db[:, seg_start_idx:seg_end_idx]
            segment_times = times[seg_start_idx:seg_end_idx]
            
            im = ax.imshow(segment_power, aspect='auto', origin='lower',
                          extent=[segment_times[0], segment_times[-1], frequencies[0], frequencies[-1]],
                          cmap='viridis')
            
            # 标记核心区域边界
            core_start_time = times[core_start]
            core_end_time = times[core_end-1] if core_end > 0 else times[-1]
            
            ax.axvline(x=core_start_time, color='orange', linestyle='--', alpha=0.8, label='核心区域')
            ax.axvline(x=core_end_time, color='orange', linestyle='--', alpha=0.8)
            
            # 标记检测到的竖线
            for i, line in enumerate(segment['vertical_lines']):
                ax.axvline(x=line['time'], color='red', linewidth=2, alpha=0.9)
                
                # 标记竖线的频率范围
                if 'high_energy_indices' in line:
                    freq_indices = line['high_energy_indices']
                    freq_range = [frequencies[freq_indices[0]], frequencies[freq_indices[-1]]]
                    ax.plot([line['time'], line['time']], freq_range, 
                           color='red', linewidth=4, alpha=0.7)
                    
                    # 添加竖线信息
                    ax.text(line['time'], frequencies[-1]*0.9, f"L{i+1}\n{line['line_strength']:.2f}", 
                           ha='center', va='top', fontsize=8, color='white',
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="red", alpha=0.7))
        
        # 设置标题和标签
        title = f"段 {segment['segment_index']}"
        if segment['expected_frequency']:
            title += f" ({segment['expected_frequency']:.0f}Hz)"
        title += f"\n竖线: {len(segment['vertical_lines'])}"
        
        ax.set_title(title, fontsize=10)
        ax.set_xlabel('时间 (s)', fontsize=8)
        ax.set_ylabel('频率 (Hz)', fontsize=8)
        
        # 显示统计信息
        stats_text = f"核心: {stats['core_frames']}帧\n排除: {stats['excluded_frames']}帧"
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8, 
               verticalalignment='top', bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.8))

def main():
    """主函数"""
    # 初始化检测器
    detector = OptimizedSegmentDetector()
    
    # 分析指定样本
    audio_path = "../test20250717/pos/sd卡/sd1_1.wav"
    
    # 执行优化检测
    results = detector.analyze_with_optimized_detection(audio_path, max_display_segments=12)
    
    if results:
        print(f"\n优化检测完成！")
        print(f"详细的时间段分割和竖线检测结果已可视化。")
    else:
        print(f"检测失败！")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
