#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加窗频谱分析脚本
对93段直接加窗计算频谱，可视化频谱、对数谱和对数功率谱
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Deja<PERSON>u Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_windowed_spectrum(audio_segment, sr, window_type='hanning'):
    """
    计算加窗音频段的频谱、对数谱和对数功率谱
    """
    
    # 标准化
    if np.max(np.abs(audio_segment)) > 0:
        audio_segment = audio_segment / np.max(np.abs(audio_segment))
    
    # 高分辨率FFT
    fft_size = 131072
    if len(audio_segment) < fft_size:
        audio_segment = np.pad(audio_segment, (0, fft_size - len(audio_segment)), 'constant')
    else:
        audio_segment = audio_segment[:fft_size]
    
    # 应用窗函数
    if window_type == 'hanning':
        window = np.hanning(len(audio_segment))
    elif window_type == 'hamming':
        window = np.hamming(len(audio_segment))
    elif window_type == 'blackman':
        window = np.blackman(len(audio_segment))
    else:
        window = np.ones(len(audio_segment))  # 矩形窗
    
    windowed_audio = audio_segment * window
    
    # FFT
    fft_result = np.fft.fft(windowed_audio)
    
    # 只取正频率部分
    positive_fft = fft_result[:fft_size//2]
    positive_freqs = np.fft.fftfreq(fft_size, 1/sr)[:fft_size//2]
    
    # 计算幅度谱
    magnitude_spectrum = np.abs(positive_fft)
    
    # 计算功率谱
    power_spectrum = magnitude_spectrum ** 2
    
    # 计算对数谱（对幅度取对数）
    log_magnitude_spectrum = np.log(magnitude_spectrum + 1e-12)
    
    # 计算对数功率谱（对功率取对数，即dB）
    log_power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)
    
    return (positive_freqs, magnitude_spectrum, power_spectrum, 
            log_magnitude_spectrum, log_power_spectrum_db, window)

def analyze_segment_windowed_spectrum(seg_idx, start_time, end_time, expected_freq, y, sr, window_type='hanning'):
    """
    分析单个段的加窗频谱
    """
    
    try:
        # 不去除开头结尾，直接使用完整段
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        
        # 检查边界
        if start_sample >= len(y) or end_sample > len(y) or start_sample >= end_sample:
            print(f"  ⚠️  段{seg_idx}: 时间边界超出音频范围")
            return None
        
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"  ⚠️  段{seg_idx}: 音频段为空")
            return None
        
        if len(segment_audio) < 1024:
            print(f"  ⚠️  段{seg_idx}: 音频段太短")
            return None
        
        # 计算加窗频谱
        freqs, magnitude, power, log_magnitude, log_power_db, window = calculate_windowed_spectrum(segment_audio, sr, window_type)
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_magnitude = magnitude[actual_idx]
            fundamental_power_db = log_power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_magnitude = np.max(magnitude)
            fundamental_power_db = np.max(log_power_db)
        
        # 使用harmonic_detection_system检测谐波
        noise_analysis = {
            'global_noise_floor_db': np.percentile(log_power_db, 25),
            'noise_variation_db': np.max(log_power_db) - np.min(log_power_db),
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }
        
        harmonic_analysis = detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_magnitude': fundamental_magnitude,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'magnitude_spectrum': magnitude,
            'power_spectrum': power,
            'log_magnitude_spectrum': log_magnitude,
            'log_power_spectrum_db': log_power_db,
            'window': window,
            'window_type': window_type,
            'segment_duration': end_time - start_time,
            'harmonic_analysis': harmonic_analysis
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_windowed_spectrum_visualization(segment_data, output_dir, filename=""):
    """
    创建加窗频谱可视化
    """
    
    if not segment_data:
        return None
    
    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    fundamental_power_db = segment_data['fundamental_power_db']
    freqs = segment_data['freqs']
    magnitude_spectrum = segment_data['magnitude_spectrum']
    power_spectrum = segment_data['power_spectrum']
    log_magnitude_spectrum = segment_data['log_magnitude_spectrum']
    log_power_spectrum_db = segment_data['log_power_spectrum_db']
    window_type = segment_data['window_type']
    segment_duration = segment_data['segment_duration']
    harmonic_analysis = segment_data['harmonic_analysis']
    
    # 创建图形 - 3个子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n加窗频谱分析 ({window_type}窗, {segment_duration:.2f}s)', 
                 fontsize=16, fontweight='bold')
    
    # 第一个子图：幅度谱
    ax1.plot(freqs, magnitude_spectrum, 'blue', linewidth=1, alpha=0.7, label='幅度谱')
    
    # 标记主频
    ax1.axvline(fundamental_freq, color='red', linestyle='-', linewidth=3, 
               alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')
    
    # 标记检测到的谐波
    if harmonic_analysis:
        harmonic_freqs = [h['freq'] for h in harmonic_analysis]
        harmonic_magnitudes = [magnitude_spectrum[np.argmin(np.abs(freqs - h['freq']))] for h in harmonic_analysis]
        ax1.scatter(harmonic_freqs, harmonic_magnitudes, c='green', s=50, alpha=0.8, 
                   marker='^', label=f'谐波({len(harmonic_analysis)}个)')
        
        # 标注谐波次数
        for i, (freq, mag) in enumerate(zip(harmonic_freqs, harmonic_magnitudes)):
            order = round(freq / fundamental_freq)
            ax1.annotate(f'{order}', (freq, mag), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8, color='green')
    
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('幅度')
    ax1.set_xlim(50, 20000)
    ax1.set_xscale('log')
    ax1.set_yscale('log')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_title('幅度谱（线性尺度）')
    
    # 第二个子图：对数谱
    ax2.plot(freqs, log_magnitude_spectrum, 'purple', linewidth=1, alpha=0.7, label='对数幅度谱')
    ax2.axvline(fundamental_freq, color='red', linestyle='--', linewidth=2, 
               alpha=0.6, label=f'主频: {fundamental_freq:.1f}Hz')
    
    # 标记谐波
    if harmonic_analysis:
        harmonic_log_mags = [log_magnitude_spectrum[np.argmin(np.abs(freqs - h['freq']))] for h in harmonic_analysis]
        ax2.scatter(harmonic_freqs, harmonic_log_mags, c='green', s=40, alpha=0.8, 
                   marker='^', label=f'谐波({len(harmonic_analysis)}个)')
    
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('对数幅度 (ln)')
    ax2.set_xlim(50, 20000)
    ax2.set_xscale('log')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_title('对数幅度谱（ln(magnitude)）')
    
    # 第三个子图：对数功率谱(dB)
    ax3.plot(freqs, log_power_spectrum_db, 'orange', linewidth=1, alpha=0.7, label='对数功率谱(dB)')
    ax3.axvline(fundamental_freq, color='red', linestyle='-', linewidth=2, 
               alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz ({fundamental_power_db:.1f}dB)')
    
    # 标记谐波
    if harmonic_analysis:
        harmonic_powers_db = [h['power_db'] for h in harmonic_analysis]
        ax3.scatter(harmonic_freqs, harmonic_powers_db, c='green', s=50, alpha=0.8, 
                   marker='^', label=f'谐波({len(harmonic_analysis)}个)')
        
        # 标注谐波功率
        for i, (freq, power_db) in enumerate(zip(harmonic_freqs, harmonic_powers_db)):
            order = round(freq / fundamental_freq)
            ax3.annotate(f'{order}({power_db:.0f})', (freq, power_db), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8, color='green')
    
    ax3.set_xlabel('频率 (Hz)')
    ax3.set_ylabel('功率 (dB)')
    ax3.set_xlim(50, 20000)
    ax3.set_xscale('log')
    ax3.set_ylim(-80, 40)
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.set_title('对数功率谱（10*log10(power)）')
    
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(output_dir, f"windowed_spectrum_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def process_single_segment_windowed_spectrum(args):
    """
    处理单个段的加窗频谱分析（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, window_type, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 分析段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_windowed_spectrum(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, window_type
        )

        if segment_data:
            # 生成可视化
            viz_path = create_windowed_spectrum_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 计算频谱特征
                magnitude_spectrum = segment_data['magnitude_spectrum']
                log_power_spectrum_db = segment_data['log_power_spectrum_db']

                # 计算频谱统计特征
                magnitude_peak = np.max(magnitude_spectrum)
                magnitude_mean = np.mean(magnitude_spectrum)
                power_peak_db = np.max(log_power_spectrum_db)
                power_mean_db = np.mean(log_power_spectrum_db)
                dynamic_range_db = power_peak_db - power_mean_db

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_freq': segment_data['fundamental_freq'],
                    'fundamental_power_db': segment_data['fundamental_power_db'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'segment_duration': segment_data['segment_duration'],
                    'magnitude_peak': magnitude_peak,
                    'power_peak_db': power_peak_db,
                    'dynamic_range_db': dynamic_range_db
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 谐波: {result['harmonic_count']}个, 动态范围: {dynamic_range_db:.1f}dB, 时长: {result['segment_duration']:.2f}s")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 对琴身内部异物文件的93段进行加窗频谱分析
    """

    print("🎯 琴身内部异物文件加窗频谱分析")
    print("📝 对93段直接加窗计算频谱，可视化频谱、对数谱和对数功率谱")
    print("📝 不去除开头结尾，使用完整段长度")
    print("="*80)

    # 查找琴身内部异物文件
    target_file = "test20250722/琴身内部异物1.1.wav"

    if not os.path.exists(target_file):
        print(f"❌ 未找到目标文件: {target_file}")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 窗函数类型
    window_type = 'hanning'  # 可选: 'hanning', 'hamming', 'blackman', 'rectangular'

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_windowed_spectrum_analysis"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        print(f"🪟 使用窗函数: {window_type}")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, window_type,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行加窗频谱分析...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_windowed_spectrum, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件加窗频谱分析完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")
        print(f"  🪟 窗函数: {window_type}")

        # 频谱特征统计
        if successful_results:
            harmonic_counts = [r['harmonic_count'] for r in successful_results]
            segment_durations = [r['segment_duration'] for r in successful_results]
            power_peaks_db = [r['power_peak_db'] for r in successful_results]
            dynamic_ranges_db = [r['dynamic_range_db'] for r in successful_results]

            print(f"\n📊 频谱特征统计:")
            print(f"  平均谐波数: {np.mean(harmonic_counts):.1f}个")
            print(f"  谐波数范围: {np.min(harmonic_counts)} ~ {np.max(harmonic_counts)}个")
            print(f"  平均段时长: {np.mean(segment_durations):.2f}秒")
            print(f"  段时长范围: {np.min(segment_durations):.2f} ~ {np.max(segment_durations):.2f}秒")
            print(f"  平均功率峰值: {np.mean(power_peaks_db):.1f}dB")
            print(f"  功率峰值范围: {np.min(power_peaks_db):.1f} ~ {np.max(power_peaks_db):.1f}dB")
            print(f"  平均动态范围: {np.mean(dynamic_ranges_db):.1f}dB")
            print(f"  动态范围: {np.min(dynamic_ranges_db):.1f} ~ {np.max(dynamic_ranges_db):.1f}dB")

            # 高动态范围段
            high_dynamic_threshold = np.mean(dynamic_ranges_db) + np.std(dynamic_ranges_db)
            high_dynamic_segments = [(r['seg_idx'], r['expected_freq'], r['dynamic_range_db'])
                                   for r in successful_results if r['dynamic_range_db'] > high_dynamic_threshold]

            print(f"\n🎯 高动态范围段（>均值+1σ）:")
            if high_dynamic_segments:
                for seg_idx, freq, dynamic_range in high_dynamic_segments:
                    print(f"  📈 段{seg_idx} ({freq:.1f}Hz): 动态范围 {dynamic_range:.1f}dB")
            else:
                print("  ✅ 无明显高动态范围段")

        print("="*80)
        print("🎯 加窗频谱分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
