#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版93个频段特征范围可视化
专注于两个关键特征的范围对比
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_range_visualization():
    """创建简化的特征范围可视化"""
    print("🎨 生成简化版93个频段特征范围可视化")
    print("="*70)
    
    # 加载数据
    df = pd.read_csv('all_93_segments_background_noise.csv')
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 准备数据
    segments = sorted(df['segment_idx'].unique())
    frequencies = [df[df['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('93个频段背景噪声特征范围对比分析', fontsize=16, fontweight='bold')
    
    # 1. background_noise_median 范围对比
    ax1 = axes[0, 0]
    plot_feature_ranges_simple(ax1, df, 'background_noise_median', segments, frequencies,
                              'background_noise_median 范围对比', 'dB')
    
    # 2. background_noise_mean 范围对比  
    ax2 = axes[0, 1]
    plot_feature_ranges_simple(ax2, df, 'background_noise_mean', segments, frequencies,
                              'background_noise_mean 范围对比', 'dB')
    
    # 3. 分离间隙对比
    ax3 = axes[1, 0]
    plot_separation_analysis(ax3, df, segments, frequencies)
    
    # 4. 关键频段详细对比
    ax4 = axes[1, 1]
    plot_key_segments_detail(ax4, df, segments, frequencies)
    
    plt.tight_layout()
    plt.savefig('simple_feature_ranges_93_segments.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，不显示

    print(f"✅ 图表已保存: simple_feature_ranges_93_segments.png")
    
    # 生成统计报告
    generate_simple_statistics(df, segments, frequencies)

def plot_feature_ranges_simple(ax, df, feature_name, segments, frequencies, title, ylabel):
    """绘制简化的特征范围对比"""
    
    target_ranges = []
    normal_ranges = []
    separable_segments = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_values = seg_data[seg_data['is_target'] == True][feature_name]
        normal_values = seg_data[seg_data['is_target'] == False][feature_name]
        
        if len(target_values) > 0 and len(normal_values) > 0:
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            target_ranges.append([target_min, target_max])
            normal_ranges.append([normal_min, normal_max])
            
            # 检查是否可分离
            if target_max < normal_min or target_min > normal_max:
                separable_segments.append(seg_idx)
        else:
            target_ranges.append([np.nan, np.nan])
            normal_ranges.append([np.nan, np.nan])
    
    # 转换为数组
    target_ranges = np.array(target_ranges)
    normal_ranges = np.array(normal_ranges)
    
    x = np.array(segments)
    
    # 绘制范围带
    ax.fill_between(x, normal_ranges[:, 0], normal_ranges[:, 1], 
                   alpha=0.3, color='blue', label=f'正常样本范围 (n=54)')
    ax.fill_between(x, target_ranges[:, 0], target_ranges[:, 1], 
                   alpha=0.6, color='red', label=f'噪声样本范围 (n=2)')
    
    # 绘制边界线
    ax.plot(x, normal_ranges[:, 0], 'b-', linewidth=1, alpha=0.8)
    ax.plot(x, normal_ranges[:, 1], 'b-', linewidth=1, alpha=0.8)
    ax.plot(x, target_ranges[:, 0], 'r-', linewidth=2)
    ax.plot(x, target_ranges[:, 1], 'r-', linewidth=2)
    
    # 标记可分离频段
    if separable_segments:
        sep_indices = [segments.index(seg) for seg in separable_segments]
        ax.scatter([segments[i] for i in sep_indices], 
                  [target_ranges[i, 1] for i in sep_indices],
                  color='yellow', s=80, marker='*', edgecolor='black', 
                  label=f'可分离频段 ({len(separable_segments)}个)', zorder=5)
    
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel(ylabel)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    # 添加频率标签
    freq_ticks = range(0, len(segments), 20)
    ax2 = ax.twiny()
    ax2.set_xlim(ax.get_xlim())
    ax2.set_xticks([segments[i] for i in freq_ticks if i < len(segments)])
    ax2.set_xticklabels([f'{frequencies[i]:.0f}Hz' for i in freq_ticks if i < len(frequencies)], 
                       rotation=45)
    
    # 添加统计信息
    stats_text = f'可分离: {len(separable_segments)}/{len(segments)} ({len(separable_segments)/len(segments)*100:.1f}%)'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7), fontsize=9)

def plot_separation_analysis(ax, df, segments, frequencies):
    """绘制分离间隙分析"""
    
    median_gaps = []
    mean_gaps = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_data = seg_data[seg_data['is_target'] == True]
        normal_data = seg_data[seg_data['is_target'] == False]
        
        gaps = []
        for feature in ['background_noise_median', 'background_noise_mean']:
            target_values = target_data[feature]
            normal_values = normal_data[feature]
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_min, target_max = np.min(target_values), np.max(target_values)
                normal_min, normal_max = np.min(normal_values), np.max(normal_values)
                
                if target_max < normal_min:
                    gap = normal_min - target_max
                elif target_min > normal_max:
                    gap = target_min - normal_max
                else:
                    gap = 0
                gaps.append(gap)
            else:
                gaps.append(0)
        
        median_gaps.append(gaps[0] if len(gaps) > 0 else 0)
        mean_gaps.append(gaps[1] if len(gaps) > 1 else 0)
    
    x = np.array(segments)
    
    # 绘制分离间隙
    ax.bar(x - 0.2, median_gaps, width=0.4, label='median分离间隙', color='red', alpha=0.7)
    ax.bar(x + 0.2, mean_gaps, width=0.4, label='mean分离间隙', color='blue', alpha=0.7)
    
    # 标记最佳分离点
    if max(median_gaps) > 0:
        best_median_idx = np.argmax(median_gaps)
        ax.scatter(segments[best_median_idx] - 0.2, median_gaps[best_median_idx], 
                  color='darkred', s=100, marker='*', zorder=5)
        ax.text(segments[best_median_idx] - 0.2, median_gaps[best_median_idx] + 0.1,
               f'{median_gaps[best_median_idx]:.2f}dB', ha='center', va='bottom', fontweight='bold')
    
    if max(mean_gaps) > 0:
        best_mean_idx = np.argmax(mean_gaps)
        ax.scatter(segments[best_mean_idx] + 0.2, mean_gaps[best_mean_idx], 
                  color='darkblue', s=100, marker='*', zorder=5)
        ax.text(segments[best_mean_idx] + 0.2, mean_gaps[best_mean_idx] + 0.1,
               f'{mean_gaps[best_mean_idx]:.2f}dB', ha='center', va='bottom', fontweight='bold')
    
    ax.set_title('各频段分离间隙对比', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('分离间隙 (dB)')
    ax.legend()
    ax.grid(True, alpha=0.3)

def plot_key_segments_detail(ax, df, segments, frequencies):
    """绘制关键频段详细对比"""
    
    # 选择有分离能力的频段和一些代表性频段
    key_segments_idx = [0, 10, 20, 30, 40, 50, 60, 69, 80, 92]  # 包含已知的可分离频段
    
    target_medians = []
    normal_medians = []
    segment_labels = []
    colors = []
    
    for i, seg_idx in enumerate(key_segments_idx):
        if seg_idx < len(segments):
            actual_seg = segments[seg_idx]
            seg_data = df[df['segment_idx'] == actual_seg]
            target_values = seg_data[seg_data['is_target'] == True]['background_noise_median']
            normal_values = seg_data[seg_data['is_target'] == False]['background_noise_median']
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_median = np.median(target_values)
                normal_median = np.median(normal_values)
                
                target_medians.append(target_median)
                normal_medians.append(normal_median)
                segment_labels.append(f'Seg{actual_seg}\n{frequencies[seg_idx]:.0f}Hz')
                
                # 检查是否可分离
                target_min, target_max = np.min(target_values), np.max(target_values)
                normal_min, normal_max = np.min(normal_values), np.max(normal_values)
                
                if target_max < normal_min or target_min > normal_max:
                    colors.append('red')  # 可分离
                else:
                    colors.append('gray')  # 不可分离
    
    # 绘制对比柱状图
    x = np.arange(len(segment_labels))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, normal_medians, width, label='正常样本中位数', color='blue', alpha=0.7)
    bars2 = ax.bar(x + width/2, target_medians, width, label='噪声样本中位数', color=colors, alpha=0.8)
    
    # 添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        # 正常样本标签
        height1 = bar1.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + 0.5,
                f'{height1:.1f}', ha='center', va='bottom', fontsize=8)
        
        # 噪声样本标签
        height2 = bar2.get_height()
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + 0.5,
                f'{height2:.1f}', ha='center', va='bottom', fontsize=8, fontweight='bold')
        
        # 差异标签
        diff = height2 - height1
        ax.text(x[i], max(height1, height2) + 2,
                f'Δ{diff:+.1f}', ha='center', va='bottom', fontsize=8, 
                color='red' if colors[i] == 'red' else 'black')
    
    ax.set_title('关键频段背景噪声中位数详细对比', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段')
    ax.set_ylabel('背景噪声中位数 (dB)')
    ax.set_xticks(x)
    ax.set_xticklabels(segment_labels, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加图例说明
    ax.text(0.02, 0.98, '红色柱=可分离频段\n灰色柱=不可分离频段', 
            transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7), fontsize=9)

def generate_simple_statistics(df, segments, frequencies):
    """生成简化统计报告"""
    print(f"\n📊 93个频段特征范围统计报告")
    print("="*70)
    
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📈 整体统计:")
    print(f"   总频段数: {len(segments)}")
    print(f"   噪声样本: {len(target_data['filename'].unique())} 个文件")
    print(f"   正常样本: {len(normal_data['filename'].unique())} 个文件")
    
    # 分析每个特征的分离能力
    for feature in ['background_noise_median', 'background_noise_mean']:
        print(f"\n🎯 {feature} 分离分析:")
        
        separable_segments = []
        separation_gaps = []
        
        for seg_idx in segments:
            seg_data = df[df['segment_idx'] == seg_idx]
            target_values = seg_data[seg_data['is_target'] == True][feature]
            normal_values = seg_data[seg_data['is_target'] == False][feature]
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_min, target_max = np.min(target_values), np.max(target_values)
                normal_min, normal_max = np.min(normal_values), np.max(normal_values)
                
                if target_max < normal_min or target_min > normal_max:
                    gap = max(normal_min - target_max, target_min - normal_max)
                    separable_segments.append({
                        'segment': seg_idx,
                        'frequency': frequencies[seg_idx],
                        'gap': gap,
                        'target_range': [target_min, target_max],
                        'normal_range': [normal_min, normal_max]
                    })
                    separation_gaps.append(gap)
        
        print(f"   可分离频段数: {len(separable_segments)}/{len(segments)} ({len(separable_segments)/len(segments)*100:.1f}%)")
        
        if separable_segments:
            separable_segments.sort(key=lambda x: x['gap'], reverse=True)
            print(f"   最大分离间隙: {max(separation_gaps):.2f} dB")
            print(f"   平均分离间隙: {np.mean(separation_gaps):.2f} dB")
            
            print(f"   前3个最佳分离频段:")
            for i, seg_info in enumerate(separable_segments[:3]):
                print(f"     {i+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): 间隙{seg_info['gap']:.2f}dB")
                print(f"        噪声范围: [{seg_info['target_range'][0]:.1f}, {seg_info['target_range'][1]:.1f}] dB")
                print(f"        正常范围: [{seg_info['normal_range'][0]:.1f}, {seg_info['normal_range'][1]:.1f}] dB")
    
    # 整体噪声水平对比
    print(f"\n🔊 整体噪声水平对比:")
    for feature in ['background_noise_median', 'background_noise_mean']:
        target_mean = target_data[feature].mean()
        normal_mean = normal_data[feature].mean()
        target_std = target_data[feature].std()
        normal_std = normal_data[feature].std()
        
        print(f"   {feature}:")
        print(f"     噪声样本: {target_mean:.2f} ± {target_std:.2f} dB")
        print(f"     正常样本: {normal_mean:.2f} ± {normal_std:.2f} dB")
        print(f"     差异: {target_mean - normal_mean:+.2f} dB")

if __name__ == "__main__":
    create_simple_range_visualization()
    print(f"\n✅ 简化版93个频段特征范围可视化完成！")
