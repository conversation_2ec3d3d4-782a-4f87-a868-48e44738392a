#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化检测系统
基于之前分析的49个有效特征和实际数据分布
"""

import os
import sys
import numpy as np
import pandas as pd
import librosa
from scipy.signal import stft, find_peaks
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

class FinalOptimizedDetectionSystem:
    def __init__(self):
        self.fs = 48000
        
        # 基于实际分析的核心特征和阈值
        self.core_features = {
            # 竖线检测 - 独立处理
            'vertical_line': {
                'enabled': True,
                'threshold': 2.0  # 线强度阈值
            },
            
            # 谐波失真特征 - 最有效的类别
            'harmonic': {
                'thd': 0.08,  # 基于实际数据分布
                'harmonic_3_ratio': 0.008
            },
            
            # 时域特征 - 高区分力
            'time_domain': {
                'td_skewness': 4.0,
                'td_kurtosis': 10.0
            },
            
            # 频域特征 - 核心特征
            'frequency_domain': {
                'fd_expected_freq_ratio': 0.2
            },
            
            # 频谱特征 - 关键特征
            'spectral': {
                'spec_bandwidth_mean': 2000
            }
        }
        
        print(f"🔧 最终优化检测系统初始化")
        print(f"📊 使用基于49个有效特征分析的核心特征")
    
    def detect_audio_file(self, audio_path, debug=False):
        """检测单个音频文件"""
        try:
            if debug:
                print(f"🔍 检测文件: {os.path.basename(audio_path)}")
            
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            # 使用多层检测策略
            detection_results = {
                'vertical_line_detection': self._detect_vertical_lines(audio_path, step_boundaries, freq_table),
                'harmonic_distortion_detection': self._detect_harmonic_distortion(audio_path, step_boundaries, freq_table),
                'spectral_anomaly_detection': self._detect_spectral_anomalies(audio_path, step_boundaries, freq_table)
            }
            
            # 综合判定
            final_decision = self._make_final_decision(detection_results, debug)
            
            return {
                'status': 'success',
                'predicted_label': final_decision['label'],
                'confidence': final_decision['confidence'],
                'detection_details': detection_results,
                'reasoning': final_decision['reasoning']
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _detect_vertical_lines(self, audio_path, step_boundaries, freq_table):
        """竖线检测"""
        try:
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            vertical_line_segments = 0
            total_segments = len(step_boundaries)
            max_line_strength = 0
            
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                start_sample = int(start_time * self.fs)
                end_sample = int(end_time * self.fs)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) == 0:
                    continue
                
                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))
                
                # STFT分析
                f, t, Zxx = stft(segment_audio, self.fs, nperseg=1024, noverlap=512)
                freq_mask = (f >= 100) & (f <= 20000)
                f_filtered = f[freq_mask]
                Zxx_filtered = Zxx[freq_mask, :]
                
                power_spectrum = np.abs(Zxx_filtered) ** 2
                power_db = 10 * np.log10(power_spectrum + 1e-12)
                
                # 总能量变化
                total_energy = np.sum(power_db, axis=0)
                energy_mean = np.mean(total_energy)
                energy_std = np.std(total_energy)
                
                # 峰值检测
                min_height = energy_mean + 0.5 * energy_std
                min_prominence = 0.3 * energy_std
                
                peaks, properties = find_peaks(total_energy, 
                                             height=min_height,
                                             distance=2,
                                             prominence=min_prominence)
                
                # 竖线检测
                for peak_idx in peaks:
                    if peak_idx < len(t):
                        peak_spectrum = power_db[:, peak_idx]
                        threshold = np.percentile(peak_spectrum, 70)
                        high_energy_mask = peak_spectrum > threshold
                        high_energy_indices = np.where(high_energy_mask)[0]
                        
                        if len(high_energy_indices) > 0:
                            # 频率跨度
                            freq_span = f_filtered[high_energy_indices[-1]] - f_filtered[high_energy_indices[0]]
                            
                            # 频率比例
                            freq_ratio = len(high_energy_indices) / len(f_filtered)
                            
                            # 线强度
                            high_energy_power = peak_spectrum[high_energy_indices]
                            background_power = np.delete(peak_spectrum, high_energy_indices)
                            if len(background_power) > 0:
                                line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                            else:
                                line_strength = 1.0
                            
                            max_line_strength = max(max_line_strength, line_strength)
                            
                            # 竖线判定
                            if line_strength >= self.core_features['vertical_line']['threshold'] and freq_span >= 1000 and freq_ratio >= 0.1:
                                vertical_line_segments += 1
                                break  # 该频段已检测到竖线
            
            vl_ratio = vertical_line_segments / total_segments if total_segments > 0 else 0
            
            return {
                'vertical_line_segments': vertical_line_segments,
                'total_segments': total_segments,
                'vl_ratio': vl_ratio,
                'max_line_strength': max_line_strength,
                'anomaly_detected': vl_ratio > 0.02  # 2%以上频段有竖线
            }
            
        except Exception as e:
            return {
                'vertical_line_segments': 0,
                'total_segments': 0,
                'vl_ratio': 0,
                'max_line_strength': 0,
                'anomaly_detected': False,
                'error': str(e)
            }
    
    def _detect_harmonic_distortion(self, audio_path, step_boundaries, freq_table):
        """谐波失真检测"""
        try:
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            anomalous_segments = 0
            total_segments = len(step_boundaries)
            max_thd = 0
            max_harmonic_3_ratio = 0
            
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                start_sample = int(start_time * self.fs)
                end_sample = int(end_time * self.fs)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) == 0:
                    continue
                
                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))
                
                # FFT分析
                fft = np.fft.fft(segment_audio)
                freqs = np.fft.fftfreq(len(segment_audio), 1/self.fs)
                magnitude = np.abs(fft)
                
                positive_freqs = freqs[:len(freqs)//2]
                positive_magnitude = magnitude[:len(magnitude)//2]
                
                # 基频能量
                fundamental_tolerance = 20
                fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
                if np.any(fundamental_mask):
                    fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
                else:
                    fundamental_energy = 0
                
                # 谐波能量
                harmonic_energies = []
                harmonic_3_energy = 0
                
                for harmonic in range(2, 6):
                    harmonic_freq = expected_freq * harmonic
                    if harmonic_freq < self.fs / 2:
                        harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                        if np.any(harmonic_mask):
                            harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                            harmonic_energies.append(harmonic_energy)
                            if harmonic == 3:
                                harmonic_3_energy = harmonic_energy
                        else:
                            harmonic_energies.append(0)
                
                # THD计算
                total_harmonic_energy = sum(harmonic_energies)
                if fundamental_energy > 0:
                    thd = np.sqrt(total_harmonic_energy / fundamental_energy)
                    harmonic_3_ratio = harmonic_3_energy / fundamental_energy
                else:
                    thd = 0
                    harmonic_3_ratio = 0
                
                max_thd = max(max_thd, thd)
                max_harmonic_3_ratio = max(max_harmonic_3_ratio, harmonic_3_ratio)
                
                # 判定该频段是否异常
                if (thd > self.core_features['harmonic']['thd'] or 
                    harmonic_3_ratio > self.core_features['harmonic']['harmonic_3_ratio']):
                    anomalous_segments += 1
            
            anomaly_ratio = anomalous_segments / total_segments if total_segments > 0 else 0
            
            return {
                'anomalous_segments': anomalous_segments,
                'total_segments': total_segments,
                'anomaly_ratio': anomaly_ratio,
                'max_thd': max_thd,
                'max_harmonic_3_ratio': max_harmonic_3_ratio,
                'anomaly_detected': anomaly_ratio > 0.15  # 15%以上频段有谐波失真
            }
            
        except Exception as e:
            return {
                'anomalous_segments': 0,
                'total_segments': 0,
                'anomaly_ratio': 0,
                'max_thd': 0,
                'max_harmonic_3_ratio': 0,
                'anomaly_detected': False,
                'error': str(e)
            }
    
    def _detect_spectral_anomalies(self, audio_path, step_boundaries, freq_table):
        """频谱异常检测"""
        try:
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            anomalous_segments = 0
            total_segments = len(step_boundaries)
            max_bandwidth = 0
            
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                start_sample = int(start_time * self.fs)
                end_sample = int(end_time * self.fs)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) == 0:
                    continue
                
                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))
                
                # STFT分析
                f, t, Zxx = stft(segment_audio, self.fs, nperseg=1024, noverlap=512)
                magnitude = np.abs(Zxx)
                power = magnitude**2
                
                # 频谱带宽计算
                freq_weighted_sum = np.sum(f[:, np.newaxis] * power, axis=0)
                total_power = np.sum(power, axis=0)
                spectral_centroid = freq_weighted_sum / (total_power + 1e-12)
                
                freq_diff = f[:, np.newaxis] - spectral_centroid[np.newaxis, :]
                spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * power, axis=0) / (total_power + 1e-12))
                mean_bandwidth = np.mean(spectral_bandwidth)
                
                max_bandwidth = max(max_bandwidth, mean_bandwidth)
                
                # 判定该频段是否异常
                if mean_bandwidth > self.core_features['spectral']['spec_bandwidth_mean']:
                    anomalous_segments += 1
            
            anomaly_ratio = anomalous_segments / total_segments if total_segments > 0 else 0
            
            return {
                'anomalous_segments': anomalous_segments,
                'total_segments': total_segments,
                'anomaly_ratio': anomaly_ratio,
                'max_bandwidth': max_bandwidth,
                'anomaly_detected': anomaly_ratio > 0.20  # 20%以上频段有频谱异常
            }
            
        except Exception as e:
            return {
                'anomalous_segments': 0,
                'total_segments': 0,
                'anomaly_ratio': 0,
                'max_bandwidth': 0,
                'anomaly_detected': False,
                'error': str(e)
            }
    
    def _make_final_decision(self, detection_results, debug=False):
        """综合判定"""
        vl_result = detection_results['vertical_line_detection']
        hd_result = detection_results['harmonic_distortion_detection']
        sa_result = detection_results['spectral_anomaly_detection']
        
        # 检测到的异常类型
        anomalies = []
        confidence_scores = []
        
        # 竖线检测
        if vl_result['anomaly_detected']:
            anomalies.append('vertical_line')
            confidence_scores.append(min(vl_result['vl_ratio'] * 50, 1.0))  # 归一化到0-1
        
        # 谐波失真检测
        if hd_result['anomaly_detected']:
            anomalies.append('harmonic_distortion')
            confidence_scores.append(min(hd_result['anomaly_ratio'] * 5, 1.0))  # 归一化到0-1
        
        # 频谱异常检测
        if sa_result['anomaly_detected']:
            anomalies.append('spectral_anomaly')
            confidence_scores.append(min(sa_result['anomaly_ratio'] * 4, 1.0))  # 归一化到0-1
        
        # 综合判定
        if len(anomalies) > 0:
            predicted_label = 'neg'
            confidence = np.mean(confidence_scores)
            reasoning = f"检测到异常: {', '.join(anomalies)}"
        else:
            predicted_label = 'pos'
            confidence = 0.9  # 高置信度正常
            reasoning = "未检测到异常"
        
        if debug:
            print(f"  竖线检测: {'异常' if vl_result['anomaly_detected'] else '正常'} "
                  f"({vl_result['vl_ratio']:.3f})")
            print(f"  谐波失真: {'异常' if hd_result['anomaly_detected'] else '正常'} "
                  f"({hd_result['anomaly_ratio']:.3f})")
            print(f"  频谱异常: {'异常' if sa_result['anomaly_detected'] else '正常'} "
                  f"({sa_result['anomaly_ratio']:.3f})")
            print(f"  最终判定: {predicted_label} (置信度: {confidence:.3f})")
        
        return {
            'label': predicted_label,
            'confidence': confidence,
            'reasoning': reasoning,
            'detected_anomalies': anomalies
        }

def validate_final_system():
    """验证最终优化检测系统"""
    print("🔍 验证最终优化检测系统")
    print("="*70)

    detector = FinalOptimizedDetectionSystem()

    # 定义测试文件夹
    test_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }

    validation_results = []

    # 测试所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue

            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)

            print(f"\n📁 测试文件夹: {folder_name} ({true_label}) - {len(wav_files)}个文件")
            print("-" * 60)

            for i, audio_path in enumerate(wav_files):
                filename = os.path.basename(audio_path)

                print(f"  🎵 [{i+1}/{len(wav_files)}] {filename}")

                # 检测
                result = detector.detect_audio_file(audio_path, debug=False)

                if result['status'] == 'success':
                    predicted_label = result['predicted_label']
                    confidence = result['confidence']
                    reasoning = result['reasoning']

                    # 判定正确性
                    is_correct = predicted_label == true_label
                    correctness_icon = "✅" if is_correct else "❌"

                    # 显示结果
                    print(f"     {correctness_icon} 预测: {predicted_label} (置信度: {confidence:.3f})")
                    print(f"        推理: {reasoning}")

                    # 保存结果
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'is_correct': is_correct,
                        'confidence': confidence,
                        'reasoning': reasoning,
                        'status': 'success'
                    })

                else:
                    print(f"     ❌ 检测失败: {result['error']}")
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': 'failed',
                        'is_correct': False,
                        'confidence': 0,
                        'reasoning': 'detection_failed',
                        'status': 'failed',
                        'error': result['error']
                    })

    # 生成验证报告
    generate_final_validation_report(validation_results)

    return validation_results

def generate_final_validation_report(results):
    """生成最终验证报告"""
    print(f"\n" + "="*70)
    print(f"📊 最终优化检测系统验证报告")
    print("="*70)

    df = pd.DataFrame(results)

    # 总体统计
    total_files = len(df)
    success_files = len(df[df['status'] == 'success'])

    print(f"\n📊 总体统计:")
    print(f"  总文件数: {total_files}")
    print(f"  成功检测: {success_files} ({success_files/total_files*100:.1f}%)")

    # 准确率统计
    success_data = df[df['status'] == 'success']

    if len(success_data) > 0:
        correct_predictions = success_data[success_data['is_correct'] == True]
        accuracy = len(correct_predictions) / len(success_data)

        print(f"\n📊 准确率统计:")
        print(f"  总体准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")

        # 按类别统计
        for label in ['pos', 'neg']:
            label_samples = success_data[success_data['true_label'] == label]
            if len(label_samples) > 0:
                label_correct = label_samples[label_samples['is_correct'] == True]
                label_accuracy = len(label_correct) / len(label_samples)
                avg_confidence = label_samples['confidence'].mean()
                print(f"  {label}样本准确率: {label_accuracy:.3f} ({len(label_correct)}/{len(label_samples)}) "
                      f"平均置信度: {avg_confidence:.3f}")

    # 错误案例分析
    error_cases = success_data[success_data['is_correct'] == False]

    if len(error_cases) > 0:
        print(f"\n📊 错误案例分析:")
        print("-" * 50)

        for _, case in error_cases.iterrows():
            print(f"  ❌ {case['filename']}")
            print(f"     真实: {case['true_label']}, 预测: {case['predicted_label']} "
                  f"(置信度: {case['confidence']:.3f})")
            print(f"     推理: {case['reasoning']}")

    # 保存验证结果
    df.to_csv('final_detection_validation.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 验证结果已保存: final_detection_validation.csv")

if __name__ == "__main__":
    validate_final_system()
