#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的高能量噪声检测算法
噪声特征：相对弱能量、变化无规律、频率间波动大、出现在非谐波位置
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy import stats
from scipy.signal import find_peaks
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def method1_relative_weak_energy_detection(freqs, power_db, fundamental_freq, exclude_mask, fundamental_power_db):
    """
    方法1: 相对弱能量检测
    检测相对于主频能量较弱但在噪声底噪中异常的点
    """
    # 噪声区域
    noise_mask = ~exclude_mask
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 计算噪声底噪
    noise_floor = np.percentile(noise_powers, 20)
    
    # 相对弱能量阈值：比主频弱15-30dB，但比噪声底噪强8-15dB
    relative_weak_min = fundamental_power_db - 30  # 不能太弱
    relative_weak_max = fundamental_power_db - 15  # 不能太强
    noise_threshold = noise_floor + 8  # 比噪声底噪强一些
    
    # 检测在合理范围内的异常能量
    weak_noise_mask = (power_db >= noise_threshold) & \
                      (power_db >= relative_weak_min) & \
                      (power_db <= relative_weak_max) & \
                      noise_mask
    
    detected_freqs = freqs[weak_noise_mask]
    detected_powers = power_db[weak_noise_mask]
    
    has_noise = len(detected_freqs) > 0
    
    return has_noise, list(zip(detected_freqs, detected_powers)), \
           f"噪声底噪: {noise_floor:.1f}dB, 检测范围: {relative_weak_min:.1f}~{relative_weak_max:.1f}dB"

def method2_irregular_variation_detection(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法2: 变化无规律检测
    检测频率间剧烈波动和不连续变化
    """
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 10:
        return False, [], "噪声点太少"
    
    # 计算局部变化率
    window_size = 20  # 频率窗口
    irregular_points = []
    
    for i in range(len(noise_powers) - window_size):
        window_powers = noise_powers[i:i+window_size]
        window_freqs = noise_freqs[i:i+window_size]
        
        # 计算局部方差和变化率
        local_var = np.var(window_powers)
        local_range = np.max(window_powers) - np.min(window_powers)
        
        # 检测剧烈变化
        if local_var > 25 and local_range > 15:  # 方差和范围阈值
            # 找到窗口内的异常点
            mean_power = np.mean(window_powers)
            std_power = np.std(window_powers)
            
            for j, (freq, power) in enumerate(zip(window_freqs, window_powers)):
                if abs(power - mean_power) > 2 * std_power:
                    irregular_points.append((freq, power))
    
    # 去重
    irregular_points = list(set(irregular_points))
    has_noise = len(irregular_points) > 0
    
    return has_noise, irregular_points, f"检测到{len(irregular_points)}个变化无规律点"

def method3_frequency_isolation_detection(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法3: 频率孤立性检测
    检测孤立的、缺乏谐波结构的能量点
    """
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 计算噪声底噪和阈值
    noise_floor = np.percentile(noise_powers, 25)
    detection_threshold = noise_floor + 10
    
    # 找到超过阈值的点
    candidate_mask = noise_powers > detection_threshold
    candidate_freqs = noise_freqs[candidate_mask]
    candidate_powers = noise_powers[candidate_mask]
    
    isolated_points = []
    
    for freq, power in zip(candidate_freqs, candidate_powers):
        # 检查周围是否有其他高能量点（孤立性检查）
        freq_tolerance = 50  # Hz
        nearby_mask = (np.abs(noise_freqs - freq) <= freq_tolerance) & \
                      (noise_powers > detection_threshold)
        nearby_count = np.sum(nearby_mask)
        
        # 如果周围高能量点很少，认为是孤立噪声
        if nearby_count <= 3:
            isolated_points.append((freq, power))
    
    has_noise = len(isolated_points) > 0
    
    return has_noise, isolated_points, f"检测到{len(isolated_points)}个孤立噪声点"

def method4_spectral_smoothness_analysis(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法4: 频谱平滑度分析
    检测频谱中的异常尖峰和突变
    """
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声点太少"
    
    # 计算频谱的平滑度
    # 使用一阶和二阶差分检测突变
    diff1 = np.diff(noise_powers)
    diff2 = np.diff(diff1)
    
    # 检测异常的一阶差分（急剧变化）
    diff1_threshold = np.std(diff1) * 3
    sharp_changes = np.where(np.abs(diff1) > diff1_threshold)[0]
    
    # 检测异常的二阶差分（尖峰）
    diff2_threshold = np.std(diff2) * 2.5
    sharp_peaks = np.where(np.abs(diff2) > diff2_threshold)[0]
    
    # 收集异常点
    anomaly_indices = np.unique(np.concatenate([sharp_changes, sharp_peaks, sharp_peaks + 1]))
    anomaly_indices = anomaly_indices[anomaly_indices < len(noise_freqs)]
    
    anomaly_points = [(noise_freqs[i], noise_powers[i]) for i in anomaly_indices]
    
    has_noise = len(anomaly_points) > 0
    
    return has_noise, anomaly_points, f"检测到{len(anomaly_points)}个频谱异常点"

def method5_local_statistical_outlier(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法5: 局部统计异常检测
    在局部环境中检测统计异常点
    """
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    window_size = 50  # 局部窗口大小
    outlier_points = []
    
    for i in range(len(noise_powers)):
        # 定义局部窗口
        start_idx = max(0, i - window_size // 2)
        end_idx = min(len(noise_powers), i + window_size // 2)
        
        local_powers = noise_powers[start_idx:end_idx]
        
        if len(local_powers) < 10:
            continue
        
        # 局部统计
        local_mean = np.mean(local_powers)
        local_std = np.std(local_powers)
        
        # 检测当前点是否为局部异常
        current_power = noise_powers[i]
        z_score = abs(current_power - local_mean) / (local_std + 1e-6)
        
        # 使用更严格的阈值（3σ准则）
        if z_score > 3.0 and current_power > local_mean:
            outlier_points.append((noise_freqs[i], current_power))
    
    has_noise = len(outlier_points) > 0
    
    return has_noise, outlier_points, f"检测到{len(outlier_points)}个局部统计异常点"

def method6_harmonic_structure_absence(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法6: 缺乏谐波结构检测
    检测没有谐波关系的异常能量点
    """
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 计算检测阈值
    noise_floor = np.percentile(noise_powers, 30)
    detection_threshold = noise_floor + 12
    
    # 找到候选点
    candidate_mask = noise_powers > detection_threshold
    candidate_freqs = noise_freqs[candidate_mask]
    candidate_powers = noise_powers[candidate_mask]
    
    non_harmonic_points = []
    
    for freq, power in zip(candidate_freqs, candidate_powers):
        # 检查是否与基频有谐波关系
        is_harmonic_related = False
        
        # 检查是否为基频的分数谐波或倍数谐波
        for ratio in [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]:  # 非整数谐波
            expected_freq = fundamental_freq * ratio
            if abs(freq - expected_freq) < 10:  # 10Hz容差
                is_harmonic_related = True
                break
        
        # 检查是否为其他可能基频的谐波
        for possible_fundamental in [fundamental_freq / 2, fundamental_freq / 3]:
            for order in range(2, 8):
                expected_freq = possible_fundamental * order
                if abs(freq - expected_freq) < 10:
                    is_harmonic_related = True
                    break
            if is_harmonic_related:
                break
        
        # 如果不是谐波相关，认为是噪声
        if not is_harmonic_related:
            non_harmonic_points.append((freq, power))
    
    has_noise = len(non_harmonic_points) > 0
    
    return has_noise, non_harmonic_points, f"检测到{len(non_harmonic_points)}个非谐波结构点"

def analyze_segment_for_real_noise(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的真实噪声
    """

    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            return None

        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 高分辨率FFT
        fft_size = 131072
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]

        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window

        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2

        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]

        # 限制到20kHz
        freq_mask = positive_freqs <= 20000
        freqs = positive_freqs[freq_mask]
        power_linear = positive_power[freq_mask]
        power_db = 10 * np.log10(power_linear + 1e-12)

        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_linear[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_power_db = power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(power_linear) + 1e-12)

        # 创建排除掩码（排除主频和谐波）
        exclude_mask = np.zeros(len(freqs), dtype=bool)

        # 排除主频±5Hz
        main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
        exclude_mask |= main_exclude

        # 排除前10个谐波±5Hz
        for order in range(2, 11):
            harmonic_freq = fundamental_freq * order
            if harmonic_freq <= freqs[-1]:
                harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
                exclude_mask |= harm_exclude

        # 应用6种正确的噪声检测方法
        methods = [
            ("相对弱能量检测", method1_relative_weak_energy_detection),
            ("变化无规律检测", method2_irregular_variation_detection),
            ("频率孤立性检测", method3_frequency_isolation_detection),
            ("频谱平滑度分析", method4_spectral_smoothness_analysis),
            ("局部统计异常", method5_local_statistical_outlier),
            ("缺乏谐波结构", method6_harmonic_structure_absence)
        ]

        results = {}
        for method_name, method_func in methods:
            if method_name == "相对弱能量检测":
                has_noise, detected_points, info = method_func(freqs, power_db, fundamental_freq, exclude_mask, fundamental_power_db)
            else:
                has_noise, detected_points, info = method_func(freqs, power_db, fundamental_freq, exclude_mask)

            results[method_name] = {
                'has_noise': has_noise,
                'detected_points': detected_points,
                'info': info
            }

        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'power_db': power_db,
            'exclude_mask': exclude_mask,
            'results': results
        }

    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_real_noise_visualization(segment_data, output_dir, filename=""):
    """
    创建真实噪声检测可视化
    """

    if not segment_data:
        return None

    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    fundamental_power_db = segment_data['fundamental_power_db']
    freqs = segment_data['freqs']
    power_db = segment_data['power_db']
    exclude_mask = segment_data['exclude_mask']
    results = segment_data['results']

    # 创建图形 - 2x3布局显示6种方法
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n真实噪声检测结果 (主频: {fundamental_power_db:.1f}dB)',
                 fontsize=16, fontweight='bold')

    method_names = list(results.keys())
    colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown']

    for i, (method_name, color) in enumerate(zip(method_names, colors)):
        ax = axes[i]
        result = results[method_name]

        # 绘制基础频谱
        ax.plot(freqs, power_db, 'lightgray', linewidth=1, alpha=0.7, label='频谱')

        # 标记主频
        ax.axvline(fundamental_freq, color='black', linestyle='-', linewidth=3,
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz ({fundamental_power_db:.1f}dB)')

        # 标记谐波区域（排除区域）
        excluded_freqs = freqs[exclude_mask]
        excluded_powers = power_db[exclude_mask]
        if len(excluded_freqs) > 0:
            ax.scatter(excluded_freqs, excluded_powers, c='lightblue', s=1,
                      alpha=0.3, label='谐波区域(排除)')

        # 计算并显示噪声底噪
        noise_mask = ~exclude_mask
        if np.any(noise_mask):
            noise_powers = power_db[noise_mask]
            noise_floor = np.percentile(noise_powers, 20)
            ax.axhline(noise_floor, color='green', linestyle='--', linewidth=1,
                      alpha=0.6, label=f'噪声底噪: {noise_floor:.1f}dB')

        # 标记检测到的噪声点
        detected_points = result['detected_points']
        if detected_points:
            det_freqs, det_powers = zip(*detected_points)
            ax.scatter(det_freqs, det_powers, c=color, s=50, alpha=0.8,
                      marker='x', linewidth=3, label=f'检测噪声({len(detected_points)}个)')

            # 标注一些检测点的功率值
            for j, (freq, power) in enumerate(detected_points[:5]):  # 只标注前5个
                ax.annotate(f'{power:.0f}', (freq, power), xytext=(5, 5),
                           textcoords='offset points', fontsize=8, color=color)

        # 设置标题和标签
        has_noise = result['has_noise']
        noise_status = "检测到噪声" if has_noise else "无噪声"
        ax.set_title(f'{method_name} - {noise_status}\n{result["info"]}', fontsize=10)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, 40)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

        # 设置边框颜色表示检测结果
        for spine in ax.spines.values():
            spine.set_color(color if has_noise else 'gray')
            spine.set_linewidth(3 if has_noise else 1)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"real_noise_detection_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment_real_noise(args):
    """
    处理单个段的真实噪声检测（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 检测段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_for_real_noise(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_real_noise_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 统计检测结果
                results = segment_data['results']
                noise_count = sum(1 for r in results.values() if r['has_noise'])

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_power_db': segment_data['fundamental_power_db'],
                    'noise_methods': noise_count,
                    'total_methods': len(results),
                    'results': results
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 噪声检测: {noise_count}/{len(results)}种方法检测到噪声")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析低频戳洞文件的真实噪声
    """

    print("🎯 低频戳洞文件真实噪声检测")
    print("📝 使用6种正确方法检测真实噪声特征")
    print("📝 噪声特征: 相对弱能量、变化无规律、频率间波动大、非谐波位置")
    print("="*80)

    # 查找低频戳洞文件
    target_file = None
    test_dirs = ["test20250717", "test20250722"]

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, _, files in os.walk(test_dir):
                for file in files:
                    if "低音戳洞" in file and file.lower().endswith('.wav'):
                        target_file = os.path.join(root, file)
                        break
                if target_file:
                    break
        if target_file:
            break

    if not target_file:
        print("❌ 未找到低频戳洞文件")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_real_noise_detection"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行真实噪声检测...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_real_noise, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 统计分析
        method_stats = {}
        method_names = ["相对弱能量检测", "变化无规律检测", "频率孤立性检测",
                       "频谱平滑度分析", "局部统计异常", "缺乏谐波结构"]

        for method_name in method_names:
            method_stats[method_name] = {
                'detected_segments': 0,
                'total_segments': len(successful_results)
            }

        for result in successful_results:
            for method_name in method_names:
                if result['results'][method_name]['has_noise']:
                    method_stats[method_name]['detected_segments'] += 1

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件真实噪声检测完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        print(f"\n🔍 各方法检测结果统计:")
        for method_name, stats in method_stats.items():
            detected = stats['detected_segments']
            total = stats['total_segments']
            percentage = (detected / total * 100) if total > 0 else 0
            print(f"  📈 {method_name}: {detected}/{total} ({percentage:.1f}%)")

        # 综合分析
        multi_method_segments = []
        for result in successful_results:
            noise_count = result['noise_methods']
            if noise_count >= 3:  # 3种以上方法检测到噪声
                multi_method_segments.append((result['seg_idx'], result['expected_freq'],
                                            noise_count, result['fundamental_power_db']))

        print(f"\n🎯 重点关注段（≥3种方法检测到真实噪声）:")
        if multi_method_segments:
            for seg_idx, freq, count, main_power in multi_method_segments:
                print(f"  ⚠️  段{seg_idx} ({freq:.1f}Hz, 主频{main_power:.1f}dB): {count}/6种方法检测到噪声")
        else:
            print("  ✅ 无段被多种方法同时检测为有真实噪声")

        # 主频功率分析
        main_powers = [r['fundamental_power_db'] for r in successful_results]
        avg_main_power = np.mean(main_powers)
        print(f"\n📊 主频功率统计:")
        print(f"  平均主频功率: {avg_main_power:.1f}dB")
        print(f"  主频功率范围: {np.min(main_powers):.1f} ~ {np.max(main_powers):.1f}dB")

        print("="*80)
        print("🎯 真实噪声检测分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
