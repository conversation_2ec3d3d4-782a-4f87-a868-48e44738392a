#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门设计噪声检测特征来分离低频噪声样本
针对前十个频段的全频噪声检测
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def extract_noise_detection_features():
    """提取专门的噪声检测特征"""
    print("🔍 提取专门的噪声检测特征")
    print("="*70)
    print("目标: 检测前十个频段的全频噪声")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_results = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取噪声检测特征
                noise_features = extract_file_noise_features(audio_path, filename)
                
                if noise_features:
                    noise_features['filename'] = filename
                    noise_features['label'] = true_label
                    noise_features['is_target'] = filename in target_files
                    all_results.append(noise_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 保存结果
    df.to_csv('noise_detection_features.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 噪声检测特征已保存: noise_detection_features.csv")
    
    # 分析结果
    analyze_noise_features(df, target_files)
    
    return df

def extract_file_noise_features(audio_path, filename):
    """提取单个文件的噪声检测特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 聚焦前10个频段 (100-180Hz左右)
        target_segments = list(range(min(10, len(step_boundaries))))
        
        noise_features = {}
        
        # 1. 全频噪声检测特征
        noise_features.update(extract_broadband_noise_features(y, sr, step_boundaries, target_segments))
        
        # 2. 频谱纯净度特征
        noise_features.update(extract_spectral_purity_features(y, sr, step_boundaries, target_segments, freq_table))
        
        # 3. 信噪比特征
        noise_features.update(extract_snr_features(y, sr, step_boundaries, target_segments, freq_table))
        
        # 4. 频谱平坦度特征
        noise_features.update(extract_spectral_flatness_features(y, sr, step_boundaries, target_segments))
        
        # 5. 噪声分布特征
        noise_features.update(extract_noise_distribution_features(y, sr, step_boundaries, target_segments))
        
        return noise_features
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_broadband_noise_features(y, sr, step_boundaries, target_segments):
    """提取宽带噪声特征"""
    features = {}
    
    try:
        # 对每个目标频段分析
        noise_powers = []
        noise_bandwidths = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # STFT分析
            f, t, Zxx = stft(segment_audio, sr, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx) ** 2
            
            # 计算每个时间帧的噪声功率
            for frame_idx in range(power_spectrum.shape[1]):
                frame_power = power_spectrum[:, frame_idx]
                
                # 噪声功率 = 非峰值频率的平均功率
                power_threshold = np.percentile(frame_power, 90)  # 90%分位数作为峰值阈值
                noise_mask = frame_power < power_threshold
                
                if np.any(noise_mask):
                    noise_power = np.mean(frame_power[noise_mask])
                    noise_powers.append(noise_power)
                    
                    # 噪声带宽 = 噪声频率的跨度
                    noise_freqs = f[noise_mask]
                    if len(noise_freqs) > 1:
                        noise_bandwidth = np.max(noise_freqs) - np.min(noise_freqs)
                        noise_bandwidths.append(noise_bandwidth)
        
        # 统计特征
        if noise_powers:
            features['noise_power_mean'] = np.mean(noise_powers)
            features['noise_power_std'] = np.std(noise_powers)
            features['noise_power_max'] = np.max(noise_powers)
            features['noise_power_ratio'] = np.mean(noise_powers) / (np.max(noise_powers) + 1e-12)
        else:
            features['noise_power_mean'] = 0
            features['noise_power_std'] = 0
            features['noise_power_max'] = 0
            features['noise_power_ratio'] = 0
        
        if noise_bandwidths:
            features['noise_bandwidth_mean'] = np.mean(noise_bandwidths)
            features['noise_bandwidth_std'] = np.std(noise_bandwidths)
            features['noise_bandwidth_max'] = np.max(noise_bandwidths)
        else:
            features['noise_bandwidth_mean'] = 0
            features['noise_bandwidth_std'] = 0
            features['noise_bandwidth_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['noise_power_mean', 'noise_power_std', 'noise_power_max', 'noise_power_ratio',
                   'noise_bandwidth_mean', 'noise_bandwidth_std', 'noise_bandwidth_max']:
            features[key] = 0
    
    return features

def extract_spectral_purity_features(y, sr, step_boundaries, target_segments, freq_table):
    """提取频谱纯净度特征"""
    features = {}
    
    try:
        purity_scores = []
        harmonic_noise_ratios = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries) or seg_idx >= len(freq_table):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # FFT分析
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(len(segment_audio), 1/sr)
            magnitude = np.abs(fft)
            
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            # 找到期望频率附近的能量
            freq_tolerance = 20
            expected_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
            
            if np.any(expected_mask):
                expected_energy = np.max(positive_magnitude[expected_mask])
                
                # 计算谐波能量
                harmonic_energy = 0
                for harmonic in range(2, 6):
                    harmonic_freq = expected_freq * harmonic
                    if harmonic_freq < sr / 2:
                        harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= freq_tolerance
                        if np.any(harmonic_mask):
                            harmonic_energy += np.max(positive_magnitude[harmonic_mask])
                
                # 计算噪声能量 (除了基频和谐波之外的能量)
                noise_mask = np.ones(len(positive_freqs), dtype=bool)
                noise_mask[expected_mask] = False
                
                for harmonic in range(2, 6):
                    harmonic_freq = expected_freq * harmonic
                    if harmonic_freq < sr / 2:
                        harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= freq_tolerance
                        noise_mask[harmonic_mask] = False
                
                noise_energy = np.sum(positive_magnitude[noise_mask])
                total_energy = np.sum(positive_magnitude)
                
                # 频谱纯净度 = 期望频率能量 / 总能量
                if total_energy > 0:
                    purity_score = expected_energy / total_energy
                    purity_scores.append(purity_score)
                
                # 谐波噪声比 = 谐波能量 / 噪声能量
                if noise_energy > 0:
                    harmonic_noise_ratio = harmonic_energy / noise_energy
                    harmonic_noise_ratios.append(harmonic_noise_ratio)
        
        # 统计特征
        if purity_scores:
            features['spectral_purity_mean'] = np.mean(purity_scores)
            features['spectral_purity_std'] = np.std(purity_scores)
            features['spectral_purity_min'] = np.min(purity_scores)
        else:
            features['spectral_purity_mean'] = 0
            features['spectral_purity_std'] = 0
            features['spectral_purity_min'] = 0
        
        if harmonic_noise_ratios:
            features['harmonic_noise_ratio_mean'] = np.mean(harmonic_noise_ratios)
            features['harmonic_noise_ratio_std'] = np.std(harmonic_noise_ratios)
            features['harmonic_noise_ratio_min'] = np.min(harmonic_noise_ratios)
        else:
            features['harmonic_noise_ratio_mean'] = 0
            features['harmonic_noise_ratio_std'] = 0
            features['harmonic_noise_ratio_min'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['spectral_purity_mean', 'spectral_purity_std', 'spectral_purity_min',
                   'harmonic_noise_ratio_mean', 'harmonic_noise_ratio_std', 'harmonic_noise_ratio_min']:
            features[key] = 0
    
    return features

def extract_snr_features(y, sr, step_boundaries, target_segments, freq_table):
    """提取信噪比特征"""
    features = {}
    
    try:
        snr_values = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries) or seg_idx >= len(freq_table):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 使用Welch方法计算功率谱密度
            freqs, psd = welch(segment_audio, sr, nperseg=1024)
            
            # 找到期望频率附近的信号功率
            freq_tolerance = 20
            signal_mask = np.abs(freqs - expected_freq) <= freq_tolerance
            
            if np.any(signal_mask):
                signal_power = np.max(psd[signal_mask])
                
                # 计算噪声功率 (排除信号频率附近)
                noise_mask = np.abs(freqs - expected_freq) > freq_tolerance * 3
                if np.any(noise_mask):
                    noise_power = np.mean(psd[noise_mask])
                    
                    # 信噪比 (dB)
                    if noise_power > 0:
                        snr_db = 10 * np.log10(signal_power / noise_power)
                        snr_values.append(snr_db)
        
        # 统计特征
        if snr_values:
            features['snr_mean'] = np.mean(snr_values)
            features['snr_std'] = np.std(snr_values)
            features['snr_min'] = np.min(snr_values)
            features['snr_max'] = np.max(snr_values)
        else:
            features['snr_mean'] = 0
            features['snr_std'] = 0
            features['snr_min'] = 0
            features['snr_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['snr_mean', 'snr_std', 'snr_min', 'snr_max']:
            features[key] = 0
    
    return features

def extract_spectral_flatness_features(y, sr, step_boundaries, target_segments):
    """提取频谱平坦度特征"""
    features = {}
    
    try:
        flatness_values = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # STFT分析
            f, t, Zxx = stft(segment_audio, sr, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx) ** 2
            
            # 计算每个时间帧的频谱平坦度
            for frame_idx in range(power_spectrum.shape[1]):
                frame_power = power_spectrum[:, frame_idx]
                
                # 频谱平坦度 = 几何平均 / 算术平均
                if np.all(frame_power > 0):
                    geometric_mean = np.exp(np.mean(np.log(frame_power + 1e-12)))
                    arithmetic_mean = np.mean(frame_power)
                    
                    if arithmetic_mean > 0:
                        flatness = geometric_mean / arithmetic_mean
                        flatness_values.append(flatness)
        
        # 统计特征
        if flatness_values:
            features['spectral_flatness_mean'] = np.mean(flatness_values)
            features['spectral_flatness_std'] = np.std(flatness_values)
            features['spectral_flatness_max'] = np.max(flatness_values)
        else:
            features['spectral_flatness_mean'] = 0
            features['spectral_flatness_std'] = 0
            features['spectral_flatness_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['spectral_flatness_mean', 'spectral_flatness_std', 'spectral_flatness_max']:
            features[key] = 0
    
    return features

def extract_noise_distribution_features(y, sr, step_boundaries, target_segments):
    """提取噪声分布特征"""
    features = {}
    
    try:
        noise_entropies = []
        noise_variances = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 计算噪声分布特征
            # 1. 幅度分布的熵
            hist, _ = np.histogram(segment_audio, bins=50, density=True)
            hist = hist + 1e-12  # 避免log(0)
            entropy = -np.sum(hist * np.log2(hist))
            noise_entropies.append(entropy)
            
            # 2. 噪声方差
            noise_variance = np.var(segment_audio)
            noise_variances.append(noise_variance)
        
        # 统计特征
        if noise_entropies:
            features['noise_entropy_mean'] = np.mean(noise_entropies)
            features['noise_entropy_std'] = np.std(noise_entropies)
            features['noise_entropy_max'] = np.max(noise_entropies)
        else:
            features['noise_entropy_mean'] = 0
            features['noise_entropy_std'] = 0
            features['noise_entropy_max'] = 0
        
        if noise_variances:
            features['noise_variance_mean'] = np.mean(noise_variances)
            features['noise_variance_std'] = np.std(noise_variances)
            features['noise_variance_max'] = np.max(noise_variances)
        else:
            features['noise_variance_mean'] = 0
            features['noise_variance_std'] = 0
            features['noise_variance_max'] = 0
            
    except Exception as e:
        # 设置默认值
        for key in ['noise_entropy_mean', 'noise_entropy_std', 'noise_entropy_max',
                   'noise_variance_mean', 'noise_variance_std', 'noise_variance_max']:
            features[key] = 0
    
    return features

def analyze_noise_features(df, target_files):
    """分析噪声特征的分离能力"""
    print(f"\n🔍 分析噪声特征的分离能力")
    print("="*70)
    
    # 获取特征列
    feature_cols = [col for col in df.columns if col not in ['filename', 'label', 'is_target']]
    print(f"📊 噪声检测特征数: {len(feature_cols)}")
    
    # 分析目标样本与其他样本的分离
    target_data = df[df['is_target'] == True]
    other_data = df[df['is_target'] == False]
    
    print(f"📊 目标样本: {len(target_data)}个")
    print(f"📊 其他样本: {len(other_data)}个")
    
    separable_features = []
    
    for feature in feature_cols:
        target_values = target_data[feature].dropna()
        other_values = other_data[feature].dropna()
        
        if len(target_values) == 0 or len(other_values) == 0:
            continue
        
        target_min, target_max = np.min(target_values), np.max(target_values)
        other_min, other_max = np.min(other_values), np.max(other_values)
        
        # 检查完全分离
        if target_max < other_min:
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max]
            })
        elif target_min > other_max:
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max]
            })
    
    # 排序并显示结果
    separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
    
    print(f"\n✅ 找到 {len(separable_features)} 个可完全分离的噪声特征:")
    
    for i, feature_info in enumerate(separable_features[:10]):
        print(f"  {i+1:2d}. {feature_info['feature']}")
        print(f"      分离类型: {feature_info['separation_type']}")
        print(f"      分离间隙: {feature_info['separation_gap']:.6f}")
        print(f"      目标范围: [{feature_info['target_range'][0]:.6f}, {feature_info['target_range'][1]:.6f}]")
        print(f"      其他范围: [{feature_info['other_range'][0]:.6f}, {feature_info['other_range'][1]:.6f}]")
    
    # 可视化最佳特征
    if len(separable_features) > 0:
        visualize_noise_separation(df, separable_features[:3], target_files)
    
    return separable_features

def visualize_noise_separation(df, top_features, target_files):
    """可视化噪声分离特征"""
    print(f"\n🎨 生成噪声分离可视化...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('噪声检测特征分离分析', fontsize=16, fontweight='bold')
    
    # 1. 最佳特征分布对比
    if len(top_features) > 0:
        ax1 = axes[0, 0]
        feature1 = top_features[0]['feature']
        
        target_data = df[df['is_target'] == True]
        other_data = df[df['is_target'] == False]
        
        target_values = target_data[feature1].dropna()
        other_values = other_data[feature1].dropna()
        
        if len(target_values) > 0 and len(other_values) > 0:
            ax1.hist(other_values, bins=30, alpha=0.6, color='blue', 
                    label=f'其他样本 (n={len(other_values)})', density=True)
            ax1.hist(target_values, bins=30, alpha=0.8, color='red', 
                    label=f'目标样本 (n={len(target_values)})', density=True)
            
            ax1.set_title(f'最佳噪声特征: {feature1}')
            ax1.set_xlabel('特征值')
            ax1.set_ylabel('密度')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
    
    # 2. 特征重要性
    ax2 = axes[0, 1]
    
    if len(top_features) >= 3:
        feature_names = [f['feature'] for f in top_features[:3]]
        gaps = [f['separation_gap'] for f in top_features[:3]]
        
        bars = ax2.bar(range(len(feature_names)), gaps, color=['red', 'orange', 'yellow'])
        ax2.set_title('噪声特征分离能力')
        ax2.set_xlabel('特征')
        ax2.set_ylabel('分离间隙')
        ax2.set_xticks(range(len(feature_names)))
        ax2.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, gap in zip(bars, gaps):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{gap:.4f}', ha='center', va='bottom', fontsize=8)
    
    # 3. 样本分类散点图
    ax3 = axes[1, 0]
    
    if len(top_features) >= 2:
        feature1 = top_features[0]['feature']
        feature2 = top_features[1]['feature']
        
        target_data = df[df['is_target'] == True]
        other_data = df[df['is_target'] == False]
        
        ax3.scatter(other_data[feature1], other_data[feature2], 
                   alpha=0.6, color='blue', label='其他样本', s=30)
        ax3.scatter(target_data[feature1], target_data[feature2], 
                   alpha=0.8, color='red', label='目标样本', s=50, marker='s')
        
        ax3.set_title(f'二维特征分离: {feature1} vs {feature2}')
        ax3.set_xlabel(feature1[:20] + '...' if len(feature1) > 20 else feature1)
        ax3.set_ylabel(feature2[:20] + '...' if len(feature2) > 20 else feature2)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 4. 特征类型分布
    ax4 = axes[1, 1]
    
    feature_types = {
        'noise_power': 0,
        'spectral_purity': 0,
        'snr': 0,
        'spectral_flatness': 0,
        'noise_distribution': 0
    }
    
    for feature_info in top_features:
        feature_name = feature_info['feature']
        if 'noise_power' in feature_name:
            feature_types['noise_power'] += 1
        elif 'spectral_purity' in feature_name or 'harmonic_noise' in feature_name:
            feature_types['spectral_purity'] += 1
        elif 'snr' in feature_name:
            feature_types['snr'] += 1
        elif 'spectral_flatness' in feature_name:
            feature_types['spectral_flatness'] += 1
        elif 'noise_entropy' in feature_name or 'noise_variance' in feature_name:
            feature_types['noise_distribution'] += 1
    
    type_names = list(feature_types.keys())
    type_counts = list(feature_types.values())
    
    colors = ['red', 'orange', 'yellow', 'green', 'blue']
    wedges, texts, autotexts = ax4.pie(type_counts, labels=type_names, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax4.set_title('有效噪声特征类型分布')
    
    plt.tight_layout()
    plt.savefig('noise_detection_separation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: noise_detection_separation_analysis.png")

if __name__ == "__main__":
    df = extract_noise_detection_features()
    print(f"\n✅ 噪声检测特征分析完成！")
