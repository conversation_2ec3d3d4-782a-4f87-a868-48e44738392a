#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对每个负样本单独分析有效特征
找出能与所有正样本完全分离的特征
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_individual_negatives():
    """对每个负样本单独分析"""
    print("🔍 对每个负样本单独分析有效特征")
    print("="*70)
    print("目标：找出每个负样本能与所有正样本完全分离的特征")
    print("="*70)
    
    # 加载特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 排除竖线检测特征和元信息（竖线检测已经单独做过）
    exclude_features = [col for col in df.columns if col.startswith('vl_')] + \
                      ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    
    candidate_features = [col for col in df.columns if col not in exclude_features]
    
    print(f"📊 候选特征数: {len(candidate_features)}个")
    
    # 获取所有负样本文件
    neg_files = df[df['label'] == 'neg']['filename'].unique()
    print(f"📊 负样本文件: {len(neg_files)}个")
    
    # 获取所有正样本数据
    pos_data = df[df['label'] == 'pos']
    print(f"📊 正样本数据: {len(pos_data)}个频段")
    
    # 对每个负样本文件单独分析
    individual_results = {}
    
    for neg_file in neg_files:
        print(f"\n🔍 分析负样本: {neg_file}")
        print("-" * 60)
        
        # 获取该负样本的数据
        neg_file_data = df[df['filename'] == neg_file]
        print(f"   该文件频段数: {len(neg_file_data)}")
        
        # 分析该文件的有效特征
        file_effective_features = analyze_file_effective_features(
            pos_data, neg_file_data, candidate_features, neg_file
        )
        
        individual_results[neg_file] = file_effective_features
    
    # 生成综合分析报告
    generate_individual_analysis_report(individual_results, neg_files)
    
    # 保存结果
    save_individual_analysis_results(individual_results)
    
    return individual_results

def analyze_file_effective_features(pos_data, neg_file_data, candidate_features, neg_file):
    """分析单个文件的有效特征"""
    effective_features = {}
    
    print(f"   分析 {len(candidate_features)} 个特征...")
    
    for feature_idx, feature in enumerate(candidate_features):
        if feature_idx % 10 == 0:
            print(f"   进度: {feature_idx}/{len(candidate_features)}")
        
        # 按频段分析该特征
        segment_separations = []
        
        for segment_idx in sorted(neg_file_data['segment_idx'].unique()):
            # 获取该频段的正样本和负样本数据
            pos_segment = pos_data[pos_data['segment_idx'] == segment_idx]
            neg_segment = neg_file_data[neg_file_data['segment_idx'] == segment_idx]
            
            if len(pos_segment) == 0 or len(neg_segment) == 0:
                continue
            
            # 检查是否可以完全分离
            separation_result = check_complete_separation(
                pos_segment, neg_segment, feature, segment_idx
            )
            
            if separation_result['separable']:
                segment_separations.append({
                    'segment_idx': segment_idx,
                    'expected_freq': pos_segment['expected_freq'].iloc[0],
                    'separation_score': separation_result['separation_score'],
                    'cohens_d': separation_result['cohens_d'],
                    'threshold': separation_result['threshold'],
                    'pos_stats': separation_result['pos_stats'],
                    'neg_stats': separation_result['neg_stats']
                })
        
        # 如果该特征在任何频段都能完全分离，则认为是有效特征
        if len(segment_separations) > 0:
            effective_features[feature] = {
                'separable_segments': segment_separations,
                'segment_count': len(segment_separations),
                'best_segment': max(segment_separations, key=lambda x: x['separation_score']),
                'avg_separation_score': np.mean([s['separation_score'] for s in segment_separations])
            }
    
    print(f"   ✅ 找到 {len(effective_features)} 个有效特征")
    
    # 显示最有效的前10个特征
    if effective_features:
        sorted_features = sorted(effective_features.items(), 
                               key=lambda x: x[1]['avg_separation_score'], 
                               reverse=True)
        
        print(f"   📊 最有效的前10个特征:")
        for i, (feature, info) in enumerate(sorted_features[:10]):
            best_seg = info['best_segment']
            print(f"     {i+1:2d}. {feature}")
            print(f"         可分离频段: {info['segment_count']}个")
            print(f"         最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
            print(f"         分离评分: {best_seg['separation_score']:.3f}")
            print(f"         Cohen's d: {best_seg['cohens_d']:.3f}")
    
    return effective_features

def check_complete_separation(pos_segment, neg_segment, feature, segment_idx):
    """检查是否可以完全分离"""
    try:
        pos_values = pos_segment[feature].dropna()
        neg_values = neg_segment[feature].dropna()
        
        if len(pos_values) == 0 or len(neg_values) == 0:
            return {'separable': False}
        
        # 计算统计量
        pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
        neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
        
        # 效应大小
        pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                             (len(neg_values) - 1) * np.var(neg_values)) / 
                            (len(pos_values) + len(neg_values) - 2))
        cohens_d = abs(pos_mean - neg_mean) / (pooled_std + 1e-12)
        
        # 计算最优分离阈值
        if neg_mean > pos_mean:
            # 负样本值更大，阈值设在两者之间
            threshold = pos_mean + 3*pos_std  # 正样本99.7%置信区间上界
            neg_above_threshold = np.sum(neg_values > threshold)
            pos_above_threshold = np.sum(pos_values > threshold)
        else:
            # 负样本值更小，阈值设在两者之间
            threshold = pos_mean - 3*pos_std  # 正样本99.7%置信区间下界
            neg_above_threshold = np.sum(neg_values < threshold)
            pos_above_threshold = np.sum(pos_values < threshold)
        
        # 完全分离的条件：
        # 1. Cohen's d > 1.5 (大效应)
        # 2. 所有正样本在阈值一侧，所有负样本在阈值另一侧
        separable = (cohens_d > 1.5 and 
                    pos_above_threshold == 0 and 
                    neg_above_threshold == len(neg_values))
        
        if separable:
            separation_score = cohens_d * (1 + neg_above_threshold / len(neg_values))
        else:
            separation_score = 0
        
        return {
            'separable': separable,
            'separation_score': separation_score,
            'cohens_d': cohens_d,
            'threshold': threshold,
            'pos_stats': {'mean': pos_mean, 'std': pos_std, 'count': len(pos_values)},
            'neg_stats': {'mean': neg_mean, 'std': neg_std, 'count': len(neg_values)},
            'pos_above_threshold': pos_above_threshold,
            'neg_above_threshold': neg_above_threshold
        }
        
    except Exception as e:
        return {'separable': False, 'error': str(e)}

def generate_individual_analysis_report(individual_results, neg_files):
    """生成个体分析报告"""
    print(f"\n📊 个体负样本分析报告")
    print("="*70)
    
    # 统计每个文件的有效特征数
    file_feature_counts = {}
    all_effective_features = set()
    
    for neg_file, features in individual_results.items():
        feature_count = len(features)
        file_feature_counts[neg_file] = feature_count
        all_effective_features.update(features.keys())
        
        print(f"\n📁 {neg_file}:")
        print(f"   有效特征数: {feature_count}")
        
        if feature_count > 0:
            # 显示最有效的前5个特征
            sorted_features = sorted(features.items(), 
                                   key=lambda x: x[1]['avg_separation_score'], 
                                   reverse=True)
            
            print(f"   最有效的前5个特征:")
            for i, (feature, info) in enumerate(sorted_features[:5]):
                best_seg = info['best_segment']
                print(f"     {i+1}. {feature}")
                print(f"        最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
                print(f"        分离评分: {best_seg['separation_score']:.3f}")
                print(f"        阈值: {best_seg['threshold']:.6f}")
        else:
            print(f"   ❌ 未找到有效特征")
    
    print(f"\n📊 总体统计:")
    print(f"   总有效特征数: {len(all_effective_features)}")
    print(f"   平均每文件特征数: {np.mean(list(file_feature_counts.values())):.1f}")
    
    # 特征覆盖分析
    feature_coverage = defaultdict(int)
    for neg_file, features in individual_results.items():
        for feature in features.keys():
            feature_coverage[feature] += 1
    
    print(f"\n📊 特征覆盖分析 (能分离多少个负样本文件):")
    print("-" * 50)
    
    sorted_coverage = sorted(feature_coverage.items(), key=lambda x: x[1], reverse=True)
    for i, (feature, count) in enumerate(sorted_coverage[:20]):
        print(f"  {i+1:2d}. {feature}: {count}/{len(neg_files)}个文件")
    
    # 找出能分离所有负样本的特征
    universal_features = [feature for feature, count in feature_coverage.items() 
                         if count == len(neg_files)]
    
    if universal_features:
        print(f"\n🎯 能分离所有负样本的通用特征:")
        for feature in universal_features:
            print(f"   ✅ {feature}")
    else:
        print(f"\n⚠️ 没有找到能分离所有负样本的通用特征")
    
    # 文件覆盖情况
    print(f"\n📊 文件覆盖情况:")
    print("-" * 50)
    
    sorted_files = sorted(file_feature_counts.items(), key=lambda x: x[1], reverse=True)
    for neg_file, count in sorted_files:
        status = "✅ 可检测" if count > 0 else "❌ 难检测"
        print(f"  {status} {neg_file}: {count}个有效特征")

def save_individual_analysis_results(individual_results):
    """保存个体分析结果"""
    print(f"\n💾 保存个体分析结果")
    print("="*70)
    
    # 创建详细结果表
    detailed_results = []
    
    for neg_file, features in individual_results.items():
        for feature, info in features.items():
            for seg_info in info['separable_segments']:
                detailed_results.append({
                    'neg_file': neg_file,
                    'feature': feature,
                    'segment_idx': seg_info['segment_idx'],
                    'expected_freq': seg_info['expected_freq'],
                    'separation_score': seg_info['separation_score'],
                    'cohens_d': seg_info['cohens_d'],
                    'threshold': seg_info['threshold'],
                    'pos_mean': seg_info['pos_stats']['mean'],
                    'pos_std': seg_info['pos_stats']['std'],
                    'pos_count': seg_info['pos_stats']['count'],
                    'neg_mean': seg_info['neg_stats']['mean'],
                    'neg_std': seg_info['neg_stats']['std'],
                    'neg_count': seg_info['neg_stats']['count']
                })
    
    if detailed_results:
        detailed_df = pd.DataFrame(detailed_results)
        detailed_df.to_csv('individual_negative_analysis_detailed.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 详细结果已保存: individual_negative_analysis_detailed.csv")
    
    # 创建摘要结果表
    summary_results = []
    
    for neg_file, features in individual_results.items():
        summary_results.append({
            'neg_file': neg_file,
            'effective_feature_count': len(features),
            'best_feature': max(features.items(), key=lambda x: x[1]['avg_separation_score'])[0] if features else '',
            'best_separation_score': max(features.items(), key=lambda x: x[1]['avg_separation_score'])[1]['avg_separation_score'] if features else 0,
            'total_separable_segments': sum(info['segment_count'] for info in features.values())
        })
    
    summary_df = pd.DataFrame(summary_results)
    summary_df.to_csv('individual_negative_analysis_summary.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 摘要结果已保存: individual_negative_analysis_summary.csv")
    
    # 创建特征覆盖表
    feature_coverage = defaultdict(list)
    for neg_file, features in individual_results.items():
        for feature in features.keys():
            feature_coverage[feature].append(neg_file)
    
    coverage_results = []
    for feature, files in feature_coverage.items():
        coverage_results.append({
            'feature': feature,
            'coverage_count': len(files),
            'coverage_ratio': len(files) / len(individual_results),
            'covered_files': ';'.join(files)
        })
    
    coverage_df = pd.DataFrame(coverage_results)
    coverage_df = coverage_df.sort_values('coverage_count', ascending=False)
    coverage_df.to_csv('feature_coverage_analysis.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 特征覆盖分析已保存: feature_coverage_analysis.csv")

if __name__ == "__main__":
    individual_results = analyze_individual_negatives()
