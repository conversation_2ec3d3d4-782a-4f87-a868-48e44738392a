import numpy as np
import librosa
import matplotlib.pyplot as plt
import os
from align import align_signals_by_energy, align_signals_by_dominant_freq
import librosa.effects as fx

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 静音剔除 ==========
def trim_valid_region(y, threshold_ratio=0.1, frame_length=2048, hop_length=512):
    energy = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
    threshold = np.max(energy) * threshold_ratio
    mask = energy > threshold
    if not np.any(mask):
        return y
    start = np.argmax(mask)
    end = len(mask) - np.argmax(mask[::-1])
    return y[start * hop_length : end * hop_length]

# ========== 特征提取 ==========
def get_energy_envelope(y, n_fft=2048, hop_length=512):
    spec_abs = np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length))
    return np.sum(spec_abs, axis=0)

def get_dominant_frequency_track(y, sr, n_fft=2048, hop_length=512):
    S = np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length))
    dom_bin = np.argmax(S, axis=0)
    freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
    return freqs[dom_bin]

def get_spectral_leakage(S, top_k=1):
    S_db = librosa.amplitude_to_db(S, ref=np.max)
    top_vals = np.partition(S_db, -top_k, axis=0)[-top_k:]
    return np.mean(S_db - top_vals[0], axis=0)

def get_spectral_flatness(y, n_fft=2048, hop_length=512):
    return librosa.feature.spectral_flatness(y=y, n_fft=n_fft, hop_length=hop_length)[0]

def get_spectral_centroid(y, sr, n_fft=2048, hop_length=512):
    # 返回每帧的质心频率
    return librosa.feature.spectral_centroid(y=y, sr=sr, n_fft=n_fft, hop_length=hop_length)[0]

def get_spectral_contrast(y, sr, n_fft=2048, hop_length=512, n_bands=6):
    # 返回每帧在 n_bands 个频带上的谱峭度；取平均即可得到单值曲线
    contrast = librosa.feature.spectral_contrast(y=y, sr=sr, n_fft=n_fft, hop_length=hop_length, n_bands=n_bands)
    return np.mean(contrast, axis=0)

# ========== 相似性计算 ==========
def cosine_similarity(a, b):
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b) + 1e-8)

# ========== 通用绘图函数 ==========
def plot_and_save(data1, data2, title, ylabel, filename, sr, hop_length):
    L = min(len(data1), len(data2))
    t = np.arange(L) * hop_length / sr
    plt.figure(figsize=(10, 4))
    plt.plot(t, data1[:L], label="参考样本", alpha=0.7)
    plt.plot(t, data2[:L], label="待测样本", alpha=0.7)
    plt.title(title)
    plt.xlabel("时间 (秒)")
    plt.ylabel(ylabel)
    plt.legend()
    plt.tight_layout()
    plt.savefig(filename, dpi=150)
    plt.close()

def normalize_audio(y, mode='rms'):
    if mode == 'peak':
        return y / (np.max(np.abs(y)) + 1e-8)
    elif mode == 'rms':
        rms = np.sqrt(np.mean(y**2)) + 1e-8
        return y / rms
    else:
        raise ValueError("mode must be 'peak' or 'rms'")


# ========== 主函数 ==========
def analyze_speaker(ref_path, test_path, sr=44100, output_dir="analysis_output"):
    strat_time = 1.09
    end_time = 11.729
    os.makedirs(output_dir, exist_ok=True)
    name_ref = os.path.splitext(os.path.basename(ref_path))[0]
    name_test = os.path.splitext(os.path.basename(test_path))[0]

    # 加载 & 对齐 & 静音剔除    
    y_ref, _  = librosa.load(ref_path, sr=sr, mono=True)
    y_test, _ = librosa.load(test_path, sr=sr, mono=True)
    # y_ref = normalize_audio(y_ref, mode='rms')
    # y_test = normalize_audio(y_test, mode='rms')

    # 2. 可选预加重或压缩
    # y_ref  = fx.preemphasis(y_ref, coef=0.95)
    # y_test = fx.preemphasis(y_test, coef=0.95)
    
    y_ref, y_test = align_signals_by_energy(y_ref, y_test, fs=sr)
    # y_ref, y_test = trim_valid_region(y_ref), trim_valid_region(y_test)
    y_ref = y_ref[int(strat_time * sr):int(end_time * sr)]
    y_test = y_test[int(strat_time * sr):int(end_time * sr)]
    min_len = min(len(y_ref), len(y_test))
    y_ref, y_test = y_ref[:min_len], y_test[:min_len]

    # 特征计算
    env_ref, env_test         = get_energy_envelope(y_ref), get_energy_envelope(y_test)
    dom_ref, dom_test         = get_dominant_frequency_track(y_ref, sr), get_dominant_frequency_track(y_test, sr)
    S_ref, S_test             = np.abs(librosa.stft(y_ref)), np.abs(librosa.stft(y_test))
    leak_ref, leak_test       = get_spectral_leakage(S_ref), get_spectral_leakage(S_test)
    flat_ref, flat_test       = get_spectral_flatness(y_ref), get_spectral_flatness(y_test)
    cent_ref, cent_test       = get_spectral_centroid(y_ref, sr), get_spectral_centroid(y_test, sr)
    contrast_ref, contrast_test = get_spectral_contrast(y_ref, sr), get_spectral_contrast(y_test, sr)

    # --- 打印若干指标 ---
    print(f"\n--- [{name_ref}] vs [{name_test}] 指标汇总 ---")
    print(f"能量包络相似度: {cosine_similarity(env_ref, env_test):.4f}")
    print(f"主频相似度:     {cosine_similarity(dom_ref, dom_test):.4f}")
    print(f"泄漏差异:       {np.mean(np.abs(leak_test - leak_ref)):.4f}")
    print(f"平坦度均值:     {flat_ref.mean():.3f} / {flat_test.mean():.3f}")
    print(f"质心均值:       {cent_ref.mean():.1f} Hz / {cent_test.mean():.1f} Hz")
    print(f"峭度均值:       {contrast_ref.mean():.3f} / {contrast_test.mean():.3f}")

    # --- 可视化 & 保存 ---
    plot_and_save(env_ref, env_test,
                  f"能量包络对比 [{name_ref} vs {name_test}]",
                  "能量",
                  os.path.join(output_dir, f"env_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

    plot_and_save(dom_ref, dom_test,
                  f"主频轨迹对比 [{name_ref} vs {name_test}]",
                  "频率 (Hz)",
                  os.path.join(output_dir, f"dom_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

    plot_and_save(leak_ref, leak_test,
                  f"频谱泄漏率对比 [{name_ref} vs {name_test}]",
                  "泄漏量 (dB)",
                  os.path.join(output_dir, f"leak_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

    plot_and_save(flat_ref, flat_test,
                  f"平坦度对比 [{name_ref} vs {name_test}]",
                  "平坦度",
                  os.path.join(output_dir, f"flatness_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

    plot_and_save(cent_ref, cent_test,
                  f"谱质心对比 [{name_ref} vs {name_test}]",
                  "质心频率 (Hz)",
                  os.path.join(output_dir, f"centroid_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

    plot_and_save(contrast_ref, contrast_test,
                  f"谱峭度对比 [{name_ref} vs {name_test}]",
                  "峭度",
                  os.path.join(output_dir, f"contrast_{name_ref}_vs_{name_test}.png"),
                  sr, hop_length=512)

if __name__ == "__main__":
    # REF_LIST = [
    #     r"test20250703\pos1.wav",
    #     r"test20250703\pos2.wav",
    #     r"test20250703\pos3.wav"
    # ]
    # TEST_LIST = [
    #     r"test20250703\neg1.wav",
    #     r"test20250703\neg2.wav",
    #     r"test20250703\neg3.wav"
    # ]
    
    
    REF_LIST = [
        r"20250707\sweep_test_0db.wav",
        # r"20250707\test0.wav",
    ]
    TEST_LIST = [
        r"20250707\test0.wav",
        r"20250707\test1.wav",
        r"20250707\test2.wav",
        r"20250707\test3.wav",
    ]

    fs = 48000
    for ref_path in REF_LIST:
        for test_path in TEST_LIST:
            analyze_speaker(ref_path, test_path, sr=fs)