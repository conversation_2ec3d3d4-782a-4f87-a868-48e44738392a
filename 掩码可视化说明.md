# 掩码可视化功能说明

## 🎯 掩码可视化简介

掩码可视化功能可以显示去除主频和谐波后的"纯噪声"频谱，帮助更清楚地观察和分析噪声特性。

## 🔧 工作原理

### 掩码创建过程
1. **主频掩码**: 去除主频±5Hz范围内的频率成分
2. **谐波掩码**: 去除前10个谐波±5Hz范围内的频率成分
3. **掩码应用**: 将掩码区域的功率值设为NaN（不显示）

### 掩码范围
```
主频掩码: [fundamental_freq - 5Hz, fundamental_freq + 5Hz]
谐波掩码: [n × fundamental_freq - 5Hz, n × fundamental_freq + 5Hz]
其中 n = 2, 3, 4, ..., 10
```

## 🎨 可视化效果

### 标准频谱 vs 掩码频谱
- **黑色线**: 原始频谱（包含所有频率成分）
- **绿色线**: 掩码后频谱（去除主频和谐波后的纯噪声）
- **红色线**: 动态噪声阈值
- **蓝色圆点**: 主频标记

### 掩码区域特征
- **空白区域**: 被掩码去除的主频和谐波位置
- **连续绿线**: 剩余的噪声频谱成分
- **对比明显**: 信号与噪声的清晰分离

## 🔧 使用方法

### 基本掩码可视化
```bash
python universal_spectrum_analyzer.py my_audio.wav --show-masked
```

### 梅尔刻度 + 掩码可视化
```bash
python universal_spectrum_analyzer.py my_audio.wav --mel-scale --show-masked
```

### 完整参数示例
```bash
python universal_spectrum_analyzer.py my_audio.wav --show-masked --freq-range 100 8000 -w 4
```

## 📁 输出文件

### 文件命名规则
- **标准**: `segment_XX_XXXXHz_log.png`
- **掩码**: `segment_XX_XXXXHz_log_masked.png`
- **梅尔掩码**: `segment_XX_XXXXHz_mel_masked.png`

### 输出目录
- 掩码文件与标准文件保存在同一目录
- 文件名包含 `_masked` 后缀以示区别

## 📊 应用场景

### 1. 噪声分析
- **纯噪声特性**: 观察去除信号后的噪声分布
- **噪声水平**: 评估不同频段的噪声强度
- **噪声类型**: 识别白噪声、粉红噪声等特征

### 2. 信号质量评估
- **信噪比分析**: 对比信号和噪声的功率差异
- **失真检测**: 识别非谐波失真成分
- **干扰识别**: 发现外部干扰信号

### 3. 算法验证
- **掩码效果**: 验证主频和谐波检测的准确性
- **阈值设置**: 评估动态噪声阈值的合理性
- **参数调优**: 优化频率搜索和掩码范围

### 4. 教学演示
- **频谱组成**: 直观展示信号的频率成分
- **噪声概念**: 理解噪声在频域的表现
- **滤波原理**: 演示频域滤波的效果

## 🔍 分析技巧

### 观察要点
1. **掩码完整性**: 检查主频和谐波是否完全去除
2. **噪声连续性**: 观察噪声频谱的平滑程度
3. **异常峰值**: 识别掩码后仍存在的异常峰值
4. **频率分布**: 分析噪声在不同频段的分布特征

### 对比分析
- **原始 vs 掩码**: 对比信号成分和噪声成分
- **不同频段**: 比较各频段的噪声特性差异
- **阈值关系**: 观察噪声与动态阈值的关系

## 💡 实用建议

### 1. 参数选择
- **频率范围**: 根据分析需求选择合适的频率范围
- **刻度选择**: 对数刻度适合工程分析，梅尔刻度适合感知分析
- **进程数**: 根据计算资源调整并行进程数

### 2. 结果解读
- **绿色连续**: 表示该频率范围主要是噪声
- **绿色断续**: 表示存在被掩码的信号成分
- **绿色峰值**: 可能是非谐波干扰或失真

### 3. 问题诊断
- **掩码不完整**: 可能是主频检测不准确
- **噪声异常**: 可能存在外部干扰或设备问题
- **阈值偏差**: 可能需要调整噪声估计参数

## 📈 技术优势

### 1. 直观性
- **视觉分离**: 清晰区分信号和噪声成分
- **对比明显**: 原始频谱与掩码频谱的直观对比
- **教学友好**: 适合教学和演示使用

### 2. 准确性
- **精确掩码**: 基于实际检测的主频和谐波位置
- **动态调整**: 每个频段独立计算掩码范围
- **算法一致**: 与动态噪声阈值计算完全一致

### 3. 实用性
- **多刻度支持**: 同时支持对数和梅尔刻度
- **批量处理**: 多进程并行生成所有频段
- **格式统一**: 与标准频谱图保持一致的格式

## 🎯 总结

掩码可视化功能为频谱分析提供了新的视角：

- **噪声研究**: 专注于纯噪声特性分析
- **质量评估**: 从噪声角度评估音频质量
- **算法验证**: 验证信号检测和噪声估计算法
- **教学工具**: 直观展示频域信号处理概念

通过原始频谱和掩码频谱的对比，可以获得更深入的频谱分析洞察！
