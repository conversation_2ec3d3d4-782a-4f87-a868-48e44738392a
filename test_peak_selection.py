#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试峰值选择功能
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
from final_audio_detection_system.freq_split_optimized import OptimizedFreqSplitter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def test_peak_selection():
    """测试峰值选择功能"""
    print("🔍 测试峰值选择功能")
    print("="*60)
    
    # 创建分割器
    splitter = OptimizedFreqSplitter()
    
    # 测试音频路径
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    print(f"测试文件: {audio_path}")
    
    # 读取测试音频
    y, sr = librosa.load(audio_path, sr=splitter.fs)
    if sr != splitter.fs:
        y = librosa.resample(y, orig_sr=sr, target_sr=splitter.fs)
    
    # 搜索参数
    search_start = 0.1
    search_end = 1.5
    corr_length = 1.0
    
    # 执行搜索
    search_start_sample = int(search_start * splitter.fs)
    search_end_sample = int(search_end * splitter.fs)
    corr_length_samples = int(corr_length * splitter.fs)
    
    search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
    
    # 准备参考信号段
    initial_corr_length = min(corr_length_samples, int(0.5 * splitter.fs))
    reference_segment = splitter.reference_signal[:initial_corr_length]
    
    # 计算相关性
    search_length = search_end_sample - search_start_sample
    correlations = []
    positions = []
    
    step_size = max(1, splitter.fs // 500)  # 2ms步长
    
    for i in range(0, search_length - initial_corr_length, step_size):
        pos = search_start_sample + i
        test_segment = y[pos:pos + initial_corr_length]
        
        if len(test_segment) == len(reference_segment):
            correlation = splitter._normalized_cross_correlation(reference_segment, test_segment)
            correlations.append(correlation)
            positions.append(pos / splitter.fs)
    
    correlations = np.array(correlations)
    positions = np.array(positions)
    
    # 分析阈值选择逻辑
    max_correlation = np.max(correlations)
    max_idx = np.argmax(correlations)
    
    # 阈值设置
    relative_threshold = max_correlation * 0.7  # 70%
    absolute_threshold = 0.4
    final_threshold = max(relative_threshold, absolute_threshold)
    
    print(f"\n阈值计算:")
    print(f"  最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
    print(f"  最终阈值: {final_threshold:.3f}")
    
    # 找到符合条件的点
    good_indices = np.where(correlations >= final_threshold)[0]
    
    print(f"\n符合条件的点分析:")
    print(f"  符合条件的点数: {len(good_indices)}")
    
    if len(good_indices) > 0:
        # 传统方法：选择第一个满足条件的点
        traditional_idx = good_indices[0]
        traditional_pos = positions[traditional_idx]
        traditional_corr = correlations[traditional_idx]
        
        print(f"\n📊 传统方法（第一个满足条件的点）:")
        print(f"  位置: {traditional_pos:.3f}s")
        print(f"  相关性: {traditional_corr:.3f}")
        
        # 峰值选择方法
        good_correlations = correlations[good_indices]
        
        # 寻找峰值点
        peaks, peak_properties = find_peaks(good_correlations, 
                                          height=final_threshold,
                                          distance=5,
                                          prominence=0.05)
        
        print(f"\n🎯 峰值选择方法:")
        print(f"  检测到峰值数: {len(peaks)}")
        
        if len(peaks) > 0:
            print(f"  所有峰值点:")
            for i, peak_idx in enumerate(peaks):
                actual_idx = good_indices[peak_idx]
                print(f"    峰值{i+1}: 时间={positions[actual_idx]:.3f}s, 相关性={correlations[actual_idx]:.3f}")
            
            # 选择第一个峰值
            first_peak_idx = peaks[0]
            selected_good_idx = good_indices[first_peak_idx]
            peak_pos = positions[selected_good_idx]
            peak_corr = correlations[selected_good_idx]
            
            print(f"\n  ✅ 选择第一个峰值:")
            print(f"    位置: {peak_pos:.3f}s")
            print(f"    相关性: {peak_corr:.3f}")
            
            # 对比结果
            print(f"\n📈 方法对比:")
            print(f"  传统方法: {traditional_pos:.3f}s (相关性: {traditional_corr:.3f})")
            print(f"  峰值方法: {peak_pos:.3f}s (相关性: {peak_corr:.3f})")
            print(f"  时间差异: {abs(peak_pos - traditional_pos)*1000:.1f}ms")
            print(f"  相关性提升: {peak_corr - traditional_corr:.3f}")
            
            if peak_corr > traditional_corr:
                print(f"  🎉 峰值方法获得了更高的相关性！")
            elif abs(peak_pos - traditional_pos) < 0.005:  # 5ms内
                print(f"  ✅ 两种方法结果相近，峰值方法更稳定")
            else:
                print(f"  ⚠️ 两种方法有显著差异")
        else:
            print(f"  ❌ 未检测到明显峰值，回退到传统方法")
    
    # 可视化对比
    fig, axes = plt.subplots(2, 1, figsize=(15, 10))
    fig.suptitle('传统方法 vs 峰值选择方法对比', fontsize=16, fontweight='bold')
    
    # 1. 完整相关性曲线
    ax1 = axes[0]
    ax1.plot(positions, correlations, 'b-', linewidth=1, alpha=0.8, label='相关性曲线')
    
    # 标记阈值线
    ax1.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, 
               label=f'阈值 ({final_threshold:.3f})')
    
    # 标记最大相关性点
    ax1.plot(positions[max_idx], correlations[max_idx], 'go', markersize=10, 
            label=f'最大相关性 ({positions[max_idx]:.3f}s)')
    
    # 标记符合条件的点
    if len(good_indices) > 0:
        ax1.plot(positions[good_indices], correlations[good_indices], 'co', 
                markersize=4, alpha=0.7, label=f'符合条件的点 ({len(good_indices)}个)')
        
        # 标记传统方法选择的点
        ax1.plot(traditional_pos, traditional_corr, 'rs', 
                markersize=12, label=f'传统方法 ({traditional_pos:.3f}s)')
        
        # 标记峰值方法选择的点
        if len(peaks) > 0:
            ax1.plot(peak_pos, peak_corr, 'r*', 
                    markersize=15, label=f'峰值方法 ({peak_pos:.3f}s)')
    
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('相关性系数')
    ax1.set_title('方法对比 - 完整视图')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0.1, 0.6)
    
    # 2. 放大关键区域
    ax2 = axes[1]
    key_start = 0.25
    key_end = 0.35
    key_mask = (positions >= key_start) & (positions <= key_end)
    
    if np.any(key_mask):
        ax2.plot(positions[key_mask], correlations[key_mask], 'b-', linewidth=2, label='相关性曲线')
        
        # 标记阈值线
        ax2.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, 
                   label=f'阈值 ({final_threshold:.3f})')
        
        # 标记选择的点
        if len(good_indices) > 0:
            # 传统方法
            if key_start <= traditional_pos <= key_end:
                ax2.plot(traditional_pos, traditional_corr, 'rs', 
                        markersize=12, label=f'传统方法')
            
            # 峰值方法
            if len(peaks) > 0 and key_start <= peak_pos <= key_end:
                ax2.plot(peak_pos, peak_corr, 'r*', 
                        markersize=15, label=f'峰值方法')
    
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('相关性系数')
    ax2.set_title(f'关键区域放大 ({key_start}s - {key_end}s)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('peak_selection_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return traditional_pos, peak_pos if len(peaks) > 0 else traditional_pos

if __name__ == "__main__":
    traditional_result, peak_result = test_peak_selection()
