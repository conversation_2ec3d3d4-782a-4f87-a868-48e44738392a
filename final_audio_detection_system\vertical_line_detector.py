#!/usr/bin/env python3
"""
竖线异常检测器
Vertical Line Anomaly Detector
检测频谱中的竖线异常（瞬时多频段能量突增）
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft
from scipy.ndimage import gaussian_filter1d
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class VerticalLineDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # 竖线检测参数 - 调整为更敏感的检测
        self.detection_params = {
            'min_frequency_span': 500,         # 最小频率跨度(Hz) - 降低
            'min_frequency_ratio': 0.1,        # 最小频率覆盖比例 - 降低
            'energy_threshold_percentile': 85, # 能量阈值百分位 - 降低
            'smoothing_sigma': 1.0,            # 平滑参数
            'min_line_strength': 0.1           # 最小竖线强度 - 降低
        }
        
        print(f"竖线异常检测器初始化完成")
        print(f"检测目标: 频谱中的瞬时多频段能量突增（竖线）")
    
    def detect_vertical_lines(self, audio_path, start_time, end_time):
        """检测指定时间段的竖线异常"""
        print(f"\n检测竖线异常: {os.path.basename(audio_path)}")
        print(f"时间段: {start_time:.3f}s - {end_time:.3f}s")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选 (100-20000Hz)
            freq_mask = (frequencies >= 100) & (frequencies <= 20000)
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 提取指定时间段
            segment_mask = (times >= start_time) & (times <= end_time)
            segment_times = times[segment_mask]
            segment_Zxx = Zxx[:, segment_mask]
            
            if segment_Zxx.shape[1] == 0:
                print(f"指定时间段无数据")
                return None
            
            print(f"分析数据: {segment_Zxx.shape[1]}个时间片, {len(frequencies)}个频率点")
            
            # 计算功率谱
            power_spectrum = np.abs(segment_Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 检测竖线 - 使用多种方法
            vertical_lines_method1 = self._detect_vertical_lines_in_spectrum(
                power_db, frequencies, segment_times
            )

            # 方法2: 基于频谱梯度的竖线检测
            vertical_lines_method2 = self._detect_lines_by_gradient(
                power_db, frequencies, segment_times
            )

            # 合并两种方法的结果
            all_lines = vertical_lines_method1 + vertical_lines_method2
            vertical_lines = self._merge_nearby_lines(all_lines)
            
            # 可视化结果
            self._visualize_vertical_line_detection(
                power_db, frequencies, segment_times, vertical_lines, 
                audio_path, start_time, end_time
            )
            
            return {
                'power_spectrum': power_db,
                'frequencies': frequencies,
                'times': segment_times,
                'vertical_lines': vertical_lines,
                'detection_summary': self._summarize_detection(vertical_lines)
            }
            
        except Exception as e:
            print(f"检测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _detect_vertical_lines_in_spectrum(self, power_db, frequencies, times):
        """在功率谱中检测竖线"""
        print("\n竖线检测分析:")
        
        vertical_lines = []
        
        # 方法1: 基于每个时间片的能量分布
        for t_idx, time_point in enumerate(times):
            time_power = power_db[:, t_idx]
            
            # 计算能量阈值
            energy_threshold = np.percentile(time_power, self.detection_params['energy_threshold_percentile'])
            
            # 找到高能量频点
            high_energy_mask = time_power > energy_threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) == 0:
                continue
            
            # 检查频率跨度
            freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
            freq_ratio = len(high_energy_indices) / len(frequencies)
            
            # 计算竖线强度
            line_strength = self._calculate_line_strength(time_power, high_energy_indices)
            
            # 判断是否为竖线
            is_vertical_line = (
                freq_span >= self.detection_params['min_frequency_span'] and
                freq_ratio >= self.detection_params['min_frequency_ratio'] and
                line_strength >= self.detection_params['min_line_strength']
            )
            
            if is_vertical_line:
                # 找到连续的频率段
                continuous_segments = self._find_continuous_frequency_segments(high_energy_indices)
                
                vertical_line = {
                    'time_index': t_idx,
                    'time': time_point,
                    'frequency_span': freq_span,
                    'frequency_ratio': freq_ratio,
                    'line_strength': line_strength,
                    'energy_threshold': energy_threshold,
                    'high_energy_indices': high_energy_indices,
                    'continuous_segments': continuous_segments,
                    'max_power': np.max(time_power[high_energy_indices]),
                    'mean_power': np.mean(time_power[high_energy_indices])
                }
                
                vertical_lines.append(vertical_line)
                
                print(f"  时刻 {time_point:.3f}s: 频率跨度={freq_span:.0f}Hz, "
                      f"覆盖比例={freq_ratio:.1%}, 强度={line_strength:.2f}")
        
        # 方法2: 基于时间维度的能量突变检测
        additional_lines = self._detect_energy_bursts(power_db, frequencies, times)
        
        # 合并结果并去重
        all_lines = vertical_lines + additional_lines
        merged_lines = self._merge_nearby_lines(all_lines)
        
        print(f"\n检测结果: 发现 {len(merged_lines)} 条竖线异常")
        
        return merged_lines
    
    def _calculate_line_strength(self, time_power, high_energy_indices):
        """计算竖线强度"""
        if len(high_energy_indices) == 0:
            return 0.0
        
        # 计算高能量区域的功率集中度
        high_energy_power = time_power[high_energy_indices]
        background_power = np.delete(time_power, high_energy_indices)
        
        if len(background_power) == 0:
            return 1.0
        
        # 强度 = 高能量区域平均功率 / 背景功率平均值
        strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
        
        # 归一化到0-1范围
        return min(1.0, strength / 100.0)
    
    def _find_continuous_frequency_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 3:  # 至少3个连续点
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        # 处理最后一段
        if len(current_segment) >= 3:
            segments.append(current_segment)
        
        return segments
    
    def _detect_energy_bursts(self, power_db, frequencies, times):
        """基于能量峰值检测竖线"""
        additional_lines = []

        # 计算每个时间片的总能量
        total_energy = np.sum(power_db, axis=0)

        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=self.detection_params['smoothing_sigma'])

        # 找到能量峰值而不是突变
        from scipy.signal import find_peaks

        # 计算能量的相对高度
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        min_height = np.min(smoothed_energy) + energy_range * 0.3  # 峰值至少要比最低值高30%

        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy,
                                     height=min_height,
                                     distance=3,  # 峰值间至少间隔3个时间片
                                     prominence=energy_range * 0.1)  # 峰值突出度

        print(f"  检测到 {len(peaks)} 个能量峰值")

        burst_indices = peaks
        
        for burst_idx in burst_indices:
            if burst_idx < len(times):
                time_point = times[burst_idx]
                time_power = power_db[:, burst_idx]

                # 对于峰值点，使用更宽松的竖线判断标准
                # 计算该时间点的功率分布特征
                power_std = np.std(time_power)
                power_mean = np.mean(time_power)

                # 找到显著高于平均值的频率点
                significance_threshold = power_mean + 1.0 * power_std
                high_energy_mask = time_power > significance_threshold
                high_energy_indices = np.where(high_energy_mask)[0]

                if len(high_energy_indices) >= 5:  # 至少5个频率点
                    freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                    freq_ratio = len(high_energy_indices) / len(frequencies)

                    # 对峰值点使用更宽松的条件
                    if freq_span >= 200:  # 至少200Hz跨度

                        # 计算峰值强度
                        peak_energy = total_energy[burst_idx]
                        baseline_energy = np.median(total_energy)
                        line_strength = min(1.0, (peak_energy - baseline_energy) / (baseline_energy + 1e-12))

                        additional_line = {
                            'time_index': burst_idx,
                            'time': time_point,
                            'frequency_span': freq_span,
                            'frequency_ratio': freq_ratio,
                            'line_strength': line_strength,
                            'energy_threshold': significance_threshold,
                            'high_energy_indices': high_energy_indices,
                            'continuous_segments': self._find_continuous_frequency_segments(high_energy_indices),
                            'max_power': np.max(time_power[high_energy_indices]),
                            'mean_power': np.mean(time_power[high_energy_indices]),
                            'detection_method': 'energy_peak',
                            'peak_energy': peak_energy,
                            'baseline_energy': baseline_energy
                        }

                        additional_lines.append(additional_line)

                        print(f"    峰值检测到竖线: 时刻={time_point:.3f}s, "
                              f"频率跨度={freq_span:.0f}Hz, 强度={line_strength:.3f}")
        
        return additional_lines

    def _detect_lines_by_gradient(self, power_db, frequencies, times):
        """基于频谱梯度检测竖线"""
        print("  使用梯度方法检测竖线...")

        gradient_lines = []

        # 计算时间维度的梯度（检测瞬时变化）
        time_gradient = np.abs(np.gradient(power_db, axis=1))

        # 对每个时间点分析
        for t_idx, time_point in enumerate(times):
            gradient_column = time_gradient[:, t_idx]
            power_column = power_db[:, t_idx]

            # 找到梯度较大的频率点
            gradient_threshold = np.percentile(gradient_column, 90)
            high_gradient_mask = gradient_column > gradient_threshold

            # 同时要求功率也较高
            power_threshold = np.percentile(power_column, 80)
            high_power_mask = power_column > power_threshold

            # 组合条件
            line_mask = high_gradient_mask & high_power_mask
            line_indices = np.where(line_mask)[0]

            if len(line_indices) >= 5:  # 至少5个频率点
                # 检查频率连续性
                continuous_segments = self._find_continuous_frequency_segments(line_indices)

                if continuous_segments:
                    freq_span = frequencies[line_indices[-1]] - frequencies[line_indices[0]]
                    freq_ratio = len(line_indices) / len(frequencies)

                    # 计算竖线强度（基于梯度和功率）
                    gradient_strength = np.mean(gradient_column[line_indices])
                    power_strength = np.mean(power_column[line_indices])
                    combined_strength = (gradient_strength + power_strength) / 200.0  # 归一化

                    gradient_line = {
                        'time_index': t_idx,
                        'time': time_point,
                        'frequency_span': freq_span,
                        'frequency_ratio': freq_ratio,
                        'line_strength': min(1.0, combined_strength),
                        'energy_threshold': power_threshold,
                        'high_energy_indices': line_indices,
                        'continuous_segments': continuous_segments,
                        'max_power': np.max(power_column[line_indices]),
                        'mean_power': np.mean(power_column[line_indices]),
                        'detection_method': 'gradient',
                        'gradient_strength': gradient_strength
                    }

                    gradient_lines.append(gradient_line)

                    print(f"    梯度法检测到竖线: 时刻={time_point:.3f}s, "
                          f"频率跨度={freq_span:.0f}Hz, 强度={combined_strength:.3f}")

        return gradient_lines
    
    def _merge_nearby_lines(self, lines):
        """合并相近的竖线"""
        if not lines:
            return []
        
        # 按时间排序
        lines.sort(key=lambda x: x['time'])
        
        merged = [lines[0]]
        
        for line in lines[1:]:
            last_line = merged[-1]
            
            # 如果时间相近（小于2个时间片），合并
            if abs(line['time'] - last_line['time']) < 0.05:  # 50ms内
                # 保留强度更高的
                if line['line_strength'] > last_line['line_strength']:
                    merged[-1] = line
            else:
                merged.append(line)
        
        return merged
    
    def _summarize_detection(self, vertical_lines):
        """总结检测结果"""
        if not vertical_lines:
            return {
                'total_lines': 0,
                'average_strength': 0,
                'frequency_spans': [],
                'time_points': []
            }
        
        return {
            'total_lines': len(vertical_lines),
            'average_strength': np.mean([line['line_strength'] for line in vertical_lines]),
            'frequency_spans': [line['frequency_span'] for line in vertical_lines],
            'time_points': [line['time'] for line in vertical_lines],
            'max_strength': max(line['line_strength'] for line in vertical_lines),
            'total_frequency_coverage': sum(line['frequency_ratio'] for line in vertical_lines)
        }
    
    def _visualize_vertical_line_detection(self, power_db, frequencies, times, vertical_lines, 
                                         audio_path, start_time, end_time):
        """可视化竖线检测结果"""
        print(f"\n生成竖线检测可视化...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'竖线异常检测: {os.path.basename(audio_path)}\n时间段: {start_time:.3f}s - {end_time:.3f}s', fontsize=14)
        
        # 1. 原始功率谱 + 检测结果
        im1 = axes[0, 0].imshow(power_db, aspect='auto', origin='lower', 
                               extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                               cmap='viridis')
        axes[0, 0].set_title('原始功率谱 + 竖线检测结果')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('频率 (Hz)')
        
        # 标记检测到的竖线
        for line in vertical_lines:
            axes[0, 0].axvline(x=line['time'], color='red', linewidth=2, alpha=0.8,
                              label=f"竖线 {line['time']:.3f}s" if line == vertical_lines[0] else "")
        
        if vertical_lines:
            axes[0, 0].legend()
        plt.colorbar(im1, ax=axes[0, 0], label='功率 (dB)')
        
        # 2. 竖线强度分布
        if vertical_lines:
            line_times = [line['time'] for line in vertical_lines]
            line_strengths = [line['line_strength'] for line in vertical_lines]
            
            axes[0, 1].scatter(line_times, line_strengths, c='red', s=100, alpha=0.7)
            axes[0, 1].set_title('竖线强度分布')
            axes[0, 1].set_xlabel('时间 (s)')
            axes[0, 1].set_ylabel('竖线强度')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 添加强度阈值线
            axes[0, 1].axhline(y=self.detection_params['min_line_strength'], 
                              color='orange', linestyle='--', alpha=0.7, label='检测阈值')
            axes[0, 1].legend()
        else:
            axes[0, 1].text(0.5, 0.5, '未检测到竖线', ha='center', va='center', transform=axes[0, 1].transAxes)
            axes[0, 1].set_title('竖线强度分布')
        
        # 3. 时间维度能量分析
        total_energy = np.sum(power_db, axis=0)
        axes[1, 0].plot(times, total_energy, 'b-', linewidth=1, label='总能量')
        
        # 平滑后的能量
        smoothed_energy = gaussian_filter1d(total_energy, sigma=self.detection_params['smoothing_sigma'])
        axes[1, 0].plot(times, smoothed_energy, 'g-', linewidth=2, label='平滑能量')
        
        # 标记竖线位置
        for line in vertical_lines:
            axes[1, 0].axvline(x=line['time'], color='red', alpha=0.7, linestyle='--')
        
        axes[1, 0].set_title('时间维度能量分析')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('总能量 (dB)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 频率覆盖分析
        if vertical_lines:
            line_indices = range(len(vertical_lines))
            freq_spans = [line['frequency_span'] for line in vertical_lines]
            freq_ratios = [line['frequency_ratio'] * 100 for line in vertical_lines]  # 转换为百分比
            
            ax4_twin = axes[1, 1].twinx()
            
            bars1 = axes[1, 1].bar([i - 0.2 for i in line_indices], freq_spans, 0.4, 
                                  label='频率跨度 (Hz)', alpha=0.7, color='blue')
            bars2 = ax4_twin.bar([i + 0.2 for i in line_indices], freq_ratios, 0.4, 
                                label='频率覆盖比例 (%)', alpha=0.7, color='orange')
            
            axes[1, 1].set_title('竖线频率特征')
            axes[1, 1].set_xlabel('竖线编号')
            axes[1, 1].set_ylabel('频率跨度 (Hz)', color='blue')
            ax4_twin.set_ylabel('频率覆盖比例 (%)', color='orange')
            
            axes[1, 1].legend(loc='upper left')
            ax4_twin.legend(loc='upper right')
            
            # 设置x轴标签
            axes[1, 1].set_xticks(line_indices)
            axes[1, 1].set_xticklabels([f'L{i+1}' for i in line_indices])
        else:
            axes[1, 1].text(0.5, 0.5, '未检测到竖线', ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('竖线频率特征')
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'vertical_line_detection_{start_time:.3f}s_{end_time:.3f}s.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  可视化结果已保存: {output_filename}")
        
        plt.show()

def main():
    """主函数"""
    # 初始化检测器
    detector = VerticalLineDetector()
    
    # 检测指定频段的竖线
    audio_path = "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav"
    start_time = 11.07
    end_time = 11.225
    
    # 执行检测
    results = detector.detect_vertical_lines(audio_path, start_time, end_time)
    
    if results and results['vertical_lines']:
        print(f"\n检测总结:")
        summary = results['detection_summary']
        print(f"  检测到竖线数量: {summary['total_lines']}")
        print(f"  平均强度: {summary['average_strength']:.3f}")
        print(f"  最大强度: {summary['max_strength']:.3f}")
        print(f"  竖线时间点: {[f'{t:.3f}s' for t in summary['time_points']]}")
        print(f"  频率跨度: {[f'{span:.0f}Hz' for span in summary['frequency_spans']]}")
    else:
        print(f"\n未检测到明显的竖线异常")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
