#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细解释正常样本各频段范围的计算方法
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def explain_normal_range_calculation():
    """详细解释正常样本范围计算方法"""
    print("📊 正常样本各频段范围计算方法详解")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    
    # 筛选正常样本数据
    normal_data = df[df['is_target'] == False]
    
    print(f"📈 数据结构说明:")
    print(f"   总记录数: {len(df)}")
    print(f"   正常样本记录数: {len(normal_data)}")
    print(f"   正常样本文件数: {len(normal_data['filename'].unique())}")
    print(f"   频段数: {len(normal_data['segment_idx'].unique())}")
    print(f"   特征数: 4个")
    
    print(f"\n🔍 数据组织方式:")
    print(f"   每个文件 × 每个频段 = 一条记录")
    print(f"   54个正常文件 × 93个频段 = 5022条记录")
    print(f"   每条记录包含4个特征值")
    
    # 选择一个具体的频段和特征进行详细说明
    example_segment = 0  # 100Hz频段
    example_feature = 'true_noise_floor_median'
    
    print(f"\n📊 以频段{example_segment} (100Hz) 的 '{example_feature}' 为例:")
    print("-" * 50)
    
    # 获取该频段该特征的所有正常样本数据
    seg_data = normal_data[normal_data['segment_idx'] == example_segment]
    feature_values = seg_data[example_feature].values
    
    print(f"   该频段正常样本数: {len(feature_values)}")
    print(f"   特征值范围: [{np.min(feature_values):.3f}, {np.max(feature_values):.3f}]")
    
    # 计算各种统计量
    stats = calculate_detailed_statistics(feature_values)
    
    print(f"\n📈 统计量计算:")
    for stat_name, value in stats.items():
        print(f"   {stat_name}: {value:.6f}")
    
    print(f"\n🎯 范围区间定义:")
    print(f"   1. 最小值-最大值范围: [{stats['min']:.3f}, {stats['max']:.3f}]")
    print(f"      含义: 所有正常样本的绝对范围")
    print(f"      用途: 检测是否完全超出正常范围")
    
    print(f"\n   2. 25%-75%分位数范围: [{stats['p25']:.3f}, {stats['p75']:.3f}]")
    print(f"      含义: 中间50%样本的分布范围")
    print(f"      用途: 检测是否偏离主要分布")
    
    print(f"\n   3. 均值±1σ范围: [{stats['mean_minus_1std']:.3f}, {stats['mean_plus_1std']:.3f}]")
    print(f"      含义: 68%样本的理论分布范围")
    print(f"      用途: 检测是否偏离正态分布")
    
    print(f"\n   4. 均值±2σ范围: [{stats['mean_minus_2std']:.3f}, {stats['mean_plus_2std']:.3f}]")
    print(f"      含义: 95%样本的理论分布范围")
    print(f"      用途: 检测显著异常")
    
    print(f"\n🔬 计算公式:")
    print(f"   均值 (mean) = Σ(xi) / n")
    print(f"   标准差 (std) = √[Σ(xi - mean)² / (n-1)]")
    print(f"   分位数 (percentile) = 排序后第k%位置的值")
    print(f"   Z-score = (x - mean) / std")
    
    # 可视化范围计算过程
    visualize_range_calculation(feature_values, stats, example_segment, example_feature)
    
    # 展示所有频段的范围计算
    demonstrate_all_segments_calculation(normal_data, example_feature)

def calculate_detailed_statistics(values):
    """计算详细的统计量"""
    stats = {}
    
    # 基本统计量
    stats['count'] = len(values)
    stats['mean'] = np.mean(values)
    stats['std'] = np.std(values, ddof=1)  # 样本标准差
    stats['min'] = np.min(values)
    stats['max'] = np.max(values)
    
    # 分位数
    stats['p25'] = np.percentile(values, 25)
    stats['p50'] = np.percentile(values, 50)  # 中位数
    stats['p75'] = np.percentile(values, 75)
    
    # 均值±标准差范围
    stats['mean_minus_1std'] = stats['mean'] - stats['std']
    stats['mean_plus_1std'] = stats['mean'] + stats['std']
    stats['mean_minus_2std'] = stats['mean'] - 2 * stats['std']
    stats['mean_plus_2std'] = stats['mean'] + 2 * stats['std']
    
    # 四分位距
    stats['iqr'] = stats['p75'] - stats['p25']
    
    # 异常值检测阈值 (1.5倍四分位距)
    stats['outlier_lower'] = stats['p25'] - 1.5 * stats['iqr']
    stats['outlier_upper'] = stats['p75'] + 1.5 * stats['iqr']
    
    return stats

def visualize_range_calculation(values, stats, segment_idx, feature_name):
    """可视化范围计算过程"""
    print(f"\n🎨 生成范围计算可视化...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 1. 直方图显示分布
    ax1.hist(values, bins=30, alpha=0.7, color='lightblue', edgecolor='black', density=True)
    
    # 添加统计线
    ax1.axvline(stats['mean'], color='red', linestyle='-', linewidth=2, label=f"均值: {stats['mean']:.3f}")
    ax1.axvline(stats['p50'], color='green', linestyle='-', linewidth=2, label=f"中位数: {stats['p50']:.3f}")
    
    # 添加范围区间
    ax1.axvspan(stats['mean_minus_1std'], stats['mean_plus_1std'], 
               alpha=0.2, color='red', label='±1σ范围')
    ax1.axvspan(stats['mean_minus_2std'], stats['mean_plus_2std'], 
               alpha=0.1, color='red', label='±2σ范围')
    ax1.axvspan(stats['p25'], stats['p75'], 
               alpha=0.2, color='green', label='25%-75%分位数')
    
    ax1.set_title(f'频段{segment_idx} {feature_name} 分布', fontweight='bold')
    ax1.set_xlabel('特征值')
    ax1.set_ylabel('密度')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 箱线图显示范围
    box_data = [values]
    bp = ax2.boxplot(box_data, patch_artist=True, labels=[f'频段{segment_idx}'])
    bp['boxes'][0].set_facecolor('lightblue')
    
    # 添加统计点
    ax2.scatter([1], [stats['mean']], color='red', s=100, marker='D', label='均值', zorder=5)
    ax2.scatter([1], [stats['p50']], color='green', s=100, marker='s', label='中位数', zorder=5)
    
    # 添加范围线
    ax2.hlines(stats['mean_minus_1std'], 0.8, 1.2, colors='red', linestyles='--', alpha=0.7)
    ax2.hlines(stats['mean_plus_1std'], 0.8, 1.2, colors='red', linestyles='--', alpha=0.7)
    ax2.text(1.25, stats['mean_minus_1std'], '均值-1σ', va='center', fontsize=8)
    ax2.text(1.25, stats['mean_plus_1std'], '均值+1σ', va='center', fontsize=8)
    
    ax2.set_title(f'频段{segment_idx} {feature_name} 箱线图', fontweight='bold')
    ax2.set_ylabel('特征值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'normal_range_calculation_example_seg{segment_idx}.png', dpi=300, bbox_inches='tight')
    print(f"✅ 可视化已保存: normal_range_calculation_example_seg{segment_idx}.png")
    plt.close()

def demonstrate_all_segments_calculation(normal_data, feature_name):
    """展示所有频段的范围计算"""
    print(f"\n📊 所有93个频段的范围计算示例 ({feature_name}):")
    print("-" * 80)
    print(f"{'频段':>4} {'频率(Hz)':>8} {'样本数':>6} {'均值':>10} {'标准差':>8} {'最小值':>8} {'最大值':>8} {'范围宽度':>8}")
    print("-" * 80)
    
    segments = sorted(normal_data['segment_idx'].unique())
    
    for seg_idx in segments[:10]:  # 只显示前10个频段作为示例
        seg_data = normal_data[normal_data['segment_idx'] == seg_idx]
        
        if len(seg_data) > 0:
            freq = seg_data['expected_freq'].iloc[0]
            values = seg_data[feature_name].values
            
            stats = calculate_detailed_statistics(values)
            range_width = stats['max'] - stats['min']
            
            print(f"{seg_idx:>4} {freq:>8.1f} {stats['count']:>6} {stats['mean']:>10.3f} "
                  f"{stats['std']:>8.3f} {stats['min']:>8.3f} {stats['max']:>8.3f} {range_width:>8.3f}")
    
    print("   ...")
    print(f"   (共93个频段，每个频段都按相同方法计算)")
    
    print(f"\n🔍 范围计算的关键步骤:")
    print(f"   1. 对每个频段，收集所有正常样本的特征值")
    print(f"   2. 计算该频段的统计量 (均值、标准差、分位数等)")
    print(f"   3. 定义多层范围区间:")
    print(f"      - 绝对范围: [最小值, 最大值]")
    print(f"      - 主要分布: [25%分位数, 75%分位数]")
    print(f"      - 正态范围: [均值±1σ, 均值±2σ]")
    print(f"   4. 用这些范围来判断新样本是否异常")
    
    print(f"\n💡 异常检测逻辑:")
    print(f"   - 超出绝对范围 → 极度异常")
    print(f"   - 超出±2σ范围 → 显著异常")
    print(f"   - 超出±1σ范围 → 轻度异常")
    print(f"   - 在25%-75%范围内 → 正常")
    
    print(f"\n📈 可视化中的范围表示:")
    print(f"   - 最浅色区域: 绝对范围 [最小值, 最大值]")
    print(f"   - 中等色区域: 25%-75%分位数范围")
    print(f"   - 较深色区域: 均值±1σ范围")
    print(f"   - 实线: 均值线")
    print(f"   - 星号标记: 超出正常范围的异常点")

if __name__ == "__main__":
    explain_normal_range_calculation()
