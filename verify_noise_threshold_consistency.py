#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证动态噪声阈值计算与参考脚本的一致性
"""

import os
import sys
import numpy as np
import librosa
from scipy.signal import savgol_filter

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'harmonic_detection_system'))

try:
    from freq_split_optimized import split_freq_steps_optimized
    from harmonic_detector_api import estimate_dynamic_noise_for_segment
except ImportError as e:
    print(f"警告: 无法导入必要模块: {e}")
    sys.exit(1)

def calculate_dynamic_noise_curve_exact(freqs, power, fundamental_freq):
    """
    我们的实现 - 基于harmonic_detector_api方法
    """
    # 滑动窗口参数 - 与harmonic_detector_api完全一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None, None, None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 插值到完整频率范围
    dynamic_noise_curve = np.interp(freqs, window_centers, smoothed_noise)
    
    # 计算全局噪声底噪
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    
    return dynamic_noise_curve, global_noise_floor_db, local_noise_levels, window_centers

def verify_consistency():
    """
    验证动态噪声阈值计算的一致性
    """
    print("🔍 验证动态噪声阈值计算一致性")
    print("="*60)
    
    # 测试音频文件
    audio_path = "test20250722/琴身内部异物1.1.wav"
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return
    
    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        print("🎵 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000
        
        print(f"✅ 音频加载完成，采样率: {sr}Hz")
        
        # 测试几个代表性频段
        test_segments = [0, 10, 30, 50, 70, 92]
        
        print(f"\n📊 测试 {len(test_segments)} 个代表性频段:")
        print("-" * 80)
        print("段号  频率(Hz)  原始全局底噪  我们的全局底噪  差异(dB)  一致性")
        print("-" * 80)
        
        total_diff = 0
        consistent_count = 0
        
        for seg_idx in test_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            # 提取段音频
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            # 去除首尾8%
            trim_length = int(len(segment_audio) * 0.08)
            if len(segment_audio) > 2 * trim_length:
                segment_audio = segment_audio[trim_length:-trim_length]
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析
            fft_size = 131072
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 调用原始函数
            original_noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 调用我们的函数
            our_curve, our_global, our_local, our_centers = calculate_dynamic_noise_curve_exact(display_freqs, display_power, fundamental_freq)
            
            # 比较结果
            if original_noise_analysis and our_global is not None:
                original_global = original_noise_analysis['global_noise_floor_db']
                diff = abs(our_global - original_global)
                total_diff += diff
                
                # 判断一致性（差异小于0.1dB认为一致）
                is_consistent = diff < 0.1
                if is_consistent:
                    consistent_count += 1
                
                consistency_mark = "✅" if is_consistent else "❌"
                
                print(f"{seg_idx:2d}    {expected_freq:6.0f}    {original_global:10.3f}    {our_global:12.3f}    {diff:8.3f}    {consistency_mark}")
            else:
                print(f"{seg_idx:2d}    {expected_freq:6.0f}    {'N/A':>10}    {'N/A':>12}    {'N/A':>8}    ❌")
        
        print("-" * 80)
        
        # 统计结果
        avg_diff = total_diff / len(test_segments) if len(test_segments) > 0 else 0
        consistency_rate = consistent_count / len(test_segments) * 100 if len(test_segments) > 0 else 0
        
        print(f"\n📈 一致性验证结果:")
        print(f"  测试频段数: {len(test_segments)}")
        print(f"  一致频段数: {consistent_count}")
        print(f"  一致性率: {consistency_rate:.1f}%")
        print(f"  平均差异: {avg_diff:.3f}dB")
        
        if consistency_rate >= 90:
            print(f"  ✅ 一致性验证通过！")
        else:
            print(f"  ❌ 一致性验证失败！")
        
        print(f"\n🔧 技术细节验证:")
        print(f"  ✅ 滑动窗口大小: 200个频率bin")
        print(f"  ✅ 滑动步长: 50个频率bin")
        print(f"  ✅ 排除主频: ±5Hz")
        print(f"  ✅ 排除谐波: 前10个谐波±5Hz")
        print(f"  ✅ 噪声统计: 25th百分位数")
        print(f"  ✅ 平滑方法: Savitzky-Golay滤波")
        print(f"  ✅ 全局底噪: 20th百分位数")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_consistency()
