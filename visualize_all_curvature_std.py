#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化test20250717所有样本的93段动态噪声阈值曲率标准差
每个文件生成一张图片，显示所有93段的曲率标准差变化
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，适合多进程
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import calculate_noise_fluctuation_features

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声，返回曲率标准差
    """
    
    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 计算曲率标准差
    if len(smoothed_noise) > 2:
        # 计算二阶差分（曲率）
        diff1 = np.diff(smoothed_noise)
        if len(diff1) > 1:
            diff2 = np.diff(diff1)
            std_curvature = np.std(diff2)
        else:
            std_curvature = 0
    else:
        std_curvature = 0
    
    return {
        'std_curvature': std_curvature,
        'smoothed_noise': smoothed_noise,
        'window_centers': window_centers
    }

def analyze_all_segments_curvature(audio_path):
    """
    分析单个文件所有93段的曲率标准差
    """
    
    filename = os.path.basename(audio_path)
    print(f"📁 分析文件: {filename}")
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 分析所有段
        segment_results = []
        
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            # 提取段音频，去掉开头和结尾各8%
            trim_percentage = 0.08  # 去掉开头和结尾各8%
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            # 确保修剪后的时间段有效
            if trimmed_end_time <= trimmed_start_time:
                print(f"  ⚠️  段{seg_idx}修剪后时间段无效，跳过")
                continue

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
            segment_audio = y[start_sample:end_sample]

            if len(segment_audio) == 0:
                print(f"  ⚠️  段{seg_idx}修剪后音频为空，跳过")
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            if noise_analysis:
                segment_results.append({
                    'seg_idx': seg_idx,
                    'freq': expected_freq,
                    'std_curvature': noise_analysis['std_curvature'],
                    'smoothed_noise': noise_analysis['smoothed_noise'],
                    'window_centers': noise_analysis['window_centers']
                })
        
        return {
            'filename': filename,
            'segment_results': segment_results
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def create_curvature_visualization(file_data, output_dir):
    """
    创建单个文件的曲率标准差可视化
    """
    
    if not file_data or not file_data['segment_results']:
        print("❌ 无有效分析结果")
        return None
    
    filename = file_data['filename']
    segment_results = file_data['segment_results']
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 提取文件名（不含路径和扩展名）
    base_filename = os.path.splitext(filename)[0]
    
    # 判断是否为异常文件
    is_abnormal = any(keyword in filename for keyword in ['低音戳洞', '153632', '155101'])
    status = "异常文件" if is_abnormal else "正常文件"
    
    fig.suptitle(f'{base_filename}\n93段动态噪声阈值曲率标准差分析 ({status})', 
                 fontsize=14, fontweight='bold')
    
    # 提取数据
    frequencies = [result['freq'] for result in segment_results]
    curvature_stds = [result['std_curvature'] for result in segment_results]
    
    # 计算统计信息
    mean_curvature = np.mean(curvature_stds)
    std_curvature = np.std(curvature_stds)
    max_curvature = np.max(curvature_stds)
    min_curvature = np.min(curvature_stds)
    
    # 子图1: 曲率标准差随频率变化
    color = 'red' if is_abnormal else 'blue'
    ax1.plot(frequencies, curvature_stds, 'o-', color=color, linewidth=2, 
             markersize=4, alpha=0.7, label=f'曲率标准差')
    
    # 添加统计线
    ax1.axhline(mean_curvature, color='green', linestyle='--', linewidth=2, 
                alpha=0.8, label=f'平均值: {mean_curvature:.3f}')
    
    # 添加异常阈值线
    ax1.axhline(0.3, color='orange', linestyle='--', linewidth=2, 
                alpha=0.8, label='异常阈值: 0.3')
    
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('曲率标准差')
    ax1.set_title(f'曲率标准差随频率变化\n平均值: {mean_curvature:.3f} ± {std_curvature:.3f}, 范围: [{min_curvature:.3f}, {max_curvature:.3f}]')
    ax1.set_xscale('log')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 子图2: 曲率标准差分布直方图
    ax2.hist(curvature_stds, bins=20, color=color, alpha=0.7, edgecolor='black')
    ax2.axvline(mean_curvature, color='green', linestyle='--', linewidth=2, 
                label=f'平均值: {mean_curvature:.3f}')
    ax2.axvline(0.3, color='orange', linestyle='--', linewidth=2, 
                label='异常阈值: 0.3')
    
    ax2.set_xlabel('曲率标准差')
    ax2.set_ylabel('频次')
    ax2.set_title(f'曲率标准差分布直方图 (共{len(curvature_stds)}段)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 添加统计信息文本
    stats_text = f"""统计信息:
平均值: {mean_curvature:.3f}
标准差: {std_curvature:.3f}
最大值: {max_curvature:.3f}
最小值: {min_curvature:.3f}
超过阈值(0.3)的段数: {sum(1 for x in curvature_stds if x > 0.3)}/{len(curvature_stds)}
分类: {status}"""
    
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)
    
    plt.tight_layout()
    
    # 保存图片
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_path = os.path.join(output_dir, f"{safe_filename}_curvature_std_analysis.png")
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def process_single_file(args):
    """
    处理单个文件的包装函数，用于多进程
    """
    wav_file, output_dir, file_index, total_files = args

    try:
        rel_path = os.path.relpath(wav_file, "test20250717")
        print(f"[{file_index}/{total_files}] 处理: {rel_path}")

        # 分析文件
        file_data = analyze_all_segments_curvature(wav_file)

        if file_data:
            # 生成可视化
            viz_path = create_curvature_visualization(file_data, output_dir)

            if viz_path:
                # 计算平均曲率标准差
                curvature_stds = [result['std_curvature'] for result in file_data['segment_results']]
                mean_curvature = np.mean(curvature_stds)

                # 判断是否为异常文件
                is_abnormal = any(keyword in file_data['filename'] for keyword in ['低音戳洞', '153632', '155101'])

                result = {
                    'success': True,
                    'filename': file_data['filename'],
                    'mean_curvature': mean_curvature,
                    'is_abnormal': is_abnormal,
                    'viz_path': viz_path,
                    'num_segments': len(file_data['segment_results'])
                }

                print(f"  ✅ [{file_index}/{total_files}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 平均曲率标准差: {mean_curvature:.3f}")

                return result
            else:
                print(f"  ❌ [{file_index}/{total_files}] 可视化失败")
                return {'success': False, 'filename': os.path.basename(wav_file), 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{file_index}/{total_files}] 分析失败")
            return {'success': False, 'filename': os.path.basename(wav_file), 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{file_index}/{total_files}] 处理异常: {e}")
        return {'success': False, 'filename': os.path.basename(wav_file), 'error': str(e)}

def main():
    """
    主函数 - 分析test20250717目录下所有wav文件的曲率标准差
    """

    print("🎯 可视化test20250717所有样本的93段动态噪声阈值曲率标准差")
    print("="*70)

    # 创建输出目录
    output_dir = "curvature_std_analysis"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 查找所有wav文件
    test_dir = "test20250717"
    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return

    # 递归查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))

    if not wav_files:
        print(f"❌ 在{test_dir}目录下未找到wav文件")
        return

    print(f"🔍 找到{len(wav_files)}个wav文件")

    # 确定进程数
    num_processes = min(cpu_count(), len(wav_files), 8)  # 最多8个进程
    print(f"🚀 使用{num_processes}个进程并行处理")

    # 准备参数
    process_args = [(wav_file, output_dir, i+1, len(wav_files)) for i, wav_file in enumerate(wav_files)]

    # 多进程处理
    start_time = time.time()
    successful_count = 0
    failed_count = 0
    abnormal_files = []
    normal_files = []

    print(f"\n⏱️  开始并行处理...")

    with Pool(processes=num_processes) as pool:
        results = pool.map(process_single_file, process_args)

    # 处理结果
    for result in results:
        if result['success']:
            successful_count += 1

            if result['is_abnormal']:
                abnormal_files.append((result['filename'], result['mean_curvature']))
            else:
                normal_files.append((result['filename'], result['mean_curvature']))
        else:
            failed_count += 1

    end_time = time.time()
    processing_time = end_time - start_time

    # 生成汇总统计
    print("\n" + "="*70)
    print(f"📊 多进程分析完成统计:")
    print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
    print(f"  🚀 平均每文件: {processing_time/len(wav_files):.1f}秒")
    print(f"  ✅ 成功: {successful_count}个文件")
    print(f"  ❌ 失败: {failed_count}个文件")
    print(f"  📁 输出目录: {output_dir}")
    print(f"  📊 生成图片: {successful_count}张")

    print(f"\n🎯 异常文件汇总 ({len(abnormal_files)}个):")
    abnormal_files.sort(key=lambda x: x[1], reverse=True)  # 按曲率标准差排序
    for filename, mean_curvature in abnormal_files:
        short_name = filename[:50] + '...' if len(filename) > 50 else filename
        print(f"  📈 {short_name}: {mean_curvature:.3f}")

    print(f"\n✅ 正常文件汇总 (前10个最高曲率标准差):")
    normal_files.sort(key=lambda x: x[1], reverse=True)  # 按曲率标准差排序
    for filename, mean_curvature in normal_files[:10]:
        short_name = filename[:50] + '...' if len(filename) > 50 else filename
        print(f"  📊 {short_name}: {mean_curvature:.3f}")

    # 统计分析
    if abnormal_files and normal_files:
        abnormal_curvatures = [x[1] for x in abnormal_files]
        normal_curvatures = [x[1] for x in normal_files]

        print(f"\n📈 统计对比:")
        print(f"  异常文件平均曲率标准差: {np.mean(abnormal_curvatures):.3f} ± {np.std(abnormal_curvatures):.3f}")
        print(f"  正常文件平均曲率标准差: {np.mean(normal_curvatures):.3f} ± {np.std(normal_curvatures):.3f}")
        print(f"  差异: {np.mean(abnormal_curvatures) - np.mean(normal_curvatures):.3f}")

    print("="*70)
    print("🎯 曲率标准差可视化分析完成！")

if __name__ == "__main__":
    main()
