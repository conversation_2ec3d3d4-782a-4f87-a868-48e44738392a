#!/usr/bin/env python3
"""
分析异响检测算法的有效性
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import seaborn as sns

def load_all_results(result_root="output_0716"):
    """加载所有分析结果"""
    results = {}
    
    for item in os.listdir(result_root):
        item_path = os.path.join(result_root, item)
        if os.path.isdir(item_path):
            csv_path = os.path.join(item_path, "features.csv")
            if os.path.exists(csv_path):
                try:
                    df = pd.read_csv(csv_path)
                    results[item] = df
                    print(f"[INFO] 加载: {item} ({len(df)} 个频率点)")
                except Exception as e:
                    print(f"[WARN] 加载失败 {item}: {e}")
    
    return results

def classify_samples(results):
    """根据文件名分类样本"""
    normal_samples = []
    anomaly_samples = []
    
    for filename, df in results.items():
        # 根据文件名判断是否为异响样本
        if any(keyword in filename for keyword in ['戳洞', '互换', 'no3_75']):
            anomaly_samples.append((filename, df))
        else:
            normal_samples.append((filename, df))
    
    print(f"[INFO] 正常样本: {len(normal_samples)} 个")
    print(f"[INFO] 异响样本: {len(anomaly_samples)} 个")
    
    return normal_samples, anomaly_samples

def analyze_feature_effectiveness(normal_samples, anomaly_samples):
    """分析各特征的区分有效性"""
    
    # 特征列表
    features = [
        'anomaly_score', 'thdn', 'narrowband_score', 'high_freq_noise_score', 
        'tremolo_score', 'flat_out', 'energy_out', 'spec_centroid', 
        'spec_bandwidth', 'subband_variance', 'high_freq_flatness',
        'high_freq_variation', 'tremolo_strength', 'tremolo_consistency'
    ]
    
    # 收集所有数据
    normal_data = defaultdict(list)
    anomaly_data = defaultdict(list)
    
    for filename, df in normal_samples:
        for feature in features:
            if feature in df.columns:
                normal_data[feature].extend(df[feature].values)
    
    for filename, df in anomaly_samples:
        for feature in features:
            if feature in df.columns:
                anomaly_data[feature].extend(df[feature].values)
    
    # 计算统计指标
    effectiveness_results = {}
    
    for feature in features:
        if feature in normal_data and feature in anomaly_data:
            normal_vals = np.array(normal_data[feature])
            anomaly_vals = np.array(anomaly_data[feature])
            
            # 去除异常值
            normal_vals = normal_vals[~np.isnan(normal_vals)]
            anomaly_vals = anomaly_vals[~np.isnan(anomaly_vals)]
            
            if len(normal_vals) > 0 and len(anomaly_vals) > 0:
                # 计算统计量
                normal_mean = np.mean(normal_vals)
                anomaly_mean = np.mean(anomaly_vals)
                normal_std = np.std(normal_vals)
                anomaly_std = np.std(anomaly_vals)
                
                # 计算分离度 (Cohen's d)
                pooled_std = np.sqrt(((len(normal_vals)-1)*normal_std**2 + 
                                    (len(anomaly_vals)-1)*anomaly_std**2) / 
                                   (len(normal_vals) + len(anomaly_vals) - 2))
                cohens_d = abs(anomaly_mean - normal_mean) / (pooled_std + 1e-12)
                
                # 计算重叠度
                overlap = calculate_overlap(normal_vals, anomaly_vals)
                
                effectiveness_results[feature] = {
                    'normal_mean': normal_mean,
                    'normal_std': normal_std,
                    'anomaly_mean': anomaly_mean,
                    'anomaly_std': anomaly_std,
                    'cohens_d': cohens_d,
                    'overlap': overlap,
                    'direction': 'higher' if anomaly_mean > normal_mean else 'lower'
                }
    
    return effectiveness_results

def calculate_overlap(normal_vals, anomaly_vals):
    """计算两个分布的重叠度"""
    # 使用直方图方法计算重叠
    all_vals = np.concatenate([normal_vals, anomaly_vals])
    bins = np.linspace(all_vals.min(), all_vals.max(), 50)
    
    normal_hist, _ = np.histogram(normal_vals, bins=bins, density=True)
    anomaly_hist, _ = np.histogram(anomaly_vals, bins=bins, density=True)
    
    # 计算重叠面积
    overlap = np.sum(np.minimum(normal_hist, anomaly_hist)) * (bins[1] - bins[0])
    return overlap

def plot_feature_comparison(normal_samples, anomaly_samples, top_features):
    """绘制特征对比图"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, feature in enumerate(top_features[:6]):
        ax = axes[i]
        
        # 收集数据
        normal_data = []
        anomaly_data = []
        
        for _, df in normal_samples:
            if feature in df.columns:
                normal_data.extend(df[feature].values)
        
        for _, df in anomaly_samples:
            if feature in df.columns:
                anomaly_data.extend(df[feature].values)
        
        # 绘制分布
        if normal_data and anomaly_data:
            ax.hist(normal_data, bins=30, alpha=0.7, label='Normal', density=True)
            ax.hist(anomaly_data, bins=30, alpha=0.7, label='Anomaly', density=True)
            ax.set_xlabel(feature)
            ax.set_ylabel('Density')
            ax.set_title(f'{feature} Distribution')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('feature_effectiveness_comparison.png', dpi=150)
    plt.close()

def generate_effectiveness_report(effectiveness_results):
    """生成有效性报告"""
    print("\n" + "="*80)
    print("异响检测算法有效性分析报告")
    print("="*80)
    
    # 按Cohen's d排序
    sorted_features = sorted(effectiveness_results.items(), 
                           key=lambda x: x[1]['cohens_d'], reverse=True)
    
    cohens_d_header = "Cohen's d"
    print(f"\n{'特征名称':<25} {cohens_d_header:<10} {'重叠度':<8} {'方向':<8} {'正常均值':<12} {'异响均值':<12}")
    print("-" * 80)
    
    effective_features = []
    moderate_features = []
    weak_features = []
    
    for feature, stats in sorted_features:
        cohens_d = stats['cohens_d']
        overlap = stats['overlap']
        direction = stats['direction']
        normal_mean = stats['normal_mean']
        anomaly_mean = stats['anomaly_mean']
        
        print(f"{feature:<25} {cohens_d:<10.3f} {overlap:<8.3f} {direction:<8} {normal_mean:<12.3f} {anomaly_mean:<12.3f}")
        
        # 分类特征有效性
        if cohens_d > 0.8:  # 大效应
            effective_features.append(feature)
        elif cohens_d > 0.5:  # 中等效应
            moderate_features.append(feature)
        else:  # 小效应
            weak_features.append(feature)
    
    print(f"\n特征有效性分类:")
    print(f"高效特征 (Cohen's d > 0.8): {effective_features}")
    print(f"中效特征 (Cohen's d > 0.5): {moderate_features}")
    print(f"低效特征 (Cohen's d ≤ 0.5): {weak_features}")
    
    return effective_features, moderate_features, weak_features

def analyze_sample_specific_performance(results):
    """分析特定样本的检测性能"""
    print(f"\n{'样本名称':<50} {'平均异响评分':<15} {'最高异响评分':<15} {'异响点比例':<15}")
    print("-" * 95)
    
    sample_scores = []
    
    for filename, df in results.items():
        avg_score = df['anomaly_score'].mean()
        max_score = df['anomaly_score'].max()
        anomaly_ratio = (df['anomaly_score'] > 0.5).sum() / len(df)
        
        print(f"{filename:<50} {avg_score:<15.3f} {max_score:<15.3f} {anomaly_ratio:<15.3f}")
        
        # 判断是否为已知异响样本
        is_known_anomaly = any(keyword in filename for keyword in ['戳洞', '互换', 'no3_75'])
        sample_scores.append({
            'filename': filename,
            'avg_score': avg_score,
            'max_score': max_score,
            'anomaly_ratio': anomaly_ratio,
            'is_known_anomaly': is_known_anomaly
        })
    
    return sample_scores

def main():
    """主分析函数"""
    print("开始分析异响检测算法有效性...")
    
    # 加载所有结果
    results = load_all_results()
    
    if not results:
        print("[ERROR] 未找到分析结果")
        return
    
    # 分类样本
    normal_samples, anomaly_samples = classify_samples(results)
    
    if not anomaly_samples:
        print("[WARN] 未找到异响样本，无法进行对比分析")
        return
    
    # 分析特征有效性
    effectiveness_results = analyze_feature_effectiveness(normal_samples, anomaly_samples)
    
    # 生成报告
    effective_features, moderate_features, weak_features = generate_effectiveness_report(effectiveness_results)
    
    # 绘制对比图
    top_features = [f for f, _ in sorted(effectiveness_results.items(), 
                                       key=lambda x: x[1]['cohens_d'], reverse=True)]
    plot_feature_comparison(normal_samples, anomaly_samples, top_features)
    
    # 分析样本特定性能
    print(f"\n样本检测性能分析:")
    sample_scores = analyze_sample_specific_performance(results)
    
    # 推荐算法配置
    print(f"\n推荐的检测算法配置:")
    print(f"1. 主要检测指标: {effective_features[:3]}")
    print(f"2. 辅助检测指标: {moderate_features[:3]}")
    print(f"3. 建议权重调整: 提高有效特征权重，降低无效特征权重")
    
    # 保存详细结果
    effectiveness_df = pd.DataFrame(effectiveness_results).T
    effectiveness_df.to_csv('feature_effectiveness_analysis.csv')
    print(f"\n详细分析结果已保存到: feature_effectiveness_analysis.csv")
    print(f"特征对比图已保存到: feature_effectiveness_comparison.png")

if __name__ == "__main__":
    main()
