import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import warnings
warnings.filterwarnings('ignore')

class OptimizedClassifier:
    """
    优化的分类器，使用精选特征进行正负样本分类
    """
    
    def __init__(self):
        # 基于特征重要性分析的精选特征
        self.selected_features = [
            # 最重要的特征（前15）
            'vertical_line_count',
            'band_3_energy_ratio_std', 
            'spectral_energy_kurtosis',
            'vertical_line_max_freq',
            'spectral_peak_kurtosis',
            'high_freq_noise_ratio_kurtosis',
            'thd',
            'spectral_energy_mean',
            'low_freq_energy_ratio',
            'low_freq_energy_variation',
            'noise_power_ratio',
            'thdn',
            'spectral_flatness_mean',
            'band_1_energy_ratio_std',
            'band_2_energy_ratio_std'
        ]
        
        # 删除的低重要性特征
        self.removed_low_importance = [
            'high_freq_noise_ratio_mean', 'high_freq_noise_ratio_max', 'vertical_line_min_freq',
            'peak_freq_deviation_mean', 'peak_freq_deviation_min', 'peak_freq_deviation_max',
            'narrow_peak_count_mean', 'narrow_peak_count_skew', 'peak_freq_deviation_std',
            'narrow_peak_count_kurtosis', 'narrow_peak_count_std', 'narrow_peak_count_max',
            'peak_freq_deviation_kurtosis', 'peak_freq_deviation_skew', 'narrow_peak_count_min',
            'spectral_flatness_skew', 'spectral_flatness_kurtosis'
        ]
        
        # 删除的冗余特征（高相关性）
        self.removed_redundant = [
            'high_freq_irregularity_std', 'spectral_peak_mean', 'high_freq_irregularity_min',
            'high_freq_noise_ratio_std', 'band_2_energy_ratio_min', 'spectral_energy_max',
            'high_freq_noise_ratio_skew', 'band_0_energy_ratio_min', 'spectral_peak_max',
            'high_freq_irregularity_mean', 'band_1_energy_ratio_min', 'vertical_line_freq_spread',
            'band_3_energy_ratio_kurtosis', 'high_freq_noise_ratio_min', 'spectral_peak_min',
            'harmonic_energy_ratio', 'band_3_energy_ratio_mean', 'out_band_energy_ratio_mean',
            'high_freq_noise_ratio_max', 'spectral_energy_min', 'band_3_energy_ratio_max',
            'out_band_energy_ratio_min', 'band_0_energy_ratio_max', 'harmonic_irregularity',
            'band_1_energy_ratio_mean', 'band_2_energy_ratio_max', 'out_band_energy_ratio_max',
            'spectral_flatness_std', 'high_freq_irregularity_max', 'spectral_flatness_max'
        ]
        
    def load_and_prepare_data(self, features_csv_path="optimized_features/all_features.csv"):
        """
        加载和准备数据
        """
        df = pd.read_csv(features_csv_path)
        
        # 排除非特征列
        exclude_cols = ['label', 'filename', 'segment_id', 'time_start', 'time_end', 'frequency', 'segment_length']
        all_feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # 获取可用的精选特征
        available_selected = [feat for feat in self.selected_features if feat in all_feature_cols]
        
        print(f"[INFO] 原始特征数: {len(all_feature_cols)}")
        print(f"[INFO] 精选特征数: {len(available_selected)}")
        print(f"[INFO] 删除低重要性特征: {len(self.removed_low_importance)}")
        print(f"[INFO] 删除冗余特征: {len(self.removed_redundant)}")
        
        # 准备数据
        X_all = df[all_feature_cols].fillna(0)
        X_selected = df[available_selected].fillna(0) if available_selected else X_all.iloc[:, :15]  # 备选方案
        y = df['label']
        
        return X_all, X_selected, y, all_feature_cols, available_selected
    
    def compare_feature_sets(self, X_all, X_selected, y, output_dir="optimization_comparison"):
        """
        对比原始特征集和优化特征集的性能
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据分割
        X_all_train, X_all_test, y_train, y_test = train_test_split(X_all, y, test_size=0.3, random_state=42, stratify=y)
        X_sel_train, X_sel_test, _, _ = train_test_split(X_selected, y, test_size=0.3, random_state=42, stratify=y)
        
        # 标准化
        scaler_all = StandardScaler()
        scaler_sel = StandardScaler()
        
        X_all_train_scaled = scaler_all.fit_transform(X_all_train)
        X_all_test_scaled = scaler_all.transform(X_all_test)
        X_sel_train_scaled = scaler_sel.fit_transform(X_sel_train)
        X_sel_test_scaled = scaler_sel.transform(X_sel_test)
        
        # 测试多种分类器
        classifiers = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(random_state=42, probability=True)
        }
        
        results = {}
        
        for clf_name, clf in classifiers.items():
            print(f"\n[INFO] 测试分类器: {clf_name}")
            
            # 原始特征集
            clf_all = clf.__class__(**clf.get_params())
            clf_all.fit(X_all_train_scaled, y_train)
            y_pred_all = clf_all.predict(X_all_test_scaled)
            y_prob_all = clf_all.predict_proba(X_all_test_scaled)[:, 1]
            
            # 优化特征集
            clf_sel = clf.__class__(**clf.get_params())
            clf_sel.fit(X_sel_train_scaled, y_train)
            y_pred_sel = clf_sel.predict(X_sel_test_scaled)
            y_prob_sel = clf_sel.predict_proba(X_sel_test_scaled)[:, 1]
            
            # 计算性能指标
            auc_all = roc_auc_score(y_test, y_prob_all)
            auc_sel = roc_auc_score(y_test, y_prob_sel)
            
            # 交叉验证
            cv_scores_all = cross_val_score(clf_all, X_all_train_scaled, y_train, cv=5, scoring='roc_auc')
            cv_scores_sel = cross_val_score(clf_sel, X_sel_train_scaled, y_train, cv=5, scoring='roc_auc')
            
            results[clf_name] = {
                'all_features': {
                    'auc': auc_all,
                    'cv_mean': cv_scores_all.mean(),
                    'cv_std': cv_scores_all.std(),
                    'y_pred': y_pred_all,
                    'y_prob': y_prob_all
                },
                'selected_features': {
                    'auc': auc_sel,
                    'cv_mean': cv_scores_sel.mean(),
                    'cv_std': cv_scores_sel.std(),
                    'y_pred': y_pred_sel,
                    'y_prob': y_prob_sel
                }
            }
            
            print(f"  原始特征集 - AUC: {auc_all:.3f}, CV: {cv_scores_all.mean():.3f}±{cv_scores_all.std():.3f}")
            print(f"  优化特征集 - AUC: {auc_sel:.3f}, CV: {cv_scores_sel.mean():.3f}±{cv_scores_sel.std():.3f}")
            print(f"  改进: {auc_sel - auc_all:+.3f}")
        
        # 可视化对比结果
        self.plot_comparison_results(results, y_test, output_dir)
        
        return results
    
    def plot_comparison_results(self, results, y_test, output_dir):
        """
        可视化对比结果
        """
        # 1. AUC对比图
        plt.figure(figsize=(12, 8))
        
        classifiers = list(results.keys())
        auc_all = [results[clf]['all_features']['auc'] for clf in classifiers]
        auc_sel = [results[clf]['selected_features']['auc'] for clf in classifiers]
        
        x = np.arange(len(classifiers))
        width = 0.35
        
        plt.subplot(2, 2, 1)
        plt.bar(x - width/2, auc_all, width, label='All Features', alpha=0.8)
        plt.bar(x + width/2, auc_sel, width, label='Selected Features', alpha=0.8)
        plt.xlabel('Classifier')
        plt.ylabel('AUC Score')
        plt.title('AUC Comparison')
        plt.xticks(x, classifiers)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 交叉验证分数对比
        plt.subplot(2, 2, 2)
        cv_all = [results[clf]['all_features']['cv_mean'] for clf in classifiers]
        cv_sel = [results[clf]['selected_features']['cv_mean'] for clf in classifiers]
        cv_std_all = [results[clf]['all_features']['cv_std'] for clf in classifiers]
        cv_std_sel = [results[clf]['selected_features']['cv_std'] for clf in classifiers]
        
        plt.errorbar(x - 0.1, cv_all, yerr=cv_std_all, fmt='o-', label='All Features', capsize=5)
        plt.errorbar(x + 0.1, cv_sel, yerr=cv_std_sel, fmt='s-', label='Selected Features', capsize=5)
        plt.xlabel('Classifier')
        plt.ylabel('Cross-Validation AUC')
        plt.title('Cross-Validation Comparison')
        plt.xticks(x, classifiers)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 3. ROC曲线对比（使用Random Forest）
        plt.subplot(2, 2, 3)
        rf_results = results['Random Forest']
        
        fpr_all, tpr_all, _ = roc_curve(y_test, rf_results['all_features']['y_prob'])
        fpr_sel, tpr_sel, _ = roc_curve(y_test, rf_results['selected_features']['y_prob'])
        
        plt.plot(fpr_all, tpr_all, label=f'All Features (AUC={rf_results["all_features"]["auc"]:.3f})')
        plt.plot(fpr_sel, tpr_sel, label=f'Selected Features (AUC={rf_results["selected_features"]["auc"]:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves (Random Forest)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 4. 改进幅度
        plt.subplot(2, 2, 4)
        improvements = [auc_sel[i] - auc_all[i] for i in range(len(classifiers))]
        colors = ['green' if imp > 0 else 'red' for imp in improvements]
        
        plt.bar(classifiers, improvements, color=colors, alpha=0.7)
        plt.xlabel('Classifier')
        plt.ylabel('AUC Improvement')
        plt.title('Performance Improvement')
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "performance_comparison.png"), dpi=150)
        plt.close()
        
        # 5. 混淆矩阵对比
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        for i, (clf_name, clf_results) in enumerate(results.items()):
            # 原始特征集混淆矩阵
            cm_all = confusion_matrix(y_test, clf_results['all_features']['y_pred'])
            sns.heatmap(cm_all, annot=True, fmt='d', ax=axes[0, i], cmap='Blues')
            axes[0, i].set_title(f'{clf_name} - All Features')
            axes[0, i].set_ylabel('True Label')
            axes[0, i].set_xlabel('Predicted Label')
            
            # 优化特征集混淆矩阵
            cm_sel = confusion_matrix(y_test, clf_results['selected_features']['y_pred'])
            sns.heatmap(cm_sel, annot=True, fmt='d', ax=axes[1, i], cmap='Greens')
            axes[1, i].set_title(f'{clf_name} - Selected Features')
            axes[1, i].set_ylabel('True Label')
            axes[1, i].set_xlabel('Predicted Label')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "confusion_matrices.png"), dpi=150)
        plt.close()
    
    def generate_optimization_report(self, results, output_dir):
        """
        生成优化效果报告
        """
        report = "特征优化效果报告\n"
        report += "=" * 50 + "\n\n"
        
        report += "1. 特征优化策略:\n"
        report += f"   - 删除低重要性特征: {len(self.removed_low_importance)} 个\n"
        report += f"   - 删除冗余特征: {len(self.removed_redundant)} 个\n"
        report += f"   - 保留核心特征: {len(self.selected_features)} 个\n\n"
        
        report += "2. 性能对比结果:\n"
        for clf_name, clf_results in results.items():
            auc_all = clf_results['all_features']['auc']
            auc_sel = clf_results['selected_features']['auc']
            improvement = auc_sel - auc_all
            
            report += f"   {clf_name}:\n"
            report += f"     原始特征集 AUC: {auc_all:.3f}\n"
            report += f"     优化特征集 AUC: {auc_sel:.3f}\n"
            report += f"     改进幅度: {improvement:+.3f}\n\n"
        
        report += "3. 核心特征列表:\n"
        for i, feat in enumerate(self.selected_features, 1):
            report += f"   {i:2d}. {feat}\n"
        
        report += "\n4. 优化效果总结:\n"
        avg_improvement = np.mean([results[clf]['selected_features']['auc'] - results[clf]['all_features']['auc'] 
                                  for clf in results.keys()])
        report += f"   - 平均AUC改进: {avg_improvement:+.3f}\n"
        report += f"   - 特征数量减少: {len(self.removed_low_importance) + len(self.removed_redundant)} 个\n"
        report += f"   - 计算效率提升: 约 {(len(self.removed_low_importance) + len(self.removed_redundant)) / (len(self.selected_features) + len(self.removed_low_importance) + len(self.removed_redundant)) * 100:.1f}%\n"
        
        with open(os.path.join(output_dir, "optimization_report.txt"), 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report

def main():
    """
    主函数：运行完整的优化对比分析
    """
    classifier = OptimizedClassifier()
    
    # 加载数据
    X_all, X_selected, y, all_feature_cols, selected_feature_cols = classifier.load_and_prepare_data()
    
    print(f"\n[INFO] 数据集信息:")
    print(f"  总样本数: {len(y)}")
    print(f"  正样本: {(y == 1).sum()}")
    print(f"  负样本: {(y == 0).sum()}")
    
    # 对比分析
    results = classifier.compare_feature_sets(X_all, X_selected, y)
    
    # 生成报告
    report = classifier.generate_optimization_report(results, "optimization_comparison")
    print(f"\n{report}")

if __name__ == "__main__":
    main()
