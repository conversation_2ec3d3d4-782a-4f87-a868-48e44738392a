#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
谐波异常检测器API简单使用演示
"""

import sys
import os

# 添加检测器路径
sys.path.append('harmonic_detection_system')
from harmonic_detection_system.harmonic_detector_api import HarmonicDetectorAPI, detect_audio_harmonic_anomaly

def simple_demo():
    """简单演示"""
    print("🎯 谐波异常检测器API简单演示")
    print("="*50)
    
    # 方法1: 便捷函数（最简单）
    print("方法1: 便捷函数")
    print("-" * 20)
    
    audio_file = "test20250717/neg/主板隔音eva取消.wav"
    if os.path.exists(audio_file):
        try:
            result = detect_audio_harmonic_anomaly(audio_file)
            status = "正常" if result == 1 else "异常"
            print(f"检测结果: {result} ({status})")
        except Exception as e:
            print(f"检测失败: {e}")
    else:
        print(f"文件不存在: {audio_file}")
    
    print()
    
    # 方法2: 类接口（推荐）
    print("方法2: 类接口")
    print("-" * 20)
    
    # 创建检测器
    detector = HarmonicDetectorAPI(anomaly_threshold=2)
    
    # 测试几个文件
    test_files = [
        "test20250717/neg/主板隔音eva取消.wav",  # 应该是异常
        "test20250717/pos/sd卡/sd1.wav",        # 应该是正常
    ]
    
    for audio_file in test_files:
        if os.path.exists(audio_file):
            try:
                # 简单检测
                result = detector.detect(audio_file)
                status = "正常" if result == 1 else "异常"
                filename = os.path.basename(audio_file)
                
                print(f"📁 {filename}: {result} ({status})")
                
                # 获取详细信息
                details = detector.detect_with_details(audio_file)
                print(f"   异常段个数: {details['anomaly_count']}个")
                print(f"   异常段比例: {details['anomaly_ratio']:.1%}")
                
            except Exception as e:
                print(f"❌ {os.path.basename(audio_file)}: 检测失败 - {e}")
        else:
            print(f"❌ 文件不存在: {audio_file}")
    
    print()
    print("✅ 演示完成!")

if __name__ == "__main__":
    simple_demo()
