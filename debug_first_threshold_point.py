#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试为什么没有选择第一个超过阈值的点
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from final_audio_detection_system.freq_split_optimized import OptimizedFreqSplitter

def debug_threshold_selection():
    """详细调试阈值选择过程"""
    print("🔍 详细调试阈值选择过程")
    print("="*60)
    
    # 创建分割器
    splitter = OptimizedFreqSplitter()
    
    # 读取测试音频
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    y, sr = librosa.load(audio_path, sr=splitter.fs)
    if sr != splitter.fs:
        y = librosa.resample(y, orig_sr=sr, target_sr=splitter.fs)
    
    # 搜索参数
    search_start = 0.1
    search_end = 2.0
    corr_length = 1.0
    
    # 执行搜索
    search_start_sample = int(search_start * splitter.fs)
    search_end_sample = int(search_end * splitter.fs)
    corr_length_samples = int(corr_length * splitter.fs)
    
    search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
    
    # 准备参考信号段
    initial_corr_length = min(corr_length_samples, int(0.5 * splitter.fs))
    reference_segment = splitter.reference_signal[:initial_corr_length]
    
    print(f"搜索参数:")
    print(f"  搜索窗口: {search_start:.2f}s - {search_end:.2f}s")
    print(f"  搜索样本范围: {search_start_sample} - {search_end_sample}")
    print(f"  参考信号长度: {initial_corr_length} 样本 ({initial_corr_length/splitter.fs:.2f}秒)")
    
    # 计算相关性
    search_length = search_end_sample - search_start_sample
    correlations = []
    positions = []
    
    step_size = max(1, splitter.fs // 500)  # 2ms步长，与原始算法一致
    print(f"  步长: {step_size} 样本 ({step_size/splitter.fs*1000:.1f}ms)")
    
    for i in range(0, search_length - initial_corr_length, step_size):
        pos = search_start_sample + i
        test_segment = y[pos:pos + initial_corr_length]
        
        if len(test_segment) == len(reference_segment):
            correlation = splitter._normalized_cross_correlation(reference_segment, test_segment)
            correlations.append(correlation)
            positions.append(pos / splitter.fs)
    
    correlations = np.array(correlations)
    positions = np.array(positions)
    
    print(f"\n计算结果:")
    print(f"  计算了 {len(correlations)} 个相关性值")
    print(f"  时间范围: {positions[0]:.3f}s - {positions[-1]:.3f}s")
    print(f"  相关性范围: {np.min(correlations):.3f} - {np.max(correlations):.3f}")
    
    # 分析阈值选择逻辑
    max_correlation = np.max(correlations)
    max_idx = np.argmax(correlations)
    
    # 当前的阈值设置
    relative_threshold = max_correlation * 0.7  # 70%
    absolute_threshold = 0.4
    final_threshold = max(relative_threshold, absolute_threshold)
    
    print(f"\n阈值计算:")
    print(f"  最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
    print(f"  70%相对阈值: {relative_threshold:.3f}")
    print(f"  绝对阈值: {absolute_threshold:.3f}")
    print(f"  最终阈值: {final_threshold:.3f}")
    
    # 找到符合条件的点
    good_indices = np.where(correlations >= final_threshold)[0]
    
    print(f"\n符合条件的点分析:")
    print(f"  符合条件的点数: {len(good_indices)}")
    
    if len(good_indices) > 0:
        print(f"  所有符合条件的点:")
        for i, idx in enumerate(good_indices[:10]):  # 显示前10个
            print(f"    {i+1}. 索引={idx}, 时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.3f}")
        
        # 第一个符合条件的点
        first_good_idx = good_indices[0]
        first_good_pos = positions[first_good_idx]
        first_good_corr = correlations[first_good_idx]
        
        print(f"\n  第一个符合条件的点:")
        print(f"    索引: {first_good_idx}")
        print(f"    时间: {first_good_pos:.3f}s")
        print(f"    相关性: {first_good_corr:.3f}")
        
        # 检查是否有更早的点接近阈值
        print(f"\n  检查更早的高相关性点:")
        early_high_corr = np.where((correlations >= final_threshold * 0.9) & 
                                  (positions < first_good_pos))[0]
        if len(early_high_corr) > 0:
            print(f"    发现 {len(early_high_corr)} 个更早的接近阈值的点:")
            for idx in early_high_corr[-5:]:  # 显示最后5个（最接近的）
                print(f"      时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.3f} "
                      f"(阈值的{correlations[idx]/final_threshold*100:.1f}%)")
        else:
            print(f"    没有发现更早的接近阈值的点")
    else:
        print(f"  ❌ 没有符合条件的点！")
        print(f"  这意味着算法会回退到最大相关性点")
    
    # 可视化分析
    plt.figure(figsize=(15, 8))
    
    # 绘制相关性曲线
    plt.plot(positions, correlations, 'b-', linewidth=1, label='相关性', alpha=0.7)
    
    # 标记阈值线
    plt.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, 
               label=f'最终阈值 ({final_threshold:.3f})')
    plt.axhline(relative_threshold, color='purple', linestyle=':', alpha=0.8, 
               label=f'70%阈值 ({relative_threshold:.3f})')
    plt.axhline(absolute_threshold, color='brown', linestyle=':', alpha=0.6, 
               label=f'绝对阈值 ({absolute_threshold:.3f})')
    
    # 标记最大相关性点
    plt.plot(positions[max_idx], correlations[max_idx], 'go', markersize=10, 
            label=f'最大相关性 ({positions[max_idx]:.3f}s)')
    
    # 标记符合条件的点
    if len(good_indices) > 0:
        plt.plot(positions[good_indices], correlations[good_indices], 'ro', 
                markersize=4, alpha=0.7, label='符合条件的点')
        
        # 特别标记第一个符合条件的点
        first_idx = good_indices[0]
        plt.plot(positions[first_idx], correlations[first_idx], 'r*', 
                markersize=15, label=f'第一个符合条件 ({positions[first_idx]:.3f}s)')
    
    # 高亮显示符合条件的区域
    good_mask = correlations >= final_threshold
    if np.any(good_mask):
        plt.fill_between(positions, 0, correlations, where=good_mask, 
                        alpha=0.2, color='green', label='符合条件区域')
    
    plt.xlabel('时间 (秒)')
    plt.ylabel('相关性系数')
    plt.title('详细阈值选择分析')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0.1, 0.5)  # 聚焦到关键区域
    plt.ylim(-0.2, 1.0)
    
    plt.tight_layout()
    plt.show()
    
    return positions, correlations, final_threshold, good_indices

if __name__ == "__main__":
    positions, correlations, threshold, good_indices = debug_threshold_selection()
