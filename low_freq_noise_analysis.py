#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析低频噪声样本的特征
聚焦"录音_153743_低音戳洞.wav"和"录音_153632.wav"
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_low_freq_noise_samples():
    """分析低频噪声样本"""
    print("🔍 专门分析低频噪声样本")
    print("="*70)
    print("目标样本: 录音_153743_低音戳洞.wav, 录音_153632.wav")
    print("="*70)
    
    # 加载特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 目标样本
    target_files = ['录音_153743_低音戳洞.wav', '录音_153632.wav']
    
    # 排除竖线检测特征和元信息
    exclude_features = [col for col in df.columns if col.startswith('vl_')] + \
                      ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    
    candidate_features = [col for col in df.columns if col not in exclude_features]
    print(f"📊 候选特征数: {len(candidate_features)}个")
    
    # 获取所有其他样本（正样本 + 其他负样本）
    other_samples = df[~df['filename'].isin(target_files)]
    print(f"📊 其他样本数据: {len(other_samples)}个频段")
    
    # 分析每个目标样本
    for target_file in target_files:
        print(f"\n🔍 分析目标样本: {target_file}")
        print("="*60)
        
        target_data = df[df['filename'] == target_file]
        if len(target_data) == 0:
            print(f"❌ 未找到文件: {target_file}")
            continue
        
        # 分析低频段特征
        analyze_low_freq_features(target_data, other_samples, candidate_features, target_file)
        
        # 可视化低频段特征
        visualize_low_freq_features(target_data, other_samples, candidate_features, target_file)

def analyze_low_freq_features(target_data, other_samples, candidate_features, target_file):
    """分析低频段特征"""
    print(f"   🔍 分析低频段特征 (0-20频段, 100-315Hz)")
    
    # 聚焦低频段 (前20个频段，大约100-315Hz)
    low_freq_segments = list(range(20))
    
    effective_features = []
    
    for feature in candidate_features:
        separable_segments = []
        
        for segment_idx in low_freq_segments:
            # 获取该频段的目标样本数据
            target_segment = target_data[target_data['segment_idx'] == segment_idx]
            # 获取该频段的所有其他样本数据
            other_segment = other_samples[other_samples['segment_idx'] == segment_idx]
            
            if len(target_segment) == 0 or len(other_segment) == 0:
                continue
            
            # 检查完全分离
            separation_result = check_complete_separation(other_segment, target_segment, feature, segment_idx)
            
            if separation_result['valid'] and separation_result.get('complete_separation', False):
                separable_segments.append({
                    'segment_idx': segment_idx,
                    'expected_freq': target_segment['expected_freq'].iloc[0],
                    'score': separation_result['score'],
                    'separation_type': separation_result['separation_type'],
                    'separation_gap': separation_result['separation_gap'],
                    'other_range': separation_result['other_range'],
                    'target_range': separation_result['target_range']
                })
        
        if len(separable_segments) > 0:
            avg_score = np.mean([seg['score'] for seg in separable_segments])
            effective_features.append({
                'feature': feature,
                'separable_segments': separable_segments,
                'avg_score': avg_score,
                'segment_count': len(separable_segments)
            })
    
    # 排序并显示结果
    effective_features.sort(key=lambda x: x['avg_score'], reverse=True)
    
    print(f"   ✅ 在低频段找到 {len(effective_features)} 个有效特征")
    
    if len(effective_features) > 0:
        print(f"   📊 最有效的前10个特征:")
        for i, feature_info in enumerate(effective_features[:10]):
            best_seg = max(feature_info['separable_segments'], key=lambda x: x['score'])
            print(f"     {i+1:2d}. {feature_info['feature']}")
            print(f"         可分离低频段数: {feature_info['segment_count']}/20")
            print(f"         平均分离评分: {feature_info['avg_score']:.1f}")
            print(f"         最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
            print(f"         分离类型: {best_seg['separation_type']}")
            print(f"         分离间隙: {best_seg['separation_gap']:.6f}")
            print(f"         其他样本范围: [{best_seg['other_range'][0]:.6f}, {best_seg['other_range'][1]:.6f}]")
            print(f"         目标样本范围: [{best_seg['target_range'][0]:.6f}, {best_seg['target_range'][1]:.6f}]")
    else:
        print(f"   ❌ 在低频段未找到有效特征")
    
    return effective_features

def check_complete_separation(other_segment, target_segment, feature, segment_idx):
    """检查与所有其他样本的完全分离"""
    try:
        other_values = other_segment[feature].dropna()
        target_values = target_segment[feature].dropna()
        
        if len(other_values) == 0 or len(target_values) == 0:
            return {'valid': False, 'score': 0}
        
        # 获取所有其他样本的完整取值范围
        other_min = np.min(other_values)
        other_max = np.max(other_values)
        
        # 获取目标样本的取值范围
        target_min = np.min(target_values)
        target_max = np.max(target_values)
        
        # 检查是否完全分离
        complete_separation = False
        separation_gap = 0
        separation_type = 'no_separation'
        
        if target_max < other_min:
            # 目标样本完全在其他样本下方
            complete_separation = True
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
        elif target_min > other_max:
            # 目标样本完全在其他样本上方
            complete_separation = True
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
        
        if complete_separation:
            # 计算分离质量评分
            other_range = other_max - other_min if other_max > other_min else 1e-10
            target_range = target_max - target_min if target_max > target_min else 1e-10
            total_range = max(other_max, target_max) - min(other_min, target_min)
            
            if total_range > 0:
                separation_score = (separation_gap / total_range) * 1000
            else:
                separation_score = 1000
            
            # 额外考虑分离的相对质量
            relative_gap = separation_gap / max(other_range, target_range)
            separation_score *= (1 + relative_gap)
            
            return {
                'valid': True,
                'score': separation_score,
                'complete_separation': True,
                'separation_type': separation_type,
                'separation_gap': separation_gap,
                'other_range': [other_min, other_max],
                'target_range': [target_min, target_max],
                'other_count': len(other_values),
                'target_count': len(target_values)
            }
        else:
            return {
                'valid': True,
                'score': 0,
                'complete_separation': False,
                'separation_type': 'overlap',
                'other_range': [other_min, other_max],
                'target_range': [target_min, target_max]
            }
        
    except Exception as e:
        return {'valid': False, 'score': 0, 'error': str(e)}

def visualize_low_freq_features(target_data, other_samples, candidate_features, target_file):
    """可视化低频段特征"""
    print(f"   🎨 生成低频段特征可视化...")
    
    # 分析低频段特征
    effective_features = analyze_low_freq_features(target_data, other_samples, candidate_features, target_file)
    
    if len(effective_features) == 0:
        print(f"   ❌ 无有效特征可视化")
        return
    
    # 选择前3个最有效的特征
    top3_features = effective_features[:3]
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{target_file} - 低频段噪声特征分析', fontsize=16, fontweight='bold')
    
    # 1. 低频段特征分布对比
    ax1 = axes[0, 0]
    
    feature1 = top3_features[0]['feature']
    
    # 获取低频段的特征值
    low_freq_segments = list(range(20))
    other_values = []
    target_values = []
    
    for seg_idx in low_freq_segments:
        other_seg = other_samples[other_samples['segment_idx'] == seg_idx]
        target_seg = target_data[target_data['segment_idx'] == seg_idx]
        
        if len(other_seg) > 0:
            other_values.extend(other_seg[feature1].dropna().tolist())
        if len(target_seg) > 0:
            target_values.extend(target_seg[feature1].dropna().tolist())
    
    if len(other_values) > 0 and len(target_values) > 0:
        ax1.hist(other_values, bins=30, alpha=0.6, color='blue', 
                label=f'所有其他样本 (n={len(other_values)})', density=True)
        ax1.hist(target_values, bins=30, alpha=0.8, color='red', 
                label=f'{target_file} (n={len(target_values)})', density=True)
        
        # 标记分离区域
        other_min, other_max = np.min(other_values), np.max(other_values)
        target_min, target_max = np.min(target_values), np.max(target_values)
        
        ax1.axvspan(other_min, other_max, alpha=0.2, color='blue', label='其他样本范围')
        ax1.axvspan(target_min, target_max, alpha=0.2, color='red', label='目标样本范围')
        
        ax1.set_title(f'低频段特征分布: {feature1}')
        ax1.set_xlabel('特征值')
        ax1.set_ylabel('密度')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 显示分离信息
        if target_max < other_min:
            separation_gap = other_min - target_max
            ax1.text(0.5, 0.95, f'完全分离: 目标样本在下方\n分离间隙: {separation_gap:.6f}', 
                    transform=ax1.transAxes, ha='center', va='top',
                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
        elif target_min > other_max:
            separation_gap = target_min - other_max
            ax1.text(0.5, 0.95, f'完全分离: 目标样本在上方\n分离间隙: {separation_gap:.6f}', 
                    transform=ax1.transAxes, ha='center', va='top',
                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    # 2. 低频段逐频段对比
    ax2 = axes[0, 1]
    
    segments = list(range(20))  # 前20个频段
    other_means = []
    target_means = []
    separation_flags = []
    
    for seg_idx in segments:
        other_seg = other_samples[other_samples['segment_idx'] == seg_idx]
        target_seg = target_data[target_data['segment_idx'] == seg_idx]
        
        if len(other_seg) > 0 and len(target_seg) > 0:
            other_vals = other_seg[feature1].dropna()
            target_vals = target_seg[feature1].dropna()
            
            if len(other_vals) > 0 and len(target_vals) > 0:
                other_mean = np.mean(other_vals)
                target_mean = np.mean(target_vals)
                
                # 检查是否分离
                other_min, other_max = np.min(other_vals), np.max(other_vals)
                target_min, target_max = np.min(target_vals), np.max(target_vals)
                is_separated = (target_max < other_min) or (target_min > other_max)
                
                other_means.append(other_mean)
                target_means.append(target_mean)
                separation_flags.append(is_separated)
            else:
                other_means.append(np.nan)
                target_means.append(np.nan)
                separation_flags.append(False)
        else:
            other_means.append(np.nan)
            target_means.append(np.nan)
            separation_flags.append(False)
    
    # 绘制均值对比
    ax2.plot(segments, other_means, 'b-o', label='其他样本均值', markersize=4)
    ax2.plot(segments, target_means, 'r-s', label=f'{target_file}均值', markersize=4)
    
    # 标记分离的频段
    separated_segments = [seg for seg, flag in zip(segments, separation_flags) if flag]
    if separated_segments:
        separated_other = [other_means[seg] for seg in separated_segments]
        ax2.scatter(separated_segments, separated_other, marker='*', s=100, 
                   color='gold', label='完全分离频段', zorder=5)
    
    ax2.set_title(f'低频段逐频段对比: {feature1}')
    ax2.set_xlabel('频段索引 (0-19)')
    ax2.set_ylabel('特征值均值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 特征重要性
    ax3 = axes[1, 0]
    
    feature_names = [info['feature'] for info in top3_features]
    scores = [info['avg_score'] for info in top3_features]
    segment_counts = [info['segment_count'] for info in top3_features]
    
    bars = ax3.bar(range(len(feature_names)), scores, color=['red', 'orange', 'yellow'])
    ax3.set_title('低频段特征重要性')
    ax3.set_xlabel('特征')
    ax3.set_ylabel('平均分离评分')
    ax3.set_xticks(range(len(feature_names)))
    ax3.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in feature_names], 
                       rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, score, count) in enumerate(zip(bars, scores, segment_counts)):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{score:.1f}\n({count}/20)', ha='center', va='bottom', fontsize=8)
    
    # 4. 频率-分离能力热力图
    ax4 = axes[1, 1]
    
    # 创建热力图数据
    heatmap_data = []
    freq_labels = []
    
    for seg_idx in range(20):
        target_seg = target_data[target_data['segment_idx'] == seg_idx]
        if len(target_seg) > 0:
            freq = target_seg['expected_freq'].iloc[0]
            freq_labels.append(f'{freq:.0f}Hz')
            
            row_data = []
            for feature_info in top3_features:
                # 检查该频段是否在分离列表中
                separable_segs = [seg['segment_idx'] for seg in feature_info['separable_segments']]
                if seg_idx in separable_segs:
                    # 找到对应的分离评分
                    seg_info = next(seg for seg in feature_info['separable_segments'] 
                                  if seg['segment_idx'] == seg_idx)
                    row_data.append(seg_info['score'])
                else:
                    row_data.append(0)
            heatmap_data.append(row_data)
    
    if heatmap_data:
        heatmap_data = np.array(heatmap_data)
        
        im = ax4.imshow(heatmap_data, cmap='Reds', aspect='auto')
        ax4.set_title('低频段分离能力热力图')
        ax4.set_xlabel('特征')
        ax4.set_ylabel('频率')
        
        # 设置标签
        ax4.set_xticks(range(len(feature_names)))
        ax4.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax4.set_yticks(range(0, len(freq_labels), 2))
        ax4.set_yticklabels([freq_labels[i] for i in range(0, len(freq_labels), 2)])
        
        # 添加颜色条
        plt.colorbar(im, ax=ax4, label='分离评分')
    
    plt.tight_layout()
    
    # 保存图片
    safe_filename = target_file.replace('.wav', '').replace('_', '-').replace(' ', '-')
    plt.savefig(f'{safe_filename}_low_freq_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"   ✅ 图表已保存: {safe_filename}_low_freq_analysis.png")

if __name__ == "__main__":
    analyze_low_freq_noise_samples()
    print(f"\n✅ 低频噪声样本分析完成！")
