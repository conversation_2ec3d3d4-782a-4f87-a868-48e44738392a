#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的全频谱分析
解决谐波漏识别和检测频率范围不全的问题
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def corrected_full_spectrum_analysis():
    """修正的全频谱分析"""
    print("🔧 修正的全频谱分析 - 解决谐波漏识别和频率范围问题")
    print("="*70)
    print("修正要点:")
    print("1. 降低峰值锐度要求，避免谐波漏识别")
    print("2. 扩展检测频率范围到完整奈奎斯特频率")
    print("3. 优化SNR阈值，平衡准确性和召回率")
    print("4. 增加更多谐波次数检测")
    print("="*70)
    
    # 两个异常样本
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    target_segments = [40, 41, 42, 43, 44]  # 重点分析这些频段
    
    # 分析每个样本
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 修正分析 {sample_name}: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        if os.path.exists(audio_path):
            # 修正的分析
            analysis_results = corrected_analyze_segments(audio_path, sample_name, target_segments)
            
            # 创建修正的可视化
            create_corrected_visualization(analysis_results, sample_name, target_segments)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")

def corrected_analyze_segments(audio_path, sample_name, target_segments):
    """修正的频段分析"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_analyses = []
        
        # 分析每个目标频段
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            print(f"   修正分析频段 {seg_idx} ({expected_freq:.1f}Hz)")
            
            # 修正的详细分析
            segment_analysis = corrected_analyze_single_segment(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
        
        return segment_analyses
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return []

def corrected_analyze_single_segment(audio, sr, expected_freq, seg_idx):
    """修正的单频段分析"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(65536, len(audio))  # 更高分辨率
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 分析完整的正频率范围
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        nyquist_freq = sr / 2
        
        print(f"     频率分辨率: {freq_resolution:.3f}Hz")
        print(f"     分析范围: 0 - {nyquist_freq}Hz")
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_corrected(positive_freqs, positive_power, expected_freq)
        
        # 2. 改进的噪声底噪估计
        noise_floor_analysis = estimate_noise_floor_improved(positive_freqs, positive_power, expected_freq)
        
        # 3. 修正的谐波检测 (扩展到完整频率范围)
        harmonic_analysis = detect_harmonics_corrected(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_floor_analysis, sr
        )
        
        # 4. 对比分析 (显示被修正的谐波)
        comparison_analysis = compare_with_previous_method(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_floor_analysis, sr
        )
        
        print(f"     主频: {fundamental_analysis['freq']:.2f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"     修正后谐波: {len(harmonic_analysis)}个")
        print(f"     噪声底噪: {noise_floor_analysis['noise_floor_db']:.1f}dB")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_floor_analysis': noise_floor_analysis,
            'harmonic_analysis': harmonic_analysis,
            'comparison_analysis': comparison_analysis,
            'freq_resolution': freq_resolution,
            'nyquist_freq': nyquist_freq
        }
        
    except Exception as e:
        print(f"     ❌ 频段{seg_idx}分析失败: {e}")
        return None

def analyze_fundamental_corrected(freqs, power, expected_freq):
    """修正的主频分析"""
    
    # 自适应带宽
    if expected_freq <= 1000:
        bandwidth = 5.0
    elif expected_freq <= 2000:
        bandwidth = 8.0
    elif expected_freq <= 5000:
        bandwidth = 12.0
    else:
        bandwidth = 20.0
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'expected_freq': expected_freq,
        'freq_error': fundamental_freq - expected_freq,
        'bandwidth': bandwidth,
        'index': actual_idx
    }

def estimate_noise_floor_improved(freqs, power, fundamental_freq):
    """改进的噪声底噪估计"""
    
    # 更保守的排除范围 (只排除明确的信号频率)
    excluded_ranges = []
    
    # 排除主频±15Hz (减少排除范围)
    excluded_ranges.append((fundamental_freq - 15, fundamental_freq + 15))
    
    # 只排除前5个最可能的谐波位置±15Hz
    for order in range(2, 6):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 15, harmonic_freq + 15))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        
        # 使用更保守的噪声底噪估计
        noise_floor_20th = np.percentile(noise_powers, 20)  # 20th percentile
        noise_floor_db = 10 * np.log10(noise_floor_20th + 1e-12)
        
        return {
            'noise_floor': noise_floor_20th,
            'noise_floor_db': noise_floor_db,
            'noise_sample_count': np.sum(noise_mask),
            'excluded_ranges': excluded_ranges
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0,
            'excluded_ranges': excluded_ranges
        }

def detect_harmonics_corrected(freqs, power, fundamental_freq, 
                              fundamental_analysis, noise_floor_analysis, sr):
    """修正的谐波检测"""
    
    if not fundamental_analysis or not noise_floor_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_floor_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 修正的SNR阈值 (降低要求)
    if fundamental_freq <= 1000:
        min_snr_db = 10.0  # 降低2dB
    elif fundamental_freq <= 3000:
        min_snr_db = 12.0  # 降低2dB
    else:
        min_snr_db = 15.0  # 降低3dB
    
    nyquist_freq = sr / 2
    
    print(f"       修正谐波检测 (SNR阈值: {min_snr_db:.1f}dB, 扩展到{nyquist_freq}Hz):")
    
    # 扩展谐波检测到更高次数
    for order in range(2, 25):  # 检测到24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            print(f"         {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率")
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 2000:
            search_bandwidth = 10.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 15.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 25.0
        else:
            search_bandwidth = 40.0  # 高频段更宽的带宽
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算各种指标
        harmonic_snr_db = harmonic_power_db - noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 修正的谐波判断条件 (放宽要求)
        conditions = {
            'snr_sufficient': harmonic_snr_db >= min_snr_db,
            'power_reasonable': relative_power_db >= -55.0,  # 放宽5dB
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.7,  # 放宽到70%
            # 移除峰值锐度要求，这是导致漏识别的主要原因
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': harmonic_snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'search_bandwidth': search_bandwidth,
                'index': actual_idx
            })
            
            print(f"         ✅ {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"SNR={harmonic_snr_db:.1f}dB, 误差{freq_error:.1f}Hz")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"         ❌ {order}次谐波: {actual_freq:.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            SNR={harmonic_snr_db:.1f}dB, 相对功率={relative_power_db:.1f}dB, "
                  f"误差={freq_error:.1f}Hz")
    
    return detected_harmonics

def compare_with_previous_method(freqs, power, fundamental_freq, 
                                fundamental_analysis, noise_floor_analysis, sr):
    """对比之前的方法，显示修正效果"""
    
    # 使用之前严格的方法检测
    strict_harmonics = detect_harmonics_strict_method(
        freqs, power, fundamental_freq, fundamental_analysis, noise_floor_analysis, sr
    )
    
    # 使用修正的方法检测
    corrected_harmonics = detect_harmonics_corrected(
        freqs, power, fundamental_freq, fundamental_analysis, noise_floor_analysis, sr
    )
    
    strict_orders = {h['order'] for h in strict_harmonics}
    corrected_orders = {h['order'] for h in corrected_harmonics}
    
    # 找出修正后新增的谐波
    newly_detected = corrected_orders - strict_orders
    
    # 找出修正后丢失的谐波 (应该很少)
    lost_harmonics = strict_orders - corrected_orders
    
    print(f"       对比分析:")
    print(f"         严格方法: {len(strict_harmonics)}个谐波")
    print(f"         修正方法: {len(corrected_harmonics)}个谐波")
    if newly_detected:
        print(f"         新增检测: {sorted(newly_detected)}次谐波")
    if lost_harmonics:
        print(f"         丢失检测: {sorted(lost_harmonics)}次谐波")
    
    return {
        'strict_count': len(strict_harmonics),
        'corrected_count': len(corrected_harmonics),
        'newly_detected': list(newly_detected),
        'lost_harmonics': list(lost_harmonics)
    }

def detect_harmonics_strict_method(freqs, power, fundamental_freq, 
                                  fundamental_analysis, noise_floor_analysis, sr):
    """之前的严格方法 (用于对比)"""
    
    detected_harmonics = []
    noise_floor_db = noise_floor_analysis['noise_floor_db']
    
    # 之前的严格阈值
    min_snr_db = 15.0
    nyquist_freq = sr / 2
    
    for order in range(2, 10):
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        search_bandwidth = 8.0
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        harmonic_snr_db = harmonic_power_db - noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_analysis['power_db']
        freq_error = actual_freq - expected_harmonic_freq
        
        # 严格条件 (包括峰值锐度)
        peak_sharpness = harmonic_power / np.mean(search_powers)
        
        is_valid = (
            harmonic_snr_db >= min_snr_db and
            relative_power_db >= -50.0 and
            abs(freq_error) <= search_bandwidth * 0.5 and
            peak_sharpness >= 2.0  # 严格的峰值锐度要求
        )
        
        if is_valid:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'snr_db': harmonic_snr_db
            })
    
    return detected_harmonics

def create_corrected_visualization(segment_analyses, sample_name, target_segments):
    """创建修正的可视化"""
    
    if not segment_analyses:
        print(f"   ❌ 无有效分析数据")
        return
    
    print(f"   🎨 生成 {sample_name} 修正的频谱可视化...")
    
    # 创建大图 (5行1列)
    fig, axes = plt.subplots(5, 1, figsize=(24, 30))
    
    for i, segment_analysis in enumerate(segment_analyses):
        ax = axes[i]
        
        # 提取数据
        freqs = segment_analysis['freqs']
        power_db = segment_analysis['power_db']
        fundamental_analysis = segment_analysis['fundamental_analysis']
        harmonic_analysis = segment_analysis['harmonic_analysis']
        noise_floor_analysis = segment_analysis['noise_floor_analysis']
        comparison_analysis = segment_analysis['comparison_analysis']
        
        seg_idx = segment_analysis['segment_idx']
        expected_freq = segment_analysis['expected_freq']
        nyquist_freq = segment_analysis['nyquist_freq']
        
        # 扩展显示范围到更高频率
        if expected_freq <= 1000:
            freq_range = (0, min(15000, nyquist_freq))  # 扩展到15kHz
        elif expected_freq <= 2000:
            freq_range = (0, min(20000, nyquist_freq))  # 扩展到20kHz
        else:
            freq_range = (0, nyquist_freq)  # 显示到奈奎斯特频率
        
        freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
        display_freqs = freqs[freq_mask]
        display_power_db = power_db[freq_mask]
        
        # 绘制基础频谱
        ax.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
        
        # 标记噪声底噪线
        noise_floor_db = noise_floor_analysis['noise_floor_db']
        ax.axhline(y=noise_floor_db, color='gray', linestyle='--', alpha=0.8, 
                  label=f'噪声底噪 {noise_floor_db:.1f}dB')
        
        # 修正的SNR阈值线
        if expected_freq <= 1000:
            snr_threshold = 10.0
        elif expected_freq <= 3000:
            snr_threshold = 12.0
        else:
            snr_threshold = 15.0
        
        threshold_line_db = noise_floor_db + snr_threshold
        ax.axhline(y=threshold_line_db, color='orange', linestyle='--', alpha=0.8,
                  label=f'修正阈值 {threshold_line_db:.1f}dB')
        
        # 标记主频
        if fundamental_analysis:
            fund_freq = fundamental_analysis['freq']
            fund_power_db = fundamental_analysis['power_db']
            if freq_range[0] <= fund_freq <= freq_range[1]:
                ax.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
                       label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
                
                # 主频标注
                ax.annotate(f'主频\n{fund_freq:.1f}Hz\n{fund_power_db:.1f}dB', 
                           xy=(fund_freq, fund_power_db), 
                           xytext=(fund_freq, fund_power_db + 15),
                           ha='center', va='bottom', fontweight='bold', color='red',
                           arrowprops=dict(arrowstyle='->', color='red', lw=2),
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记修正后检测到的谐波
        harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta', 'olive', 'navy', 'maroon']
        
        for j, harmonic in enumerate(harmonic_analysis):
            if j >= len(harmonic_colors):
                color = 'gray'
            else:
                color = harmonic_colors[j]
                
            harm_freq = harmonic['freq']
            harm_power_db = harmonic['power_db']
            harm_order = harmonic['order']
            harm_snr = harmonic['snr_db']
            
            if freq_range[0] <= harm_freq <= freq_range[1]:
                # 标记新增检测的谐波
                if harm_order in comparison_analysis['newly_detected']:
                    marker_style = 's'  # 方形标记新增的
                    marker_size = 12
                    alpha = 1.0
                    label_suffix = ' (新增)'
                else:
                    marker_style = 'o'  # 圆形标记原有的
                    marker_size = 10
                    alpha = 0.8
                    label_suffix = ''
                
                ax.plot(harm_freq, harm_power_db, marker_style, color=color, 
                       markersize=marker_size, alpha=alpha,
                       label=f'{harm_order}次谐波 {harm_freq:.0f}Hz{label_suffix}')
                
                # 谐波标注 (只标注前8个和新增的)
                if j < 8 or harm_order in comparison_analysis['newly_detected']:
                    ax.annotate(f'{harm_order}次\n{harm_freq:.0f}Hz\nSNR={harm_snr:.1f}dB', 
                               xy=(harm_freq, harm_power_db), 
                               xytext=(harm_freq, harm_power_db + 8),
                               ha='center', va='bottom', fontsize=8, color=color,
                               arrowprops=dict(arrowstyle='->', color=color, lw=1))
        
        # 设置图表属性
        ax.set_title(f'{sample_name} - 频段{seg_idx} ({expected_freq:.1f}Hz) 修正频谱分析 '
                    f'(检测到{len(harmonic_analysis)}个谐波)', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(freq_range)
        ax.set_ylim(np.min(display_power_db) - 10, np.max(display_power_db) + 25)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.grid(True, alpha=0.3)
        
        # 添加修正信息文本框
        info_text = f"修正分析结果:\n"
        info_text += f"检测范围: 0 - {nyquist_freq:.0f}Hz\n"
        info_text += f"修正后谐波: {len(harmonic_analysis)}个\n"
        info_text += f"新增检测: {len(comparison_analysis['newly_detected'])}个\n"
        if comparison_analysis['newly_detected']:
            info_text += f"新增次数: {sorted(comparison_analysis['newly_detected'])}\n"
        info_text += f"SNR阈值: {snr_threshold:.1f}dB (已降低)\n"
        info_text += f"频率分辨率: {segment_analysis['freq_resolution']:.3f}Hz"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'corrected_full_spectrum_{sample_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 修正频谱可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    corrected_full_spectrum_analysis()
