import numpy as np
import librosa
import matplotlib.pyplot as plt
import scipy.signal
from scipy.fft import rfft
import os

plt.rcParams['font.family'] = ['sans-serif']                # 全局默认字体族
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']  
                                                           # 依次尝试这几个字体
plt.rcParams['axes.unicode_minus'] = False                  # 关闭 Unicode 负号

# ========== 对齐函数 ==========
def align_signals_by_energy(y_ref, y_test, sr=44100, n_fft=2048, hop_length=512):
    E_ref  = np.sum(np.abs(librosa.stft(y_ref,  n_fft=n_fft, hop_length=hop_length)), axis=0)
    E_test = np.sum(np.abs(librosa.stft(y_test, n_fft=n_fft, hop_length=hop_length)), axis=0)
    corr         = scipy.signal.correlate(E_test, E_ref, mode='full')
    delay_frames = np.argmax(corr) - len(E_ref) + 1
    delay_samples= delay_frames * hop_length
    if delay_samples > 0:
        y_test = y_test[delay_samples:]
        y_ref  = y_ref[:len(y_test)]
    elif delay_samples < 0:
        y_ref  = y_ref[-delay_samples:]
        y_test = y_test[:len(y_ref)]
    return y_ref, y_test, delay_samples

# ========== 配置 ==========
REF_PATH    = "test20250703/pos1.wav"
TEST_PATH   = "test20250703/nag3.wav"
OUTPUT_DIR  = "thdn_compare"
os.makedirs(OUTPUT_DIR, exist_ok=True)

fs         = 44100    # 采样率
N          = 4096     # FFT 窗长
hop_length = 2048     # 帧移
harmonics  = [2,3,4,5]# 谐波次数
top_db     = 30       # split 时的静音阈值（可调）

# ========== 加载 & 对齐 ==========
y_ref,  _ = librosa.load(REF_PATH,  sr=fs, mono=True)
y_test, _ = librosa.load(TEST_PATH, sr=fs, mono=True)
min_len   = min(len(y_ref), len(y_test))
y_ref     = y_ref[:min_len]
y_test    = y_test[:min_len]
y_ref, y_test, delay = align_signals_by_energy(y_ref, y_test, sr=fs, n_fft=2048, hop_length=hop_length)
min_len = min(len(y_ref), len(y_test))
y_ref, y_test = y_ref[:min_len], y_test[:min_len]
print(f"已对齐，延迟 {delay} 样本 ({delay/fs:.3f} 秒)")

# ========== 静音分割（用 split） ==========
# 返回的是非静音区间的样本索引对列表 [[s1,e1], [s2,e2], ...]
intervals = librosa.effects.split(y_ref, top_db=top_db,
                                  frame_length=N, hop_length=hop_length)

# 只取第一个和最后一个非静音区间
start_sample = intervals[0,0]
end_sample   = intervals[-1,1]
print(f"检测到有效区间: {start_sample} ~ {end_sample} 样本 " 
      f"({start_sample/fs:.2f}s ~ {end_sample/fs:.2f}s)")

# 裁剪有效部分
y_ref  = y_ref [start_sample:end_sample]
y_test = y_test[start_sample:end_sample]
min_len = len(y_ref)

# ========== 分帧 THD+N 计算 ==========
freqs    = np.fft.rfftfreq(N, 1/fs)
frames   = (min_len - N) // hop_length + 1
times    = (start_sample/ fs) + np.arange(frames) * hop_length / fs

thdn_ref  = []
thdn_test = []
window    = np.hanning(N)

for i in range(frames):
    offset = start_sample + i * hop_length
    seg_ref  = y_ref [i*hop_length : i*hop_length+N] * window
    seg_test = y_test[i*hop_length : i*hop_length+N] * window

    fft_ref  = np.abs(rfft(seg_ref))
    fft_test = np.abs(rfft(seg_test))

    idx0 = np.argmax(fft_ref)
    P1_ref  = fft_ref [idx0]**2
    P1_test = fft_test[idx0]**2

    harm_bins = {idx0}
    H_ref, H_test = 0.0, 0.0
    for h in harmonics:
        fh = freqs[idx0] * h
        if fh >= fs/2: break
        idxh = np.argmin(np.abs(freqs - fh))
        harm_bins.add(idxh)
        H_ref  += fft_ref [idxh]**2
        H_test += fft_test[idxh]**2

    mask = np.ones_like(fft_ref, dtype=bool)
    mask[list(harm_bins)] = False
    N_ref  = np.sum(fft_ref [mask]**2)
    N_test = np.sum(fft_test[mask]**2)

    thdn_ref .append(100 * np.sqrt((H_ref  + N_ref ) / (P1_ref  + 1e-12)))
    thdn_test.append(100 * np.sqrt((H_test + N_test) / (P1_test + 1e-12)))



# ========== 结果直接打印 ==========

# 假设你已知扫频起止频率
f_start = 20      # 起始频率（Hz），请根据实际信号设置
f_end   = 20000   # 终止频率（Hz），请根据实际信号设置
T = min_len / fs  # 扫频总时长（秒）

frame_freqs = f_start + (f_end - f_start) * (np.arange(frames) * hop_length / fs) / T

THDN_ABS_THRESHOLD = 5    # 绝对阈值（%）
THDN_DIFF_THRESHOLD = 2   # 相对提升阈值（%）
ABNORMAL_RATIO_THRESHOLD = 0.1  # 异常点占比阈值（10%）

thdn_ref_arr = np.array(thdn_ref)
thdn_test_arr = np.array(thdn_test)
delta_thdn = thdn_test_arr - thdn_ref_arr

# 双重判据
abnormal_mask = (thdn_test_arr > THDN_ABS_THRESHOLD) | (delta_thdn > THDN_DIFF_THRESHOLD)
abnormal_ratio = np.mean(abnormal_mask)
max_delta = np.max(delta_thdn)

print(f"{'主频(Hz)':>10} | {'参考THD+N(%)':>12} | {'待测THD+N(%)':>12} | {'ΔTHD+N(%)':>10} | {'异常':>4}")
print("-"*55)
for f, ref, test, dthd, abn in zip(frame_freqs, thdn_ref_arr, thdn_test_arr, delta_thdn, abnormal_mask):
    flag = "异常" if abn else ""
    print(f"{f:10.1f} | {ref:12.2f} | {test:12.2f} | {dthd:10.2f} | {flag:>4}")

if np.any(abnormal_mask):
    print("\n检测到异常THD+N主频点：")
    for f, test, dthd in zip(frame_freqs[abnormal_mask], thdn_test_arr[abnormal_mask], delta_thdn[abnormal_mask]):
        print(f"主频: {f:.1f}Hz, THD+N: {test:.2f}%, ΔTHD+N: {dthd:.2f}%")
else:
    print("\n未检测到异常THD+N主频点。")

# 综合判据
if abnormal_ratio > ABNORMAL_RATIO_THRESHOLD or max_delta > (2 * THDN_DIFF_THRESHOLD):
    print(f"\n【综合判定】：THD+N异常（异常点占比 {abnormal_ratio:.1%}，最大ΔTHD+N {max_delta:.2f}%）")
else:
    print(f"\n【综合判定】：THD+N正常（异常点占比 {abnormal_ratio:.1%}，最大ΔTHD+N {max_delta:.2f}%）")

# ========== 可视化 ==========
fig, ax = plt.subplots(figsize=(10,5))
ax.plot(frame_freqs, thdn_ref,  label="参考 THD+N (%)")
ax.plot(frame_freqs, thdn_test, label="待测 THD+N (%)")
ax.set_xlabel("主频 (Hz)")
ax.set_ylabel("THD+N (%)")
ax.set_title("扫频信号 THD+N 对比（主频坐标）")
ax.grid(True, linestyle='--', alpha=0.5)
ax.legend()
plt.tight_layout()

# 文件名包含两个输入信号
ref_name = os.path.splitext(os.path.basename(REF_PATH))[0]
test_name = os.path.splitext(os.path.basename(TEST_PATH))[0]
img_name = f"thdn_{ref_name}_vs_{test_name}.png"
plt.savefig(img_name, dpi=150)
# plt.show()

# ========== 差值可视化 ==========
fig, ax = plt.subplots(figsize=(10,5))
ax.plot(frame_freqs, delta_thdn, label="ΔTHD+N (待测-参考) (%)")
ax.axhline(0, color='gray', linestyle='--', linewidth=1)
ax.axhline(THDN_DIFF_THRESHOLD, color='r', linestyle='--', label=f"异常阈值 {THDN_DIFF_THRESHOLD}%")
ax.set_xlabel("主频 (Hz)")
ax.set_ylabel("ΔTHD+N (%)")
ax.set_title("THD+N 对比差（待测-参考）")
ax.grid(True, linestyle='--', alpha=0.5)
ax.legend()
plt.tight_layout()

diff_img_name = f"thdn_diff_{ref_name}_vs_{test_name}.png"
plt.savefig(diff_img_name, dpi=150)
# plt.show()