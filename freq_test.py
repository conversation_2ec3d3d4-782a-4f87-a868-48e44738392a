import numpy as np
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
from scipy import signal
import warnings
import librosa

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning)

def detect_speaker_anomaly_freq(
    ref_path: str,
    rec_path: str,
    plot_result: bool = True,
    fs: int = 48000,
    smooth_win_len: int = 51,
    start_freq: int = 100,
    stop_freq: int = 20000
) -> tuple[float, str, dict]:
    """
    通过频域对比检测喇叭异常（免时域对齐）
    参数:
        ref_path: 参考扫频信号文件路径
        rec_path: 录制信号文件路径
        plot_result: 是否绘制诊断图
        fs: 采样率
        smooth_win_len: 平滑窗口长度
    返回:
        confidence: 喇叭正常置信度 (0.0-1.0)
        diagnosis: 诊断结果文本
        metrics: 关键指标字典
    """
    try:
        # 1. 加载音频文件
        ref, _ = librosa.load(ref_path, sr=fs, mono=True)
        rec, _ = librosa.load(rec_path, sr=fs, mono=True)
        
        # 2. 统一信号长度
        min_len = min(len(ref), len(rec))
        ref = ref[:min_len]
        rec = rec[:min_len]
        
        # 3. 加窗处理
        window = signal.windows.hann(min_len)
        ref_win = ref * window
        rec_win = rec * window
        
        # 4. 计算频响曲线
        fft_ref = np.fft.rfft(ref_win)
        fft_rec = np.fft.rfft(rec_win)
        freqs = np.fft.rfftfreq(min_len, 1/fs)
        
        # 计算功率谱比
        with np.errstate(divide='ignore', invalid='ignore'):
            magnitude_ratio = np.abs(fft_rec) / (np.abs(fft_ref) + 1e-10)
            response_db = 20 * np.log10(magnitude_ratio)
        
        # 处理无效值
        response_db = np.nan_to_num(response_db, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 5. 有效频带分析 
        mask = (freqs >= start_freq) & (freqs <= stop_freq)
        valid_freqs = freqs[mask]
        valid_response = response_db[mask]
        
        # 6. 计算关键指标
        # 频响标准差
        std_dev = np.std(valid_response) if len(valid_response) > 0 else 0
        
        # 最大偏差
        max_deviation = np.max(np.abs(valid_response)) if len(valid_response) > 0 else 0
        
        # 平均偏差
        mean_deviation = np.mean(np.abs(valid_response)) if len(valid_response) > 0 else 0
        
        # 7. 异常谐振峰检测
        try:
            # 平滑频响曲线
            smoothed = signal.savgol_filter(response_db, window_length=smooth_win_len, polyorder=3)
            
            # 动态峰值阈值
            peak_threshold = max(6, 2 * std_dev)
            
            # 检测显著峰值
            peaks, properties = signal.find_peaks(
                smoothed, 
                height=peak_threshold,
                prominence=3
            )
            
            # 收集谐振峰信息
            resonance_peaks = []
            for p in peaks:
                if start_freq <= freqs[p] <= stop_freq:  # 只考虑有效频带内的峰值
                    resonance_peaks.append({
                        "frequency": freqs[p],
                        "magnitude": smoothed[p],
                        "prominence": properties['prominences'][np.where(peaks == p)[0][0]]
                    })
            
            resonance_count = len(resonance_peaks)
        except:
            resonance_peaks = []
            resonance_count = 0
        
        # 8. 计算置信度
        # 基于标准差和最大偏差
        std_score = max(0, 1 - std_dev / 8)  # 标准差>8dB得0分
        max_dev_score = max(0, 1 - max_deviation / 15)  # 最大偏差>15dB得0分
        
        # 基于谐振峰数量
        resonance_score = max(0, 1 - resonance_count * 0.2)  # 每个谐振峰减少20%分数
        
        # 综合置信度
        confidence = 0.6 * std_score + 0.3 * max_dev_score + 0.1 * resonance_score
        confidence = max(0, min(1, confidence))
        
        # 9. 诊断结果
        if confidence > 0.8:
            diagnosis = "喇叭状态正常"
        elif confidence > 0.6:
            diagnosis = "喇叭轻微异常"
        elif confidence > 0.4:
            diagnosis = "喇叭中度异常"
        else:
            diagnosis = "喇叭严重异常"
        
        # 10. 可视化结果
        if plot_result:
            plt.figure(figsize=(14, 8))

            # 只画有效频带
            mask = (freqs >= start_freq) & (freqs <= stop_freq)
            plot_freqs = freqs[mask]
            plot_response = response_db[mask]
            plot_smoothed = smoothed[mask]

            # 频响曲线
            plt.subplot(2, 1, 1)
            plt.semilogx(plot_freqs, plot_response, label='频响曲线')

            # 添加平滑曲线
            if len(plot_response) > 100:
                plt.semilogx(plot_freqs, plot_smoothed, 'r-', linewidth=2, label='平滑曲线')

            # 标记有效频带
            plt.axvspan(start_freq, stop_freq, alpha=0.1, color='green', label=f'分析频带 ({start_freq}Hz-{stop_freq}Hz)')

            # 标记谐振峰（只标记在有效区间内的）
            if resonance_peaks:
                for peak in resonance_peaks:
                    freq = peak["frequency"]
                    mag = peak["magnitude"]
                    if start_freq <= freq <= stop_freq:
                        plt.plot(freq, mag, 'ro')
                        plt.annotate(f"{freq:.0f}Hz", (freq, mag), 
                                    xytext=(0, 10), textcoords='offset points')

            plt.title('喇叭频响分析')
            plt.xlabel('频率 (Hz)')
            plt.ylabel('幅度 (dB)')
            plt.grid(True, which='both')
            plt.axhline(0, color='black', linestyle='--')
            plt.legend()
            
            # 诊断信息
            plt.subplot(2, 1, 2)
            plt.axis('off')
            
            # 谐振峰详情
            resonance_text = "未检测到显著谐振峰"
            if resonance_peaks:
                resonance_text = "检测到谐振峰:\n"
                for i, peak in enumerate(resonance_peaks, 1):
                    resonance_text += f"{i}. {peak['frequency']:.0f}Hz: {peak['magnitude']:.1f}dB (突出度:{peak['prominence']:.1f}dB)\n"
            
            info_text = (
                f"诊断结果: {diagnosis}\n"
                f"置信度: {confidence:.1%}\n\n"
                f"关键指标:\n"
                f"- 频响标准差: {std_dev:.2f} dB\n"
                f"- 最大偏差: {max_deviation:.2f} dB\n"
                f"- 平均偏差: {mean_deviation:.2f} dB\n"
                f"- 谐振峰数量: {resonance_count}个\n\n"
                f"{resonance_text}"
            )
            plt.text(0.05, 0.5, info_text, fontsize=12, 
                    bbox=dict(facecolor='lightyellow', alpha=0.5))
            
            plt.tight_layout()
            plt.savefig('speaker_diagnosis.png', dpi=120)
            plt.show()
        
        return confidence, diagnosis, {
            'std_dev': std_dev,
            'max_deviation': max_deviation,
            'mean_deviation': mean_deviation,
            'resonance_count': resonance_count,
            'resonance_peaks': resonance_peaks,
            'freq_response': list(zip(freqs, response_db))
        }
    
    except Exception as e:
        print(f"检测过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, "检测失败", {}

# 使用示例
if __name__ == "__main__":
    # 替换为您的音频文件路径
    ref_file = r"gen_chirps\sf100-ef20000-octR20-mc10-md10-fs48000.wav"    # 标准扫频信号
    rec_file = r"gen_chirps\sf100-ef20000-octR20-mc10-md10-fs48000-eq.wav"   # 麦克风录制信号
    
    print("="*50)
    print("喇叭异常检测 - 频域分析")
    print("="*50)
    
    confidence, result, metrics = detect_speaker_anomaly_freq(ref_file, rec_file)
    
    if confidence > 0:
        print(f"\n检测结果: {result}")
        print(f"置信度: {confidence:.1%}")
        print("关键指标:")
        print(f"- 频响标准差: {metrics['std_dev']:.2f} dB")
        print(f"- 最大偏差: {metrics['max_deviation']:.2f} dB")
        print(f"- 平均偏差: {metrics['mean_deviation']:.2f} dB")
        print(f"- 谐振峰数量: {metrics['resonance_count']}")
        
        if metrics['resonance_peaks']:
            print("- 谐振峰详情:")
            for i, peak in enumerate(metrics['resonance_peaks'], 1):
                print(f"  {i}. {peak['frequency']:.0f}Hz: {peak['magnitude']:.1f}dB (突出度:{peak['prominence']:.1f}dB)")
        
        print("="*50)

    else:
        print("检测失败，请检查输入文件")