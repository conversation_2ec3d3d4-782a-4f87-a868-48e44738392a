#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THD+N分析：只检测主频，主频置零后的频谱可视化
使用对数刻度，可视化主频和主频带宽
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
import time

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from freq_split_optimized import split_freq_steps_optimized

# 尝试导入matplotlib
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("matplotlib不可用，将保存数据而不生成图像")
    MATPLOTLIB_AVAILABLE = False

def find_log_symmetric_valleys(freqs, power_spectrum, peak_freq, search_factor=1.5):
    """
    在主频两边对数域对称搜索谷值

    Args:
        freqs: 频率数组
        power_spectrum: 功率谱
        peak_freq: 主频频率
        search_factor: 搜索因子，左右搜索范围为 peak_freq/search_factor 到 peak_freq*search_factor

    Returns:
        left_valley_freq: 左侧谷值频率
        right_valley_freq: 右侧谷值频率
        bandwidth: 置零带宽
    """
    # 找到主频的索引
    peak_idx = np.argmin(np.abs(freqs - peak_freq))

    # 计算对数域对称搜索范围
    left_search_freq = peak_freq / search_factor
    right_search_freq = peak_freq * search_factor

    # 确保搜索范围在有效频率内
    left_search_freq = max(freqs[0], left_search_freq)
    right_search_freq = min(freqs[-1], right_search_freq)

    # 找到搜索范围的索引
    left_search_idx = np.argmin(np.abs(freqs - left_search_freq))
    right_search_idx = np.argmin(np.abs(freqs - right_search_freq))

    # 在左侧搜索范围内找最小值（谷值）
    if peak_idx > left_search_idx:
        left_region = power_spectrum[left_search_idx:peak_idx]
        left_valley_local_idx = np.argmin(left_region)
        left_valley_idx = left_search_idx + left_valley_local_idx
        left_valley_freq = freqs[left_valley_idx]
    else:
        left_valley_freq = left_search_freq

    # 在右侧搜索范围内找最小值（谷值）
    if right_search_idx > peak_idx:
        right_region = power_spectrum[peak_idx + 1:right_search_idx + 1]
        right_valley_local_idx = np.argmin(right_region)
        right_valley_idx = peak_idx + 1 + right_valley_local_idx
        right_valley_freq = freqs[right_valley_idx]
    else:
        right_valley_freq = right_search_freq

    # 确保对数域对称性：计算对数比率
    left_ratio = peak_freq / left_valley_freq
    right_ratio = right_valley_freq / peak_freq

    # 使用较小的比率确保对数域对称
    symmetric_ratio = min(left_ratio, right_ratio)

    # 重新计算对数域对称的谷值位置
    left_valley_freq = peak_freq / symmetric_ratio
    right_valley_freq = peak_freq * symmetric_ratio

    # 限制置零范围：不超过主频频率一半和两倍（对数域限制）
    min_freq = peak_freq / 2
    max_freq = peak_freq * 2

    # 调整左谷值
    if left_valley_freq < min_freq:
        left_valley_freq = min_freq
        # 保持对数域对称性
        right_valley_freq = peak_freq * (peak_freq / left_valley_freq)

    # 调整右谷值
    if right_valley_freq > max_freq:
        right_valley_freq = max_freq
        # 保持对数域对称性
        left_valley_freq = peak_freq / (right_valley_freq / peak_freq)

    # 计算带宽
    bandwidth = right_valley_freq - left_valley_freq

    # 确保带宽合理（至少10Hz，最多主频的100%）
    min_bandwidth = 10.0
    max_bandwidth = peak_freq * 1.0

    if bandwidth < min_bandwidth:
        # 扩展到最小带宽，保持对数域对称
        # 计算需要的对数比率
        required_ratio = np.sqrt(1 + min_bandwidth / peak_freq)
        left_valley_freq = peak_freq / required_ratio
        right_valley_freq = peak_freq * required_ratio
        bandwidth = min_bandwidth
    elif bandwidth > max_bandwidth:
        # 缩小到最大带宽，保持对数域对称
        # 计算允许的最大对数比率
        max_ratio = np.sqrt(1 + max_bandwidth / peak_freq)
        left_valley_freq = peak_freq / max_ratio
        right_valley_freq = peak_freq * max_ratio
        bandwidth = max_bandwidth

    return left_valley_freq, right_valley_freq, bandwidth

def zero_fundamental_only(freqs, power_spectrum, fundamental_freq):
    """
    只将主频置零，根据主频两边对称的谷值确定置零范围

    Args:
        freqs: 频率数组
        power_spectrum: 功率谱
        fundamental_freq: 主频频率

    Returns:
        zeroed_spectrum: 主频置零后的功率谱
        zero_mask: 置零区域的掩码
        bandwidth: 使用的带宽
        left_valley_freq: 左谷值频率
        right_valley_freq: 右谷值频率
    """
    zeroed_spectrum = power_spectrum.copy()

    # 根据主频两边对数域对称的谷值确定置零范围
    left_valley_freq, right_valley_freq, bandwidth = find_log_symmetric_valleys(
        freqs, power_spectrum, fundamental_freq
    )

    # 主频置零（使用对称谷值范围）
    zero_mask = (freqs >= left_valley_freq) & (freqs <= right_valley_freq)
    zeroed_spectrum[zero_mask] = 0

    return zeroed_spectrum, zero_mask, bandwidth, left_valley_freq, right_valley_freq

def analyze_segment_fundamental_only(args):
    """
    分析单个频段，只检测主频并置零
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, output_dir = args
    
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频 - 取最中间的100ms (4800个点)
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        total_segment = audio_data[start_sample:end_sample]

        if len(total_segment) == 0:
            print(f"    警告: 段 {seg_idx} 音频长度为0")
            return False

        # 计算最中间100ms的位置
        target_samples = 4800  # 100ms * 48000Hz
        total_length = len(total_segment)

        if total_length >= target_samples:
            # 从中间提取4800个点
            center_idx = total_length // 2
            half_target = target_samples // 2
            start_idx = center_idx - half_target
            end_idx = center_idx + half_target
            segment_audio = total_segment[start_idx:end_idx]
        else:
            # 如果总长度不足4800点，使用全部
            segment_audio = total_segment
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 直接使用实际信号长度进行FFT分析
        fft_size = len(segment_audio)  # 使用实际信号长度，约4800点
        freq_resolution = sr / fft_size  # 计算频率分辨率

        # 应用窗函数
        window = np.hanning(fft_size)
        segment_audio = segment_audio * window

        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到0~24kHz
        freq_mask = (positive_freqs >= 0) & (positive_freqs <= 24000)
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频（使用原始功率谱）
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]  # 使用原始功率谱
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 主频置零（根据对称谷值确定范围）
        zeroed_spectrum, zero_mask, bandwidth, left_valley_freq, right_valley_freq = zero_fundamental_only(
            display_freqs, display_power, fundamental_freq
        )
        zeroed_db = 10 * np.log10(zeroed_spectrum + 1e-12)
        
        # 计算THD+N相关指标
        fundamental_power = display_power[np.argmin(np.abs(display_freqs - fundamental_freq))]
        noise_power = np.sum(zeroed_spectrum)  # 主频置零后的剩余功率
        
        # THD+N计算（这里主要是噪声，因为没有检测谐波）
        thd_n_ratio = noise_power / fundamental_power if fundamental_power > 0 else 0
        thd_n_db = 10 * np.log10(thd_n_ratio + 1e-12)
        thd_n_percent = np.sqrt(thd_n_ratio) * 100
        
        # 可视化
        if MATPLOTLIB_AVAILABLE:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
            
            # 设置频率显示范围到0~24kHz
            min_freq_display = max(1, np.min(display_freqs[display_freqs > 0]))  # 避免0频率的对数显示问题
            max_freq_display = min(24000, np.max(display_freqs))
            
            # 上图：原始频谱
            ax1.semilogx(display_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='原始频谱')

            # 标记主频
            fundamental_idx = np.argmin(np.abs(display_freqs - fundamental_freq))
            ax1.plot(fundamental_freq, power_db[fundamental_idx], 'o', color='blue', markersize=8,
                     markeredgecolor='darkblue', markeredgewidth=1.5,
                     label=f'主频 {fundamental_freq:.1f}Hz')
            
            # 显示主频置零区域（基于谷值）
            ax1.axvspan(left_valley_freq, right_valley_freq,
                       alpha=0.3, color='blue', label=f'主频置零区域 ({left_valley_freq:.1f}-{right_valley_freq:.1f}Hz)')

            # 标记对数域对称谷值点
            left_valley_idx = np.argmin(np.abs(display_freqs - left_valley_freq))
            right_valley_idx = np.argmin(np.abs(display_freqs - right_valley_freq))
            ax1.plot(left_valley_freq, power_db[left_valley_idx], 'v', color='red', markersize=7,
                     markeredgecolor='darkred', markeredgewidth=1.0, label='对数域对称谷值边界')
            ax1.plot(right_valley_freq, power_db[right_valley_idx], 'v', color='red', markersize=7,
                     markeredgecolor='darkred', markeredgewidth=1.0)
            
            # 设置坐标和样式
            ax1.set_xlim(min_freq_display, max_freq_display)
            ax1.set_ylim(-80, 60)  # 优化Y轴范围
            ax1.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')
            ax1.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            ax1.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz - 原始频谱与对数域对称谷值检测', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
            ax1.legend(fontsize=11, loc='upper right', framealpha=0.9)

            # 下图：主频置零后的频谱
            ax2.semilogx(display_freqs, zeroed_db, color='red', linewidth=0.8, alpha=0.8,
                         label='主频置零后频谱 (噪声+失真)')
            
            # 显示置零区域（基于谷值）
            ax2.axvspan(left_valley_freq, right_valley_freq,
                       alpha=0.3, color='gray', label=f'置零区域 ({left_valley_freq:.1f}-{right_valley_freq:.1f}Hz)')
            
            ax2.set_xlim(min_freq_display, max_freq_display)
            ax2.set_ylim(-80, 60)  # 优化Y轴范围
            ax2.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')
            ax2.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            ax2.set_title(f'频段 {seg_idx:02d}: 噪声功率={thd_n_percent:.3f}% ({thd_n_db:.1f}dB) - 对数域对称谷值范围{bandwidth:.1f}Hz',
                          fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
            ax2.legend(fontsize=11, loc='upper right', framealpha=0.9)
            
            plt.tight_layout()
            
            # 保存图像
            output_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_fundamental_only.png')
            plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"    保存图像: {output_filename}")
        
        # 保存数据
        data_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_data.npz')
        np.savez(data_filename,
                 freqs=display_freqs,
                 original_spectrum=display_power,
                 zeroed_spectrum=zeroed_spectrum,
                 zero_mask=zero_mask,
                 fundamental_freq=fundamental_freq,
                 bandwidth=bandwidth,
                 left_valley_freq=left_valley_freq,
                 right_valley_freq=right_valley_freq,
                 thd_n_ratio=thd_n_ratio,
                 thd_n_db=thd_n_db,
                 thd_n_percent=thd_n_percent)
        
        # 打印对数域对称谷值信息
        left_ratio = fundamental_freq / left_valley_freq
        right_ratio = right_valley_freq / fundamental_freq
        print(f"    主频: {fundamental_freq:.1f}Hz")
        print(f"    对数域对称谷值: {left_valley_freq:.1f}Hz (/{left_ratio:.2f}) - {right_valley_freq:.1f}Hz (×{right_ratio:.2f})")
        print(f"    带宽: {bandwidth:.1f}Hz, 噪声: {thd_n_percent:.3f}%")
        print(f"    FFT: {fft_size}点, 分辨率: {freq_resolution:.1f}Hz")
        
        return True, {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'bandwidth': bandwidth,
            'left_valley_freq': left_valley_freq,
            'right_valley_freq': right_valley_freq,
            'thd_n_percent': thd_n_percent,
            'thd_n_db': thd_n_db
        }
        
    except Exception as e:
        print(f"    错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """
    主函数：只检测主频的THD+N分析
    """
    # 音频文件路径和参数设置
    audio_path = None  # 使用gen_freq_step生成的参考信号

    print(f"🎯 主频置零频谱分析（只检测主频）")
    print(f"📁 音频文件: {audio_path}")
    print(f"🔧 分析参数: 0Hz - 24000Hz, 对数刻度, 对数域对称谷值检测")
    print("="*70)
    
    # 创建输出目录
    audio_name = "参考信号93段"
    output_dir = f"{audio_name}_主频置零分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 直接使用gen_freq_step生成参考信号
        print("🔍 生成参考信号...")
        from chirp import gen_freq_step

        # 生成参考信号 - 与freq_split_optimized参数一致
        sine_wave, t_list, freq_ssample_dict = gen_freq_step(
            start_freq=100, stop_freq=24000, octave=12,
            min_cycles=10, min_duration=153, fs=48000
        )

        # 从freq_ssample_dict提取频段信息
        step_boundaries = []
        frequency_points = []

        for freq in sorted(freq_ssample_dict.keys()):
            start_time, start_sample, duration_samples = freq_ssample_dict[freq]
            end_time = start_time + duration_samples / 48000
            step_boundaries.append((start_time, end_time))
            frequency_points.append(freq)

        freq_table = frequency_points

        print(f"✅ 参考信号生成完成，共{len(step_boundaries)}段")
        print(f"📊 信号时长: {len(sine_wave)/48000:.2f}秒")

        # 使用生成的参考信号
        y = sine_wave
        sr = 48000
        print(f"✅ 使用参考信号，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")
        
        # 准备多进程任务参数
        print(f"🚀 准备多进程分析...")
        tasks = []
        
        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = frequency_points[i]
            tasks.append((
                i+1,  # seg_idx
                start_time,
                end_time,
                expected_freq,
                y,  # 共享音频数据
                sr,
                output_dir
            ))
        
        # 多进程并行处理
        num_workers = 4
        print(f"📊 使用{num_workers}个进程并行分析{len(step_boundaries)}个频段...")
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            results = list(executor.map(analyze_segment_fundamental_only, tasks))
        
        # 统计结果
        successful_count = 0
        analysis_results = []
        
        for result in results:
            if isinstance(result, tuple):
                success, data = result
                if success and data:
                    successful_count += 1
                    analysis_results.append(data)
            else:
                if result:
                    successful_count += 1
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n" + "="*70)
        print(f"✅ 主频置零分析完成!")
        print(f"  成功分析: {successful_count}/{len(step_boundaries)} 个频段")
        print(f"  总耗时: {processing_time:.1f}秒")
        print(f"  输出目录: {output_dir}")
        
        # 保存汇总结果
        if analysis_results:
            summary_file = os.path.join(output_dir, "主频置零分析汇总.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"主频置零分析汇总报告\n")
                f.write(f"="*50 + "\n\n")
                f.write(f"音频文件: {audio_path}\n")
                f.write(f"分析方法: 只检测主频并置零，计算剩余噪声功率\n")
                f.write(f"成功分析: {successful_count}/{len(step_boundaries)} 个频段\n\n")
                
                f.write(f"分析结果:\n")
                f.write(f"{'序号':<4} {'期望频率(Hz)':<12} {'实际主频(Hz)':<12} {'左谷值(Hz)':<10} {'右谷值(Hz)':<10} {'带宽(Hz)':<10} {'噪声(%)':<10} {'噪声(dB)':<10}\n")
                f.write(f"-" * 100 + "\n")

                for result in analysis_results:
                    f.write(f"{result['segment_idx']:<4} {result['expected_freq']:<12.1f} "
                           f"{result['fundamental_freq']:<12.1f} {result['left_valley_freq']:<10.1f} "
                           f"{result['right_valley_freq']:<10.1f} {result['bandwidth']:<10.1f} "
                           f"{result['thd_n_percent']:<10.3f} {result['thd_n_db']:<10.1f}\n")
                
                # 统计信息
                noise_values = [r['thd_n_percent'] for r in analysis_results]
                bandwidth_values = [r['bandwidth'] for r in analysis_results]
                f.write(f"\n统计信息:\n")
                f.write(f"  噪声功率最小值: {min(noise_values):.3f}%\n")
                f.write(f"  噪声功率最大值: {max(noise_values):.3f}%\n")
                f.write(f"  噪声功率平均值: {np.mean(noise_values):.3f}%\n")
                f.write(f"  噪声功率标准差: {np.std(noise_values):.3f}%\n")
                f.write(f"  带宽最小值: {min(bandwidth_values):.1f}Hz\n")
                f.write(f"  带宽最大值: {max(bandwidth_values):.1f}Hz\n")
                f.write(f"  带宽平均值: {np.mean(bandwidth_values):.1f}Hz\n")
            
            print(f"📄 分析汇总已保存: {summary_file}")
        
    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
