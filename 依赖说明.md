# spk_test 项目依赖说明

## 📋 项目概述
本项目是一个音频异响检测系统，包含音频分析、特征提取、机器学习分类等功能。

## 🔧 核心依赖库

### 必需依赖 (9个)
| 库名 | 版本要求 | 用途 | 主要功能 |
|------|----------|------|----------|
| **numpy** | >=1.21.0 | 数值计算 | 数组操作、数学运算、信号处理基础 |
| **pandas** | >=1.3.0 | 数据分析 | 数据框操作、CSV读写、特征分析 |
| **scipy** | >=1.7.0 | 科学计算 | 信号处理、统计分析、滤波器设计 |
| **librosa** | >=0.9.0 | 音频分析 | 音频加载、STFT、特征提取、频谱分析 |
| **soundfile** | >=0.10.0 | 音频I/O | WAV文件读写 |
| **scikit-learn** | >=1.0.0 | 机器学习 | 分类器、特征选择、模型评估 |
| **matplotlib** | >=3.5.0 | 可视化 | 频谱图、波形图、分析结果可视化 |
| **seaborn** | >=0.11.0 | 统计可视化 | 相关性矩阵、特征分布图 |
| **tqdm** | >=4.62.0 | 进度条 | 批处理进度显示 |

### 可选依赖
| 库名 | 用途 | 说明 |
|------|------|------|
| **jupyter** | Notebook支持 | 运行 .ipynb 文件时需要 |
| **ipython** | 交互式Python | 增强的Python交互环境 |

## 📁 依赖使用分布

### 按文件分类
```
核心音频处理文件:
├── step_chirp_test.py      # numpy, librosa, matplotlib, pandas, scipy
├── freq_split.py           # numpy, librosa, matplotlib
├── optimized_feature_extractor.py  # numpy, librosa, pandas, scipy
├── thdn.py                 # numpy, librosa, matplotlib, scipy
└── peak_test.py            # numpy, librosa, matplotlib, scipy

机器学习相关:
├── feature_analysis.py     # pandas, sklearn, matplotlib, seaborn
├── easy_detector.py        # pandas, sklearn, numpy
└── optimized_classifier.py # pandas, sklearn, matplotlib, seaborn

音频生成和分析:
├── chirp.py               # numpy, matplotlib, soundfile
├── square_analysis.py     # numpy, matplotlib, scipy, soundfile, librosa
└── spk_test*.py          # numpy, librosa, matplotlib, scipy
```

### 按功能分类
```
音频处理: librosa, soundfile, numpy, scipy
数据分析: pandas, numpy
机器学习: scikit-learn, pandas, numpy  
可视化: matplotlib, seaborn
工具: tqdm
```

## 🚀 安装指南

### 方法1: 使用 requirements.txt (推荐)
```bash
pip install -r requirements.txt
```

### 方法2: 使用简化版本
```bash
pip install -r requirements_simple.txt
```

### 方法3: 手动安装核心库
```bash
pip install numpy pandas scipy librosa soundfile scikit-learn matplotlib seaborn tqdm
```

### 方法4: 使用 conda (推荐给 Anaconda 用户)
```bash
conda install numpy pandas scipy matplotlib seaborn scikit-learn
pip install librosa soundfile tqdm
```

## ⚙️ 环境要求

### Python 版本
- **最低要求**: Python 3.7
- **推荐版本**: Python 3.8 - 3.11
- **当前测试**: Python 3.9.21 ✅

### 操作系统
- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+)

### 硬件要求
- **内存**: 最少 4GB RAM (推荐 8GB+)
- **存储**: 至少 2GB 可用空间
- **CPU**: 支持多核处理器 (音频处理较耗CPU)

## 🔍 依赖检查

运行以下命令检查环境:
```python
python -c "
import numpy, pandas, scipy, librosa, soundfile
import sklearn, matplotlib, seaborn, tqdm
print('✅ 所有核心依赖已安装!')
print(f'numpy: {numpy.__version__}')
print(f'librosa: {librosa.__version__}')
print(f'sklearn: {sklearn.__version__}')
"
```

## ⚠️ 常见问题

### 1. librosa 安装失败
```bash
# 解决方案1: 更新pip
pip install --upgrade pip
pip install librosa

# 解决方案2: 使用conda
conda install -c conda-forge librosa
```

### 2. soundfile 安装失败
```bash
# Windows用户可能需要安装Visual C++构建工具
pip install soundfile --no-cache-dir
```

### 3. 中文字体显示问题
```python
# 在代码中添加字体设置
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
```

### 4. 内存不足
- 处理大音频文件时可能需要更多内存
- 建议分批处理或使用更小的音频片段

## 📊 当前环境状态

基于最新检查，当前环境中的包版本:
```
✅ numpy           1.26.4
✅ pandas          2.2.3  
✅ scipy           1.13.1
✅ librosa         0.10.2.post1
✅ soundfile       0.13.1
✅ scikit-learn    1.6.1
✅ matplotlib      3.9.2
✅ seaborn         0.13.2
✅ tqdm            4.67.1
```

## 🎯 快速验证

运行以下命令验证安装:
```bash
cd spk_test
python -c "from easy_detector import AudioAnomalyDetector; print('✅ 项目依赖验证成功!')"
```

---

**更新时间**: 2025-01-17  
**Python版本**: 3.9.21  
**依赖总数**: 9个核心库 + 2个可选库
