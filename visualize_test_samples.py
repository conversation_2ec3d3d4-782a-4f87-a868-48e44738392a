#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化测试两个文件夹样本
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def test_and_visualize_samples():
    """测试并可视化样本"""
    print("🔍 可视化测试样本")
    print("="*60)
    
    # 定义测试文件夹 - 使用正确的相对路径
    folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }
    
    results = []
    
    # 测试所有文件夹
    for category, folder_list in folders.items():
        for folder in folder_list:
            print(f"\n📁 测试文件夹: {folder} ({category})")
            print("-" * 50)
            
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            # 获取所有wav文件
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            print(f"找到 {len(wav_files)} 个wav文件")
            
            for i, audio_path in enumerate(wav_files[:3]):  # 测试前3个文件
                filename = os.path.basename(audio_path)
                folder_name = os.path.basename(folder)
                
                print(f"\n🎵 [{i+1}/{min(3, len(wav_files))}] 测试文件: {filename}")
                
                try:
                    # 调用优化的频率分割算法
                    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                        audio_path,
                        min_duration=153,
                        plot=True,  # 生成可视化图表
                        debug=False,  # 减少输出
                        search_window_start=0.1,
                        search_window_end=1.5,
                        correlation_length=1.0
                    )
                    
                    # 提取关键信息
                    start_offset = alignment_info.get('start_offset', 0)
                    correlation_score = alignment_info.get('correlation_score', 0)
                    alignment_quality = alignment_info.get('alignment_quality', {})
                    
                    # 计算质量指标
                    overall_quality = alignment_quality.get('overall_quality', 'unknown')
                    composite_score = alignment_quality.get('composite_score', 0)
                    time_correlation = alignment_quality.get('time_correlation', 0)
                    freq_similarity = alignment_quality.get('freq_similarity', 0)
                    
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': start_offset,
                        'correlation_score': correlation_score,
                        'overall_quality': overall_quality,
                        'composite_score': composite_score,
                        'time_correlation': time_correlation,
                        'freq_similarity': freq_similarity,
                        'step_count': len(step_bounds),
                        'freq_count': len(freq_table),
                        'status': 'success'
                    }
                    
                    print(f"  ✅ 成功: 开始时间={start_offset:.3f}s, 相关性={correlation_score:.3f}")
                    print(f"     质量={overall_quality}, 评分={composite_score:.3f}")
                    
                except Exception as e:
                    print(f"  ❌ 失败: {str(e)}")
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': 0,
                        'correlation_score': 0,
                        'overall_quality': 'failed',
                        'composite_score': 0,
                        'time_correlation': 0,
                        'freq_similarity': 0,
                        'step_count': 0,
                        'freq_count': 0,
                        'status': 'failed',
                        'error': str(e)
                    }
                
                results.append(result)
    
    # 生成可视化统计报告
    if not results:
        print("❌ 没有测试结果")
        return
    
    df = pd.DataFrame(results)
    success_df = df[df['status'] == 'success']
    
    # 创建综合可视化图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('音频样本测试结果可视化分析', fontsize=16, fontweight='bold')
    
    # 1. 成功率对比
    ax1 = axes[0, 0]
    success_rate = df.groupby('category')['status'].apply(lambda x: (x == 'success').mean() * 100)
    bars = ax1.bar(success_rate.index, success_rate.values, color=['green', 'red'], alpha=0.7)
    ax1.set_title('成功率对比')
    ax1.set_ylabel('成功率 (%)')
    ax1.set_ylim(0, 100)
    for bar, rate in zip(bars, success_rate.values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{rate:.1f}%', ha='center', va='bottom')
    
    # 2. 开始时间分布
    ax2 = axes[0, 1]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            ax2.hist(cat_data['start_offset'], alpha=0.6, label=f'{category} (n={len(cat_data)})', bins=10)
        ax2.set_title('开始时间分布')
        ax2.set_xlabel('开始时间 (秒)')
        ax2.set_ylabel('频次')
        ax2.legend()
    
    # 3. 相关性分布
    ax3 = axes[0, 2]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            ax3.hist(cat_data['correlation_score'], alpha=0.6, label=f'{category} (n={len(cat_data)})', bins=10)
        ax3.set_title('相关性分布')
        ax3.set_xlabel('相关性系数')
        ax3.set_ylabel('频次')
        ax3.legend()
    
    # 4. 文件夹详细对比
    ax4 = axes[1, 0]
    if len(success_df) > 0:
        folder_stats = success_df.groupby(['category', 'folder']).agg({
            'correlation_score': 'mean',
            'start_offset': 'mean'
        }).reset_index()
        
        x_pos = np.arange(len(folder_stats))
        bars = ax4.bar(x_pos, folder_stats['correlation_score'], alpha=0.7)
        ax4.set_title('各文件夹平均相关性')
        ax4.set_ylabel('平均相关性')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels([f"{row['category']}/{row['folder']}" for _, row in folder_stats.iterrows()], 
                           rotation=45, ha='right')
        
        # 添加数值标签
        for bar, score in zip(bars, folder_stats['correlation_score']):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{score:.3f}', ha='center', va='bottom')
    
    # 5. 散点图：开始时间 vs 相关性
    ax5 = axes[1, 1]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            ax5.scatter(cat_data['start_offset'], cat_data['correlation_score'], 
                       label=category, alpha=0.7, s=60)
        ax5.set_title('开始时间 vs 相关性')
        ax5.set_xlabel('开始时间 (秒)')
        ax5.set_ylabel('相关性系数')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
    
    # 6. 质量评估分布
    ax6 = axes[1, 2]
    if len(success_df) > 0:
        quality_counts = success_df.groupby(['category', 'overall_quality']).size().unstack(fill_value=0)
        quality_counts.plot(kind='bar', ax=ax6, alpha=0.7)
        ax6.set_title('质量评估分布')
        ax6.set_xlabel('类别')
        ax6.set_ylabel('样本数量')
        ax6.legend(title='质量等级')
        ax6.tick_params(axis='x', rotation=0)
    
    plt.tight_layout()
    plt.savefig('audio_samples_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成详细统计报告
    print("\n" + "="*60)
    print("📊 详细统计报告")
    print("="*60)
    
    for category in df['category'].unique():
        cat_data = df[df['category'] == category]
        success_data = cat_data[cat_data['status'] == 'success']
        
        print(f"\n📁 {category.upper()} 类别:")
        print(f"  总样本数: {len(cat_data)}")
        print(f"  成功样本数: {len(success_data)}")
        print(f"  成功率: {len(success_data)/len(cat_data)*100:.1f}%")
        
        if len(success_data) > 0:
            print(f"  开始时间统计:")
            print(f"    平均值: {success_data['start_offset'].mean():.3f}s")
            print(f"    标准差: {success_data['start_offset'].std():.3f}s")
            print(f"    范围: {success_data['start_offset'].min():.3f}s - {success_data['start_offset'].max():.3f}s")
            
            print(f"  相关性统计:")
            print(f"    平均值: {success_data['correlation_score'].mean():.3f}")
            print(f"    标准差: {success_data['correlation_score'].std():.3f}")
            print(f"    范围: {success_data['correlation_score'].min():.3f} - {success_data['correlation_score'].max():.3f}")
    
    # 保存结果
    output_file = "visual_test_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    print(f"📊 可视化图表已保存到: audio_samples_analysis.png")
    
    return df

if __name__ == "__main__":
    results_df = test_and_visualize_samples()
