import numpy as np
import librosa
import matplotlib.pyplot as plt
import os
from align import align_signals_by_energy, align_signals_by_dominant_freq

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


# ========== 静音剔除 ==========
def trim_valid_region(y, threshold_ratio=0.1, frame_length=2048, hop_length=512):
    energy = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
    threshold = np.max(energy) * threshold_ratio
    mask = energy > threshold
    if not np.any(mask):
        return y
    start = np.argmax(mask)
    end = len(mask) - np.argmax(mask[::-1])
    return y[start * hop_length : end * hop_length]

# ========== 特征提取 ==========
def get_energy_envelope(y, n_fft=2048, hop_length=512):
    spec_abs = np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length))
    return np.sum(spec_abs, axis=0)

def get_dominant_frequency_track(y, sr, n_fft=2048, hop_length=512):
    S = np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length))
    dom_bin = np.argmax(S, axis=0)
    freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
    return freqs[dom_bin]

def get_spectral_leakage(S, top_k=1):
    S_db = librosa.amplitude_to_db(S, ref=np.max)
    top_vals = np.partition(S_db, -top_k, axis=0)[-top_k:]
    leakage = np.mean(S_db - top_vals[0], axis=0)
    return leakage

# ========== 相似性计算 ==========
def cosine_similarity(a, b):
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b) + 1e-8)

# ========== 主函数 ==========
def analyze_speaker(ref_path, test_path, sr=44100, output_dir="analysis_output"):
    os.makedirs(output_dir, exist_ok=True)
    name_ref = os.path.splitext(os.path.basename(ref_path))[0]
    name_test = os.path.splitext(os.path.basename(test_path))[0]

    y_ref, _ = librosa.load(ref_path, sr=sr, mono=True)
    y_test, _ = librosa.load(test_path, sr=sr, mono=True)

    # 对齐 + 静音剔除
    y_ref, y_test = align_signals_by_dominant_freq(y_ref, y_test, fs=sr)
    y_ref = trim_valid_region(y_ref)
    y_test = trim_valid_region(y_test)
    min_len = min(len(y_ref), len(y_test))
    y_ref = y_ref[:min_len]
    y_test = y_test[:min_len]

    # ========== 能量包络 ==========
    env_ref = get_energy_envelope(y_ref)
    env_test = get_energy_envelope(y_test)
    L = min(len(env_ref), len(env_test))
    env_sim = cosine_similarity(env_ref[:L], env_test[:L])
    
    eps = 1e-10
    diff_db = 20 * np.log10((env_test + eps) / (env_ref + eps))
    abnormal_ratio = np.mean(np.abs(diff_db) > 6)  # 超过 ±6dB 的帧比例

    # ========== 主频轨迹 ==========
    dom_ref = get_dominant_frequency_track(y_ref, sr)
    dom_test = get_dominant_frequency_track(y_test, sr)
    L = min(len(dom_ref), len(dom_test))
    dom_sim = cosine_similarity(dom_ref[:L], dom_test[:L])

    # ========== 泄漏率 ==========
    S_ref = np.abs(librosa.stft(y_ref))
    S_test = np.abs(librosa.stft(y_test))
    leak_ref = get_spectral_leakage(S_ref)
    leak_test = get_spectral_leakage(S_test)
    L = min(len(leak_ref), len(leak_test))
    leak_diff = np.mean(np.abs(leak_test[:L] - leak_ref[:L]))

    # ========== 综合判断 ==========
    print(f"\n=== 喇叭异常检测结果 [{name_ref} vs {name_test}] ===")
    print(f"能量包络相似度:     {env_sim:.4f}")
    print(f"主频轨迹相似度:     {dom_sim:.4f}")
    print(f"频谱泄漏率差异:     {leak_diff:.4f}")
    print(f"能量包络 dB 异常比例: {abnormal_ratio*100:.2f}%")

    if env_sim < 0.99 or dom_sim < 0.98 or leak_diff > 1.0 or abnormal_ratio > 0.15:
        result = "异常"
        print("⚠️ 判定结果：异常")
    else:
        result = "正常"
        print("✅ 判定结果：正常")

    # ========== 图像输出 ==========
    def plot_and_save(data1, data2, title, ylabel, filename, sr=44100, hop_length=512):
        L = min(len(data1), len(data2))
        t = np.arange(L) * hop_length / sr  # 时间轴（秒）
        plt.figure(figsize=(10, 4))
        plt.plot(t, data1[:L], label="参考样本", alpha=0.7)
        plt.plot(t, data2[:L], label="待测样本", alpha=0.7)
        plt.title(f"{title}\n{name_ref} vs {name_test} [{result}]")
        plt.xlabel("时间 (秒)")
        plt.ylabel(ylabel)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, filename), dpi=150)
        plt.close()

    plot_and_save(env_ref[:L], env_test[:L], "能量包络对比", "能量", f"energy_env_{name_ref}_vs_{name_test}.png", sr, 512)
    plot_and_save(dom_ref[:L], dom_test[:L], "主频轨迹对比", "频率 (Hz)", f"dominant_freq_{name_ref}_vs_{name_test}.png", sr, 512)
    plot_and_save(leak_ref[:L], leak_test[:L], "频谱泄漏率对比", "泄漏量 (dB)", f"leakage_{name_ref}_vs_{name_test}.png", sr, 512)
    

    t = np.arange(len(diff_db)) * 512 / sr
    plt.figure(figsize=(10, 4))
    plt.plot(t, diff_db, color="purple", label="能量差异 (dB)")
    plt.axhline(6, color='r', linestyle='--', label="±6 dB 阈值")
    plt.axhline(-6, color='r', linestyle='--')
    plt.title(f"能量包络 dB 差异\n{name_ref} vs {name_test} [{result}]")
    plt.xlabel("时间 (秒)")
    plt.ylabel("dB")
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"energy_diff_db_{name_ref}_vs_{name_test}.png"), dpi=150)
    plt.close()


if __name__ == "__main__":
    # REF_LIST = [
    #     r"test20250703\pos1.wav",
    #     r"test20250703\pos2.wav",
    #     r"test20250703\pos3.wav"
    # ]
    # TEST_LIST = [
    #     r"test20250703\neg1.wav",
    #     r"test20250703\neg2.wav",
    #     r"test20250703\neg3.wav"
    # ]
    
    
    REF_LIST = [
        r"20250707\sweep_test_0db.wav",
        # r"20250707\test0.wav",
    ]
    TEST_LIST = [
        r"20250707\test0.wav",
        r"20250707\test1.wav",
        r"20250707\test2.wav",
        r"20250707\test3.wav",
    ]

    fs = 48000
    for ref_path in REF_LIST:
        for test_path in TEST_LIST:
            analyze_speaker(ref_path, test_path, sr=48000)
