🎯 音频异响检测系统 - 开始使用
=====================================

恭喜！您已经获得了完整的音频异响检测系统。

📁 当前文件夹包含：
- 14个完整的项目文件
- 预训练的检测模型
- 详细的使用文档
- 示例代码和测试脚本

🚀 立即开始使用：

方法1 - 最简单的方式：
------------------------
1. 打开命令行，进入此文件夹
2. 运行：python quick_start.py
3. 系统会自动检查环境并运行演示

方法2 - 检测您的音频文件：
---------------------------
1. 将您的WAV音频文件放在此文件夹中
2. 运行以下Python代码：

from easy_detector import AudioAnomalyDetector
detector = AudioAnomalyDetector()
detector.load_model("audio_anomaly_detector.pkl")
result = detector.detect_anomaly("您的音频文件.wav")
print(f"检测结果: {result['overall_status']}")

方法3 - 批量检测：
------------------
detector.batch_detect("音频文件夹路径", "结果.csv")

📋 重要文件说明：
- easy_detector.py：主要接口（最重要）
- README.md：详细说明文档
- audio_anomaly_detector.pkl：预训练模型
- requirements.txt：依赖包列表

⚠️ 注意事项：
- 只支持WAV格式音频
- 建议Python 3.7+
- 如果缺少依赖包，运行：pip install -r requirements.txt

🔍 检测结果说明：
- "正常"：音频质量良好
- "可疑"：存在轻微异常
- "异常"：检测到明显异响

📞 获取帮助：
- 查看 README_使用指南.md 获取详细说明
- 运行 usage_guide.py 查看完整示例
- 查看 项目清单.md 了解所有文件

🎉 开始您的音频异响检测之旅吧！
