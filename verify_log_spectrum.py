#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证对数频谱分析结果
"""

import os
import glob
from PIL import Image

def verify_log_spectrum_results():
    """
    验证对数频谱分析结果
    """
    print("🔍 验证琴身内部异物1.1.wav对数频谱分析结果")
    print("="*60)
    
    # 检查输出目录
    output_dir = "琴身内部异物1.1_对数频谱分析"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    print(f"✅ 输出目录存在: {output_dir}")
    
    # 检查PNG文件
    png_files = glob.glob(os.path.join(output_dir, "log_segment_*.png"))
    png_files.sort()
    
    print(f"📊 找到 {len(png_files)} 个对数频谱图像文件")
    
    if len(png_files) != 93:
        print(f"⚠️  警告: 期望93个文件，实际找到{len(png_files)}个")
    else:
        print("✅ 文件数量正确: 93个频段")
    
    # 检查汇总文件
    summary_file = os.path.join(output_dir, "对数频谱分析汇总.txt")
    if os.path.exists(summary_file):
        print("✅ 汇总文件存在")
        
        # 读取汇总信息
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print("\n📄 汇总信息:")
            lines = content.split('\n')
            for line in lines[:18]:  # 显示前18行
                if line.strip():
                    print(f"  {line}")
    else:
        print("❌ 汇总文件不存在")
    
    # 显示文件大小统计
    if png_files:
        file_sizes = []
        for png_file in png_files:
            size = os.path.getsize(png_file)
            file_sizes.append(size)
        
        avg_size = sum(file_sizes) / len(file_sizes)
        min_size = min(file_sizes)
        max_size = max(file_sizes)
        total_size = sum(file_sizes)
        
        print(f"\n📈 文件大小统计:")
        print(f"  平均大小: {avg_size/1024:.1f} KB")
        print(f"  最小文件: {min_size/1024:.1f} KB")
        print(f"  最大文件: {max_size/1024:.1f} KB")
        print(f"  总大小: {total_size/1024/1024:.1f} MB")
    
    # 检查图像规格
    if png_files:
        try:
            with Image.open(png_files[0]) as img:
                width, height = img.size
                print(f"\n🖼️  图像规格:")
                print(f"  尺寸: {width}×{height}px")
                print(f"  模式: {img.mode}")
                print(f"  格式: {img.format}")
        except Exception as e:
            print(f"  ❌ 无法读取图像信息: {e}")
    
    # 显示频段范围
    print(f"\n🎵 频段范围:")
    if len(png_files) >= 6:
        # 显示前3个和后3个文件
        print("  前3个频段:")
        for i in range(3):
            filename = os.path.basename(png_files[i])
            freq_str = filename.split('_')[2].replace('Hz.png', '')
            print(f"    {filename} ({freq_str}Hz)")
        
        print("  ...")
        
        print("  后3个频段:")
        for i in range(-3, 0):
            filename = os.path.basename(png_files[i])
            freq_str = filename.split('_')[2].replace('Hz.png', '')
            print(f"    {filename} ({freq_str}Hz)")
    
    print(f"\n✅ 验证完成!")
    print(f"📁 所有文件保存在: {output_dir}")
    print(f"🎯 对数频谱特点:")
    print(f"   ✅ 使用semilogx对数频率轴")
    print(f"   ✅ 每段去除首尾8%数据")
    print(f"   ✅ 与harmonic_detector_api_fast.py参数一致")
    print(f"   ✅ 只显示频谱和动态噪声阈值")
    print(f"   ✅ 主频用蓝色圆点标记")
    
    # 对比不同版本
    print(f"\n📊 版本对比:")
    
    # 检查其他版本
    other_dirs = [
        "琴身内部异物1.1_频谱分析",
        "琴身内部异物1.1_频谱分析_粗线版本"
    ]
    
    for other_dir in other_dirs:
        if os.path.exists(other_dir):
            other_files = glob.glob(os.path.join(other_dir, "segment_*.png"))
            if other_files:
                other_size = sum(os.path.getsize(f) for f in other_files)
                print(f"  {other_dir}: {len(other_files)}个文件, {other_size/1024/1024:.1f}MB")
    
    current_size = sum(os.path.getsize(f) for f in png_files) if png_files else 0
    print(f"  {output_dir}: {len(png_files)}个文件, {current_size/1024/1024:.1f}MB")

if __name__ == "__main__":
    verify_log_spectrum_results()
