#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比目标异常文件与正常文件的详细特征差异
找出真正的区分特征
"""

import os
import sys
import numpy as np
from analyze_noise_curve_patterns import analyze_single_file_noise_pattern

def main():
    """
    对比目标文件与正常文件的特征
    """
    
    print("🔍 对比目标异常文件与正常文件的特征差异")
    print("="*60)
    
    # 目标异常文件
    target_patterns = ['低音戳洞', '153632', '155101']
    
    # 正常文件
    normal_patterns = ['ok1.wav', 'ok2.wav', 'sd1.wav', 'sd2.wav']
    
    test_dir = "test20250717"
    
    # 查找文件
    target_files = []
    normal_files = []
    
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                # 检查是否为目标文件
                if any(pattern in file for pattern in target_patterns):
                    target_files.append(os.path.join(root, file))
                # 检查是否为正常文件
                elif any(pattern in file for pattern in normal_patterns):
                    normal_files.append(os.path.join(root, file))
    
    print(f"🎯 目标异常文件: {len(target_files)}个")
    for file in target_files:
        print(f"  - {os.path.basename(file)}")
    
    print(f"\n✅ 正常文件: {len(normal_files)}个")
    for file in normal_files:
        print(f"  - {os.path.basename(file)}")
    
    print("\n" + "="*60)
    
    # 分析所有文件
    all_results = {}
    
    print("📊 分析目标异常文件:")
    for i, target_file in enumerate(target_files, 1):
        filename = os.path.basename(target_file)
        print(f"[{i}/{len(target_files)}] {filename}")
        
        result = analyze_single_file_noise_pattern(target_file)
        if result:
            all_results[filename] = {
                'type': 'target',
                'data': result
            }
            print(f"  ✅ 分析完成")
        else:
            print(f"  ❌ 分析失败")
    
    print("\n📊 分析正常文件:")
    for i, normal_file in enumerate(normal_files, 1):
        filename = os.path.basename(normal_file)
        print(f"[{i}/{len(normal_files)}] {filename}")
        
        result = analyze_single_file_noise_pattern(normal_file)
        if result:
            all_results[filename] = {
                'type': 'normal',
                'data': result
            }
            print(f"  ✅ 分析完成")
        else:
            print(f"  ❌ 分析失败")
    
    # 提取和对比特征
    print("\n" + "="*60)
    print("📈 特征对比分析:")
    
    target_features = []
    normal_features = []
    
    for filename, info in all_results.items():
        if info['data'] and info['data']['noise_patterns']:
            # 计算平均特征
            patterns = info['data']['noise_patterns']
            
            # 基础特征
            avg_noise_floor = np.mean([p['noise_analysis']['global_noise_floor_db'] for p in patterns])
            avg_noise_variation = np.mean([p['noise_analysis']['noise_variation_db'] for p in patterns])
            
            # 曲线特征
            all_curve_features = []
            for pattern in patterns:
                if 'curve_features' in pattern['noise_analysis']:
                    all_curve_features.append(pattern['noise_analysis']['curve_features'])
            
            if all_curve_features:
                avg_curve_features = {}
                for key in all_curve_features[0].keys():
                    values = [f[key] for f in all_curve_features if key in f]
                    if values:
                        avg_curve_features[key] = np.mean(values)
                
                feature_set = {
                    'filename': filename,
                    'avg_noise_floor': avg_noise_floor,
                    'avg_noise_variation': avg_noise_variation,
                    **avg_curve_features
                }
                
                if info['type'] == 'target':
                    target_features.append(feature_set)
                else:
                    normal_features.append(feature_set)
    
    # 打印详细对比
    print("\n🎯 目标异常文件特征:")
    for features in target_features:
        print(f"\n📁 {features['filename'][:40]}...")
        print(f"  噪声底噪: {features['avg_noise_floor']:.1f}dB")
        print(f"  噪声变化: {features['avg_noise_variation']:.1f}dB")
        print(f"  趋势系数: {features['trend_coefficient']:.1f}")
        print(f"  频域差异: {features['low_mid_diff']:.1f}dB")
        print(f"  平均斜率: {features['mean_slope']:.3f}")
        print(f"  斜率标准差: {features['std_slope']:.3f}")
        print(f"  平均曲率: {features['mean_curvature']:.3f}")
        print(f"  曲率标准差: {features['std_curvature']:.3f}")
        print(f"  峰值数: {features['num_peaks']}")
        print(f"  谷值数: {features['num_valleys']}")
    
    print("\n✅ 正常文件特征:")
    for features in normal_features:
        print(f"\n📁 {features['filename']}")
        print(f"  噪声底噪: {features['avg_noise_floor']:.1f}dB")
        print(f"  噪声变化: {features['avg_noise_variation']:.1f}dB")
        print(f"  趋势系数: {features['trend_coefficient']:.1f}")
        print(f"  频域差异: {features['low_mid_diff']:.1f}dB")
        print(f"  平均斜率: {features['mean_slope']:.3f}")
        print(f"  斜率标准差: {features['std_slope']:.3f}")
        print(f"  平均曲率: {features['mean_curvature']:.3f}")
        print(f"  曲率标准差: {features['std_curvature']:.3f}")
        print(f"  峰值数: {features['num_peaks']}")
        print(f"  谷值数: {features['num_valleys']}")
    
    # 计算统计差异
    print("\n" + "="*60)
    print("📊 统计差异分析:")
    
    if target_features and normal_features:
        feature_keys = ['avg_noise_floor', 'avg_noise_variation', 'trend_coefficient', 
                       'low_mid_diff', 'mean_slope', 'std_slope', 'mean_curvature', 
                       'std_curvature', 'num_peaks', 'num_valleys']
        
        for key in feature_keys:
            target_values = [f[key] for f in target_features if key in f]
            normal_values = [f[key] for f in normal_features if key in f]
            
            if target_values and normal_values:
                target_mean = np.mean(target_values)
                normal_mean = np.mean(normal_values)
                target_std = np.std(target_values)
                normal_std = np.std(normal_values)
                
                print(f"\n📈 {key}:")
                print(f"  目标文件: {target_mean:.3f} ± {target_std:.3f}")
                print(f"  正常文件: {normal_mean:.3f} ± {normal_std:.3f}")
                print(f"  差异: {abs(target_mean - normal_mean):.3f}")
                
                # 计算区分度
                if target_std + normal_std > 0:
                    separation = abs(target_mean - normal_mean) / (target_std + normal_std)
                    print(f"  区分度: {separation:.3f}")
    
    print("\n" + "="*60)
    print("🎯 特征对比分析完成！")

if __name__ == "__main__":
    main()
