# 谐波异常检测器API文档

## 概述

谐波异常检测器API是一个规范化的音频谐波异常检测接口，基于93段频谱分析和pos文件夹正常样本阈值，提供简单易用的检测功能。

## 特性

- ✅ **简单易用**: 单函数调用即可完成检测
- ✅ **标准化输出**: 1表示正常，0表示异常
- ✅ **可配置阈值**: 支持自定义异常段个数阈值
- ✅ **详细信息**: 可获取异常段详情和统计信息
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **高性能**: 基于优化的谐波检测算法

## 安装依赖

```bash
pip install numpy matplotlib librosa scipy
```

## 快速开始

### 方法1: 便捷函数（推荐）

```python
from harmonic_detector_api import detect_audio_harmonic_anomaly

# 检测音频文件
result = detect_audio_harmonic_anomaly("audio.wav")

if result == 1:
    print("音频正常")
else:
    print("音频异常")
```

### 方法2: 类接口

```python
from harmonic_detector_api import HarmonicDetectorAPI

# 创建检测器
detector = HarmonicDetectorAPI(anomaly_threshold=2)

# 检测音频
result = detector.detect("audio.wav")
print(f"检测结果: {result}")  # 1=正常, 0=异常
```

## API参考

### 便捷函数

#### `detect_audio_harmonic_anomaly(audio_path, anomaly_threshold=2)`

检测音频文件的谐波异常。

**参数:**
- `audio_path` (str): 音频文件路径
- `anomaly_threshold` (int): 异常段个数阈值，默认为2

**返回值:**
- `int`: 1表示正常，0表示异常

**异常:**
- `FileNotFoundError`: 音频文件不存在
- `ValueError`: 音频文件格式不支持
- `Exception`: 其他检测错误

### 类接口

#### `HarmonicDetectorAPI(anomaly_threshold=2)`

谐波异常检测器类。

**参数:**
- `anomaly_threshold` (int): 异常段个数阈值，默认为2

#### `detect(audio_path)`

检测单个音频文件。

**参数:**
- `audio_path` (str): 音频文件路径

**返回值:**
- `int`: 1表示正常，0表示异常

#### `detect_with_details(audio_path)`

检测音频文件并返回详细信息。

**参数:**
- `audio_path` (str): 音频文件路径

**返回值:**
- `dict`: 详细检测结果
  ```python
  {
      'result': int,           # 1=正常, 0=异常
      'anomaly_count': int,    # 异常段个数
      'threshold': int,        # 异常段阈值
      'total_segments': int,   # 总段数(93)
      'anomaly_ratio': float,  # 异常段比例
      'anomaly_segments': list # 异常段详情
  }
  ```

## 使用示例

### 基本检测

```python
from harmonic_detector_api import detect_audio_harmonic_anomaly

# 检测单个文件
result = detect_audio_harmonic_anomaly("test.wav")
print("正常" if result == 1 else "异常")
```

### 详细信息检测

```python
from harmonic_detector_api import HarmonicDetectorAPI

detector = HarmonicDetectorAPI()
details = detector.detect_with_details("test.wav")

print(f"检测结果: {'正常' if details['result'] == 1 else '异常'}")
print(f"异常段个数: {details['anomaly_count']}")
print(f"异常段比例: {details['anomaly_ratio']:.1%}")
```

### 自定义阈值

```python
# 使用更严格的阈值（1个异常段就判定为异常）
detector = HarmonicDetectorAPI(anomaly_threshold=1)
result = detector.detect("test.wav")

# 使用更宽松的阈值（5个异常段才判定为异常）
detector = HarmonicDetectorAPI(anomaly_threshold=5)
result = detector.detect("test.wav")
```

### 批量检测

```python
import os
from harmonic_detector_api import HarmonicDetectorAPI

detector = HarmonicDetectorAPI()

# 检测目录下所有wav文件
audio_dir = "audio_files/"
for filename in os.listdir(audio_dir):
    if filename.endswith('.wav'):
        filepath = os.path.join(audio_dir, filename)
        try:
            result = detector.detect(filepath)
            status = "正常" if result == 1 else "异常"
            print(f"{filename}: {status}")
        except Exception as e:
            print(f"{filename}: 检测失败 - {e}")
```

### 错误处理

```python
from harmonic_detector_api import HarmonicDetectorAPI

detector = HarmonicDetectorAPI()

try:
    result = detector.detect("audio.wav")
    print(f"检测结果: {result}")
except FileNotFoundError:
    print("音频文件不存在")
except ValueError:
    print("音频文件格式不支持")
except Exception as e:
    print(f"检测失败: {e}")
```

## 检测原理

### 算法流程
1. **频段分割**: 将音频分为93个频段（100Hz-20kHz）
2. **谐波检测**: 每段检测2-24次谐波
3. **阈值比较**: 与pos样本最大值阈值比较
4. **异常判定**: 异常段个数 >= 阈值 → 异常

### 阈值设置
- **默认阈值**: 2个异常段
- **基准数据**: pos文件夹47个正常样本的最大值
- **93段阈值**: 从15个（低频）到0个（高频）

### 检测精度
- **正常样本**: pos目录47个文件，100%正确识别
- **异常样本**: neg目录8个文件，87.5%正确识别
- **处理速度**: 约0.9秒/文件（多进程）

## 支持的音频格式

- WAV (推荐)
- MP3
- FLAC
- 其他librosa支持的格式

## 系统要求

- Python 3.6+
- 内存: 建议2GB以上
- CPU: 支持多核并行处理

## 注意事项

1. **音频质量**: 建议使用48kHz采样率的音频
2. **文件大小**: 支持各种时长的音频文件
3. **并发处理**: 类实例非线程安全，多线程使用时请创建独立实例
4. **错误处理**: 建议使用try-catch处理可能的异常

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本检测功能
- 提供详细信息接口
- 完善错误处理机制

## 技术支持

如有问题或建议，请联系开发团队。
