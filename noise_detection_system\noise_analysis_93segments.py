#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
93段噪声特征分析脚本
基于谐波定位中的噪声特征，分析音频的噪声分布和特性
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import json
from datetime import datetime

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features,
        'local_noise_levels': smoothed_noise,
        'window_centers': window_centers,
        'raw_noise_levels': local_noise_levels
    }

def analyze_segment_noise(audio_path, seg_idx, start_time, end_time, expected_freq, y, sr):
    """分析单个频段的噪声特征"""
    
    try:
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            return None
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
            fundamental_power_db = 10 * np.log10(display_power[actual_idx] + 1e-12)
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(display_power) + 1e-12)
        
        # 动态噪声分析
        noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
        
        if noise_analysis:
            # 计算信噪比
            snr_db = fundamental_power_db - noise_analysis['global_noise_floor_db']
            
            # 计算频谱统计特征
            power_db = 10 * np.log10(display_power + 1e-12)
            spectral_mean = np.mean(power_db)
            spectral_std = np.std(power_db)
            spectral_skewness = np.mean(((power_db - spectral_mean) / spectral_std) ** 3) if spectral_std > 0 else 0
            spectral_kurtosis = np.mean(((power_db - spectral_mean) / spectral_std) ** 4) if spectral_std > 0 else 0
            
            return {
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'fundamental_freq': fundamental_freq,
                'fundamental_power_db': fundamental_power_db,
                'snr_db': snr_db,
                'global_noise_floor_db': noise_analysis['global_noise_floor_db'],
                'noise_variation_db': noise_analysis['noise_variation_db'],
                'noise_fluctuation_features': noise_analysis['noise_fluctuation_features'],
                'spectral_statistics': {
                    'mean_db': spectral_mean,
                    'std_db': spectral_std,
                    'skewness': spectral_skewness,
                    'kurtosis': spectral_kurtosis
                },
                'local_noise_levels': noise_analysis['local_noise_levels'],
                'window_centers': noise_analysis['window_centers']
            }
        else:
            return None
            
    except Exception as e:
        print(f"段{seg_idx}噪声分析失败: {e}")
        return None

def analyze_audio_noise_93segments(audio_path):
    """分析音频文件的93段噪声特征"""
    
    print(f"🔍 分析文件: {os.path.basename(audio_path)}")
    print("="*60)
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"📊 音频信息: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        print(f"📊 分析段数: {len(step_boundaries)}段")
        print()
        
        # 分析所有段的噪声特征
        noise_results = []
        
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            print(f"分析段{seg_idx:2d} ({expected_freq:6.0f}Hz)...", end=" ")
            
            noise_result = analyze_segment_noise(
                audio_path, seg_idx, start_time, end_time, expected_freq, y, sr
            )
            
            if noise_result:
                noise_results.append(noise_result)
                print(f"✅ 噪声底噪: {noise_result['global_noise_floor_db']:.1f}dB, "
                      f"SNR: {noise_result['snr_db']:.1f}dB")
            else:
                print("❌ 分析失败")
        
        return {
            'filename': os.path.basename(audio_path),
            'filepath': audio_path,
            'analysis_time': datetime.now().isoformat(),
            'total_segments': len(step_boundaries),
            'successful_segments': len(noise_results),
            'noise_results': noise_results
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def create_noise_analysis_visualization(analysis_result, output_path=None):
    """创建噪声分析可视化"""

    if not analysis_result or not analysis_result['noise_results']:
        print("❌ 无有效分析结果，无法生成可视化")
        return None

    noise_results = analysis_result['noise_results']

    # 提取数据
    segment_indices = [r['seg_idx'] for r in noise_results]
    expected_freqs = [r['expected_freq'] for r in noise_results]
    noise_floors = [r['global_noise_floor_db'] for r in noise_results]
    snr_values = [r['snr_db'] for r in noise_results]
    noise_variations = [r['noise_variation_db'] for r in noise_results]
    stability_scores = [r['noise_fluctuation_features']['fluctuation_stability_score'] for r in noise_results]

    # 创建4子图布局
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))

    fig.suptitle(f'93段噪声特征分析\n文件: {analysis_result["filename"]}\n'
                f'成功分析: {len(noise_results)}/{analysis_result["total_segments"]}段',
                fontsize=16, fontweight='bold')

    # 1. 噪声底噪随频率变化
    ax1 = axes[0, 0]
    ax1.plot(expected_freqs, noise_floors, 'b-', linewidth=2, alpha=0.8, label='噪声底噪')
    ax1.scatter(expected_freqs, noise_floors, c='blue', s=30, alpha=0.7)
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('噪声底噪 (dB)')
    ax1.set_title('噪声底噪随频率变化')
    ax1.set_xscale('log')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 添加统计信息
    mean_noise = np.mean(noise_floors)
    std_noise = np.std(noise_floors)
    ax1.axhline(mean_noise, color='red', linestyle='--', alpha=0.7, label=f'平均值: {mean_noise:.1f}dB')
    ax1.fill_between(expected_freqs, mean_noise - std_noise, mean_noise + std_noise,
                     alpha=0.2, color='red', label=f'±1σ: {std_noise:.1f}dB')
    ax1.legend()

    # 2. 信噪比分布
    ax2 = axes[0, 1]
    ax2.plot(expected_freqs, snr_values, 'g-', linewidth=2, alpha=0.8, label='SNR')
    ax2.scatter(expected_freqs, snr_values, c='green', s=30, alpha=0.7)
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('信噪比 (dB)')
    ax2.set_title('信噪比随频率变化')
    ax2.set_xscale('log')
    ax2.grid(True, alpha=0.3)

    # SNR阈值线
    ax2.axhline(10, color='orange', linestyle='--', alpha=0.7, label='10dB阈值')
    ax2.axhline(20, color='red', linestyle='--', alpha=0.7, label='20dB阈值')
    ax2.legend()

    # 3. 噪声变化程度
    ax3 = axes[1, 0]
    ax3.plot(expected_freqs, noise_variations, 'purple', linewidth=2, alpha=0.8, label='噪声变化')
    ax3.scatter(expected_freqs, noise_variations, c='purple', s=30, alpha=0.7)
    ax3.set_xlabel('频率 (Hz)')
    ax3.set_ylabel('噪声变化 (dB)')
    ax3.set_title('噪声变化程度随频率变化')
    ax3.set_xscale('log')
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    # 4. 噪声稳定性评分
    ax4 = axes[1, 1]
    ax4.plot(expected_freqs, stability_scores, 'orange', linewidth=2, alpha=0.8, label='稳定性评分')
    ax4.scatter(expected_freqs, stability_scores, c='orange', s=30, alpha=0.7)
    ax4.set_xlabel('频率 (Hz)')
    ax4.set_ylabel('稳定性评分 (0-1)')
    ax4.set_title('噪声稳定性评分随频率变化')
    ax4.set_xscale('log')
    ax4.set_ylim(0, 1)
    ax4.grid(True, alpha=0.3)

    # 稳定性阈值线
    ax4.axhline(0.8, color='green', linestyle='--', alpha=0.7, label='高稳定性(0.8)')
    ax4.axhline(0.6, color='orange', linestyle='--', alpha=0.7, label='中稳定性(0.6)')
    ax4.legend()

    plt.tight_layout()

    # 保存图片
    if output_path is None:
        safe_filename = ''.join(c for c in analysis_result['filename'] if c.isalnum() or c in '_-')
        output_path = f"noise_analysis_{safe_filename}.png"

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def print_noise_statistics(analysis_result):
    """打印噪声统计信息"""

    if not analysis_result or not analysis_result['noise_results']:
        print("❌ 无有效分析结果")
        return

    noise_results = analysis_result['noise_results']

    print(f"\n📊 噪声特征统计分析")
    print("="*60)

    # 提取数据
    noise_floors = [r['global_noise_floor_db'] for r in noise_results]
    snr_values = [r['snr_db'] for r in noise_results]
    noise_variations = [r['noise_variation_db'] for r in noise_results]
    stability_scores = [r['noise_fluctuation_features']['fluctuation_stability_score'] for r in noise_results]

    # 基本统计
    print(f"噪声底噪统计:")
    print(f"  平均值: {np.mean(noise_floors):.2f} dB")
    print(f"  标准差: {np.std(noise_floors):.2f} dB")
    print(f"  范围: {np.min(noise_floors):.2f} ~ {np.max(noise_floors):.2f} dB")
    print()

    print(f"信噪比统计:")
    print(f"  平均值: {np.mean(snr_values):.2f} dB")
    print(f"  标准差: {np.std(snr_values):.2f} dB")
    print(f"  范围: {np.min(snr_values):.2f} ~ {np.max(snr_values):.2f} dB")
    print(f"  >10dB段数: {sum(1 for snr in snr_values if snr > 10)}/{len(snr_values)}")
    print(f"  >20dB段数: {sum(1 for snr in snr_values if snr > 20)}/{len(snr_values)}")
    print()

    print(f"噪声变化统计:")
    print(f"  平均值: {np.mean(noise_variations):.2f} dB")
    print(f"  标准差: {np.std(noise_variations):.2f} dB")
    print(f"  范围: {np.min(noise_variations):.2f} ~ {np.max(noise_variations):.2f} dB")
    print()

    print(f"稳定性评分统计:")
    print(f"  平均值: {np.mean(stability_scores):.3f}")
    print(f"  标准差: {np.std(stability_scores):.3f}")
    print(f"  范围: {np.min(stability_scores):.3f} ~ {np.max(stability_scores):.3f}")
    print(f"  高稳定性(>0.8): {sum(1 for s in stability_scores if s > 0.8)}/{len(stability_scores)}")
    print(f"  中稳定性(>0.6): {sum(1 for s in stability_scores if s > 0.6)}/{len(stability_scores)}")
    print()

    # 频段分析
    print(f"频段特征分析:")

    # 低频段 (100-500Hz)
    low_freq_results = [r for r in noise_results if r['expected_freq'] <= 500]
    if low_freq_results:
        low_noise_floors = [r['global_noise_floor_db'] for r in low_freq_results]
        low_snr = [r['snr_db'] for r in low_freq_results]
        print(f"  低频段 (≤500Hz): {len(low_freq_results)}段")
        print(f"    平均噪声底噪: {np.mean(low_noise_floors):.2f} dB")
        print(f"    平均SNR: {np.mean(low_snr):.2f} dB")

    # 中频段 (500-5000Hz)
    mid_freq_results = [r for r in noise_results if 500 < r['expected_freq'] <= 5000]
    if mid_freq_results:
        mid_noise_floors = [r['global_noise_floor_db'] for r in mid_freq_results]
        mid_snr = [r['snr_db'] for r in mid_freq_results]
        print(f"  中频段 (500-5000Hz): {len(mid_freq_results)}段")
        print(f"    平均噪声底噪: {np.mean(mid_noise_floors):.2f} dB")
        print(f"    平均SNR: {np.mean(mid_snr):.2f} dB")

    # 高频段 (>5000Hz)
    high_freq_results = [r for r in noise_results if r['expected_freq'] > 5000]
    if high_freq_results:
        high_noise_floors = [r['global_noise_floor_db'] for r in high_freq_results]
        high_snr = [r['snr_db'] for r in high_freq_results]
        print(f"  高频段 (>5000Hz): {len(high_freq_results)}段")
        print(f"    平均噪声底噪: {np.mean(high_noise_floors):.2f} dB")
        print(f"    平均SNR: {np.mean(high_snr):.2f} dB")

def save_noise_analysis_report(analysis_result, output_path=None):
    """保存噪声分析报告"""

    if not analysis_result:
        return None

    if output_path is None:
        safe_filename = ''.join(c for c in analysis_result['filename'] if c.isalnum() or c in '_-')
        output_path = f"noise_analysis_report_{safe_filename}.json"

    # 转换numpy数组为列表以便JSON序列化
    def convert_numpy_to_list(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_to_list(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_to_list(item) for item in obj]
        else:
            return obj

    # 转换数据
    serializable_result = convert_numpy_to_list(analysis_result)

    # 保存JSON报告
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_result, f, ensure_ascii=False, indent=2)

    return output_path

def main():
    """主函数 - 分析低音戳洞文件"""

    # 查找低音戳洞文件
    target_files = []

    # 在test20250717目录中查找
    test_dir = "test20250717"
    if os.path.exists(test_dir):
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if "低音戳洞" in file and file.lower().endswith('.wav'):
                    target_files.append(os.path.join(root, file))

    if not target_files:
        print("❌ 未找到包含'低音戳洞'的wav文件")
        print("请检查文件是否存在于test20250717目录中")
        return

    print(f"🎯 找到{len(target_files)}个低音戳洞文件:")
    for file in target_files:
        print(f"  📁 {file}")
    print()

    # 分析第一个文件
    target_file = target_files[0]

    # 执行噪声分析
    analysis_result = analyze_audio_noise_93segments(target_file)

    if analysis_result:
        print(f"\n✅ 分析完成!")

        # 打印统计信息
        print_noise_statistics(analysis_result)

        # 生成可视化
        viz_path = create_noise_analysis_visualization(analysis_result)
        if viz_path:
            print(f"📊 可视化已保存: {viz_path}")

        # 保存报告
        report_path = save_noise_analysis_report(analysis_result)
        if report_path:
            print(f"📄 报告已保存: {report_path}")
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
