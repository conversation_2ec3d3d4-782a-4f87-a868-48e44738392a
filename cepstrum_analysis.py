#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
倒谱分析脚本
分析琴身内部异物文件的93段倒谱，标注主频和谐波
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_cepstrum(audio_segment, sr):
    """
    计算音频段的倒谱
    """

    # 标准化
    if np.max(np.abs(audio_segment)) > 0:
        audio_segment = audio_segment / np.max(np.abs(audio_segment))

    # 高分辨率FFT
    fft_size = 131072
    if len(audio_segment) < fft_size:
        audio_segment = np.pad(audio_segment, (0, fft_size - len(audio_segment)), 'constant')
    else:
        audio_segment = audio_segment[:fft_size]

    # 应用窗函数
    window = np.hanning(len(audio_segment))
    audio_segment = audio_segment * window

    # FFT
    fft = np.fft.fft(audio_segment)
    magnitude = np.abs(fft)

    # 计算功率谱（使用完整的FFT结果）
    power_spectrum = magnitude ** 2

    # 避免log(0)
    power_spectrum = np.maximum(power_spectrum, 1e-12)

    # 计算倒谱：IFFT(log(功率谱))
    log_power = np.log(power_spectrum)

    # 对log功率谱做IFFT得到倒谱
    cepstrum = np.fft.ifft(log_power)
    cepstrum = np.real(cepstrum)  # 取实部

    # quefrency轴（倒谱的"频率"轴）- 正确的计算方式
    quefrency = np.arange(len(cepstrum)) / sr

    # 只返回正频率部分的功率谱用于显示
    positive_power_spectrum = power_spectrum[:fft_size//2]
    positive_freqs = np.fft.fftfreq(fft_size, 1/sr)[:fft_size//2]

    return cepstrum, quefrency, positive_power_spectrum, positive_freqs

def analyze_segment_cepstrum(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的倒谱
    """
    
    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                print(f"  ⚠️  段{seg_idx}: 修剪后时长不足")
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        # 检查边界
        if start_sample >= len(y) or end_sample > len(y) or start_sample >= end_sample:
            print(f"  ⚠️  段{seg_idx}: 时间边界超出音频范围 ({start_sample}-{end_sample} vs {len(y)})")
            return None

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            print(f"  ⚠️  段{seg_idx}: 音频段为空")
            return None

        if len(segment_audio) < 1024:  # 至少需要1024个样本
            print(f"  ⚠️  段{seg_idx}: 音频段太短 ({len(segment_audio)}样本)")
            return None
        
        # 计算倒谱
        cepstrum, quefrency, power_spectrum, freqs = calculate_cepstrum(segment_audio, sr)
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_spectrum[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 使用harmonic_detection_system检测谐波
        noise_analysis = {
            'global_noise_floor_db': -60,
            'noise_variation_db': 10,
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }
        
        harmonic_analysis = detect_harmonics_for_segment(freqs, power_spectrum, fundamental_freq, noise_analysis)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'cepstrum': cepstrum,
            'quefrency': quefrency,
            'power_spectrum': power_spectrum,
            'freqs': freqs,
            'harmonic_analysis': harmonic_analysis
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_cepstrum_visualization(segment_data, output_dir, filename=""):
    """
    创建倒谱可视化
    """
    
    if not segment_data:
        return None
    
    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    cepstrum = segment_data['cepstrum']
    quefrency = segment_data['quefrency']
    power_spectrum = segment_data['power_spectrum']
    freqs = segment_data['freqs']
    harmonic_analysis = segment_data['harmonic_analysis']
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n倒谱分析与谐波检测', 
                 fontsize=16, fontweight='bold')
    
    # 上图：功率谱
    power_db = 10 * np.log10(power_spectrum + 1e-12)
    ax1.plot(freqs, power_db, 'blue', linewidth=1, alpha=0.7, label='功率谱')
    
    # 标记主频
    ax1.axvline(fundamental_freq, color='red', linestyle='-', linewidth=3, 
               alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')
    
    # 标记检测到的谐波
    if harmonic_analysis:
        harmonic_freqs = [h['freq'] for h in harmonic_analysis]
        harmonic_powers_db = [h['power_db'] for h in harmonic_analysis]
        ax1.scatter(harmonic_freqs, harmonic_powers_db, c='green', s=50, alpha=0.8, 
                   marker='^', label=f'谐波({len(harmonic_analysis)}个)')
        
        # 标注谐波次数
        for i, (freq, power_db) in enumerate(zip(harmonic_freqs, harmonic_powers_db)):
            order = round(freq / fundamental_freq)
            ax1.annotate(f'{order}', (freq, power_db), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8, color='green')
    
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('功率 (dB)')
    ax1.set_xlim(50, 20000)
    ax1.set_xscale('log')
    ax1.set_ylim(-80, 40)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_title('功率谱与谐波检测')
    
    # 下图：倒谱
    # 只显示前半部分倒谱（对称性）
    half_length = len(cepstrum) // 2
    ax2.plot(quefrency[:half_length] * 1000, cepstrum[:half_length], 'purple', linewidth=1.5, label='倒谱')
    
    # 标记主频对应的quefrency峰值
    fundamental_period = 1.0 / fundamental_freq  # 基频周期(秒)
    fundamental_quefrency_ms = fundamental_period * 1000  # 转换为毫秒

    # 在倒谱中寻找基频附近的峰值
    quefrency_ms = quefrency[:half_length] * 1000

    # 限制搜索范围在合理的quefrency范围内
    if fundamental_quefrency_ms < quefrency_ms[-1] and fundamental_quefrency_ms > 1.0:
        search_range = max(1.0, fundamental_quefrency_ms * 0.1)  # 动态搜索范围
        search_mask = (quefrency_ms >= fundamental_quefrency_ms - search_range) & \
                      (quefrency_ms <= fundamental_quefrency_ms + search_range)

        if np.any(search_mask):
            search_cepstrum = cepstrum[:half_length][search_mask]
            search_quefrency_ms = quefrency_ms[search_mask]
            if len(search_cepstrum) > 0:
                max_idx = np.argmax(np.abs(search_cepstrum))
                peak_quefrency_ms = search_quefrency_ms[max_idx]
                peak_value = search_cepstrum[max_idx]

                ax2.axvline(peak_quefrency_ms, color='red', linestyle='--', linewidth=2,
                           alpha=0.8, label=f'基频峰: {peak_quefrency_ms:.1f}ms')
                ax2.scatter([peak_quefrency_ms], [peak_value], c='red', s=100, alpha=0.8, marker='o')

    # 标记理论基频位置（即使没有明显峰值）
    if fundamental_quefrency_ms < quefrency_ms[-1] and fundamental_quefrency_ms > 0:
        ax2.axvline(fundamental_quefrency_ms, color='orange', linestyle=':', linewidth=1,
                   alpha=0.6, label=f'理论基频: {fundamental_quefrency_ms:.1f}ms')
    
    # 标记谐波对应的quefrency
    if harmonic_analysis and len(harmonic_analysis) > 1:
        for i, harmonic in enumerate(harmonic_analysis[1:3]):  # 只标记前2个谐波
            harmonic_period = 1.0 / harmonic['freq']
            harmonic_quefrency_ms = harmonic_period * 1000

            if harmonic_quefrency_ms < quefrency_ms[-1] and harmonic_quefrency_ms > 0:
                ax2.axvline(harmonic_quefrency_ms, color='green', linestyle=':', linewidth=1,
                           alpha=0.6, label=f'谐波{i+2}: {harmonic_quefrency_ms:.1f}ms')
    
    ax2.set_xlabel('Quefrency (ms)')
    ax2.set_ylabel('倒谱幅度')
    ax2.set_xlim(0, 50)  # 显示前50ms
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_title('倒谱分析')
    
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(output_dir, f"cepstrum_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def process_single_segment_cepstrum(args):
    """
    处理单个段的倒谱分析（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 分析段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_cepstrum(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_cepstrum_visualization(segment_data, output_dir, filename)

            if viz_path:
                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_freq': segment_data['fundamental_freq'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'cepstrum_peak': np.max(np.abs(segment_data['cepstrum'][:len(segment_data['cepstrum'])//2]))
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 谐波: {result['harmonic_count']}个, 倒谱峰值: {result['cepstrum_peak']:.3f}")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析琴身内部异物文件的倒谱
    """

    print("🎯 琴身内部异物文件倒谱分析")
    print("📝 分析93段倒谱，标注主频和谐波")
    print("📝 每段去掉开头结尾8%比例再计算")
    print("="*80)

    # 查找琴身内部异物文件
    target_file = "test20250722/琴身内部异物1.1.wav"

    if not os.path.exists(target_file):
        print(f"❌ 未找到目标文件: {target_file}")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_cepstrum_analysis"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行倒谱分析...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_cepstrum, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件倒谱分析完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        # 谐波和倒谱统计
        harmonic_counts = [r['harmonic_count'] for r in successful_results]
        cepstrum_peaks = [r['cepstrum_peak'] for r in successful_results]

        print(f"\n📊 倒谱特征统计:")
        print(f"  平均谐波数: {np.mean(harmonic_counts):.1f}个")
        print(f"  谐波数范围: {np.min(harmonic_counts)} ~ {np.max(harmonic_counts)}个")
        print(f"  平均倒谱峰值: {np.mean(cepstrum_peaks):.3f}")
        print(f"  倒谱峰值范围: {np.min(cepstrum_peaks):.3f} ~ {np.max(cepstrum_peaks):.3f}")

        # 高倒谱峰值段
        high_peak_segments = [(r['seg_idx'], r['expected_freq'], r['cepstrum_peak'])
                             for r in successful_results if r['cepstrum_peak'] > np.mean(cepstrum_peaks) + np.std(cepstrum_peaks)]

        print(f"\n🎯 高倒谱峰值段（>均值+1σ）:")
        if high_peak_segments:
            for seg_idx, freq, peak in high_peak_segments:
                print(f"  📈 段{seg_idx} ({freq:.1f}Hz): 倒谱峰值 {peak:.3f}")
        else:
            print("  ✅ 无明显高峰值段")

        print("="*80)
        print("🎯 倒谱分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
