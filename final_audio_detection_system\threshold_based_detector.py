#!/usr/bin/env python3
"""
基于阈值的频点异常检测器
Threshold-Based Frequency Point Anomaly Detector
切割频段后，计算频谱，使用阈值检测连续频点异常
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

class ThresholdBasedDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,        # 约21ms @48kHz
            'noverlap': 768,        # 75%重叠
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),        # 完整扫频范围
            'edge_exclude_ratio': 0.2,              # 排除频段边界20%
            'amplitude_threshold_db': -20,          # 幅度阈值(dB) - 降低阈值
            'min_continuous_points': 3,             # 最少连续频点数
            'min_anomalous_segments': 5             # 最少异常频段数
        }
        
        print(f"基于阈值的频点异常检测器初始化完成")
        print(f"检测逻辑: 频段切割 → 频谱计算 → 阈值检测 → 连续频点判断")
        print(f"幅度阈值: {self.detection_params['amplitude_threshold_db']}dB")
        print(f"最少连续频点: {self.detection_params['min_continuous_points']}个")
    
    def detect_threshold_anomalies(self, audio_path):
        """基于阈值检测频点异常"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT时频分析 - 完整频率范围
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 使用freq_split进行频段分割
            analysis = self._analyze_with_threshold(
                audio_path, Zxx, frequencies, times
            )
            
            # 基于连续频点异常判断
            anomaly_score, anomaly_detected = self._threshold_decision(analysis)
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'analysis': analysis,
                'detection_method': 'threshold_based_frequency_points',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_with_threshold(self, audio_path, Zxx, frequencies, times):
        """使用阈值分析频点异常"""
        analysis = {
            'freq_split_success': False,
            'total_segments': 0,
            'anomalous_segments': 0,
            'segment_details': [],
            'max_continuous_points': 0,
            'total_continuous_groups': 0
        }
        
        try:
            # 使用freq_split进行频段分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path,
                **self.freq_split_params,
                plot=False
            )
            
            if len(step_boundaries) == 0:
                return analysis
            
            analysis['freq_split_success'] = True
            analysis['total_segments'] = len(step_boundaries)
            
            print(f"{os.path.basename(audio_path)}: 分析{len(step_boundaries)}个频段")
            
            # 分析每个频段
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                segment_result = self._analyze_segment_threshold(
                    Zxx, frequencies, times, seg_start_time, seg_end_time, seg_idx
                )
                
                analysis['segment_details'].append(segment_result)
                
                if segment_result['has_anomaly']:
                    analysis['anomalous_segments'] += 1
                    analysis['max_continuous_points'] = max(
                        analysis['max_continuous_points'],
                        segment_result['max_continuous_points']
                    )
                    analysis['total_continuous_groups'] += segment_result['continuous_groups']
            
            print(f"  异常频段: {analysis['anomalous_segments']}/{analysis['total_segments']}")
            print(f"  最大连续频点: {analysis['max_continuous_points']}")
            
        except Exception as e:
            print(f"阈值分析失败: {e}")
        
        return analysis
    
    def _analyze_segment_threshold(self, Zxx, frequencies, times, seg_start_time, seg_end_time, seg_idx):
        """分析单个频段的阈值异常"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'has_anomaly': False,
            'max_continuous_points': 0,
            'continuous_groups': 0,
            'anomaly_details': []
        }
        
        # 找到频段对应的时间范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        # 排除频段边界
        edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
        seg_core_start = seg_start_idx + edge_exclude
        seg_core_end = seg_end_idx - edge_exclude
        
        if seg_core_end <= seg_core_start:
            return result
        
        # 提取频段核心部分的频谱数据
        segment_spectrum = Zxx[:, seg_core_start:seg_core_end]
        
        # 对每个时间片进行阈值检测
        for t_idx in range(segment_spectrum.shape[1]):
            time_spectrum = segment_spectrum[:, t_idx]
            
            # 转换为dB
            amplitude_db = 20 * np.log10(np.abs(time_spectrum) + 1e-12)
            
            # 使用阈值检测高于阈值的频点
            above_threshold = amplitude_db > self.detection_params['amplitude_threshold_db']
            
            # 查找连续的频点组
            continuous_groups = self._find_continuous_frequency_groups(above_threshold)
            
            # 检查是否有足够长的连续频点
            for group in continuous_groups:
                if len(group) >= self.detection_params['min_continuous_points']:
                    result['has_anomaly'] = True
                    result['max_continuous_points'] = max(
                        result['max_continuous_points'], 
                        len(group)
                    )
                    result['continuous_groups'] += 1
                    
                    # 记录异常详情
                    freq_start = frequencies[group[0]]
                    freq_end = frequencies[group[-1]]
                    max_amplitude = np.max(amplitude_db[group])
                    
                    result['anomaly_details'].append({
                        'time_idx': t_idx,
                        'frequency_range': (freq_start, freq_end),
                        'continuous_points': len(group),
                        'max_amplitude_db': max_amplitude
                    })
        
        return result
    
    def _find_continuous_frequency_groups(self, above_threshold_mask):
        """查找连续的频点组"""
        groups = []
        current_group = []
        
        for i, is_above in enumerate(above_threshold_mask):
            if is_above:
                current_group.append(i)
            else:
                if len(current_group) > 0:
                    groups.append(current_group)
                    current_group = []
        
        # 处理最后一组
        if len(current_group) > 0:
            groups.append(current_group)
        
        return groups
    
    def _threshold_decision(self, analysis):
        """基于阈值检测结果判断异常"""
        if not analysis['freq_split_success']:
            return 0.0, False
        
        anomalous_segments = analysis['anomalous_segments']
        total_segments = analysis['total_segments']
        max_continuous_points = analysis['max_continuous_points']
        total_continuous_groups = analysis['total_continuous_groups']
        
        if anomalous_segments == 0:
            return 0.0, False
        
        # 计算异常分数
        segment_ratio = anomalous_segments / total_segments
        continuous_score = min(1.0, max_continuous_points / 20.0)
        group_score = min(1.0, total_continuous_groups / 50.0)
        
        # 加权计算最终分数
        anomaly_score = (
            segment_ratio * 0.5 +
            continuous_score * 0.3 +
            group_score * 0.2
        )
        
        # 判断是否异常
        anomaly_detected = (
            anomalous_segments >= self.detection_params['min_anomalous_segments'] and
            anomaly_score > 0.3
        )
        
        return anomaly_score, anomaly_detected
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'analysis': {},
            'detection_method': 'threshold_based_frequency_points',
            'error': True,
            'error_message': error_msg
        }
    
    def test_all_samples(self):
        """测试所有样本"""
        print("\n" + "="*80)
        print("基于阈值的频点异常检测")
        print("="*80)
        
        test_files = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav",
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        results = []
        for file_path in test_files:
            if os.path.exists(file_path):
                print(f"\n测试文件: {os.path.basename(file_path)}")
                print("-" * 50)
                
                result = self.detect_threshold_anomalies(file_path)
                result['filename'] = os.path.basename(file_path)
                results.append(result)
                
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    
                    analysis = result['analysis']
                    print(f"异常频段: {analysis.get('anomalous_segments', 0)}/{analysis.get('total_segments', 0)}")
                    print(f"最大连续频点: {analysis.get('max_continuous_points', 0)}")
                    print(f"连续组总数: {analysis.get('total_continuous_groups', 0)}")
                    
                    # 显示部分异常详情
                    segment_details = analysis.get('segment_details', [])
                    anomalous_details = [s for s in segment_details if s['has_anomaly']]
                    if anomalous_details:
                        print(f"异常频段示例:")
                        for i, detail in enumerate(anomalous_details[:3]):  # 只显示前3个
                            print(f"  频段{detail['segment_index']}: {detail['continuous_groups']}个连续组, "
                                  f"最大{detail['max_continuous_points']}个连续频点")
                else:
                    print(f"处理失败: {result['error_message']}")
        
        # 统计结果
        valid_results = [r for r in results if not r['error']]
        anomaly_detected = [r for r in valid_results if r['anomaly_detected']]
        
        print(f"\n" + "="*60)
        print(f"基于阈值的检测统计:")
        print(f"  总文件数: {len(valid_results)}")
        print(f"  检出异常: {len(anomaly_detected)}个")
        print(f"  检出率: {len(anomaly_detected)/len(valid_results)*100:.1f}%" if valid_results else "  检出率: 0%")
        
        return results

def main():
    """主函数"""
    detector = ThresholdBasedDetector()
    results = detector.test_all_samples()
    
    # 保存结果
    results_data = []
    for result in results:
        if not result['error']:
            analysis = result['analysis']
            row = {
                'filename': result['filename'],
                'anomaly_detected': result['anomaly_detected'],
                'confidence': result['confidence'],
                'anomaly_score': result['anomaly_score'],
                'freq_split_success': analysis.get('freq_split_success', False),
                'total_segments': analysis.get('total_segments', 0),
                'anomalous_segments': analysis.get('anomalous_segments', 0),
                'max_continuous_points': analysis.get('max_continuous_points', 0),
                'total_continuous_groups': analysis.get('total_continuous_groups', 0),
                'error': result['error']
            }
            results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('threshold_based_detection_results.csv', index=False)
    
    print(f"\n基于阈值的频点异常检测结果已保存: threshold_based_detection_results.csv")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
