#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化噪声估算：调整参数减少噪声波动，提高检测稳定性
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter, medfilt

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def optimized_noise_estimation():
    """优化噪声估算"""
    print("🎯 优化噪声估算 - 低音戳洞文件前4个频段")
    print("="*50)
    print("优化策略:")
    print("1. 更大的滑动窗口 (500 bins)")
    print("2. 中值滤波预处理")
    print("3. 更强的平滑滤波")
    print("4. 多层噪声估算")
    print("5. 稳定的阈值策略")
    print("="*50)
    
    # 低音戳洞文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析文件
    hole_data = analyze_with_optimized_noise(hole_file)
    
    if hole_data:
        create_optimized_visualization(hole_data)

def analyze_with_optimized_noise(audio_path):
    """使用优化噪声估算分析"""
    try:
        print(f"\n使用优化噪声估算分析...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前4段
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 超高分辨率FFT分析
            fft_size = 131072  # 128k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            power_db = 10 * np.log10(power + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            print(f"  分析频段 {seg_idx} ({expected_freq:.0f}Hz)")
            
            # 1. 主频分析
            fundamental_analysis = analyze_fundamental_optimized(display_freqs, display_power, expected_freq)
            
            # 2. 优化噪声估计
            optimized_noise_analysis = estimate_optimized_noise(display_freqs, display_power, fundamental_analysis)
            
            # 3. 稳定谐波检测
            harmonic_analysis = detect_harmonics_stable(
                display_freqs, display_power, fundamental_analysis, optimized_noise_analysis
            )
            
            if fundamental_analysis:
                print(f"    主频: {fundamental_analysis['freq']:.3f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
                print(f"    稳定噪声底噪: {optimized_noise_analysis['stable_noise_floor_db']:.1f}dB")
                print(f"    噪声变化范围: {optimized_noise_analysis['noise_variation_db']:.1f}dB")
                print(f"    稳定检测谐波: {len(harmonic_analysis)}个")
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': display_freqs,
                'power': display_power,
                'power_db': display_power_db,
                'fundamental_analysis': fundamental_analysis,
                'optimized_noise_analysis': optimized_noise_analysis,
                'harmonic_analysis': harmonic_analysis,
                'freq_resolution': display_freqs[1] - display_freqs[0]
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def analyze_fundamental_optimized(freqs, power, expected_freq):
    """优化主频分析"""
    
    # 搜索范围
    search_bandwidth = 2.0  # ±2Hz
    search_mask = (freqs >= expected_freq - search_bandwidth) & (freqs <= expected_freq + search_bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def estimate_optimized_noise(freqs, power, fundamental_analysis):
    """优化噪声估计 - 减少波动"""
    
    if not fundamental_analysis:
        return {'stable_noise_floor_db': -80}
    
    fundamental_freq = fundamental_analysis['freq']
    
    # 更大的滑动窗口参数
    window_size = 500  # 500个频率bin (更大窗口)
    step_size = 100    # 100个bin步长 (更大步长)
    
    # 创建更严格的排除掩码
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±10Hz (更大范围)
    main_exclude = (freqs >= fundamental_freq - 10) & (freqs <= fundamental_freq + 10)
    exclude_mask |= main_exclude
    
    # 排除前15个谐波位置±10Hz (更大范围)
    for order in range(2, 16):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 10) & (freqs <= harmonic_freq + 10)
            exclude_mask |= harm_exclude
    
    # 预处理：中值滤波去除尖峰
    power_db = 10 * np.log10(power + 1e-12)
    power_db_filtered = medfilt(power_db, kernel_size=5)
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 50:  # 至少50个样本
            window_noise_powers = power_db_filtered[noise_mask]
            
            # 使用更保守的噪声估计
            local_noise = np.percentile(window_noise_powers, 15)  # 15th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return {'stable_noise_floor_db': -80}
    
    # 强平滑处理
    if len(local_noise_levels) > 7:
        try:
            # 第一次平滑
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 15), 3)
            # 第二次平滑
            if len(smoothed_noise) > 5:
                smoothed_noise = savgol_filter(smoothed_noise, 
                                             min(len(smoothed_noise), 9), 2)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 进一步减少变化：使用移动平均
    if len(smoothed_noise) > 3:
        window_avg = 3
        averaged_noise = []
        for i in range(len(smoothed_noise)):
            start = max(0, i - window_avg // 2)
            end = min(len(smoothed_noise), i + window_avg // 2 + 1)
            averaged_noise.append(np.mean(smoothed_noise[start:end]))
        smoothed_noise = averaged_noise
    
    # 插值到完整频率网格
    local_noise_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    # 稳定的全局噪声统计
    stable_noise_floor_db = np.percentile(smoothed_noise, 10)  # 10th percentile
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 限制噪声变化范围
    max_allowed_variation = 20.0  # 最大允许20dB变化
    if noise_variation_db > max_allowed_variation:
        # 压缩噪声变化
        noise_center = np.median(smoothed_noise)
        compressed_noise = noise_center + (np.array(smoothed_noise) - noise_center) * (max_allowed_variation / noise_variation_db)
        local_noise_interp = np.interp(freqs, window_centers, compressed_noise)
        noise_variation_db = max_allowed_variation
        stable_noise_floor_db = np.percentile(compressed_noise, 10)
    
    return {
        'stable_noise_floor_db': stable_noise_floor_db,
        'local_noise_db': local_noise_interp,
        'noise_variation_db': noise_variation_db,
        'window_centers': window_centers,
        'local_noise_levels': smoothed_noise,
        'noise_samples_used': np.sum(~exclude_mask)
    }

def detect_harmonics_stable(freqs, power, fundamental_analysis, optimized_noise_analysis):
    """稳定谐波检测"""
    
    if not fundamental_analysis:
        return []
    
    fundamental_freq = fundamental_analysis['freq']
    fundamental_power_db = fundamental_analysis['power_db']
    stable_noise_floor_db = optimized_noise_analysis['stable_noise_floor_db']
    local_noise_db = optimized_noise_analysis['local_noise_db']
    noise_variation_db = optimized_noise_analysis['noise_variation_db']
    
    detected_harmonics = []
    
    # 稳定的SNR阈值策略
    if noise_variation_db <= 10:
        base_snr_threshold = 12.0  # 噪声稳定时要求高
    elif noise_variation_db <= 15:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 8.0   # 噪声变化大时降低要求
    
    print(f"      稳定谐波检测 (稳定噪声: {stable_noise_floor_db:.1f}dB, 变化: {noise_variation_db:.1f}dB, SNR阈值: {base_snr_threshold:.1f}dB):")
    
    for order in range(2, 21):  # 检测2-20次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= 20000:
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 3.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 5.0
        else:
            search_bandwidth = 8.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 获取该频率的局部噪声
        local_noise_at_freq = local_noise_db[actual_idx]
        
        # 计算各种指标
        stable_snr_db = harmonic_power_db - stable_noise_floor_db
        local_snr_db = harmonic_power_db - local_noise_at_freq
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整 (更保守)
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 3000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 8000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 稳定的谐波判断条件
        conditions = {
            'local_snr_sufficient': local_snr_db >= adjusted_snr_threshold,
            'stable_snr_sufficient': stable_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -45.0,  # 更严格
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.6,  # 更严格
            'power_reasonable': harmonic_power_db >= stable_noise_floor_db + adjusted_snr_threshold
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'stable_snr_db': stable_snr_db,
                'local_snr_db': local_snr_db,
                'local_noise_db': local_noise_at_freq,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
            
            print(f"        ✅ {order:2d}次谐波: {actual_freq:7.1f}Hz, {harmonic_power_db:6.1f}dB, "
                  f"稳定SNR={stable_snr_db:5.1f}dB, 局部SNR={local_snr_db:5.1f}dB")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"        ❌ {order:2d}次谐波: {actual_freq:7.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            稳定SNR={stable_snr_db:5.1f}dB, 局部SNR={local_snr_db:5.1f}dB, "
                  f"相对功率={relative_power_db:5.1f}dB, 误差={freq_error:+5.1f}Hz")
    
    return detected_harmonics

def create_optimized_visualization(hole_data):
    """创建优化噪声可视化"""
    print(f"\n🎨 生成优化噪声检测可视化...")
    
    # 创建图表 (4行1列)
    fig, axes = plt.subplots(4, 1, figsize=(20, 24))
    fig.suptitle(f'Optimized Noise Estimation Harmonic Detection\nFile: {hole_data["filename"]}', 
                 fontsize=16, fontweight='bold')
    
    segments = hole_data['segments']
    
    # 谐波颜色
    harmonic_colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, segment in enumerate(segments):
        ax = axes[i]
        
        fundamental_analysis = segment['fundamental_analysis']
        harmonic_analysis = segment['harmonic_analysis']
        optimized_noise_analysis = segment['optimized_noise_analysis']
        
        # 绘制频谱
        ax.plot(segment['freqs'], segment['power_db'], 'k-', linewidth=0.8, alpha=0.7, 
               label='Spectrum')
        
        # 绘制优化噪声曲线
        ax.plot(segment['freqs'], optimized_noise_analysis['local_noise_db'], 
               color='blue', linewidth=2, alpha=0.8, label='Optimized Noise Floor')
        
        # 填充噪声变化区域 (更小的范围)
        noise_variation = optimized_noise_analysis['noise_variation_db']
        noise_min = optimized_noise_analysis['local_noise_db'] - noise_variation/4
        noise_max = optimized_noise_analysis['local_noise_db'] + noise_variation/4
        ax.fill_between(segment['freqs'], noise_min, noise_max, 
                       color='blue', alpha=0.2, label=f'Noise Variation (±{noise_variation/4:.1f}dB)')
        
        # 标记稳定噪声底噪
        stable_noise_db = optimized_noise_analysis['stable_noise_floor_db']
        ax.axhline(y=stable_noise_db, color='gray', linestyle='--', alpha=0.6, 
                  label=f'Stable Noise Floor {stable_noise_db:.1f}dB')
        
        # 标记主频
        if fundamental_analysis:
            main_freq = fundamental_analysis['freq']
            main_power = fundamental_analysis['power_db']
            
            ax.plot(main_freq, main_power, 'ro', markersize=12, 
                   label=f'Fundamental {main_freq:.2f}Hz ({main_power:.1f}dB)')
            
            # 主频标注
            ax.annotate(f'Fundamental\n{main_freq:.2f}Hz\n{main_power:.1f}dB', 
                       xy=(main_freq, main_power), 
                       xytext=(main_freq + 1000, main_power + 10),
                       ha='left', va='bottom', fontweight='bold', color='red', fontsize=10,
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记稳定检测的谐波
        for j, harmonic in enumerate(harmonic_analysis):
            color = harmonic_colors[j % len(harmonic_colors)]
            order = harmonic['order']
            freq = harmonic['freq']
            power_db = harmonic['power_db']
            stable_snr_db = harmonic['stable_snr_db']
            local_noise_db = harmonic['local_noise_db']
            
            # 谐波标记
            ax.plot(freq, power_db, 's', color=color, markersize=10, alpha=0.9,
                   label=f'{order}th Harmonic {freq:.0f}Hz' if j < 8 else "")
            
            # 绘制稳定SNR连线
            ax.plot([freq, freq], [local_noise_db, power_db], 
                   color=color, linestyle=':', linewidth=2, alpha=0.7)
            
            # 标注谐波
            ax.annotate(f'{order}th\n{freq:.0f}Hz\nSNR={stable_snr_db:.1f}dB', 
                       xy=(freq, power_db), 
                       xytext=(freq, power_db + 10),
                       ha='center', va='bottom', fontsize=8, color=color, fontweight='bold',
                       arrowprops=dict(arrowstyle='->', color=color, lw=1.5),
                       bbox=dict(boxstyle='round,pad=0.2', facecolor=color, alpha=0.3))
        
        # 设置图表属性
        expected_freq = segment['expected_freq']
        noise_variation = optimized_noise_analysis['noise_variation_db']
        ax.set_title(f'Segment {segment["seg_idx"]} - Expected: {expected_freq:.0f}Hz, '
                    f'Stable Harmonics: {len(harmonic_analysis)}, Noise Variation: {noise_variation:.1f}dB', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power (dB)')
        
        # 设置x轴范围和刻度
        ax.set_xlim(0, 20000)
        ax.set_xticks([0, 1000, 2000, 5000, 10000, 15000, 20000])
        ax.set_xticklabels(['0', '1k', '2k', '5k', '10k', '15k', '20k'])
        
        # 设置y轴范围
        y_min = np.percentile(segment['power_db'], 1) - 5
        y_max = np.percentile(segment['power_db'], 99) + 20
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8, loc='upper right', ncol=2)
        ax.grid(True, alpha=0.3)
        
        # 添加详细信息文本框
        info_text = f"Optimized Noise Estimation:\n"
        info_text += f"• Large window: 500 bins, step 100\n"
        info_text += f"• Median filter preprocessing\n"
        info_text += f"• Double Savitzky-Golay smoothing\n"
        info_text += f"• Noise variation limit: 20dB\n"
        info_text += f"• Stable threshold strategy\n\n"
        
        if fundamental_analysis:
            info_text += f"Results:\n"
            info_text += f"Fundamental: {fundamental_analysis['freq']:.3f}Hz\n"
            info_text += f"Stable Harmonics: {len(harmonic_analysis)}\n"
            info_text += f"Noise Variation: {noise_variation:.1f}dB\n"
            info_text += f"Noise Samples: {optimized_noise_analysis['noise_samples_used']}"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.4', facecolor='lightgreen', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'optimized_noise_estimation.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 优化噪声估算可视化已保存: {filename}")
    plt.close()
    
    # 打印详细统计
    print(f"\n📊 优化噪声估算统计:")
    print(f"{'='*70}")
    
    total_harmonics = 0
    
    for i, segment in enumerate(segments):
        harmonic_count = len(segment['harmonic_analysis'])
        total_harmonics += harmonic_count
        noise_variation = segment['optimized_noise_analysis']['noise_variation_db']
        stable_noise = segment['optimized_noise_analysis']['stable_noise_floor_db']
        
        print(f"\n频段 {i} ({segment['expected_freq']:.0f}Hz):")
        print(f"  检测到的谐波: {harmonic_count}个")
        print(f"  稳定噪声底噪: {stable_noise:.1f}dB")
        print(f"  噪声变化: {noise_variation:.1f}dB")
        
        if harmonic_count > 0:
            print(f"  谐波详情:")
            for harmonic in segment['harmonic_analysis']:
                print(f"    {harmonic['order']:2d}次: {harmonic['freq']:7.1f}Hz, "
                      f"{harmonic['power_db']:6.1f}dB, 稳定SNR={harmonic['stable_snr_db']:5.1f}dB, "
                      f"局部SNR={harmonic['local_snr_db']:5.1f}dB")
        else:
            print(f"  未检测到谐波")
    
    print(f"\n总体统计:")
    print(f"  总检测谐波: {total_harmonics}个")
    print(f"  平均每段: {total_harmonics/len(segments):.1f}个")

if __name__ == "__main__":
    optimized_noise_estimation()
