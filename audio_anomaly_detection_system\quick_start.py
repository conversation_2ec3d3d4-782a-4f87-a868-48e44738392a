#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频异响检测系统 - 快速开始脚本
Quick Start Script for Audio Anomaly Detection System
"""

import os
import sys

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'numpy', 'pandas', 'librosa', 'sklearn', 
        'matplotlib', 'seaborn', 'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n🎉 所有依赖包已安装!")
        return True

def quick_demo():
    """快速演示"""
    print("\n" + "="*60)
    print("🎯 音频异响检测系统 - 快速演示")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    try:
        from easy_detector import AudioAnomalyDetector
        
        print("\n[INFO] 创建检测器...")
        detector = AudioAnomalyDetector()
        
        # 检查是否有预训练模型
        model_file = "audio_anomaly_detector.pkl"
        
        if os.path.exists(model_file):
            print(f"[INFO] 发现预训练模型: {model_file}")
            detector.load_model(model_file)
            print("✅ 模型加载成功!")
            
            # 如果有测试音频，进行检测
            test_audio = find_test_audio()
            if test_audio:
                print(f"\n[INFO] 检测测试音频: {test_audio}")
                result = detector.detect_anomaly(test_audio)
                
                if result:
                    print("\n📊 检测结果:")
                    print(f"  状态: {result['overall_status']}")
                    print(f"  置信度: {result['confidence']}")
                    print(f"  正常概率: {result['normal_probability']:.3f}")
                    print(f"  正常频段: {result['normal_segments']}/{result['total_segments']}")
                else:
                    print("❌ 检测失败")
            else:
                print("\n[INFO] 未找到测试音频文件")
                print("请将WAV格式的音频文件放在当前目录或dataset文件夹中")
        
        else:
            print(f"[INFO] 未发现预训练模型")
            
            # 检查是否有数据集可以训练
            if os.path.exists("../dataset") and os.path.exists("../dataset/pos") and os.path.exists("../dataset/neg"):
                print("[INFO] 发现数据集，开始训练...")
                detector.train_from_dataset("../dataset")
                print("✅ 训练完成!")
            else:
                print("\n[INFO] 未发现数据集")
                print("请按以下结构准备数据集:")
                print("dataset/")
                print("  ├── pos/  (正常音频)")
                print("  └── neg/  (异常音频)")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有文件都在同一目录中")
    except Exception as e:
        print(f"❌ 运行错误: {e}")

def find_test_audio():
    """查找测试音频文件"""
    # 查找当前目录的WAV文件
    for file in os.listdir("."):
        if file.lower().endswith('.wav'):
            return file
    
    # 查找上级目录的dataset
    dataset_dirs = ["../dataset/pos", "../dataset/neg"]
    for dir_path in dataset_dirs:
        if os.path.exists(dir_path):
            for file in os.listdir(dir_path):
                if file.lower().endswith('.wav'):
                    return os.path.join(dir_path, file)
    
    return None

def show_usage():
    """显示使用说明"""
    print("\n" + "="*60)
    print("📋 使用说明")
    print("="*60)
    
    usage_text = """
1. 检测单个文件:
   python -c "
   from easy_detector import AudioAnomalyDetector
   detector = AudioAnomalyDetector()
   detector.load_model('audio_anomaly_detector.pkl')
   result = detector.detect_anomaly('your_audio.wav')
   print(result)
   "

2. 批量检测:
   python -c "
   from easy_detector import AudioAnomalyDetector
   detector = AudioAnomalyDetector()
   detector.load_model('audio_anomaly_detector.pkl')
   results = detector.batch_detect('audio_folder', 'results.csv')
   "

3. 训练自定义模型:
   python -c "
   from easy_detector import AudioAnomalyDetector
   detector = AudioAnomalyDetector()
   detector.train_from_dataset('dataset')
   "

4. 运行完整示例:
   python usage_guide.py

5. 查看详细文档:
   查看 README.md 和 README_使用指南.md
    """
    
    print(usage_text)

def main():
    """主函数"""
    print("🎵 音频异响检测系统")
    print("Audio Anomaly Detection System")
    print("Version 1.0")
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            show_usage()
            return
        elif sys.argv[1] == "--check":
            check_dependencies()
            return
    
    # 运行快速演示
    quick_demo()
    
    # 显示使用说明
    show_usage()

if __name__ == "__main__":
    main()
