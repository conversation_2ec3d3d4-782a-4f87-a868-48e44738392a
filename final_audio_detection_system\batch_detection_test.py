#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量检测所有正负样本
测试竖线检测器在所有样本上的性能
"""

import os
import glob
import pandas as pd
import numpy as np
from segment_vertical_line_detector import SegmentVerticalLineDetector
import matplotlib.pyplot as plt
import seaborn as sns

class BatchDetectionTester:
    def __init__(self):
        self.detector = SegmentVerticalLineDetector()
        self.results = []
        
    def detect_all_samples(self, audio_dir="..", pattern="**/*.wav"):
        """检测所有音频样本"""
        print("🔍 批量检测所有正负样本")
        print("="*60)
        
        # 查找所有音频文件
        audio_files = glob.glob(os.path.join(audio_dir, pattern), recursive=True)
        audio_files.sort()
        
        print(f"找到 {len(audio_files)} 个音频文件")
        
        for i, audio_path in enumerate(audio_files):
            print(f"\n[{i+1}/{len(audio_files)}] 检测: {os.path.basename(audio_path)}")
            
            try:
                # 执行检测
                result = self.detector.detect_vertical_lines_in_segments(audio_path, max_display_segments=0)
                
                # 提取关键信息
                sample_result = self._extract_sample_result(audio_path, result)
                self.results.append(sample_result)
                
                print(f"  ✅ 检测完成: {sample_result['anomaly_count']}/{sample_result['total_segments']} 异常 "
                      f"({sample_result['anomaly_rate']:.1f}%)")
                
            except Exception as e:
                print(f"  ❌ 检测失败: {str(e)}")
                # 记录失败的样本
                self.results.append({
                    'filename': os.path.basename(audio_path),
                    'sample_type': self._classify_sample_type(audio_path),
                    'detection_status': 'failed',
                    'error': str(e),
                    'total_segments': 0,
                    'anomaly_count': 0,
                    'anomaly_rate': 0,
                    'line_count': 0
                })
        
        return self.results
    
    def _extract_sample_result(self, audio_path, detection_result):
        """提取样本检测结果"""
        filename = os.path.basename(audio_path)
        sample_type = self._classify_sample_type(audio_path)
        
        if detection_result and 'segment_results' in detection_result:
            segment_results = detection_result['segment_results']
            successful_segments = [seg for seg in segment_results if seg['analysis_success']]
            anomalous_segments = [seg for seg in successful_segments if seg['vertical_lines']]
            
            total_segments = len(successful_segments)
            anomaly_count = len(anomalous_segments)
            anomaly_rate = (anomaly_count / total_segments * 100) if total_segments > 0 else 0
            
            # 统计竖线总数
            line_count = sum(len(seg['vertical_lines']) for seg in anomalous_segments)
            
            return {
                'filename': filename,
                'sample_type': sample_type,
                'detection_status': 'success',
                'total_segments': total_segments,
                'anomaly_count': anomaly_count,
                'anomaly_rate': anomaly_rate,
                'line_count': line_count,
                'audio_duration': detection_result.get('audio_duration', 0),
                'frequency_range': detection_result.get('frequency_range', 'unknown')
            }
        else:
            return {
                'filename': filename,
                'sample_type': sample_type,
                'detection_status': 'no_result',
                'total_segments': 0,
                'anomaly_count': 0,
                'anomaly_rate': 0,
                'line_count': 0
            }
    
    def _classify_sample_type(self, audio_path):
        """根据文件名分类样本类型"""
        filename = os.path.basename(audio_path).lower()
        path_lower = audio_path.lower()
        
        # 正样本（包含竖线异常）
        if any(keyword in filename for keyword in ['竖线', '戳洞', 'vertical', 'spike', 'click']):
            return 'positive'
        if any(keyword in path_lower for keyword in ['pos', 'no', '156', '153']):
            return 'positive'
        
        # 负样本（正常扫频）
        if any(keyword in filename for keyword in ['扫频', 'sweep', '正常', 'normal', 'clean']):
            return 'negative'
        if any(keyword in path_lower for keyword in ['neg', 'ok', 'wavok']):
            return 'negative'
        
        # 其他类型
        if any(keyword in filename for keyword in ['噪声', 'noise']):
            return 'noise'
        
        return 'unknown'
    
    def analyze_results(self):
        """分析检测结果"""
        if not self.results:
            print("没有检测结果可分析")
            return
        
        df = pd.DataFrame(self.results)
        
        print(f"\n📊 批量检测结果分析")
        print("="*60)
        
        # 总体统计
        total_samples = len(df)
        successful_detections = len(df[df['detection_status'] == 'success'])
        
        print(f"总样本数: {total_samples}")
        print(f"成功检测: {successful_detections}")
        print(f"检测成功率: {successful_detections/total_samples*100:.1f}%")
        
        # 按样本类型分析
        print(f"\n📈 按样本类型分析:")
        for sample_type in df['sample_type'].unique():
            type_df = df[df['sample_type'] == sample_type]
            successful_type = type_df[type_df['detection_status'] == 'success']
            
            if len(successful_type) > 0:
                avg_anomaly_rate = successful_type['anomaly_rate'].mean()
                avg_line_count = successful_type['line_count'].mean()
                
                print(f"  {sample_type.upper()}样本 ({len(type_df)}个):")
                print(f"    平均异常率: {avg_anomaly_rate:.1f}%")
                print(f"    平均竖线数: {avg_line_count:.1f}")
                print(f"    异常率范围: {successful_type['anomaly_rate'].min():.1f}% - {successful_type['anomaly_rate'].max():.1f}%")
        
        return df
    
    def save_results(self, output_file="batch_detection_results.csv"):
        """保存检测结果到CSV文件"""
        if self.results:
            df = pd.DataFrame(self.results)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 检测结果已保存到: {output_file}")
            return df
        return None

def main():
    """主函数"""
    tester = BatchDetectionTester()
    
    # 检测所有样本
    results = tester.detect_all_samples()
    
    # 分析结果
    df = tester.analyze_results()
    
    # 保存结果
    tester.save_results()
    
    print(f"\n🎯 批量检测完成！")

if __name__ == "__main__":
    main()
