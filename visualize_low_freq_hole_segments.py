#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独可视化低音戳洞样本前5段主频谐波定位
详细展示100Hz-200Hz低频段的主频和谐波特征
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_low_freq_hole_segments():
    """可视化低音戳洞样本前5段"""
    print("🎯 低音戳洞样本前5段主频谐波定位可视化")
    print("="*70)
    print("分析目标:")
    print("1. 低音戳洞样本的前5个频段 (100-200Hz)")
    print("2. 详细的主频和谐波定位")
    print("3. 高分辨率频谱分析")
    print("4. 与正常样本对比")
    print("="*70)
    
    # 低音戳洞样本
    target_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    # 正常样本作为对比
    normal_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav'
    
    if not os.path.exists(target_file):
        print(f"❌ 低音戳洞文件不存在: {target_file}")
        return
    
    if not os.path.exists(normal_file):
        print(f"❌ 正常样本文件不存在: {normal_file}")
        return
    
    print(f"\n🎵 分析低音戳洞样本: {os.path.basename(target_file)}")
    
    # 分析低音戳洞样本前5段
    hole_analysis = analyze_first_5_segments(target_file, "低音戳洞样本")
    
    print(f"\n🎵 分析正常样本: {os.path.basename(normal_file)}")
    
    # 分析正常样本前5段作为对比
    normal_analysis = analyze_first_5_segments(normal_file, "正常样本")
    
    if hole_analysis and normal_analysis:
        # 创建对比可视化
        create_low_freq_comparison_visualization(hole_analysis, normal_analysis)
    else:
        print("❌ 分析失败，无法生成可视化")

def analyze_first_5_segments(audio_path, sample_name):
    """分析前5个频段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"   📊 总频段数: {len(step_boundaries)}")
        
        segment_analyses = []
        
        # 只分析前5个频段
        for seg_idx in range(min(5, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            print(f"   分析频段 {seg_idx} ({expected_freq:.1f}Hz)")
            
            # 超高分辨率分析
            segment_analysis = ultra_high_resolution_analysis(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
        
        return {
            'sample_name': sample_name,
            'audio_path': audio_path,
            'segment_analyses': segment_analyses
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def ultra_high_resolution_analysis(audio, sr, expected_freq, seg_idx):
    """超高分辨率分析"""
    
    try:
        # 超高分辨率FFT (256k点)
        fft_size = max(262144, len(audio))  # 256k点FFT
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只分析正频率
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        
        print(f"     超高分辨率: {freq_resolution:.6f}Hz")
        
        # 1. 精确主频分析
        fundamental_analysis = analyze_fundamental_ultra_precise(positive_freqs, positive_power, expected_freq)
        
        # 2. 精确噪声估计
        noise_analysis = estimate_noise_ultra_precise(positive_freqs, positive_power, expected_freq)
        
        # 3. 完整谐波检测
        harmonic_analysis = detect_harmonics_ultra_precise(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_analysis, sr
        )
        
        # 4. 频谱峰值分析
        peak_analysis = analyze_spectrum_peaks(positive_freqs, positive_power, expected_freq)
        
        print(f"     主频: {fundamental_analysis['freq']:.3f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"     噪声底噪: {noise_analysis['noise_floor_db']:.1f}dB")
        print(f"     检测谐波: {len(harmonic_analysis)}个")
        print(f"     频谱峰值: {len(peak_analysis)}个")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'peak_analysis': peak_analysis,
            'freq_resolution': freq_resolution
        }
        
    except Exception as e:
        print(f"     ❌ 频段{seg_idx}分析失败: {e}")
        return None

def analyze_fundamental_ultra_precise(freqs, power, expected_freq):
    """超精确主频分析"""
    
    # 非常精确的搜索带宽
    bandwidth = 1.0  # 1Hz带宽
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    search_freqs = freqs[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    # 分析峰值特征
    # 计算峰值宽度 (3dB带宽)
    half_power = fundamental_power / 2
    left_idx = actual_idx
    right_idx = actual_idx
    
    while left_idx > 0 and power[left_idx] > half_power:
        left_idx -= 1
    while right_idx < len(power) - 1 and power[right_idx] > half_power:
        right_idx += 1
    
    peak_width_hz = freqs[right_idx] - freqs[left_idx]
    
    # 计算峰值锐度
    peak_sharpness = fundamental_power / np.mean(search_powers)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'bandwidth': bandwidth,
        'freq_error': fundamental_freq - expected_freq,
        'peak_width_hz': peak_width_hz,
        'peak_sharpness': peak_sharpness,
        'index': actual_idx
    }

def estimate_noise_ultra_precise(freqs, power, fundamental_freq):
    """超精确噪声估计"""
    
    # 排除主频±2Hz
    excluded_ranges = [(fundamental_freq - 2, fundamental_freq + 2)]
    
    # 排除可能的谐波位置±2Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 2, harmonic_freq + 2))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        noise_floor = np.percentile(noise_powers, 10)  # 10th percentile
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        noise_std = np.std(10 * np.log10(noise_powers + 1e-12))
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_std_db': noise_std,
            'noise_sample_count': np.sum(noise_mask)
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_std_db': 0,
            'noise_sample_count': 0
        }

def detect_harmonics_ultra_precise(freqs, power, fundamental_freq, 
                                  fundamental_analysis, noise_analysis, sr):
    """超精确谐波检测"""
    
    if not fundamental_analysis or not noise_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 非常低的SNR阈值用于低频段
    base_snr_threshold = 3.0
    
    nyquist_freq = sr / 2
    
    for order in range(2, 51):  # 检测到50次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        # 精确搜索带宽
        search_bandwidth = 2.0  # 2Hz带宽
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        snr_db = harmonic_power_db - noise_floor_db
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 2.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 4.0
        else:
            freq_adjustment = 6.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 相对功率和频率误差
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 检测条件 (非常宽松)
        conditions = {
            'snr_sufficient': snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -70.0,  # 非常宽松
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.95
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'index': actual_idx
            })
    
    return detected_harmonics

def analyze_spectrum_peaks(freqs, power, expected_freq):
    """分析频谱峰值"""
    
    # 在期望频率附近寻找所有峰值
    analysis_range = (max(0, expected_freq - 50), expected_freq + 200)
    range_mask = (freqs >= analysis_range[0]) & (freqs <= analysis_range[1])
    
    if not np.any(range_mask):
        return []
    
    range_freqs = freqs[range_mask]
    range_powers = power[range_mask]
    range_power_db = 10 * np.log10(range_powers + 1e-12)
    
    # 使用scipy找峰值
    peaks, properties = find_peaks(
        range_power_db, 
        height=-60,  # 最小高度-60dB
        distance=int(1.0 / (range_freqs[1] - range_freqs[0])),  # 最小间距1Hz
        prominence=3.0  # 最小突出度3dB
    )
    
    peak_analysis = []
    for i, peak_idx in enumerate(peaks):
        peak_freq = range_freqs[peak_idx]
        peak_power = range_powers[peak_idx]
        peak_power_db = range_power_db[peak_idx]
        peak_prominence = properties['prominences'][i]
        
        peak_analysis.append({
            'freq': peak_freq,
            'power': peak_power,
            'power_db': peak_power_db,
            'prominence_db': peak_prominence,
            'freq_offset': peak_freq - expected_freq
        })
    
    return peak_analysis

def create_low_freq_comparison_visualization(hole_analysis, normal_analysis):
    """创建低频段对比可视化"""

    print(f"\n🎨 生成低音戳洞前5段对比可视化...")

    # 创建大图 (5行1列，每行显示一个频段的对比)
    fig, axes = plt.subplots(5, 1, figsize=(24, 30))

    hole_segments = hole_analysis['segment_analyses']
    normal_segments = normal_analysis['segment_analyses']

    # 确保两个样本的频段数量一致
    min_segments = min(len(hole_segments), len(normal_segments))

    for i in range(min_segments):
        ax = axes[i]

        hole_segment = hole_segments[i]
        normal_segment = normal_segments[i]

        # 在同一个图上绘制两个样本的对比
        plot_segment_comparison(ax, hole_segment, normal_segment,
                               hole_analysis['sample_name'], normal_analysis['sample_name'])

    plt.tight_layout()

    # 保存图片
    filename = 'low_freq_hole_first5_segments_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 低频段对比可视化已保存: {filename}")
    plt.close()

def plot_segment_comparison(ax, hole_segment, normal_segment, hole_name, normal_name):
    """在同一个图上绘制两个样本的频段对比"""

    # 提取数据
    hole_freqs = hole_segment['freqs']
    hole_power_db = hole_segment['power_db']
    hole_fundamental = hole_segment['fundamental_analysis']
    hole_harmonics = hole_segment['harmonic_analysis']
    hole_noise = hole_segment['noise_analysis']

    normal_freqs = normal_segment['freqs']
    normal_power_db = normal_segment['power_db']
    normal_fundamental = normal_segment['fundamental_analysis']
    normal_harmonics = normal_segment['harmonic_analysis']
    normal_noise = normal_segment['noise_analysis']

    seg_idx = hole_segment['segment_idx']
    expected_freq = hole_segment['expected_freq']

    # 扩展显示范围到20kHz
    freq_range = (0, 20000)

    # 处理低音戳洞样本数据
    hole_freq_mask = (hole_freqs >= freq_range[0]) & (hole_freqs <= freq_range[1])
    hole_display_freqs = hole_freqs[hole_freq_mask]
    hole_display_power_db = hole_power_db[hole_freq_mask]

    # 处理正常样本数据
    normal_freq_mask = (normal_freqs >= freq_range[0]) & (normal_freqs <= freq_range[1])
    normal_display_freqs = normal_freqs[normal_freq_mask]
    normal_display_power_db = normal_power_db[normal_freq_mask]
    
    # 绘制两个样本的频谱
    ax.plot(hole_display_freqs, hole_display_power_db, 'r-', linewidth=1.0, alpha=0.7,
           label=f'{hole_name} 频谱')
    ax.plot(normal_display_freqs, normal_display_power_db, 'b-', linewidth=1.0, alpha=0.7,
           label=f'{normal_name} 频谱')

    # 标记噪声底噪线
    hole_noise_db = hole_noise['noise_floor_db']
    normal_noise_db = normal_noise['noise_floor_db']
    ax.axhline(y=hole_noise_db, color='red', linestyle='--', alpha=0.6,
              label=f'{hole_name} 噪声底噪 {hole_noise_db:.1f}dB')
    ax.axhline(y=normal_noise_db, color='blue', linestyle='--', alpha=0.6,
              label=f'{normal_name} 噪声底噪 {normal_noise_db:.1f}dB')

    # 标记主频
    if hole_fundamental:
        hole_fund_freq = hole_fundamental['freq']
        hole_fund_power_db = hole_fundamental['power_db']

        if freq_range[0] <= hole_fund_freq <= freq_range[1]:
            ax.plot(hole_fund_freq, hole_fund_power_db, 'ro', markersize=10,
                   label=f'{hole_name} 主频 {hole_fund_freq:.1f}Hz')

            # 主频标注
            ax.annotate(f'{hole_name}\n主频 {hole_fund_freq:.1f}Hz\n{hole_fund_power_db:.1f}dB',
                       xy=(hole_fund_freq, hole_fund_power_db),
                       xytext=(hole_fund_freq + 1000, hole_fund_power_db + 10),
                       ha='left', va='bottom', fontweight='bold', color='red',
                       arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.8))

    if normal_fundamental:
        normal_fund_freq = normal_fundamental['freq']
        normal_fund_power_db = normal_fundamental['power_db']

        if freq_range[0] <= normal_fund_freq <= freq_range[1]:
            ax.plot(normal_fund_freq, normal_fund_power_db, 'bo', markersize=10,
                   label=f'{normal_name} 主频 {normal_fund_freq:.1f}Hz')

            # 主频标注
            ax.annotate(f'{normal_name}\n主频 {normal_fund_freq:.1f}Hz\n{normal_fund_power_db:.1f}dB',
                       xy=(normal_fund_freq, normal_fund_power_db),
                       xytext=(normal_fund_freq + 1000, normal_fund_power_db - 10),
                       ha='left', va='top', fontweight='bold', color='blue',
                       arrowprops=dict(arrowstyle='->', color='blue', lw=1.5),
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
    
    # 标记低音戳洞样本的谐波 (红色系)
    hole_harmonic_colors = ['darkred', 'red', 'orange', 'coral', 'pink']

    for j, harmonic in enumerate(hole_harmonics[:10]):  # 只显示前10个谐波
        if j >= len(hole_harmonic_colors):
            color = 'darkred'
        else:
            color = hole_harmonic_colors[j]

        order = harmonic['order']
        freq = harmonic['freq']
        power_db = harmonic['power_db']

        if freq_range[0] <= freq <= freq_range[1]:
            ax.plot(freq, power_db, 's', color=color, markersize=6, alpha=0.8,
                   label=f'{hole_name} {order}次谐波' if j < 5 else "")

            # 标注前5个谐波
            if j < 5:
                ax.annotate(f'{order}次\n{freq:.0f}Hz',
                           xy=(freq, power_db), xytext=(freq, power_db + 5),
                           ha='center', va='bottom', fontsize=7, color=color,
                           arrowprops=dict(arrowstyle='->', color=color, lw=0.8))

    # 标记正常样本的谐波 (蓝色系)
    normal_harmonic_colors = ['darkblue', 'blue', 'cyan', 'lightblue', 'skyblue']

    for j, harmonic in enumerate(normal_harmonics[:10]):  # 只显示前10个谐波
        if j >= len(normal_harmonic_colors):
            color = 'darkblue'
        else:
            color = normal_harmonic_colors[j]

        order = harmonic['order']
        freq = harmonic['freq']
        power_db = harmonic['power_db']

        if freq_range[0] <= freq <= freq_range[1]:
            ax.plot(freq, power_db, '^', color=color, markersize=6, alpha=0.8,
                   label=f'{normal_name} {order}次谐波' if j < 5 else "")

            # 标注前5个谐波
            if j < 5:
                ax.annotate(f'{order}次\n{freq:.0f}Hz',
                           xy=(freq, power_db), xytext=(freq, power_db - 8),
                           ha='center', va='top', fontsize=7, color=color,
                           arrowprops=dict(arrowstyle='->', color=color, lw=0.8))
    
    # 设置图表属性
    ax.set_title(f'频段{seg_idx} ({expected_freq:.1f}Hz) 对比分析 - 完整频谱 (0-20kHz)\n'
                f'{hole_name}: {len(hole_harmonics)}个谐波 vs {normal_name}: {len(normal_harmonics)}个谐波',
                fontweight='bold', fontsize=14)
    ax.set_xlabel('频率 (Hz)')
    ax.set_ylabel('功率 (dB)')
    ax.set_xlim(freq_range)

    # 计算合适的y轴范围
    all_power_values = np.concatenate([hole_display_power_db, normal_display_power_db])
    y_min = np.min(all_power_values) - 10
    y_max = np.max(all_power_values) + 20
    ax.set_ylim(y_min, y_max)

    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax.grid(True, alpha=0.3)

    # 设置x轴为对数刻度以更好显示宽频范围
    ax.set_xscale('log')

    # 添加对比统计信息文本框
    info_text = f"对比统计:\n"
    info_text += f"{hole_name}:\n"
    if hole_fundamental:
        info_text += f"  主频: {hole_fundamental['freq']:.1f}Hz ({hole_fundamental['power_db']:.1f}dB)\n"
        info_text += f"  主频误差: {hole_fundamental['freq_error']:+.1f}Hz\n"
    info_text += f"  检测谐波: {len(hole_harmonics)}个\n"
    info_text += f"  噪声底噪: {hole_noise_db:.1f}dB\n\n"

    info_text += f"{normal_name}:\n"
    if normal_fundamental:
        info_text += f"  主频: {normal_fundamental['freq']:.1f}Hz ({normal_fundamental['power_db']:.1f}dB)\n"
        info_text += f"  主频误差: {normal_fundamental['freq_error']:+.1f}Hz\n"
    info_text += f"  检测谐波: {len(normal_harmonics)}个\n"
    info_text += f"  噪声底噪: {normal_noise_db:.1f}dB\n\n"

    # 计算差异
    harmonic_diff = len(hole_harmonics) - len(normal_harmonics)
    noise_diff = hole_noise_db - normal_noise_db
    info_text += f"差异分析:\n"
    info_text += f"  谐波数差异: {harmonic_diff:+d}个\n"
    info_text += f"  噪声差异: {noise_diff:+.1f}dB"

    ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
           verticalalignment='top', fontsize=9,
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))

if __name__ == "__main__":
    visualize_low_freq_hole_segments()
