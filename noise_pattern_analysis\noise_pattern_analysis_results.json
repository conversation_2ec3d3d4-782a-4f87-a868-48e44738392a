{"录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav": {"classification": {"classification": "abnormal", "specific_type": "medium_complexity_pattern", "abnormal_score": 11, "features": {"std_curvature": 0.4274381162967638, "std_slope": 0.6646790768065222, "num_peaks": 117.25, "num_valleys": 116.25}, "feature_checks": {"high_curvature_std": true, "high_slope_std": true, "many_peaks": true, "many_valleys": true}}, "num_patterns": 4}, "录音_步进扫频_100Hz至20000Hz_20250714_153632.wav": {"classification": {"classification": "abnormal", "specific_type": "medium_complexity_pattern", "abnormal_score": 11, "features": {"std_curvature": 0.4422075585463165, "std_slope": 0.7107667335995111, "num_peaks": 113.75, "num_valleys": 113.0}, "feature_checks": {"high_curvature_std": true, "high_slope_std": true, "many_peaks": true, "many_valleys": true}}, "num_patterns": 4}, "录音_步进扫频_100Hz至20000Hz_20250714_155101.wav": {"classification": {"classification": "abnormal", "specific_type": "high_complexity_pattern", "abnormal_score": 9, "features": {"std_curvature": 0.5157961085574561, "std_slope": 0.8649612720220047, "num_peaks": 103.75, "num_valleys": 103.75}, "feature_checks": {"high_curvature_std": true, "high_slope_std": true, "many_peaks": false, "many_valleys": false}}, "num_patterns": 4}}