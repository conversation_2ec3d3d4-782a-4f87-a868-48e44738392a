#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单展示每个样本的频段分割效果（不生成图表）
"""

import os
import sys
import glob
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

def show_frequency_split_simple():
    """简单展示每个样本的频段分割效果"""
    print("🔍 展示每个样本的频段分割效果")
    print("="*60)
    
    # 定义测试文件夹
    folders = [
        r"../test20250717/pos/sd卡",
        r"../test20250717/pos/完美", 
        r"../test20250717/pos/转接板",
        r"../test20250717/pos/铁网",
        r"../test20250717/neg",
        r"../待定"
    ]
    
    # 测试所有文件夹
    for folder in folders:
        print(f"\n📁 文件夹: {folder}")
        print("-" * 50)
        
        if not os.path.exists(folder):
            print(f"❌ 文件夹不存在: {folder}")
            continue
        
        # 获取所有wav文件
        wav_files = glob.glob(os.path.join(folder, "*.wav"))
        print(f"找到 {len(wav_files)} 个wav文件")
        
        if len(wav_files) == 0:
            print("📂 文件夹为空")
            continue
        
        # 测试每个文件（限制数量）
        test_count = min(3, len(wav_files))
        for i, audio_path in enumerate(wav_files[:test_count]):
            filename = os.path.basename(audio_path)
            
            print(f"\n🎵 [{i+1}/{test_count}] 文件: {filename}")
            print("=" * 40)
            
            try:
                # 调用优化的频率分割算法，不生成图表
                step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                    audio_path,
                    min_duration=153,
                    plot=False,  # 不生成图表
                    debug=False,  # 不显示详细调试信息
                    search_window_start=0.1,
                    search_window_end=1.5,
                    correlation_length=1.0
                )
                
                # 提取关键信息
                start_offset = alignment_info.get('start_offset', 0)
                correlation_score = alignment_info.get('correlation_score', 0)
                alignment_quality = alignment_info.get('alignment_quality', {})
                
                # 显示结果摘要
                print(f"📊 分割结果摘要:")
                print(f"  开始时间: {start_offset:.3f}s")
                print(f"  相关性: {correlation_score:.3f}")
                print(f"  频段数量: {len(freq_table)}")
                print(f"  步进区间数: {len(step_bounds)}")
                
                if alignment_quality:
                    overall_quality = alignment_quality.get('overall_quality', 'unknown')
                    composite_score = alignment_quality.get('composite_score', 0)
                    print(f"  整体质量: {overall_quality}")
                    print(f"  综合评分: {composite_score:.3f}")
                
                # 显示频段范围信息
                if len(freq_table) > 0:
                    min_freq = min(freq_table)
                    max_freq = max(freq_table)
                    freq_range = max_freq - min_freq
                    print(f"  频率范围: {min_freq:.1f}Hz - {max_freq:.1f}Hz (跨度: {freq_range:.1f}Hz)")
                
                # 显示时间范围信息
                if len(step_bounds) > 0:
                    total_duration = step_bounds[-1][1] - step_bounds[0][0]
                    avg_step_duration = total_duration / len(step_bounds)
                    print(f"  时间范围: {step_bounds[0][0]:.3f}s - {step_bounds[-1][1]:.3f}s")
                    print(f"  总时长: {total_duration:.3f}s")
                    print(f"  平均步长: {avg_step_duration:.3f}s")
                
                # 显示前10个频段信息
                print(f"\n🎼 前10个频段信息:")
                for j in range(min(10, len(freq_table))):
                    freq = freq_table[j]
                    if j < len(step_bounds):
                        start_time, end_time = step_bounds[j]
                        duration = end_time - start_time
                        print(f"    {j+1:2d}. {freq:6.1f}Hz  时间:{start_time:.3f}s-{end_time:.3f}s  时长:{duration:.3f}s")
                    else:
                        print(f"    {j+1:2d}. {freq:6.1f}Hz")
                
                if len(freq_table) > 10:
                    print(f"    ... 还有 {len(freq_table)-10} 个频段")
                
                # 显示最后几个频段
                if len(freq_table) > 10:
                    print(f"\n🎼 最后3个频段信息:")
                    for j in range(max(0, len(freq_table)-3), len(freq_table)):
                        freq = freq_table[j]
                        if j < len(step_bounds):
                            start_time, end_time = step_bounds[j]
                            duration = end_time - start_time
                            print(f"    {j+1:2d}. {freq:6.1f}Hz  时间:{start_time:.3f}s-{end_time:.3f}s  时长:{duration:.3f}s")
                        else:
                            print(f"    {j+1:2d}. {freq:6.1f}Hz")
                
                # 质量评估
                if correlation_score >= 0.8:
                    quality_icon = "🟢"
                    quality_text = "优秀"
                elif correlation_score >= 0.6:
                    quality_icon = "🟡"
                    quality_text = "良好"
                else:
                    quality_icon = "🔴"
                    quality_text = "需要改进"
                
                print(f"\n{quality_icon} 质量评估: {quality_text}")
                print(f"✅ 处理成功")
                
            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
            
            print("-" * 40)
    
    print(f"\n🎉 所有样本展示完成！")

if __name__ == "__main__":
    show_frequency_split_simple()
