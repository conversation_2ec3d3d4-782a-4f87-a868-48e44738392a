#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独显示低音戳洞文件前4个频段的主频谐波定位图
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def single_hole_visualization():
    """单独显示低音戳洞前4段"""
    print("🎯 低音戳洞文件前4个频段主频谐波定位图")
    print("="*50)
    
    # 低音戳洞文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析文件
    hole_data = analyze_hole_file(hole_file)
    
    if hole_data:
        create_hole_visualization(hole_data)

def analyze_hole_file(audio_path):
    """分析低音戳洞文件"""
    try:
        print(f"\n分析低音戳洞文件...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前4段
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 32768  # 32k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power_db = 20 * np.log10(magnitude + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            # 找主频
            search_range = (expected_freq - 2, expected_freq + 2)
            search_mask = (display_freqs >= search_range[0]) & (display_freqs <= search_range[1])
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power_db[search_mask]
                max_idx = search_indices[np.argmax(search_powers)]
                main_freq = display_freqs[max_idx]
                main_power = display_power_db[max_idx]
            else:
                main_freq = expected_freq
                main_power = -60
            
            # 估计噪声底噪
            # 排除主频和谐波区域
            noise_mask = np.ones(len(display_freqs), dtype=bool)
            
            # 排除主频±5Hz
            main_exclude = (display_freqs >= main_freq - 5) & (display_freqs <= main_freq + 5)
            noise_mask &= ~main_exclude
            
            # 排除前10个谐波位置±5Hz
            for order in range(2, 11):
                harmonic_freq = main_freq * order
                if harmonic_freq <= 20000:
                    harm_exclude = (display_freqs >= harmonic_freq - 5) & (display_freqs <= harmonic_freq + 5)
                    noise_mask &= ~harm_exclude
            
            if np.any(noise_mask):
                noise_powers = display_power_db[noise_mask]
                noise_floor = np.percentile(noise_powers, 15)  # 15th percentile
            else:
                noise_floor = -80
            
            # 谐波检测
            harmonics = []
            
            for order in range(2, 31):  # 2-30次谐波
                harmonic_freq = main_freq * order
                if harmonic_freq >= 20000:
                    break
                
                # 在谐波频率附近搜索
                harm_range = (harmonic_freq - 3, harmonic_freq + 3)
                harm_mask = (display_freqs >= harm_range[0]) & (display_freqs <= harm_range[1])
                
                if np.any(harm_mask):
                    harm_indices = np.where(harm_mask)[0]
                    harm_powers = display_power_db[harm_mask]
                    harm_max_idx = harm_indices[np.argmax(harm_powers)]
                    harm_freq_actual = display_freqs[harm_max_idx]
                    harm_power = display_power_db[harm_max_idx]
                    
                    # SNR检查
                    snr = harm_power - noise_floor
                    relative_power = harm_power - main_power
                    
                    # 宽松的检测条件
                    if snr > 8 and relative_power > -60:  # SNR > 8dB, 相对功率 > -60dB
                        harmonics.append({
                            'order': order,
                            'freq': harm_freq_actual,
                            'expected_freq': harmonic_freq,
                            'power': harm_power,
                            'snr': snr,
                            'relative_power': relative_power,
                            'freq_error': harm_freq_actual - harmonic_freq
                        })
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': display_freqs,
                'power_db': display_power_db,
                'main_freq': main_freq,
                'main_power': main_power,
                'harmonics': harmonics,
                'noise_floor': noise_floor,
                'freq_resolution': display_freqs[1] - display_freqs[0]
            })
            
            print(f"  段{seg_idx}: {expected_freq:.0f}Hz -> {main_freq:.2f}Hz ({main_power:.1f}dB), {len(harmonics)}个谐波, 噪声底噪{noise_floor:.1f}dB")
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def create_hole_visualization(hole_data):
    """创建低音戳洞可视化"""
    print(f"\n🎨 生成低音戳洞前4段可视化...")
    
    # 创建图表 (4行1列)
    fig, axes = plt.subplots(4, 1, figsize=(20, 24))
    fig.suptitle(f'Low Frequency Hole Sample - First 4 Segments\nFile: {hole_data["filename"]}', 
                 fontsize=16, fontweight='bold')
    
    segments = hole_data['segments']
    
    # 谐波颜色
    harmonic_colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, segment in enumerate(segments):
        ax = axes[i]
        
        # 绘制频谱
        ax.plot(segment['freqs'], segment['power_db'], 'k-', linewidth=0.8, alpha=0.7, 
               label='Spectrum')
        
        # 标记噪声底噪
        ax.axhline(y=segment['noise_floor'], color='gray', linestyle='--', alpha=0.6, 
                  label=f'Noise Floor {segment["noise_floor"]:.1f}dB')
        
        # 标记主频
        ax.plot(segment['main_freq'], segment['main_power'], 'ro', markersize=12, 
               label=f'Fundamental {segment["main_freq"]:.2f}Hz ({segment["main_power"]:.1f}dB)')
        
        # 主频标注
        ax.annotate(f'Fundamental\n{segment["main_freq"]:.2f}Hz\n{segment["main_power"]:.1f}dB', 
                   xy=(segment['main_freq'], segment['main_power']), 
                   xytext=(segment['main_freq'] + 1000, segment['main_power'] + 10),
                   ha='left', va='bottom', fontweight='bold', color='red', fontsize=10,
                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记谐波
        for j, harmonic in enumerate(segment['harmonics']):
            color = harmonic_colors[j % len(harmonic_colors)]
            order = harmonic['order']
            freq = harmonic['freq']
            power = harmonic['power']
            snr = harmonic['snr']
            
            # 谐波标记
            ax.plot(freq, power, 's', color=color, markersize=8, alpha=0.8,
                   label=f'{order}th Harmonic {freq:.0f}Hz' if j < 10 else "")
            
            # 标注前15个谐波
            if j < 15:
                ax.annotate(f'{order}th\n{freq:.0f}Hz\nSNR={snr:.1f}dB', 
                           xy=(freq, power), 
                           xytext=(freq, power + 8),
                           ha='center', va='bottom', fontsize=8, color=color,
                           arrowprops=dict(arrowstyle='->', color=color, lw=1))
        
        # 设置图表属性
        expected_freq = segment['expected_freq']
        ax.set_title(f'Segment {segment["seg_idx"]} - Expected: {expected_freq:.0f}Hz, '
                    f'Actual: {segment["main_freq"]:.2f}Hz, {len(segment["harmonics"])} Harmonics Detected', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power (dB)')
        
        # 设置x轴范围和刻度
        ax.set_xlim(0, 20000)
        ax.set_xticks([0, 1000, 2000, 5000, 10000, 15000, 20000])
        ax.set_xticklabels(['0', '1k', '2k', '5k', '10k', '15k', '20k'])
        
        # 设置y轴范围
        y_min = np.percentile(segment['power_db'], 1) - 5
        y_max = np.percentile(segment['power_db'], 99) + 15
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8, loc='upper right', ncol=2)
        ax.grid(True, alpha=0.3)
        
        # 添加详细信息文本框
        info_text = f"Detailed Analysis:\n"
        info_text += f"Expected Frequency: {expected_freq:.0f}Hz\n"
        info_text += f"Actual Frequency: {segment['main_freq']:.3f}Hz\n"
        info_text += f"Frequency Error: {segment['main_freq'] - expected_freq:+.3f}Hz\n"
        info_text += f"Fundamental Power: {segment['main_power']:.1f}dB\n"
        info_text += f"Noise Floor: {segment['noise_floor']:.1f}dB\n"
        info_text += f"SNR: {segment['main_power'] - segment['noise_floor']:.1f}dB\n"
        info_text += f"Harmonics Detected: {len(segment['harmonics'])}\n"
        info_text += f"Frequency Resolution: {segment['freq_resolution']:.4f}Hz"
        
        if segment['harmonics']:
            info_text += f"\nHighest Harmonic: {segment['harmonics'][-1]['order']}th ({segment['harmonics'][-1]['freq']:.0f}Hz)"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcyan', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'low_freq_hole_first4_segments.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 低音戳洞前4段可视化已保存: {filename}")
    plt.close()
    
    # 打印详细统计
    print(f"\n📊 详细统计信息:")
    print(f"{'='*70}")
    print(f"文件: {hole_data['filename']}")
    print(f"分析频段: 前4段")
    
    for i, segment in enumerate(segments):
        print(f"\n频段 {i} ({segment['expected_freq']:.0f}Hz):")
        print(f"  期望频率: {segment['expected_freq']:.0f}Hz")
        print(f"  实际频率: {segment['main_freq']:.3f}Hz")
        print(f"  频率误差: {segment['main_freq'] - segment['expected_freq']:+.3f}Hz")
        print(f"  主频功率: {segment['main_power']:.1f}dB")
        print(f"  噪声底噪: {segment['noise_floor']:.1f}dB")
        print(f"  信噪比: {segment['main_power'] - segment['noise_floor']:.1f}dB")
        print(f"  检测谐波: {len(segment['harmonics'])}个")
        
        if segment['harmonics']:
            print(f"  谐波列表:")
            for j, harmonic in enumerate(segment['harmonics'][:10]):  # 只显示前10个
                print(f"    {harmonic['order']:2d}次: {harmonic['freq']:7.1f}Hz, "
                      f"{harmonic['power']:6.1f}dB, SNR={harmonic['snr']:5.1f}dB, "
                      f"误差{harmonic['freq_error']:+5.1f}Hz")
            
            if len(segment['harmonics']) > 10:
                print(f"    ... 还有{len(segment['harmonics']) - 10}个谐波")

if __name__ == "__main__":
    single_hole_visualization()
