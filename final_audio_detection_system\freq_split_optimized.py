#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的频率分割模块
通过生成参考扫频信号并与待测信号对齐来精确定位开始时间
"""

import numpy as np
import librosa
import librosa.display
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import correlate
import sys
import os
sys.path.append('..')
from chirp import gen_freq_step
import soundfile as sf

class OptimizedFreqSplitter:
    def __init__(self, start_freq=100, stop_freq=20000, octave=12, fs=48000, 
                 min_cycles=10, min_duration=156):
        """
        初始化优化的频率分割器
        
        参数:
        start_freq: 起始频率
        stop_freq: 结束频率
        octave: 倍频程分辨率
        fs: 采样率
        min_cycles: 最小周期数
        min_duration: 最小持续时间(ms)
        """
        self.start_freq = start_freq
        self.stop_freq = stop_freq
        self.octave = octave
        self.fs = fs
        self.min_cycles = min_cycles
        self.min_duration = min_duration
        
        # 生成参考扫频信号
        self.reference_signal, self.reference_times, self.freq_dict = self._generate_reference_signal()
        self.freq_table = list(self.freq_dict.keys())
        self.step_durations = [self.freq_dict[f][2] / fs for f in self.freq_table]
        
        print(f"参考信号生成完成: {len(self.freq_table)}个频点, 总时长{self.reference_times[-1]:.2f}秒")
    
    def _generate_reference_signal(self):
        """生成参考扫频信号"""
        sine_wave, t_list, freq_ssample_dict = gen_freq_step(
            self.start_freq, self.stop_freq, self.min_cycles, 
            self.min_duration, self.octave, self.fs, A=1
        )
        return sine_wave, t_list, freq_ssample_dict
    
    def split_freq_steps_with_alignment(self, audio_path, 
                                      search_window_start=0.1, search_window_end=2.0,
                                      correlation_length=2.0, plot=True, debug=False):
        """
        使用参考信号对齐的方法分割频率步进
        
        参数:
        audio_path: 音频文件路径
        search_window_start: 搜索窗口开始时间(秒)
        search_window_end: 搜索窗口结束时间(秒)
        correlation_length: 用于相关性计算的信号长度(秒)
        plot: 是否绘制结果
        debug: 是否显示调试信息
        
        返回:
        step_boundaries: 步进边界列表
        freq_table: 频率表
        alignment_info: 对齐信息
        """
        print(f"\n🔍 使用参考信号对齐方法分析: {os.path.basename(audio_path)}")
        
        # 读取音频
        y, sr = librosa.load(audio_path, sr=self.fs)
        print(f"音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        
        # 如果采样率不匹配，重采样
        if sr != self.fs:
            y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            sr = self.fs
            print(f"重采样到{self.fs}Hz")
        
        # 在搜索窗口内寻找最佳对齐位置
        alignment_result = self._find_alignment_position(
            y, search_window_start, search_window_end, correlation_length, debug
        )
        
        if alignment_result is None:
            print("❌ 对齐失败，使用传统方法")
            return self._fallback_to_traditional_method(y, sr, plot)
        
        start_offset, correlation_score, search_info = alignment_result
        print(f"✅ 对齐成功: 开始时间={start_offset:.3f}秒, 相关性={correlation_score:.3f}")
        
        # 计算步进边界
        step_boundaries = self._calculate_step_boundaries(start_offset)
        
        # 验证对齐质量
        alignment_quality = self._validate_alignment(y, start_offset, correlation_length)
        
        # 可视化结果
        if plot:
            self._plot_alignment_results(y, sr, step_boundaries, start_offset, 
                                       correlation_score, search_info, alignment_quality)
        
        alignment_info = {
            'start_offset': start_offset,
            'correlation_score': correlation_score,
            'alignment_quality': alignment_quality,
            'search_info': search_info
        }
        
        return step_boundaries, self.freq_table, alignment_info
    
    def _find_alignment_position(self, y, search_start, search_end, corr_length, debug):
        """在搜索窗口内寻找最佳对齐位置 - 简化版本"""
        # 确定搜索范围
        search_start_sample = int(search_start * self.fs)
        search_end_sample = int(search_end * self.fs)
        corr_length_samples = int(corr_length * self.fs)

        # 确保搜索范围有效
        search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
        if search_start_sample >= search_end_sample:
            print(f"❌ 搜索范围无效: {search_start:.2f}s - {search_end:.2f}s")
            return None

        # 准备参考信号段 - 使用较短的段进行初始对齐
        initial_corr_length = min(corr_length_samples, int(0.5 * self.fs))  # 最多0.5秒
        reference_segment = self.reference_signal[:initial_corr_length]

        # 在搜索窗口内滑动计算相关性
        search_length = search_end_sample - search_start_sample
        correlations = []
        positions = []

        print(f"在{search_start:.2f}s-{search_end:.2f}s窗口内搜索最佳对齐位置...")
        print(f"使用{initial_corr_length/self.fs:.2f}秒参考段进行对齐...")

        # 使用滑动窗口计算相关性
        step_size = max(1, self.fs // 1000)  # 1ms步长
        for i in range(0, search_length - initial_corr_length, step_size):
            pos = search_start_sample + i
            test_segment = y[pos:pos + initial_corr_length]

            if len(test_segment) == len(reference_segment):
                # 计算归一化互相关
                correlation = self._normalized_cross_correlation(reference_segment, test_segment)
                correlations.append(correlation)
                positions.append(pos / self.fs)

        if not correlations:
            print("❌ 未找到有效的相关性计算结果")
            return None

        # 智能选择：在相关性差不多的情况下，优先选择时间更靠前的点
        correlations = np.array(correlations)
        positions = np.array(positions)

        # 找到最大相关性
        max_correlation = np.max(correlations)
        max_idx = np.argmax(correlations)

        # 定义"差不多"的阈值：最大相关性的80%，更宽松地选择早期点
        similarity_threshold = max_correlation * 0.8

        # 找到所有相关性超过阈值的点
        good_indices = np.where(correlations >= similarity_threshold)[0]

        if len(good_indices) > 1:
            # 在相关性足够好的点中，选择时间最靠前的
            earliest_idx = good_indices[0]  # 因为是按时间顺序搜索的，第一个就是最早的
            best_position = positions[earliest_idx]
            best_correlation = correlations[earliest_idx]
            selection_method = "earliest_good_correlation"

            if debug:
                print(f"  智能选择策略:")
                print(f"    最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
                print(f"    相关性阈值: {similarity_threshold:.3f}")
                print(f"    符合条件的点: {len(good_indices)}个")
                print(f"    选择最早的点: {best_position:.3f}s (相关性: {best_correlation:.3f})")
        else:
            # 只有一个好的点，直接使用
            best_position = positions[max_idx]
            best_correlation = correlations[max_idx]
            selection_method = "max_correlation"

            if debug:
                print(f"  选择最大相关性点: {best_position:.3f}s (相关性: {best_correlation:.3f})")
        
        if debug:
            print(f"相关性搜索结果:")
            print(f"  搜索范围: {len(positions)}个位置")
            print(f"  最大相关性: {best_correlation:.4f}")
            print(f"  最佳位置: {best_position:.3f}秒")
            
            # 显示前5个最佳位置
            sorted_indices = np.argsort(correlations)[::-1]
            print(f"  前5个最佳位置:")
            for i in range(min(5, len(sorted_indices))):
                idx = sorted_indices[i]
                print(f"    {i+1}. 时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.4f}")
        
        search_info = {
            'search_range': (search_start, search_end),
            'correlations': correlations,
            'positions': positions,
            'max_correlation': best_correlation,
            'search_points': len(positions)
        }
        
        return best_position, best_correlation, search_info

    def _find_best_alignment_multi_strategy(self, correlations, positions, energy_ratios, debug):
        """使用多种策略寻找最佳对齐位置"""

        # 策略1: 传统的最大相关性
        max_corr_idx = np.argmax(correlations)
        max_corr_pos = positions[max_corr_idx]
        max_corr_val = correlations[max_corr_idx]

        # 策略2: 第一个超过阈值的显著峰值
        correlation_threshold = 0.6  # 相关性阈值
        energy_threshold = 0.3       # 能量比例阈值

        # 找到同时满足相关性和能量条件的位置
        valid_mask = (correlations >= correlation_threshold) & (energy_ratios >= energy_threshold)
        valid_indices = np.where(valid_mask)[0]

        if len(valid_indices) > 0:
            # 第一个有效位置
            first_valid_idx = valid_indices[0]
            first_valid_pos = positions[first_valid_idx]
            first_valid_corr = correlations[first_valid_idx]

            # 策略3: 在有效位置中找局部最大值
            if len(valid_indices) > 1:
                # 在有效区域内寻找局部峰值
                valid_correlations = correlations[valid_indices]
                local_peaks = self._find_local_peaks(valid_correlations, min_distance=5)

                if len(local_peaks) > 0:
                    # 选择第一个局部峰值
                    first_peak_idx = valid_indices[local_peaks[0]]
                    peak_pos = positions[first_peak_idx]
                    peak_corr = correlations[first_peak_idx]

                    if debug:
                        print(f"  策略分析:")
                        print(f"    最大相关性: 位置={max_corr_pos:.3f}s, 相关性={max_corr_val:.3f}")
                        print(f"    第一个有效: 位置={first_valid_pos:.3f}s, 相关性={first_valid_corr:.3f}")
                        print(f"    第一个峰值: 位置={peak_pos:.3f}s, 相关性={peak_corr:.3f}")

                    # 选择策略：如果第一个峰值的相关性足够好，优先选择
                    if peak_corr >= 0.7:
                        return peak_pos, peak_corr
                    elif first_valid_corr >= 0.7:
                        return first_valid_pos, first_valid_corr
                    else:
                        return max_corr_pos, max_corr_val
                else:
                    # 没有局部峰值，选择第一个有效位置
                    if debug:
                        print(f"  选择第一个有效位置: {first_valid_pos:.3f}s, 相关性={first_valid_corr:.3f}")
                    return first_valid_pos, first_valid_corr
            else:
                # 只有一个有效位置
                if debug:
                    print(f"  选择唯一有效位置: {first_valid_pos:.3f}s, 相关性={first_valid_corr:.3f}")
                return first_valid_pos, first_valid_corr
        else:
            # 没有满足条件的位置，使用最大相关性
            if debug:
                print(f"  未找到满足阈值的位置，使用最大相关性: {max_corr_pos:.3f}s, 相关性={max_corr_val:.3f}")
            return max_corr_pos, max_corr_val

    def _find_local_peaks(self, data, min_distance=3):
        """寻找局部峰值"""
        peaks = []
        for i in range(min_distance, len(data) - min_distance):
            # 检查是否为局部最大值
            is_peak = True
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and data[j] >= data[i]:
                    is_peak = False
                    break
            if is_peak:
                peaks.append(i)
        return peaks
    
    def _normalized_cross_correlation(self, ref_signal, test_signal):
        """计算归一化互相关"""
        # 确保信号长度相同
        min_len = min(len(ref_signal), len(test_signal))
        ref_signal = ref_signal[:min_len]
        test_signal = test_signal[:min_len]
        
        # 归一化
        ref_norm = ref_signal - np.mean(ref_signal)
        test_norm = test_signal - np.mean(test_signal)
        
        # 计算相关系数
        correlation = np.corrcoef(ref_norm, test_norm)[0, 1]
        
        # 处理NaN值
        if np.isnan(correlation):
            correlation = 0.0
        
        return correlation
    
    def _calculate_step_boundaries(self, start_offset):
        """根据对齐的开始时间计算步进边界"""
        step_boundaries = []
        t = start_offset
        
        for duration in self.step_durations:
            step_boundaries.append((t, t + duration))
            t += duration
        
        print(f"计算出{len(step_boundaries)}个步进区间")
        return step_boundaries
    
    def _validate_alignment(self, y, start_offset, validation_length):
        """验证对齐质量"""
        validation_samples = int(validation_length * self.fs)
        start_sample = int(start_offset * self.fs)
        
        if start_sample + validation_samples > len(y):
            validation_samples = len(y) - start_sample
        
        if validation_samples <= 0:
            return {'quality': 'poor', 'score': 0.0, 'reason': 'insufficient_data'}
        
        # 提取对应的音频段和参考段
        audio_segment = y[start_sample:start_sample + validation_samples]
        ref_segment = self.reference_signal[:validation_samples]
        
        # 计算多个质量指标
        correlation = self._normalized_cross_correlation(ref_segment, audio_segment)
        
        # 频域相似性
        freq_similarity = self._frequency_domain_similarity(ref_segment, audio_segment)
        
        # 综合评分
        overall_score = (correlation + freq_similarity) / 2
        
        if overall_score > 0.7:
            quality = 'excellent'
        elif overall_score > 0.5:
            quality = 'good'
        elif overall_score > 0.3:
            quality = 'fair'
        else:
            quality = 'poor'
        
        return {
            'quality': quality,
            'overall_score': overall_score,
            'time_correlation': correlation,
            'frequency_similarity': freq_similarity,
            'validation_length': validation_length
        }
    
    def _frequency_domain_similarity(self, ref_signal, test_signal):
        """计算频域相似性"""
        # 计算功率谱密度
        f_ref, psd_ref = signal.welch(ref_signal, fs=self.fs, nperseg=1024)
        f_test, psd_test = signal.welch(test_signal, fs=self.fs, nperseg=1024)
        
        # 归一化
        psd_ref = psd_ref / np.sum(psd_ref)
        psd_test = psd_test / np.sum(psd_test)
        
        # 计算相关性
        similarity = np.corrcoef(psd_ref, psd_test)[0, 1]
        
        if np.isnan(similarity):
            similarity = 0.0
        
        return similarity
    
    def _fallback_to_traditional_method(self, y, sr, plot):
        """回退到改进的能量阈值方法"""
        print("使用改进的能量阈值方法...")

        # 方法1: RMS能量检测
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms_db = librosa.amplitude_to_db(rms, ref=np.max)
        frame_times = librosa.frames_to_time(np.arange(len(rms_db)), sr=sr, hop_length=512)

        # 方法2: 频谱质心检测（扫频信号的特征）
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=512)[0]

        # 方法3: 零交叉率检测
        zcr = librosa.feature.zero_crossing_rate(y, frame_length=2048, hop_length=512)[0]

        # 综合分析寻找扫频开始点
        start_candidates = []

        # 候选1: 能量阈值
        energy_threshold_db = -35  # 降低阈值
        above_threshold = np.where(rms_db > energy_threshold_db)[0]
        if len(above_threshold) > 0:
            start_candidates.append(('energy', frame_times[above_threshold[0]]))

        # 候选2: 频谱质心变化
        if len(spectral_centroids) > 10:
            # 寻找频谱质心开始上升的点（扫频特征）
            centroid_diff = np.diff(spectral_centroids)
            rising_points = np.where(centroid_diff > np.std(centroid_diff))[0]
            if len(rising_points) > 0:
                start_candidates.append(('spectral', frame_times[rising_points[0]]))

        # 候选3: 零交叉率变化
        if len(zcr) > 10:
            zcr_mean = np.mean(zcr)
            zcr_std = np.std(zcr)
            active_points = np.where(zcr > zcr_mean + 0.5 * zcr_std)[0]
            if len(active_points) > 0:
                start_candidates.append(('zcr', frame_times[active_points[0]]))

        # 选择最早的合理起点
        if start_candidates:
            start_times = [t for _, t in start_candidates]
            # 选择最早但不早于0.05秒的起点
            valid_starts = [t for t in start_times if t >= 0.05]
            if valid_starts:
                start_offset = min(valid_starts)
                method_used = [method for method, t in start_candidates if t == start_offset][0]
                print(f"  选择{method_used}方法检测的起点: {start_offset:.3f}秒")
            else:
                start_offset = min(start_times)
                print(f"  使用最早检测点: {start_offset:.3f}秒")
        else:
            start_offset = 0.2  # 默认起点
            print(f"  使用默认起点: {start_offset:.3f}秒")

        step_boundaries = self._calculate_step_boundaries(start_offset)

        return step_boundaries, self.freq_table, {
            'method': 'improved_traditional',
            'start_offset': start_offset,
            'candidates': start_candidates
        }

    def _plot_alignment_results(self, y, sr, step_boundaries, start_offset,
                               correlation_score, search_info, alignment_quality):
        """绘制对齐结果"""
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 1. 时域波形对比
        ax1 = axes[0]
        time_axis = np.arange(len(y)) / sr
        ax1.plot(time_axis, y, 'b-', alpha=0.7, label='待测信号')

        # 显示参考信号（对齐后）
        ref_start_sample = int(start_offset * sr)
        ref_end_sample = min(ref_start_sample + len(self.reference_signal), len(y))
        ref_time_axis = time_axis[ref_start_sample:ref_end_sample]
        ref_signal_aligned = self.reference_signal[:len(ref_time_axis)]

        if len(ref_time_axis) > 0:
            # 归一化参考信号幅度
            ref_signal_normalized = ref_signal_aligned * np.max(np.abs(y)) / np.max(np.abs(ref_signal_aligned))
            ax1.plot(ref_time_axis, ref_signal_normalized, 'r-', alpha=0.8, label='参考信号(对齐)')

        # 标记步进边界
        for i, (start_t, _) in enumerate(step_boundaries[:10]):  # 只显示前10个
            ax1.axvline(start_t, color='cyan', linestyle='--', alpha=0.6)
            if i == 0:
                ax1.axvline(start_t, color='cyan', linestyle='--', alpha=0.6, label='步进边界')

        ax1.axvline(start_offset, color='red', linestyle='-', linewidth=2, label=f'对齐起点({start_offset:.3f}s)')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('幅度')
        ax1.set_title(f'时域对齐结果 (相关性: {correlation_score:.3f}, 质量: {alignment_quality["quality"]})')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, min(5, time_axis[-1]))  # 只显示前5秒

        # 2. 相关性搜索结果 + 智能选择策略
        ax2 = axes[1]
        if 'correlations' in search_info:
            # 绘制相关性曲线
            ax2.plot(search_info['positions'], search_info['correlations'], 'b-', linewidth=2, label='相关性')

            # 计算并显示实际使用的阈值
            max_correlation = search_info['max_correlation']
            relative_threshold = max_correlation * 0.75  # 75%阈值
            absolute_threshold = 0.4
            final_threshold = max(relative_threshold, absolute_threshold)

            # 标记最大相关性位置
            max_idx = np.argmax(search_info['correlations'])
            ax2.plot(search_info['positions'][max_idx], search_info['correlations'][max_idx],
                    'go', markersize=8, label=f'最大相关性 ({max_correlation:.3f})')

            # 标记实际选择的位置
            ax2.axvline(start_offset, color='red', linestyle='-', linewidth=2,
                       label=f'选择位置 ({start_offset:.3f}s)')

            # 标记智能选择阈值线
            ax2.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, alpha=0.8,
                       label=f'选择阈值 ({final_threshold:.3f})')
            ax2.axhline(relative_threshold, color='purple', linestyle=':', alpha=0.6,
                       label=f'75%阈值 ({relative_threshold:.3f})')
            ax2.axhline(absolute_threshold, color='brown', linestyle=':', alpha=0.6,
                       label=f'绝对阈值 ({absolute_threshold:.3f})')

            # 高亮显示符合条件的区域
            good_mask = search_info['correlations'] >= final_threshold
            if np.any(good_mask):
                ax2.fill_between(search_info['positions'], 0, search_info['correlations'],
                               where=good_mask, alpha=0.2, color='green',
                               label='符合条件区域')

        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('相关性系数')
        ax2.set_title('多策略对齐分析')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)

        # 3. 频谱图
        ax3 = axes[2]
        S = librosa.stft(y, n_fft=2048, hop_length=512)
        S_db = librosa.amplitude_to_db(np.abs(S), ref=np.max)

        img = librosa.display.specshow(S_db, sr=sr, hop_length=512, x_axis='time',
                                      y_axis='log', cmap='magma', ax=ax3)

        # 标记步进边界
        for i, (start_t, _) in enumerate(step_boundaries[:20]):  # 显示前20个
            ax3.axvline(start_t, color='cyan', linestyle='--', linewidth=1, alpha=0.8)

        ax3.axvline(start_offset, color='red', linestyle='-', linewidth=2, alpha=0.9)
        ax3.set_title('频谱图 + 对齐后的步进分割')
        ax3.set_xlim(0, min(10, time_axis[-1]))  # 只显示前10秒

        plt.colorbar(img, ax=ax3, format='%+2.0f dB')
        plt.tight_layout()
        plt.show()

        # 打印对齐质量报告
        print(f"\n📊 对齐质量报告:")
        print(f"  总体质量: {alignment_quality['quality']}")
        print(f"  综合评分: {alignment_quality['overall_score']:.3f}")
        print(f"  时域相关性: {alignment_quality['time_correlation']:.3f}")
        print(f"  频域相似性: {alignment_quality['frequency_similarity']:.3f}")
        print(f"  验证长度: {alignment_quality['validation_length']:.1f}秒")

def split_freq_steps_optimized(audio_path, start_freq=100, stop_freq=20000, octave=12,
                              min_cycles=10, min_duration=156, fs=48000,
                              search_window_start=0.1, search_window_end=2.0,
                              correlation_length=2.0, plot=True, debug=False):
    """
    优化的频率步进分割函数

    参数:
    audio_path: 音频文件路径
    start_freq, stop_freq: 频率范围
    octave: 倍频程分辨率
    min_cycles, min_duration: 信号生成参数
    fs: 采样率
    search_window_start, search_window_end: 搜索窗口
    correlation_length: 相关性计算长度
    plot: 是否绘图
    debug: 是否显示调试信息

    返回:
    step_boundaries: 步进边界
    freq_table: 频率表
    alignment_info: 对齐信息
    """
    splitter = OptimizedFreqSplitter(start_freq, stop_freq, octave, fs, min_cycles, min_duration)
    return splitter.split_freq_steps_with_alignment(
        audio_path, search_window_start, search_window_end, correlation_length, plot, debug
    )

if __name__ == "__main__":
    # 测试优化的频率分割
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"

    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
        audio_path,
        min_duration=153,
        plot=True,
        debug=True,
        search_window_start=0.1,
        search_window_end=1.5,
        correlation_length=1.0
    )

    print(f"\n✅ 分割完成:")
    print(f"  步进区间数: {len(step_bounds)}")
    print(f"  频率点数: {len(freq_table)}")
    print(f"  对齐信息: {alignment_info}")
