#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按93个频段分别分析"喇叭eva没贴"样本与正样本的区别
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_speaker_eva_by_segments():
    """按频段分析喇叭eva没贴样本"""
    print("🔍 按93个频段分别分析'喇叭eva没贴'样本与正样本的区别")
    print("="*70)
    
    # 检查是否有之前的分析数据
    if not os.path.exists('speaker_eva_discrimination_analysis.csv'):
        print("❌ 请先运行 analyze_speaker_eva_missing.py 生成分析数据")
        return
    
    # 加载特征数据（从之前的分析中）
    # 我们需要重新加载原始特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    # 加载完整特征数据
    df = pd.read_csv('comprehensive_features.csv')
    
    # 筛选出喇叭eva没贴样本和部分正样本
    speaker_eva_files = ['喇叭eva没贴.wav', '喇叭eva没贴_1.wav']
    
    # 获取喇叭eva没贴样本
    eva_samples = df[df['filename'].isin(speaker_eva_files)]
    
    # 获取正样本（随机选择一些作为对比）
    pos_samples = df[df['label'] == 'pos']
    
    if len(eva_samples) == 0:
        print("❌ 未找到喇叭eva没贴样本数据")
        return
    
    print(f"📊 找到喇叭eva没贴样本: {len(eva_samples)}个频段")
    print(f"📊 正样本对比数据: {len(pos_samples)}个频段")
    
    # 最有用的5个特征
    top_features = [
        'vl_energy_mean',
        'harmonic_total_ratio', 
        'harmonic_3_ratio',
        'thd',
        'harmonic_4_ratio'
    ]
    
    # 按频段分析
    segment_analysis_results = []
    
    print(f"\n📊 按频段分析最有用的5个特征:")
    print("="*70)
    
    for segment_idx in sorted(eva_samples['segment_idx'].unique()):
        # 获取该频段的数据
        eva_segment = eva_samples[eva_samples['segment_idx'] == segment_idx]
        pos_segment = pos_samples[pos_samples['segment_idx'] == segment_idx]
        
        if len(eva_segment) == 0 or len(pos_segment) == 0:
            continue
        
        expected_freq = eva_segment['expected_freq'].iloc[0]
        
        print(f"\n📊 频段 {segment_idx}: {expected_freq:.1f}Hz")
        print(f"   喇叭eva样本: {len(eva_segment)}个, 正样本: {len(pos_segment)}个")
        
        segment_result = {
            'segment_idx': segment_idx,
            'expected_freq': expected_freq,
            'eva_count': len(eva_segment),
            'pos_count': len(pos_segment)
        }
        
        # 分析每个特征在该频段的区分能力
        best_features = []
        
        for feature in top_features:
            try:
                eva_values = eva_segment[feature].dropna().values
                pos_values = pos_segment[feature].dropna().values
                
                if len(eva_values) == 0 or len(pos_values) == 0:
                    continue
                
                # 统计检验
                t_stat, p_value = stats.ttest_ind(pos_values, eva_values)
                
                # 效应大小
                pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                                     (len(eva_values) - 1) * np.var(eva_values)) / 
                                    (len(pos_values) + len(eva_values) - 2))
                cohens_d = abs(np.mean(pos_values) - np.mean(eva_values)) / (pooled_std + 1e-12)
                
                # 基础统计
                pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
                eva_mean, eva_std = np.mean(eva_values), np.std(eva_values)
                
                # 重叠度计算
                overlap = max(0, min(pos_mean + 2*pos_std, eva_mean + 2*eva_std) - 
                             max(pos_mean - 2*pos_std, eva_mean - 2*eva_std))
                total_range = max(pos_mean + 2*pos_std, eva_mean + 2*eva_std) - \
                             min(pos_mean - 2*pos_std, eva_mean - 2*eva_std)
                overlap_ratio = overlap / (total_range + 1e-12)
                
                separation_score = cohens_d * (1 - overlap_ratio)
                
                # 判断区分效果
                if separation_score > 1.0 and overlap_ratio < 0.2:
                    effect_level = "🎯 可以区分"
                elif separation_score > 0.5 and overlap_ratio < 0.5:
                    effect_level = "✅ 区分较好"
                elif separation_score > 0.2:
                    effect_level = "🟡 区分一般"
                else:
                    effect_level = "❌ 区分较差"
                
                feature_result = {
                    'feature': feature,
                    'pos_mean': pos_mean,
                    'pos_std': pos_std,
                    'eva_mean': eva_mean,
                    'eva_std': eva_std,
                    'cohens_d': cohens_d,
                    'p_value': p_value,
                    'separation_score': separation_score,
                    'overlap_ratio': overlap_ratio,
                    'effect_level': effect_level
                }
                
                segment_result[f'{feature}_separation_score'] = separation_score
                segment_result[f'{feature}_cohens_d'] = cohens_d
                segment_result[f'{feature}_p_value'] = p_value
                segment_result[f'{feature}_pos_mean'] = pos_mean
                segment_result[f'{feature}_eva_mean'] = eva_mean
                segment_result[f'{feature}_effect_level'] = effect_level
                
                best_features.append(feature_result)
                
            except Exception as e:
                continue
        
        # 排序并显示该频段最有效的特征
        best_features.sort(key=lambda x: x['separation_score'], reverse=True)
        
        print(f"   最有区分力的特征:")
        for i, feat in enumerate(best_features[:3]):  # 显示前3个
            significance = "***" if feat['p_value'] < 0.001 else "**" if feat['p_value'] < 0.01 else "*" if feat['p_value'] < 0.05 else ""
            print(f"     {i+1}. {feat['feature']}: {feat['effect_level']}")
            print(f"        分离评分: {feat['separation_score']:.3f}, Cohen's d: {feat['cohens_d']:.3f} {significance}")
            print(f"        正样本: {feat['pos_mean']:.6f}±{feat['pos_std']:.6f}")
            print(f"        喇叭eva: {feat['eva_mean']:.6f}±{feat['eva_std']:.6f}")
        
        # 记录该频段最佳特征
        if best_features:
            segment_result['best_feature'] = best_features[0]['feature']
            segment_result['best_separation_score'] = best_features[0]['separation_score']
            segment_result['best_effect_level'] = best_features[0]['effect_level']
        else:
            segment_result['best_feature'] = 'none'
            segment_result['best_separation_score'] = 0
            segment_result['best_effect_level'] = '❌ 无有效特征'
        
        segment_analysis_results.append(segment_result)
    
    # 转换为DataFrame
    results_df = pd.DataFrame(segment_analysis_results)
    
    # 生成频段分析摘要
    generate_segment_summary(results_df)
    
    # 可视化频段分析结果
    visualize_segment_analysis(results_df)
    
    # 保存结果
    results_df.to_csv('speaker_eva_segment_analysis.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 频段分析结果已保存: speaker_eva_segment_analysis.csv")
    
    return results_df

def generate_segment_summary(results_df):
    """生成频段分析摘要"""
    print(f"\n📊 频段分析摘要")
    print("="*70)
    
    total_segments = len(results_df)
    
    # 按效果等级统计
    effect_counts = {}
    for level in ['🎯 可以区分', '✅ 区分较好', '🟡 区分一般', '❌ 区分较差', '❌ 无有效特征']:
        count = len(results_df[results_df['best_effect_level'] == level])
        if count > 0:
            effect_counts[level] = count
    
    print(f"总频段数: {total_segments}")
    print(f"区分效果统计:")
    for level, count in effect_counts.items():
        percentage = count / total_segments * 100
        print(f"  {level}: {count}个频段 ({percentage:.1f}%)")
    
    # 找出区分效果最好的频段
    best_segments = results_df.nlargest(10, 'best_separation_score')
    
    print(f"\n📊 区分效果最好的前10个频段:")
    print("-" * 50)
    for i, (_, row) in enumerate(best_segments.iterrows()):
        print(f"{i+1:2d}. 频段{row['segment_idx']:2d} ({row['expected_freq']:6.1f}Hz): "
              f"{row['best_effect_level']} - {row['best_feature']}")
        print(f"     分离评分: {row['best_separation_score']:.3f}")
    
    # 按频率范围统计
    print(f"\n📊 按频率范围统计:")
    print("-" * 50)
    
    frequency_ranges = [
        (100, 500, "低频"),
        (500, 2000, "中低频"),
        (2000, 8000, "中频"),
        (8000, 20000, "高频")
    ]
    
    for freq_min, freq_max, freq_name in frequency_ranges:
        range_segments = results_df[
            (results_df['expected_freq'] >= freq_min) & 
            (results_df['expected_freq'] <= freq_max)
        ]
        
        if len(range_segments) > 0:
            avg_separation = range_segments['best_separation_score'].mean()
            good_segments = len(range_segments[
                range_segments['best_effect_level'].isin(['🎯 可以区分', '✅ 区分较好'])
            ])
            
            print(f"  {freq_name} ({freq_min}-{freq_max}Hz): {len(range_segments)}个频段")
            print(f"    平均分离评分: {avg_separation:.3f}")
            print(f"    有效区分频段: {good_segments}/{len(range_segments)} ({good_segments/len(range_segments)*100:.1f}%)")

def visualize_segment_analysis(results_df):
    """可视化频段分析结果"""
    print(f"\n🔍 生成频段分析可视化")
    print("="*70)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('喇叭eva没贴 - 按频段分析结果', fontsize=16, fontweight='bold')
    
    # 1. 分离评分随频段变化
    ax1 = axes[0, 0]
    ax1.plot(results_df['segment_idx'], results_df['best_separation_score'], 'b-o', markersize=4)
    ax1.set_title('各频段最佳分离评分')
    ax1.set_xlabel('频段索引')
    ax1.set_ylabel('分离评分')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='一般区分线')
    ax1.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='良好区分线')
    ax1.legend()
    
    # 2. 分离评分随频率变化
    ax2 = axes[0, 1]
    ax2.semilogx(results_df['expected_freq'], results_df['best_separation_score'], 'g-o', markersize=4)
    ax2.set_title('各频率最佳分离评分')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('分离评分')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7)
    ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7)
    
    # 3. 区分效果分布
    ax3 = axes[1, 0]
    effect_counts = results_df['best_effect_level'].value_counts()
    colors = ['red', 'orange', 'yellow', 'lightgreen', 'green'][:len(effect_counts)]
    ax3.pie(effect_counts.values, labels=effect_counts.index, autopct='%1.1f%%', colors=colors)
    ax3.set_title('区分效果分布')
    
    # 4. 最佳特征分布
    ax4 = axes[1, 1]
    feature_counts = results_df['best_feature'].value_counts()
    ax4.bar(range(len(feature_counts)), feature_counts.values)
    ax4.set_title('各频段最佳特征分布')
    ax4.set_xlabel('特征')
    ax4.set_ylabel('频段数量')
    ax4.set_xticks(range(len(feature_counts)))
    ax4.set_xticklabels(feature_counts.index, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('speaker_eva_segment_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    results_df = analyze_speaker_eva_by_segments()
