#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的正常范围计算方法
基于分位数而不是正态分布假设
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def corrected_range_calculation():
    """修正的范围计算方法"""
    print("🔧 修正的正常范围计算方法")
    print("="*70)
    print("基于分位数的稳健统计方法，不依赖正态分布假设")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    normal_data = df[df['is_target'] == False]
    target_data = df[df['is_target'] == True]
    
    # 以100Hz频段的true_noise_floor_median为例
    segment_idx = 0
    feature = 'true_noise_floor_median'
    
    normal_seg = normal_data[normal_data['segment_idx'] == segment_idx]
    target_seg = target_data[target_data['segment_idx'] == segment_idx]
    
    normal_values = normal_seg[feature].values
    target_values = target_seg[feature].values
    
    print(f"📊 以频段{segment_idx} ({normal_seg['expected_freq'].iloc[0]:.1f}Hz) 的 {feature} 为例")
    print(f"   正常样本数: {len(normal_values)}")
    print(f"   噪声样本数: {len(target_values)}")
    
    # 修正的范围计算
    corrected_ranges = calculate_corrected_ranges(normal_values)
    
    print(f"\n📈 修正的范围计算结果:")
    for range_name, (lower, upper, description) in corrected_ranges.items():
        width = upper - lower
        print(f"   {range_name}: [{lower:.6f}, {upper:.6f}] (宽度: {width:.6f})")
        print(f"     含义: {description}")
    
    # 检查噪声样本位置
    print(f"\n🎯 噪声样本位置分析:")
    for i, target_val in enumerate(target_values):
        print(f"   噪声样本{i+1}: {target_val:.6f}")
        
        # 检查在哪个范围内
        anomaly_level = classify_anomaly_level(target_val, corrected_ranges)
        print(f"     分类: {anomaly_level}")
    
    # 可视化修正的范围
    visualize_corrected_ranges(normal_values, target_values, corrected_ranges, segment_idx, feature)
    
    # 应用到所有频段
    analyze_all_segments_corrected(normal_data, target_data)

def calculate_corrected_ranges(values):
    """计算修正的范围 - 基于分位数方法"""
    
    # 基本统计量
    mean = np.mean(values)
    median = np.median(values)
    std = np.std(values, ddof=1)
    
    # 分位数
    p1 = np.percentile(values, 1)
    p5 = np.percentile(values, 5)
    p10 = np.percentile(values, 10)
    p25 = np.percentile(values, 25)
    p75 = np.percentile(values, 75)
    p90 = np.percentile(values, 90)
    p95 = np.percentile(values, 95)
    p99 = np.percentile(values, 99)
    
    # 四分位距
    iqr = p75 - p25
    
    # 修正的范围定义
    ranges = {
        'absolute': (np.min(values), np.max(values), '绝对范围 - 所有数据的边界'),
        'p99_range': (p1, p99, '99%数据范围 - 排除极端1%'),
        'p95_range': (p5, p95, '90%数据范围 - 排除极端10%'),
        'p80_range': (p10, p90, '80%数据范围 - 排除极端20%'),
        'iqr_range': (p25, p75, '四分位距范围 - 中间50%数据'),
        'iqr_extended': (p25 - 1.5*iqr, p75 + 1.5*iqr, '扩展四分位距 - 箱线图异常值检测'),
        'robust_range': (median - 2*iqr, median + 2*iqr, '稳健范围 - 基于中位数和IQR')
    }
    
    # 确保扩展范围不超出绝对范围
    abs_min, abs_max = ranges['absolute'][:2]
    
    # 修正扩展四分位距
    iqr_ext_lower = max(ranges['iqr_extended'][0], abs_min)
    iqr_ext_upper = min(ranges['iqr_extended'][1], abs_max)
    ranges['iqr_extended'] = (iqr_ext_lower, iqr_ext_upper, ranges['iqr_extended'][2])
    
    # 修正稳健范围
    robust_lower = max(ranges['robust_range'][0], abs_min)
    robust_upper = min(ranges['robust_range'][1], abs_max)
    ranges['robust_range'] = (robust_lower, robust_upper, ranges['robust_range'][2])
    
    return ranges

def classify_anomaly_level(value, ranges):
    """基于修正范围分类异常级别"""
    
    # 按严格程度检查
    if ranges['iqr_range'][0] <= value <= ranges['iqr_range'][1]:
        return "正常 - 在核心分布范围内"
    elif ranges['p80_range'][0] <= value <= ranges['p80_range'][1]:
        return "轻微偏离 - 在80%范围内"
    elif ranges['p95_range'][0] <= value <= ranges['p95_range'][1]:
        return "中度异常 - 在90%范围内"
    elif ranges['iqr_extended'][0] <= value <= ranges['iqr_extended'][1]:
        return "显著异常 - 在扩展四分位距内"
    elif ranges['absolute'][0] <= value <= ranges['absolute'][1]:
        return "极度异常 - 在绝对范围内但超出统计范围"
    else:
        return "超极异常 - 完全超出历史数据范围"

def visualize_corrected_ranges(normal_values, target_values, ranges, segment_idx, feature):
    """可视化修正的范围"""
    print(f"\n🎨 生成修正范围可视化...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 1. 直方图 + 范围
    ax1.hist(normal_values, bins=30, alpha=0.7, color='lightblue', edgecolor='black', density=True)
    
    # 添加范围区间
    colors = ['red', 'orange', 'yellow', 'lightgreen', 'blue', 'purple', 'brown']
    alphas = [0.1, 0.15, 0.2, 0.25, 0.3, 0.2, 0.15]
    
    for i, (range_name, (lower, upper, desc)) in enumerate(ranges.items()):
        if i < len(colors):
            ax1.axvspan(lower, upper, alpha=alphas[i], color=colors[i], 
                       label=f'{range_name}: [{lower:.2f}, {upper:.2f}]')
    
    # 添加噪声样本点
    for i, target_val in enumerate(target_values):
        ax1.axvline(target_val, color='red', linestyle='--', linewidth=2, 
                   label=f'噪声样本{i+1}: {target_val:.3f}')
    
    ax1.set_title(f'修正范围可视化 - 频段{segment_idx} {feature}', fontweight='bold')
    ax1.set_xlabel('特征值')
    ax1.set_ylabel('密度')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    # 2. 箱线图 + 范围标记
    bp = ax2.boxplot([normal_values], patch_artist=True, labels=['正常样本'])
    bp['boxes'][0].set_facecolor('lightblue')
    
    # 添加噪声样本点
    for i, target_val in enumerate(target_values):
        ax2.scatter([1], [target_val], color='red', s=100, marker='*', 
                   label=f'噪声样本{i+1}', zorder=5)
    
    # 添加关键范围线
    key_ranges = ['p95_range', 'iqr_extended', 'absolute']
    range_colors = ['orange', 'green', 'red']
    
    for range_name, color in zip(key_ranges, range_colors):
        lower, upper = ranges[range_name][:2]
        ax2.hlines([lower, upper], 0.8, 1.2, colors=color, linestyles='--', alpha=0.7)
        ax2.text(1.25, lower, f'{range_name}_lower', va='center', fontsize=8, color=color)
        ax2.text(1.25, upper, f'{range_name}_upper', va='center', fontsize=8, color=color)
    
    ax2.set_title(f'箱线图 + 关键范围 - 频段{segment_idx} {feature}', fontweight='bold')
    ax2.set_ylabel('特征值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'corrected_ranges_seg{segment_idx}_{feature}.png', dpi=300, bbox_inches='tight')
    print(f"✅ 可视化已保存: corrected_ranges_seg{segment_idx}_{feature}.png")
    plt.close()

def analyze_all_segments_corrected(normal_data, target_data):
    """使用修正方法分析所有频段"""
    print(f"\n📊 使用修正方法分析所有频段的分离效果")
    print("="*70)
    
    features = ['true_noise_floor_median', 'true_noise_floor_mean', 
               'noise_floor_stability_mean', 'noise_floor_stability_std']
    
    segments = sorted(normal_data['segment_idx'].unique())
    
    for feature in features:
        print(f"\n🎯 {feature} 分离分析:")
        print("-" * 50)
        
        separable_segments = []
        
        for seg_idx in segments:
            normal_seg = normal_data[normal_data['segment_idx'] == seg_idx]
            target_seg = target_data[target_data['segment_idx'] == seg_idx]
            
            if len(normal_seg) > 0 and len(target_seg) > 0:
                normal_values = normal_seg[feature].values
                target_values = target_seg[feature].values
                
                # 计算修正范围
                ranges = calculate_corrected_ranges(normal_values)
                
                # 检查噪声样本是否可分离
                all_anomalous = True
                anomaly_levels = []
                
                for target_val in target_values:
                    level = classify_anomaly_level(target_val, ranges)
                    anomaly_levels.append(level)
                    
                    # 如果在核心分布内，则不算异常
                    if "正常" in level or "轻微偏离" in level:
                        all_anomalous = False
                
                if all_anomalous:
                    freq = normal_seg['expected_freq'].iloc[0]
                    
                    # 计算分离强度
                    abs_range = ranges['absolute']
                    iqr_range = ranges['iqr_range']
                    
                    # 计算噪声样本到正常范围的最小距离
                    min_distance = float('inf')
                    for target_val in target_values:
                        if target_val < abs_range[0]:
                            distance = abs_range[0] - target_val
                        elif target_val > abs_range[1]:
                            distance = target_val - abs_range[1]
                        else:
                            distance = 0
                        min_distance = min(min_distance, distance)
                    
                    separable_segments.append({
                        'segment': seg_idx,
                        'frequency': freq,
                        'separation_distance': min_distance,
                        'anomaly_levels': anomaly_levels
                    })
        
        # 排序并显示结果
        separable_segments.sort(key=lambda x: x['separation_distance'], reverse=True)
        
        print(f"   可分离频段数: {len(separable_segments)}/{len(segments)} ({len(separable_segments)/len(segments)*100:.1f}%)")
        
        if len(separable_segments) > 0:
            print(f"   前5个最佳分离频段:")
            for i, seg_info in enumerate(separable_segments[:5]):
                print(f"     {i+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): "
                      f"分离距离{seg_info['separation_distance']:.3f}")
                print(f"        异常级别: {', '.join(seg_info['anomaly_levels'])}")

if __name__ == "__main__":
    corrected_range_calculation()
