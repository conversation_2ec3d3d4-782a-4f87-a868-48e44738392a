#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的谐波计算方法
考虑频谱范围限制和不同频率的主频能量带宽差异
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
import librosa

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def corrected_harmonic_calculation():
    """修正的谐波计算方法演示"""
    print("🔧 修正的谐波计算方法")
    print("="*70)
    print("修正要点:")
    print("1. 谐波必须在实际频谱范围内搜索")
    print("2. 不同频率的主频能量带宽不同")
    print("3. 低频主频带宽窄，高频主频带宽宽")
    print("4. 谐波搜索带宽应该适应频率")
    print("="*70)
    
    # 生成多个频率的演示信号
    demo_frequencies = [100, 500, 1000, 5000, 10000, 15000]
    
    for freq in demo_frequencies:
        print(f"\n🎯 演示频率: {freq}Hz")
        print("-" * 40)
        
        # 生成该频率的信号
        demo_signal = generate_frequency_specific_signal(freq)
        
        # 修正的谐波计算
        corrected_harmonic_analysis(demo_signal, freq)
    
    # 可视化不同频率的带宽差异
    visualize_bandwidth_differences()

def generate_frequency_specific_signal(fundamental_freq):
    """生成特定频率的信号"""
    
    fs = 48000
    duration = 1.0
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    # 基频信号
    signal = np.sin(2 * np.pi * fundamental_freq * t)
    
    # 添加谐波 (只添加在奈奎斯特频率内的谐波)
    harmonic_amplitudes = [0.3, 0.1, 0.05, 0.02]
    for i, amp in enumerate(harmonic_amplitudes):
        harmonic_order = i + 2
        harmonic_freq = fundamental_freq * harmonic_order
        
        if harmonic_freq < fs / 2:  # 只添加在奈奎斯特频率内的谐波
            signal += amp * np.sin(2 * np.pi * harmonic_freq * t)
    
    # 添加噪声
    noise_level = 0.05
    signal += noise_level * np.random.randn(len(t))
    
    return {
        'signal': signal,
        'fs': fs,
        'fundamental_freq': fundamental_freq,
        'duration': duration
    }

def corrected_harmonic_analysis(demo_signal, fundamental_freq):
    """修正的谐波分析"""
    
    signal = demo_signal['signal']
    fs = demo_signal['fs']
    
    # 高分辨率FFT
    fft_size = max(16384, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    
    freq_resolution = positive_freqs[1] - positive_freqs[0]
    nyquist_freq = fs / 2
    
    print(f"   频率分辨率: {freq_resolution:.3f} Hz")
    print(f"   奈奎斯特频率: {nyquist_freq} Hz")
    print(f"   分析频率范围: 0 - {positive_freqs[-1]:.0f} Hz")
    
    # 1. 修正的主频带宽计算
    fundamental_bandwidth = calculate_adaptive_bandwidth(fundamental_freq)
    print(f"   主频带宽: ±{fundamental_bandwidth:.1f} Hz")
    
    # 2. 找到主频
    fundamental_result = find_fundamental_corrected(positive_freqs, positive_power, 
                                                   fundamental_freq, fundamental_bandwidth)
    
    # 3. 修正的谐波搜索
    harmonic_results = find_harmonics_corrected(positive_freqs, positive_power, 
                                               fundamental_freq, nyquist_freq)
    
    # 4. 显示结果
    display_corrected_results(fundamental_result, harmonic_results, fundamental_freq, nyquist_freq)

def calculate_adaptive_bandwidth(frequency):
    """计算自适应带宽"""
    
    # 基于频率的自适应带宽计算
    # 低频: 窄带宽, 高频: 宽带宽
    
    if frequency <= 200:
        # 极低频: ±2Hz
        bandwidth = 2.0
    elif frequency <= 500:
        # 低频: ±3Hz
        bandwidth = 3.0
    elif frequency <= 1000:
        # 中低频: ±5Hz
        bandwidth = 5.0
    elif frequency <= 2000:
        # 中频: ±8Hz
        bandwidth = 8.0
    elif frequency <= 5000:
        # 中高频: ±12Hz
        bandwidth = 12.0
    elif frequency <= 10000:
        # 高频: ±20Hz
        bandwidth = 20.0
    else:
        # 超高频: ±30Hz
        bandwidth = 30.0
    
    return bandwidth

def find_fundamental_corrected(freqs, power, expected_freq, bandwidth):
    """修正的主频查找"""
    
    # 搜索范围
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    return {
        'freq': freqs[actual_idx],
        'power': power[actual_idx],
        'power_db': 10 * np.log10(power[actual_idx] + 1e-12),
        'bandwidth_used': bandwidth,
        'freq_error': freqs[actual_idx] - expected_freq
    }

def find_harmonics_corrected(freqs, power, fundamental_freq, nyquist_freq):
    """修正的谐波查找"""
    
    harmonics = []
    max_freq_in_spectrum = freqs[-1]  # 实际频谱的最大频率
    
    print(f"   谐波搜索限制:")
    print(f"     奈奎斯特频率: {nyquist_freq} Hz")
    print(f"     实际频谱范围: 0 - {max_freq_in_spectrum:.0f} Hz")
    
    # 搜索谐波
    for order in range(2, 20):  # 最多搜索到19次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查1: 是否超过奈奎斯特频率
        if expected_harmonic_freq >= nyquist_freq:
            print(f"     {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率，停止搜索")
            break
        
        # 检查2: 是否超过实际频谱范围
        if expected_harmonic_freq > max_freq_in_spectrum:
            print(f"     {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过频谱范围，停止搜索")
            break
        
        # 计算该谐波的自适应带宽
        harmonic_bandwidth = calculate_adaptive_bandwidth(expected_harmonic_freq)
        
        # 搜索谐波峰值
        search_mask = (freqs >= expected_harmonic_freq - harmonic_bandwidth) & \
                     (freqs <= expected_harmonic_freq + harmonic_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power[search_mask]
            
            # 找到局部最大值
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            
            actual_freq = freqs[actual_idx]
            harmonic_power = power[actual_idx]
            harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
            
            # 验证是否是真正的谐波峰值 (功率阈值)
            # 谐波功率应该显著高于周围的噪声
            noise_threshold_db = -60  # 相对阈值
            
            if harmonic_power_db > noise_threshold_db:
                harmonics.append({
                    'order': order,
                    'expected_freq': expected_harmonic_freq,
                    'actual_freq': actual_freq,
                    'power': harmonic_power,
                    'power_db': harmonic_power_db,
                    'bandwidth_used': harmonic_bandwidth,
                    'freq_error': actual_freq - expected_harmonic_freq
                })
                
                print(f"     {order}次谐波: 期望{expected_harmonic_freq:.0f}Hz, "
                      f"实际{actual_freq:.1f}Hz, 功率{harmonic_power_db:.1f}dB, "
                      f"带宽±{harmonic_bandwidth:.1f}Hz")
            else:
                print(f"     {order}次谐波: 功率过低 ({harmonic_power_db:.1f}dB < {noise_threshold_db}dB)")
        else:
            print(f"     {order}次谐波: 搜索范围无效")
    
    return harmonics

def display_corrected_results(fundamental_result, harmonic_results, fundamental_freq, nyquist_freq):
    """显示修正的结果"""
    
    if fundamental_result:
        print(f"\n   ✅ 主频结果:")
        print(f"     频率: {fundamental_result['freq']:.2f} Hz (误差: {fundamental_result['freq_error']:.2f} Hz)")
        print(f"     功率: {fundamental_result['power_db']:.2f} dB")
        print(f"     使用带宽: ±{fundamental_result['bandwidth_used']:.1f} Hz")
    
    if harmonic_results:
        print(f"\n   ✅ 谐波结果:")
        print(f"     找到谐波数: {len(harmonic_results)}")
        
        total_harmonic_power = sum(h['power'] for h in harmonic_results)
        total_harmonic_power_db = 10 * np.log10(total_harmonic_power + 1e-12)
        
        print(f"     总谐波功率: {total_harmonic_power_db:.2f} dB")
        
        # 计算理论最大谐波次数
        max_theoretical_order = int(nyquist_freq / fundamental_freq)
        print(f"     理论最大谐波次数: {max_theoretical_order}")
        print(f"     实际找到最高次数: {max(h['order'] for h in harmonic_results)}")
        
        # 显示每个谐波的详细信息
        for harmonic in harmonic_results:
            print(f"       {harmonic['order']}次: {harmonic['actual_freq']:.1f}Hz, "
                  f"{harmonic['power_db']:.1f}dB, 误差{harmonic['freq_error']:.1f}Hz")
    else:
        print(f"\n   ❌ 未找到明显谐波")
        max_theoretical_order = int(nyquist_freq / fundamental_freq)
        print(f"     理论最大谐波次数: {max_theoretical_order}")

def visualize_bandwidth_differences():
    """可视化不同频率的带宽差异"""
    
    print(f"\n🎨 生成带宽差异可视化...")
    
    # 测试频率范围
    test_frequencies = np.logspace(2, 4.3, 50)  # 100Hz到20kHz
    bandwidths = [calculate_adaptive_bandwidth(f) for f in test_frequencies]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 上图: 带宽 vs 频率
    ax1.semilogx(test_frequencies, bandwidths, 'b-o', markersize=4, linewidth=2)
    ax1.set_title('自适应带宽 vs 频率', fontweight='bold')
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('带宽 (±Hz)')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(100, 20000)
    
    # 添加频率区间标注
    freq_ranges = [
        (100, 200, '极低频\n±2Hz'),
        (200, 500, '低频\n±3Hz'),
        (500, 1000, '中低频\n±5Hz'),
        (1000, 2000, '中频\n±8Hz'),
        (2000, 5000, '中高频\n±12Hz'),
        (5000, 10000, '高频\n±20Hz'),
        (10000, 20000, '超高频\n±30Hz')
    ]
    
    colors = ['red', 'orange', 'yellow', 'green', 'blue', 'purple', 'brown']
    for i, (f_low, f_high, label) in enumerate(freq_ranges):
        color = colors[i % len(colors)]
        ax1.axvspan(f_low, f_high, alpha=0.2, color=color)
        ax1.text((f_low + f_high) / 2, max(bandwidths) * 0.8, label, 
                ha='center', va='center', fontsize=8, fontweight='bold')
    
    # 下图: 相对带宽 vs 频率
    relative_bandwidths = [2 * bw / freq * 100 for freq, bw in zip(test_frequencies, bandwidths)]
    
    ax2.semilogx(test_frequencies, relative_bandwidths, 'r-s', markersize=4, linewidth=2)
    ax2.set_title('相对带宽 (2×带宽/频率) vs 频率', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('相对带宽 (%)')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(100, 20000)
    
    plt.tight_layout()
    plt.savefig('adaptive_bandwidth_analysis.png', dpi=300, bbox_inches='tight')
    print(f"   ✅ 可视化已保存: adaptive_bandwidth_analysis.png")
    plt.close()
    
    # 显示带宽设计原理
    print(f"\n💡 自适应带宽设计原理:")
    print(f"   1. 低频信号更稳定，使用窄带宽 (±2-5Hz)")
    print(f"   2. 高频信号更容易漂移，使用宽带宽 (±20-30Hz)")
    print(f"   3. 相对带宽随频率降低，保持检测精度")
    print(f"   4. 避免谐波搜索范围重叠")

def demonstrate_frequency_limits():
    """演示频率限制的重要性"""
    
    print(f"\n📊 频率限制演示:")
    print("-" * 40)
    
    test_cases = [
        (100, 48000),   # 低频，很多谐波
        (1000, 48000),  # 中频，适中谐波
        (5000, 48000),  # 高频，少量谐波
        (15000, 48000), # 超高频，几乎无谐波
    ]
    
    for fundamental, fs in test_cases:
        nyquist = fs / 2
        max_order = int(nyquist / fundamental)
        
        print(f"\n   基频 {fundamental}Hz (采样率 {fs}Hz):")
        print(f"     奈奎斯特频率: {nyquist} Hz")
        print(f"     理论最大谐波次数: {max_order}")
        
        # 列出所有可能的谐波
        possible_harmonics = []
        for order in range(2, max_order + 1):
            harmonic_freq = fundamental * order
            if harmonic_freq < nyquist:
                possible_harmonics.append(f"{order}次({harmonic_freq}Hz)")
        
        if possible_harmonics:
            print(f"     可能的谐波: {', '.join(possible_harmonics[:5])}")
            if len(possible_harmonics) > 5:
                print(f"     ... 还有 {len(possible_harmonics) - 5} 个谐波")
        else:
            print(f"     ❌ 无可能的谐波 (基频过高)")

if __name__ == "__main__":
    corrected_harmonic_calculation()
    demonstrate_frequency_limits()
