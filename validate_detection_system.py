#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证检测系统在test20250717和待定文件夹的检测结果
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class AudioDetectionValidator:
    def __init__(self):
        # 加载阈值数据
        self.threshold_df = pd.read_csv('segment_feature_thresholds.csv')
        
        # 最有用的5个特征
        self.top_features = [
            'vl_energy_mean',
            'harmonic_total_ratio', 
            'harmonic_3_ratio',
            'thd',
            'harmonic_4_ratio'
        ]
        
        print("🔧 检测系统初始化完成")
        print(f"📊 加载了{len(self.threshold_df)}个频段的阈值数据")
    
    def extract_segment_features(self, audio_path, segment_idx, start_time, end_time, expected_freq):
        """提取单个频段的特征（简化版）"""
        try:
            import librosa
            from scipy.signal import stft, find_peaks
            from scipy import stats
            
            # 加载音频
            y, sr = librosa.load(audio_path, sr=48000)
            if sr != 48000:
                y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            
            # 提取频段音频
            start_sample = int(start_time * 48000)
            end_sample = int(end_time * 48000)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            features = {}
            
            # 1. 竖线能量特征
            try:
                f, t, Zxx = stft(segment_audio, 48000, nperseg=1024, noverlap=512)
                power_spectrum = np.abs(Zxx)**2
                power_db = 10 * np.log10(power_spectrum + 1e-12)
                total_energy = np.sum(power_db, axis=0)
                features['vl_energy_mean'] = np.mean(total_energy)
            except:
                features['vl_energy_mean'] = 0
            
            # 2. 谐波失真特征
            try:
                # FFT分析
                fft = np.fft.fft(segment_audio)
                freqs = np.fft.fftfreq(len(segment_audio), 1/48000)
                magnitude = np.abs(fft)
                
                # 只考虑正频率
                positive_freqs = freqs[:len(freqs)//2]
                positive_magnitude = magnitude[:len(magnitude)//2]
                
                # 基频能量
                fundamental_tolerance = 20  # Hz
                fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
                if np.any(fundamental_mask):
                    fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
                else:
                    fundamental_energy = 0
                
                # 谐波能量
                harmonic_energies = []
                for harmonic in range(2, 6):  # 2-5次谐波
                    harmonic_freq = expected_freq * harmonic
                    if harmonic_freq < 48000 / 2:
                        harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                        if np.any(harmonic_mask):
                            harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                            harmonic_energies.append(harmonic_energy)
                        else:
                            harmonic_energies.append(0)
                
                # THD计算
                total_harmonic_energy = sum(harmonic_energies)
                total_energy = np.sum(positive_magnitude**2)
                
                if fundamental_energy > 0:
                    features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
                    features['harmonic_3_ratio'] = harmonic_energies[1] / fundamental_energy if len(harmonic_energies) > 1 else 0
                    features['harmonic_4_ratio'] = harmonic_energies[2] / fundamental_energy if len(harmonic_energies) > 2 else 0
                else:
                    features['thd'] = 0
                    features['harmonic_3_ratio'] = 0
                    features['harmonic_4_ratio'] = 0
                
                features['harmonic_total_ratio'] = total_harmonic_energy / total_energy if total_energy > 0 else 0
                
            except:
                features['thd'] = 0
                features['harmonic_total_ratio'] = 0
                features['harmonic_3_ratio'] = 0
                features['harmonic_4_ratio'] = 0
            
            return features
            
        except Exception as e:
            print(f"提取频段{segment_idx}特征失败: {e}")
            return None
    
    def detect_audio_file(self, audio_path):
        """检测单个音频文件"""
        try:
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            segment_results = []
            anomaly_segments = 0
            total_segments = len(step_boundaries)
            
            # 检测每个频段
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                # 提取特征
                features = self.extract_segment_features(audio_path, seg_idx, start_time, end_time, expected_freq)
                
                if features is None:
                    continue
                
                # 获取该频段的阈值
                segment_threshold = self.threshold_df[self.threshold_df['segment_idx'] == seg_idx]
                
                if len(segment_threshold) == 0:
                    continue
                
                segment_threshold = segment_threshold.iloc[0]
                
                # 检测异常
                anomaly_votes = []
                feature_details = {}
                
                for feature in self.top_features:
                    if feature not in features:
                        continue
                    
                    feature_value = features[feature]
                    separable_key = f'{feature}_separable'
                    threshold_key = f'{feature}_threshold'
                    
                    if separable_key in segment_threshold and segment_threshold[separable_key]:
                        threshold = segment_threshold[threshold_key]
                        is_anomaly = feature_value >= threshold
                        anomaly_votes.append(is_anomaly)
                        
                        feature_details[feature] = {
                            'value': feature_value,
                            'threshold': threshold,
                            'is_anomaly': is_anomaly,
                            'separable': True
                        }
                    else:
                        feature_details[feature] = {
                            'value': feature_value,
                            'threshold': None,
                            'is_anomaly': None,
                            'separable': False
                        }
                
                # 频段级判定
                if len(anomaly_votes) > 0:
                    segment_anomaly_ratio = sum(anomaly_votes) / len(anomaly_votes)
                    is_segment_anomaly = segment_anomaly_ratio >= 0.4  # 40%以上特征异常
                else:
                    segment_anomaly_ratio = 0
                    is_segment_anomaly = False
                
                if is_segment_anomaly:
                    anomaly_segments += 1
                
                segment_results.append({
                    'segment_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'start_time': start_time,
                    'end_time': end_time,
                    'anomaly_ratio': segment_anomaly_ratio,
                    'is_anomaly': is_segment_anomaly,
                    'separable_features': len(anomaly_votes),
                    'feature_details': feature_details
                })
            
            # 文件级判定
            if total_segments > 0:
                file_anomaly_ratio = anomaly_segments / total_segments
                # 如果超过20%的频段异常，则判定为负样本
                predicted_label = 'neg' if file_anomaly_ratio >= 0.2 else 'pos'
            else:
                file_anomaly_ratio = 0
                predicted_label = 'unknown'
            
            return {
                'status': 'success',
                'predicted_label': predicted_label,
                'file_anomaly_ratio': file_anomaly_ratio,
                'anomaly_segments': anomaly_segments,
                'total_segments': total_segments,
                'segment_results': segment_results
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }

def validate_detection_system():
    """验证检测系统"""
    print("🔍 验证检测系统在test20250717和待定文件夹的检测结果")
    print("="*70)
    
    # 初始化检测器
    detector = AudioDetectionValidator()
    
    # 定义测试文件夹
    test_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美", 
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ],
        'pending': [
            r"../待定"
        ]
    }
    
    validation_results = []
    
    # 测试所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)
            
            print(f"\n📁 测试文件夹: {folder_name} ({true_label}) - {len(wav_files)}个文件")
            print("-" * 60)
            
            for i, audio_path in enumerate(wav_files):
                filename = os.path.basename(audio_path)
                
                print(f"  🎵 [{i+1}/{len(wav_files)}] {filename}")
                
                # 检测
                result = detector.detect_audio_file(audio_path)
                
                if result['status'] == 'success':
                    predicted_label = result['predicted_label']
                    file_anomaly_ratio = result['file_anomaly_ratio']
                    anomaly_segments = result['anomaly_segments']
                    total_segments = result['total_segments']
                    
                    # 判定正确性
                    if true_label == 'pending':
                        is_correct = None  # 待定样本无法判定正确性
                        correctness_icon = "❓"
                    else:
                        is_correct = predicted_label == true_label
                        correctness_icon = "✅" if is_correct else "❌"
                    
                    # 显示结果
                    print(f"     {correctness_icon} 预测: {predicted_label}, 实际: {true_label}")
                    print(f"        异常频段: {anomaly_segments}/{total_segments} ({file_anomaly_ratio:.3f})")
                    
                    # 保存结果
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'is_correct': is_correct,
                        'file_anomaly_ratio': file_anomaly_ratio,
                        'anomaly_segments': anomaly_segments,
                        'total_segments': total_segments,
                        'status': 'success'
                    })
                    
                else:
                    print(f"     ❌ 检测失败: {result['error']}")
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': 'failed',
                        'is_correct': False,
                        'file_anomaly_ratio': 0,
                        'anomaly_segments': 0,
                        'total_segments': 0,
                        'status': 'failed',
                        'error': result['error']
                    })
    
    # 生成验证报告
    generate_validation_report(validation_results)
    
    return validation_results

def generate_validation_report(results):
    """生成验证报告"""
    print(f"\n" + "="*70)
    print(f"📊 检测系统验证报告")
    print("="*70)
    
    df = pd.DataFrame(results)
    
    # 总体统计
    total_files = len(df)
    success_files = len(df[df['status'] == 'success'])
    failed_files = total_files - success_files
    
    print(f"\n📊 总体统计:")
    print(f"  总文件数: {total_files}")
    print(f"  成功检测: {success_files} ({success_files/total_files*100:.1f}%)")
    print(f"  检测失败: {failed_files} ({failed_files/total_files*100:.1f}%)")
    
    # 准确率统计（排除待定样本）
    known_samples = df[(df['true_label'] != 'pending') & (df['status'] == 'success')]
    
    if len(known_samples) > 0:
        correct_predictions = known_samples[known_samples['is_correct'] == True]
        accuracy = len(correct_predictions) / len(known_samples)
        
        print(f"\n📊 准确率统计 (排除待定样本):")
        print(f"  已知样本数: {len(known_samples)}")
        print(f"  正确预测: {len(correct_predictions)}")
        print(f"  总体准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
        
        # 按类别统计
        for label in ['pos', 'neg']:
            label_samples = known_samples[known_samples['true_label'] == label]
            if len(label_samples) > 0:
                label_correct = label_samples[label_samples['is_correct'] == True]
                label_accuracy = len(label_correct) / len(label_samples)
                print(f"  {label}样本准确率: {label_accuracy:.3f} ({len(label_correct)}/{len(label_samples)})")
    
    # 按文件夹统计
    print(f"\n📊 按文件夹统计:")
    print("-" * 50)
    
    for folder in df['folder'].unique():
        folder_data = df[df['folder'] == folder]
        folder_success = folder_data[folder_data['status'] == 'success']
        
        if len(folder_success) > 0:
            true_label = folder_data['true_label'].iloc[0]
            
            if true_label != 'pending':
                folder_correct = folder_success[folder_success['is_correct'] == True]
                folder_accuracy = len(folder_correct) / len(folder_success)
                accuracy_text = f"准确率: {folder_accuracy:.3f}"
            else:
                accuracy_text = "待定样本"
            
            print(f"  {folder} ({true_label}): {len(folder_success)}/{len(folder_data)}个成功, {accuracy_text}")
    
    # 异常检测统计
    print(f"\n📊 异常检测统计:")
    print("-" * 50)
    
    success_data = df[df['status'] == 'success']
    
    if len(success_data) > 0:
        print(f"  平均异常频段比例: {success_data['file_anomaly_ratio'].mean():.3f}")
        print(f"  异常频段比例范围: {success_data['file_anomaly_ratio'].min():.3f} - {success_data['file_anomaly_ratio'].max():.3f}")
        
        # 按预测标签统计
        for pred_label in ['pos', 'neg']:
            pred_data = success_data[success_data['predicted_label'] == pred_label]
            if len(pred_data) > 0:
                avg_ratio = pred_data['file_anomaly_ratio'].mean()
                print(f"  预测为{pred_label}的平均异常比例: {avg_ratio:.3f}")
    
    # 错误案例分析
    error_cases = known_samples[known_samples['is_correct'] == False]
    
    if len(error_cases) > 0:
        print(f"\n📊 错误案例分析:")
        print("-" * 50)
        
        for _, case in error_cases.iterrows():
            print(f"  ❌ {case['filename']}")
            print(f"     真实: {case['true_label']}, 预测: {case['predicted_label']}")
            print(f"     异常比例: {case['file_anomaly_ratio']:.3f}")
    
    # 待定样本分析
    pending_samples = df[df['true_label'] == 'pending']
    
    if len(pending_samples) > 0:
        print(f"\n📊 待定样本分析:")
        print("-" * 50)
        
        for _, sample in pending_samples.iterrows():
            if sample['status'] == 'success':
                print(f"  ❓ {sample['filename']}")
                print(f"     预测: {sample['predicted_label']}")
                print(f"     异常比例: {sample['file_anomaly_ratio']:.3f}")
    
    # 保存验证结果
    df.to_csv('detection_validation_results.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 验证结果已保存: detection_validation_results.csv")

if __name__ == "__main__":
    validation_results = validate_detection_system()
