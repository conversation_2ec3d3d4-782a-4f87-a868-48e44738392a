#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试test20250717和待定两个文件夹的样本对比
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def test_two_folders_comparison():
    """测试test20250717和待定两个文件夹的样本对比"""
    print("🔍 测试test20250717和待定两个文件夹的样本对比")
    print("="*70)
    
    # 定义测试文件夹
    folders = {
        'test20250717_pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'test20250717_neg': [
            r"../test20250717/neg"
        ],
        'pending': [
            r"../待定"
        ]
    }
    
    results = []
    
    # 测试所有文件夹
    for category, folder_list in folders.items():
        for folder in folder_list:
            print(f"\n📁 测试文件夹: {folder} ({category})")
            print("-" * 60)
            
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            # 获取所有wav文件
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            print(f"找到 {len(wav_files)} 个wav文件")
            
            # 根据文件夹大小决定测试数量
            test_count = min(3, len(wav_files)) if len(wav_files) > 5 else len(wav_files)
            
            for i, audio_path in enumerate(wav_files[:test_count]):
                filename = os.path.basename(audio_path)
                folder_name = os.path.basename(folder)
                
                print(f"\n🎵 [{i+1}/{test_count}] 测试文件: {filename}")
                
                try:
                    # 调用优化的频率分割算法
                    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                        audio_path,
                        min_duration=153,
                        plot=False,  # 不绘图，加快测试速度
                        debug=False,  # 减少输出
                        search_window_start=0.1,
                        search_window_end=1.5,
                        correlation_length=1.0
                    )
                    
                    # 提取关键信息
                    start_offset = alignment_info.get('start_offset', 0)
                    correlation_score = alignment_info.get('correlation_score', 0)
                    alignment_quality = alignment_info.get('alignment_quality', {})
                    
                    # 计算质量指标
                    overall_quality = alignment_quality.get('overall_quality', 'unknown')
                    composite_score = alignment_quality.get('composite_score', 0)
                    time_correlation = alignment_quality.get('time_correlation', 0)
                    freq_similarity = alignment_quality.get('freq_similarity', 0)
                    
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': start_offset,
                        'correlation_score': correlation_score,
                        'overall_quality': overall_quality,
                        'composite_score': composite_score,
                        'time_correlation': time_correlation,
                        'freq_similarity': freq_similarity,
                        'step_count': len(step_bounds),
                        'freq_count': len(freq_table),
                        'status': 'success'
                    }
                    
                    print(f"  ✅ 成功: 开始时间={start_offset:.3f}s, 相关性={correlation_score:.3f}")
                    print(f"     质量={overall_quality}, 评分={composite_score:.3f}")
                    
                except Exception as e:
                    print(f"  ❌ 失败: {str(e)}")
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': 0,
                        'correlation_score': 0,
                        'overall_quality': 'failed',
                        'composite_score': 0,
                        'time_correlation': 0,
                        'freq_similarity': 0,
                        'step_count': 0,
                        'freq_count': 0,
                        'status': 'failed',
                        'error': str(e)
                    }
                
                results.append(result)
    
    # 生成对比分析
    if not results:
        print("❌ 没有测试结果")
        return
    
    df = pd.DataFrame(results)
    success_df = df[df['status'] == 'success']
    
    # 创建对比可视化图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('test20250717 vs 待定文件夹样本对比分析', fontsize=16, fontweight='bold')
    
    # 1. 成功率对比
    ax1 = axes[0, 0]
    success_rate = df.groupby('category')['status'].apply(lambda x: (x == 'success').mean() * 100)
    colors = ['green', 'red', 'orange']
    bars = ax1.bar(success_rate.index, success_rate.values, color=colors, alpha=0.7)
    ax1.set_title('成功率对比')
    ax1.set_ylabel('成功率 (%)')
    ax1.set_ylim(0, 100)
    ax1.tick_params(axis='x', rotation=45)
    for bar, rate in zip(bars, success_rate.values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 2. 开始时间分布对比
    ax2 = axes[0, 1]
    if len(success_df) > 0:
        for i, category in enumerate(success_df['category'].unique()):
            cat_data = success_df[success_df['category'] == category]
            color = ['green', 'red', 'orange'][i]
            ax2.hist(cat_data['start_offset'], alpha=0.6, label=f'{category} (n={len(cat_data)})', 
                    bins=8, color=color)
        ax2.set_title('开始时间分布对比')
        ax2.set_xlabel('开始时间 (秒)')
        ax2.set_ylabel('频次')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 相关性分布对比
    ax3 = axes[0, 2]
    if len(success_df) > 0:
        for i, category in enumerate(success_df['category'].unique()):
            cat_data = success_df[success_df['category'] == category]
            color = ['green', 'red', 'orange'][i]
            ax3.hist(cat_data['correlation_score'], alpha=0.6, label=f'{category} (n={len(cat_data)})', 
                    bins=8, color=color)
        ax3.set_title('相关性分布对比')
        ax3.set_xlabel('相关性系数')
        ax3.set_ylabel('频次')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 添加质量阈值线
        ax3.axvline(0.8, color='orange', linestyle='--', alpha=0.8, label='高质量阈值')
        ax3.axvline(0.6, color='yellow', linestyle='--', alpha=0.8, label='中等质量阈值')
    
    # 4. 箱线图：相关性对比
    ax4 = axes[1, 0]
    if len(success_df) > 0:
        categories = success_df['category'].unique()
        data_for_box = [success_df[success_df['category'] == cat]['correlation_score'].values 
                       for cat in categories]
        box_plot = ax4.boxplot(data_for_box, labels=categories, patch_artist=True)
        
        # 设置颜色
        colors = ['lightgreen', 'lightcoral', 'lightsalmon']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
        
        ax4.set_title('相关性分布箱线图')
        ax4.set_ylabel('相关性系数')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
    
    # 5. 散点图：开始时间 vs 相关性
    ax5 = axes[1, 1]
    if len(success_df) > 0:
        for i, category in enumerate(success_df['category'].unique()):
            cat_data = success_df[success_df['category'] == category]
            color = ['green', 'red', 'orange'][i]
            ax5.scatter(cat_data['start_offset'], cat_data['correlation_score'], 
                       label=category, alpha=0.7, s=80, color=color)
        ax5.set_title('开始时间 vs 相关性')
        ax5.set_xlabel('开始时间 (秒)')
        ax5.set_ylabel('相关性系数')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
    
    # 6. 质量分布对比
    ax6 = axes[1, 2]
    if len(success_df) > 0:
        quality_data = []
        quality_labels = []
        
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            high_quality = len(cat_data[cat_data['correlation_score'] >= 0.8])
            medium_quality = len(cat_data[(cat_data['correlation_score'] >= 0.6) & 
                                        (cat_data['correlation_score'] < 0.8)])
            low_quality = len(cat_data[cat_data['correlation_score'] < 0.6])
            
            quality_data.extend([high_quality, medium_quality, low_quality])
            quality_labels.extend([f'{category}\n高质量', f'{category}\n中等', f'{category}\n低质量'])
        
        # 重新组织数据用于分组条形图
        categories = success_df['category'].unique()
        high_counts = []
        medium_counts = []
        low_counts = []
        
        for category in categories:
            cat_data = success_df[success_df['category'] == category]
            high_counts.append(len(cat_data[cat_data['correlation_score'] >= 0.8]))
            medium_counts.append(len(cat_data[(cat_data['correlation_score'] >= 0.6) & 
                                            (cat_data['correlation_score'] < 0.8)]))
            low_counts.append(len(cat_data[cat_data['correlation_score'] < 0.6]))
        
        x = np.arange(len(categories))
        width = 0.25
        
        ax6.bar(x - width, high_counts, width, label='高质量(≥0.8)', color='green', alpha=0.7)
        ax6.bar(x, medium_counts, width, label='中等质量(0.6-0.8)', color='orange', alpha=0.7)
        ax6.bar(x + width, low_counts, width, label='低质量(<0.6)', color='red', alpha=0.7)
        
        ax6.set_title('质量分布对比')
        ax6.set_ylabel('样本数量')
        ax6.set_xticks(x)
        ax6.set_xticklabels(categories, rotation=45)
        ax6.legend()
        ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('two_folders_comparison_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成详细统计报告
    print("\n" + "="*70)
    print("📊 两个文件夹对比统计报告")
    print("="*70)
    
    for category in df['category'].unique():
        cat_data = df[df['category'] == category]
        success_data = cat_data[cat_data['status'] == 'success']
        
        print(f"\n📁 {category.upper()} 类别:")
        print(f"  总样本数: {len(cat_data)}")
        print(f"  成功样本数: {len(success_data)}")
        print(f"  成功率: {len(success_data)/len(cat_data)*100:.1f}%")
        
        if len(success_data) > 0:
            print(f"  开始时间统计:")
            print(f"    平均值: {success_data['start_offset'].mean():.3f}s")
            print(f"    标准差: {success_data['start_offset'].std():.3f}s")
            print(f"    范围: {success_data['start_offset'].min():.3f}s - {success_data['start_offset'].max():.3f}s")
            
            print(f"  相关性统计:")
            print(f"    平均值: {success_data['correlation_score'].mean():.3f}")
            print(f"    标准差: {success_data['correlation_score'].std():.3f}")
            print(f"    范围: {success_data['correlation_score'].min():.3f} - {success_data['correlation_score'].max():.3f}")
            
            # 质量分析
            high_quality = len(success_data[success_data['correlation_score'] >= 0.8])
            medium_quality = len(success_data[(success_data['correlation_score'] >= 0.6) & 
                                            (success_data['correlation_score'] < 0.8)])
            low_quality = len(success_data[success_data['correlation_score'] < 0.6])
            
            print(f"  质量分布:")
            print(f"    高质量 (≥0.8): {high_quality}个 ({high_quality/len(success_data)*100:.1f}%)")
            print(f"    中等质量 (0.6-0.8): {medium_quality}个 ({medium_quality/len(success_data)*100:.1f}%)")
            print(f"    低质量 (<0.6): {low_quality}个 ({low_quality/len(success_data)*100:.1f}%)")
    
    # 跨类别对比分析
    if len(success_df) > 0:
        print(f"\n🔍 跨类别对比分析:")
        print("-" * 50)
        
        # 相关性对比
        pos_data = success_df[success_df['category'] == 'test20250717_pos']
        neg_data = success_df[success_df['category'] == 'test20250717_neg']
        pending_data = success_df[success_df['category'] == 'pending']
        
        if len(pos_data) > 0 and len(neg_data) > 0:
            print(f"  正样本 vs 负样本:")
            print(f"    平均相关性差异: {pos_data['correlation_score'].mean() - neg_data['correlation_score'].mean():.3f}")
            print(f"    时间一致性对比: pos={pos_data['start_offset'].std():.3f}s, neg={neg_data['start_offset'].std():.3f}s")
        
        if len(pending_data) > 0:
            print(f"  待定样本特征:")
            print(f"    平均相关性: {pending_data['correlation_score'].mean():.3f}")
            print(f"    时间一致性: {pending_data['start_offset'].std():.3f}s")
            
            # 判断待定样本更接近哪一类
            if len(pos_data) > 0 and len(neg_data) > 0:
                pos_similarity = abs(pending_data['correlation_score'].mean() - pos_data['correlation_score'].mean())
                neg_similarity = abs(pending_data['correlation_score'].mean() - neg_data['correlation_score'].mean())
                
                if pos_similarity < neg_similarity:
                    print(f"    🎯 待定样本更接近正样本 (相关性差异: {pos_similarity:.3f})")
                else:
                    print(f"    🎯 待定样本更接近负样本 (相关性差异: {neg_similarity:.3f})")
    
    # 保存结果
    output_file = "two_folders_comparison_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    print(f"📊 可视化图表已保存到: two_folders_comparison_analysis.png")
    
    return df

if __name__ == "__main__":
    results_df = test_two_folders_comparison()
