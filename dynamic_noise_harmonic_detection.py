#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态噪声谐波检测：使用之前成功的动态噪声估算算法
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def dynamic_noise_harmonic_detection():
    """动态噪声谐波检测"""
    print("🎯 动态噪声谐波检测 - 低音戳洞文件前4个频段")
    print("="*50)
    print("动态噪声算法:")
    print("1. 滑动窗口局部噪声估计")
    print("2. <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>平滑滤波")
    print("3. 噪声变化自适应SNR阈值")
    print("4. 频率相关阈值调整")
    print("5. 严格的谐波验证")
    print("="*50)
    
    # 低音戳洞文件
    hole_file = r'test20250717\neg\录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    # hole_file = r'test20250717\pos\完美\ok1.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析文件
    hole_data = analyze_with_dynamic_noise(hole_file)
    
    if hole_data:
        create_dynamic_noise_visualization(hole_data)

def analyze_with_dynamic_noise(audio_path):
    """使用动态噪声分析"""
    try:
        print(f"\n使用动态噪声算法分析...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前4段
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 超高分辨率FFT分析
            fft_size = 131072  # 128k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            power_db = 10 * np.log10(power + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            print(f"  分析频段 {seg_idx} ({expected_freq:.0f}Hz)")
            
            # 1. 主频分析
            fundamental_analysis = analyze_fundamental_dynamic(display_freqs, display_power, expected_freq)
            
            # 2. 动态噪声估计
            dynamic_noise_analysis = estimate_dynamic_noise(display_freqs, display_power, fundamental_analysis)
            
            # 3. 动态谐波检测
            harmonic_analysis = detect_harmonics_dynamic(
                display_freqs, display_power, fundamental_analysis, dynamic_noise_analysis, sr
            )
            
            if fundamental_analysis:
                fluctuation_features = dynamic_noise_analysis['noise_fluctuation_features']
                print(f"    主频: {fundamental_analysis['freq']:.3f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
                print(f"    全局噪声底噪: {dynamic_noise_analysis['global_noise_floor_db']:.1f}dB")
                print(f"    噪声变化范围: {dynamic_noise_analysis['noise_variation_db']:.1f}dB")
                print(f"    噪声稳定性评分: {fluctuation_features['fluctuation_stability_score']:.3f}")
                print(f"    噪声波动标准差: {fluctuation_features['fluctuation_std']:.2f}dB")
                print(f"    动态检测谐波: {len(harmonic_analysis)}个")
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': display_freqs,
                'power': display_power,
                'power_db': display_power_db,
                'fundamental_analysis': fundamental_analysis,
                'dynamic_noise_analysis': dynamic_noise_analysis,
                'harmonic_analysis': harmonic_analysis,
                'freq_resolution': display_freqs[1] - display_freqs[0]
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def analyze_fundamental_dynamic(freqs, power, expected_freq):
    """动态主频分析"""
    
    # 搜索范围
    search_bandwidth = 2.0  # ±2Hz
    search_mask = (freqs >= expected_freq - search_bandwidth) & (freqs <= expected_freq + search_bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""

    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_trend': 0.0,
            'fluctuation_smoothness': 1.0,
            'fluctuation_stability_score': 1.0
        }

    noise_array = np.array(noise_levels)

    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)

    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)

    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0

    # 4. 趋势 - 线性回归斜率
    if len(freq_centers) == len(noise_levels):
        try:
            # 计算频率与噪声的线性关系
            freq_array = np.array(freq_centers)
            coeffs = np.polyfit(freq_array, noise_array, 1)
            fluctuation_trend = coeffs[0]  # 斜率，正值表示噪声随频率增加
        except:
            fluctuation_trend = 0.0
    else:
        fluctuation_trend = 0.0

    # 5. 平滑度 - 相邻点差值的平均
    if len(noise_array) > 1:
        adjacent_diffs = np.abs(np.diff(noise_array))
        fluctuation_smoothness = 1.0 / (1.0 + np.mean(adjacent_diffs))  # 值越大越平滑
    else:
        fluctuation_smoothness = 1.0

    # 6. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    # 基于标准差和变异系数的综合评分
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)  # 标准差越小分数越高
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)  # 变异系数越小分数越高
    fluctuation_stability_score = (std_score + coeff_score) / 2.0

    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_trend': fluctuation_trend,
        'fluctuation_smoothness': fluctuation_smoothness,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise(freqs, power, fundamental_analysis):
    """动态噪声估计 - 使用滑动窗口"""
    
    if not fundamental_analysis:
        return {'global_noise_floor_db': -80}
    
    fundamental_freq = fundamental_analysis['freq']
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和明显的谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            local_noise = np.percentile(window_noise_powers, 25)  # 25th percentile
            local_noise_db = 10 * np.log10(local_noise + 1e-12)
            
            local_noise_levels.append(local_noise_db)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return {'global_noise_floor_db': -80}
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 插值到完整频率网格
    local_noise_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(local_noise_levels, 20)
    noise_variation_db = np.max(local_noise_levels) - np.min(local_noise_levels)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(local_noise_levels, window_centers)

    return {
        'global_noise_floor_db': global_noise_floor_db,
        'local_noise_db': local_noise_interp,
        'noise_variation_db': noise_variation_db,
        'window_centers': window_centers,
        'local_noise_levels': local_noise_levels,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_dynamic(freqs, power, fundamental_analysis, dynamic_noise_analysis, sr):
    """动态谐波检测"""
    
    if not fundamental_analysis:
        return []
    
    fundamental_freq = fundamental_analysis['freq']
    fundamental_power_db = fundamental_analysis['power_db']
    global_noise_floor_db = dynamic_noise_analysis['global_noise_floor_db']
    local_noise_db = dynamic_noise_analysis['local_noise_db']
    noise_variation_db = dynamic_noise_analysis['noise_variation_db']
    
    detected_harmonics = []
    
    # 根据噪声变化和波动特征调整SNR阈值
    fluctuation_features = dynamic_noise_analysis['noise_fluctuation_features']
    stability_score = fluctuation_features['fluctuation_stability_score']
    fluctuation_std = fluctuation_features['fluctuation_std']

    # 基础SNR阈值调整
    if noise_variation_db > 10:
        base_snr_threshold = 8.0  # 噪声变化大时降低要求
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0

    # 根据噪声稳定性进一步调整
    if stability_score > 0.8:  # 噪声很稳定
        stability_adjustment = 1.0  # 可以提高要求
    elif stability_score > 0.6:  # 噪声中等稳定
        stability_adjustment = 0.0  # 保持基础要求
    else:  # 噪声不稳定
        stability_adjustment = -1.0  # 降低要求

    base_snr_threshold += stability_adjustment
    
    print(f"      动态谐波检测 (全局噪声: {global_noise_floor_db:.1f}dB, 变化: {noise_variation_db:.1f}dB, 稳定性: {stability_score:.2f}, SNR阈值: {base_snr_threshold:.1f}dB):")
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= sr/2:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 获取该频率的局部噪声
        local_noise_at_freq = local_noise_db[actual_idx]
        
        # 计算各种指标
        global_snr_db = harmonic_power_db - global_noise_floor_db
        local_snr_db = harmonic_power_db - local_noise_at_freq
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 动态谐波判断条件
        conditions = {
            'local_snr_sufficient': local_snr_db >= adjusted_snr_threshold,
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db,
                'local_snr_db': local_snr_db,
                'local_noise_db': local_noise_at_freq,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
            
            print(f"        ✅ {order:2d}次谐波: {actual_freq:7.1f}Hz, {harmonic_power_db:6.1f}dB, "
                  f"局部SNR={local_snr_db:5.1f}dB, 全局SNR={global_snr_db:5.1f}dB")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"        ❌ {order:2d}次谐波: {actual_freq:7.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            局部SNR={local_snr_db:5.1f}dB, 全局SNR={global_snr_db:5.1f}dB, "
                  f"相对功率={relative_power_db:5.1f}dB, 误差={freq_error:+5.1f}Hz")
    
    return detected_harmonics

def create_dynamic_noise_visualization(hole_data):
    """创建动态噪声可视化"""
    print(f"\n🎨 生成动态噪声检测可视化...")
    
    # 创建图表 (4行1列)
    fig, axes = plt.subplots(4, 1, figsize=(20, 24))
    fig.suptitle(f'Dynamic Noise Harmonic Detection\nFile: {hole_data["filename"]}', 
                 fontsize=16, fontweight='bold')
    
    segments = hole_data['segments']
    
    # 谐波颜色
    harmonic_colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, segment in enumerate(segments):
        ax = axes[i]
        
        fundamental_analysis = segment['fundamental_analysis']
        harmonic_analysis = segment['harmonic_analysis']
        dynamic_noise_analysis = segment['dynamic_noise_analysis']
        
        # 绘制频谱
        ax.plot(segment['freqs'], segment['power_db'], 'k-', linewidth=0.8, alpha=0.7, 
               label='Spectrum')
        
        # 绘制动态噪声曲线
        ax.plot(segment['freqs'], dynamic_noise_analysis['local_noise_db'], 
               color='purple', linewidth=2, alpha=0.8, label='Dynamic Noise Floor')
        
        # 填充噪声变化区域
        noise_min = dynamic_noise_analysis['local_noise_db'] - 3
        noise_max = dynamic_noise_analysis['local_noise_db'] + 3
        ax.fill_between(segment['freqs'], noise_min, noise_max, 
                       color='purple', alpha=0.2, label='Noise Variation')
        
        # 标记全局噪声底噪
        global_noise_db = dynamic_noise_analysis['global_noise_floor_db']
        ax.axhline(y=global_noise_db, color='gray', linestyle='--', alpha=0.6, 
                  label=f'Global Noise Floor {global_noise_db:.1f}dB')
        
        # 标记主频
        if fundamental_analysis:
            main_freq = fundamental_analysis['freq']
            main_power = fundamental_analysis['power_db']
            
            ax.plot(main_freq, main_power, 'ro', markersize=12, 
                   label=f'Fundamental {main_freq:.2f}Hz ({main_power:.1f}dB)')
            
            # 主频标注
            ax.annotate(f'Fundamental\n{main_freq:.2f}Hz\n{main_power:.1f}dB', 
                       xy=(main_freq, main_power), 
                       xytext=(main_freq + 1000, main_power + 10),
                       ha='left', va='bottom', fontweight='bold', color='red', fontsize=10,
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记动态检测的谐波
        for j, harmonic in enumerate(harmonic_analysis):
            color = harmonic_colors[j % len(harmonic_colors)]
            order = harmonic['order']
            freq = harmonic['freq']
            power_db = harmonic['power_db']
            local_snr_db = harmonic['local_snr_db']
            local_noise_db = harmonic['local_noise_db']
            
            # 谐波标记
            ax.plot(freq, power_db, 's', color=color, markersize=10, alpha=0.9,
                   label=f'{order}th Harmonic {freq:.0f}Hz' if j < 8 else "")
            
            # 绘制局部SNR连线
            ax.plot([freq, freq], [local_noise_db, power_db], 
                   color=color, linestyle=':', linewidth=2, alpha=0.7)
            
            # 标注谐波
            ax.annotate(f'{order}th\n{freq:.0f}Hz\nLocal SNR={local_snr_db:.1f}dB', 
                       xy=(freq, power_db), 
                       xytext=(freq, power_db + 10),
                       ha='center', va='bottom', fontsize=8, color=color, fontweight='bold',
                       arrowprops=dict(arrowstyle='->', color=color, lw=1.5),
                       bbox=dict(boxstyle='round,pad=0.2', facecolor=color, alpha=0.3))
        
        # 设置图表属性
        expected_freq = segment['expected_freq']
        noise_variation = dynamic_noise_analysis['noise_variation_db']
        stability_score = dynamic_noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
        ax.set_title(f'Segment {segment["seg_idx"]} - Expected: {expected_freq:.0f}Hz, '
                    f'Dynamic Harmonics: {len(harmonic_analysis)}, Noise Variation: {noise_variation:.1f}dB, Stability: {stability_score:.2f}',
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power (dB)')
        
        # 设置x轴范围和刻度
        ax.set_xlim(0, 20000)
        ax.set_xticks([0, 1000, 2000, 5000, 10000, 15000, 20000])
        ax.set_xticklabels(['0', '1k', '2k', '5k', '10k', '15k', '20k'])
        
        # 设置y轴范围
        y_min = np.percentile(segment['power_db'], 1) - 5
        y_max = np.percentile(segment['power_db'], 99) + 20
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8, loc='upper right', ncol=2)
        ax.grid(True, alpha=0.3)
        
        # 添加详细信息文本框
        fluctuation_features = dynamic_noise_analysis['noise_fluctuation_features']
        info_text = f"Dynamic Noise Analysis:\n"
        info_text += f"• Sliding window: 200 bins, step 50\n"
        info_text += f"• Savitzky-Golay smoothing\n"
        info_text += f"• Noise variation: {noise_variation:.1f}dB\n"
        info_text += f"• Stability score: {fluctuation_features['fluctuation_stability_score']:.3f}\n"
        info_text += f"• Fluctuation std: {fluctuation_features['fluctuation_std']:.2f}dB\n"
        info_text += f"• Adaptive SNR threshold\n\n"

        if fundamental_analysis:
            info_text += f"Results:\n"
            info_text += f"Fundamental: {fundamental_analysis['freq']:.3f}Hz\n"
            info_text += f"Frequency Error: {fundamental_analysis['freq_error']:+.3f}Hz\n"
            info_text += f"Dynamic Harmonics: {len(harmonic_analysis)}\n"
            info_text += f"Frequency Resolution: {segment['freq_resolution']:.4f}Hz\n"
            info_text += f"Noise Trend: {fluctuation_features['fluctuation_trend']:+.2e}dB/Hz"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcyan', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'dynamic_noise_harmonic_detection__.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 动态噪声检测可视化已保存: {filename}")
    plt.close()
    
    # 打印详细统计
    print(f"\n📊 动态噪声检测统计:")
    print(f"{'='*70}")
    
    total_harmonics = 0
    
    for i, segment in enumerate(segments):
        harmonic_count = len(segment['harmonic_analysis'])
        total_harmonics += harmonic_count
        noise_variation = segment['dynamic_noise_analysis']['noise_variation_db']
        fluctuation_features = segment['dynamic_noise_analysis']['noise_fluctuation_features']

        print(f"\n频段 {i} ({segment['expected_freq']:.0f}Hz):")
        print(f"  检测到的谐波: {harmonic_count}个")
        print(f"  噪声变化: {noise_variation:.1f}dB")
        print(f"  噪声稳定性: {fluctuation_features['fluctuation_stability_score']:.3f}")
        print(f"  波动标准差: {fluctuation_features['fluctuation_std']:.2f}dB")
        print(f"  波动范围: {fluctuation_features['fluctuation_range']:.2f}dB")
        print(f"  变异系数: {fluctuation_features['fluctuation_coefficient']:.4f}")
        print(f"  噪声趋势: {fluctuation_features['fluctuation_trend']:+.2e}dB/Hz")
        
        if harmonic_count > 0:
            print(f"  谐波详情:")
            for harmonic in segment['harmonic_analysis']:
                print(f"    {harmonic['order']:2d}次: {harmonic['freq']:7.1f}Hz, "
                      f"{harmonic['power_db']:6.1f}dB, 局部SNR={harmonic['local_snr_db']:5.1f}dB, "
                      f"全局SNR={harmonic['global_snr_db']:5.1f}dB")
        else:
            print(f"  未检测到谐波")
    
    print(f"\n总体统计:")
    print(f"  总检测谐波: {total_harmonics}个")
    print(f"  平均每段: {total_harmonics/len(segments):.1f}个")

if __name__ == "__main__":
    dynamic_noise_harmonic_detection()
