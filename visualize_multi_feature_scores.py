#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化test20250717所有样本的93段多特征评分
使用曲率标准差、斜率标准差、峰值数、谷值数的组合评分系统
每段去掉开头和结尾8%后进行计算
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，适合多进程
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import calculate_noise_fluctuation_features

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def analyze_noise_curve_features(noise_levels, window_centers):
    """
    分析噪声曲线的特征用于多特征评分
    """
    
    if len(noise_levels) < 5:
        return {}
    
    # 基本统计特征
    mean_noise = np.mean(noise_levels)
    std_noise = np.std(noise_levels)
    min_noise = np.min(noise_levels)
    max_noise = np.max(noise_levels)
    range_noise = max_noise - min_noise
    
    # 曲线形状特征
    # 1. 计算一阶差分（斜率变化）
    diff1 = np.diff(noise_levels)
    mean_slope = np.mean(diff1)
    std_slope = np.std(diff1)
    
    # 2. 计算二阶差分（曲率变化）
    if len(diff1) > 1:
        diff2 = np.diff(diff1)
        mean_curvature = np.mean(diff2)
        std_curvature = np.std(diff2)
    else:
        mean_curvature = 0
        std_curvature = 0
    
    # 3. 峰值和谷值分析
    # 寻找局部极值
    peaks = []
    valleys = []
    for i in range(1, len(noise_levels) - 1):
        if noise_levels[i] > noise_levels[i-1] and noise_levels[i] > noise_levels[i+1]:
            peaks.append((window_centers[i], noise_levels[i]))
        elif noise_levels[i] < noise_levels[i-1] and noise_levels[i] < noise_levels[i+1]:
            valleys.append((window_centers[i], noise_levels[i]))
    
    return {
        # 基本统计
        'mean_noise': mean_noise,
        'std_noise': std_noise,
        'min_noise': min_noise,
        'max_noise': max_noise,
        'range_noise': range_noise,
        
        # 曲线形状
        'mean_slope': mean_slope,
        'std_slope': std_slope,
        'mean_curvature': mean_curvature,
        'std_curvature': std_curvature,
        
        # 极值特征
        'num_peaks': len(peaks),
        'num_valleys': len(valleys),
        'peak_valley_ratio': len(peaks) / max(len(valleys), 1)
    }

def calculate_multi_feature_score(curve_features):
    """
    基于多特征计算异常评分
    """
    
    if not curve_features:
        return 0
    
    std_curvature = curve_features.get('std_curvature', 0)
    std_slope = curve_features.get('std_slope', 0)
    num_peaks = curve_features.get('num_peaks', 0)
    num_valleys = curve_features.get('num_valleys', 0)
    
    abnormal_score = 0
    
    # 规则1: 曲率标准差 (最强区分特征)
    if std_curvature > 0.3:  # 阈值设在两组均值之间
        abnormal_score += 4  # 最高权重
    elif std_curvature > 0.2:
        abnormal_score += 2
    
    # 规则2: 斜率标准差 (第二强区分特征)
    if std_slope > 0.5:  # 阈值设在两组均值之间
        abnormal_score += 3
    elif std_slope > 0.35:
        abnormal_score += 1
    
    # 规则3: 峰值数量
    if num_peaks > 105:  # 阈值设在两组均值之间
        abnormal_score += 2
    elif num_peaks > 100:
        abnormal_score += 1
    
    # 规则4: 谷值数量
    if num_valleys > 105:  # 阈值设在两组均值之间
        abnormal_score += 2
    elif num_valleys > 100:
        abnormal_score += 1
    
    return abnormal_score

def estimate_dynamic_noise_for_segment_with_features(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声，返回多特征评分
    """
    
    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 分析曲线特征
    curve_features = analyze_noise_curve_features(smoothed_noise, window_centers)
    
    # 计算多特征评分
    multi_feature_score = calculate_multi_feature_score(curve_features)
    
    return {
        'multi_feature_score': multi_feature_score,
        'curve_features': curve_features,
        'smoothed_noise': smoothed_noise,
        'window_centers': window_centers
    }

def analyze_all_segments_multi_features(audio_path):
    """
    分析单个文件所有93段的多特征评分（每段去掉开头和结尾8%）
    """
    
    filename = os.path.basename(audio_path)
    print(f"📁 分析文件: {filename}")
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 分析所有段
        segment_results = []
        
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            # 提取段音频，去掉开头和结尾各8%
            trim_percentage = 0.08  # 去掉开头和结尾各8%
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage
            
            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration
            
            # 确保修剪后的时间段有效
            if trimmed_end_time <= trimmed_start_time:
                continue
            
            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment_with_features(display_freqs, display_power, fundamental_freq)
            
            if noise_analysis:
                segment_results.append({
                    'seg_idx': seg_idx,
                    'freq': expected_freq,
                    'multi_feature_score': noise_analysis['multi_feature_score'],
                    'curve_features': noise_analysis['curve_features'],
                    'smoothed_noise': noise_analysis['smoothed_noise'],
                    'window_centers': noise_analysis['window_centers'],
                    'trimmed_duration': trimmed_end_time - trimmed_start_time,
                    'original_duration': end_time - start_time
                })
        
        return {
            'filename': filename,
            'segment_results': segment_results
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def create_multi_feature_visualization(file_data, output_dir):
    """
    创建单个文件的多特征评分可视化
    """

    if not file_data or not file_data['segment_results']:
        print("❌ 无有效分析结果")
        return None

    filename = file_data['filename']
    segment_results = file_data['segment_results']

    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # 提取文件名（不含路径和扩展名）
    base_filename = os.path.splitext(filename)[0]

    # 判断是否为异常文件
    is_abnormal = any(keyword in filename for keyword in ['低音戳洞', '153632', '155101'])
    status = "异常文件" if is_abnormal else "正常文件"

    fig.suptitle(f'{base_filename}\n93段多特征评分分析 (每段去掉开头结尾8%) - {status}',
                 fontsize=16, fontweight='bold')

    # 提取数据
    frequencies = [result['freq'] for result in segment_results]
    multi_feature_scores = [result['multi_feature_score'] for result in segment_results]

    # 提取各个特征
    std_curvatures = [result['curve_features']['std_curvature'] for result in segment_results]
    std_slopes = [result['curve_features']['std_slope'] for result in segment_results]
    num_peaks = [result['curve_features']['num_peaks'] for result in segment_results]
    num_valleys = [result['curve_features']['num_valleys'] for result in segment_results]

    # 计算统计信息
    mean_score = np.mean(multi_feature_scores)
    std_score = np.std(multi_feature_scores)
    max_score = np.max(multi_feature_scores)
    min_score = np.min(multi_feature_scores)

    # 颜色设置
    color = 'red' if is_abnormal else 'blue'

    # 子图1: 多特征评分随频率变化
    ax1.plot(frequencies, multi_feature_scores, 'o-', color=color, linewidth=2,
             markersize=4, alpha=0.7, label=f'多特征评分')

    # 添加统计线
    ax1.axhline(mean_score, color='green', linestyle='--', linewidth=2,
                alpha=0.8, label=f'平均值: {mean_score:.1f}')

    # 添加异常阈值线
    ax1.axhline(6, color='orange', linestyle='--', linewidth=2,
                alpha=0.8, label='异常阈值: 6')

    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('多特征评分')
    ax1.set_title(f'多特征评分随频率变化\n平均值: {mean_score:.1f} ± {std_score:.1f}, 范围: [{min_score}, {max_score}]')
    ax1.set_xscale('log')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 子图2: 多特征评分分布直方图
    ax2.hist(multi_feature_scores, bins=range(int(max_score)+2), color=color, alpha=0.7, edgecolor='black')
    ax2.axvline(mean_score, color='green', linestyle='--', linewidth=2,
                label=f'平均值: {mean_score:.1f}')
    ax2.axvline(6, color='orange', linestyle='--', linewidth=2,
                label='异常阈值: 6')

    ax2.set_xlabel('多特征评分')
    ax2.set_ylabel('频次')
    ax2.set_title(f'多特征评分分布直方图 (共{len(multi_feature_scores)}段)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 子图3: 各特征对比
    x_pos = np.arange(len(frequencies))
    width = 0.2

    ax3.bar(x_pos - 1.5*width, np.array(std_curvatures)*10, width, label='曲率标准差×10', alpha=0.7)
    ax3.bar(x_pos - 0.5*width, std_slopes, width, label='斜率标准差', alpha=0.7)
    ax3.bar(x_pos + 0.5*width, np.array(num_peaks)/20, width, label='峰值数/20', alpha=0.7)
    ax3.bar(x_pos + 1.5*width, np.array(num_valleys)/20, width, label='谷值数/20', alpha=0.7)

    ax3.set_xlabel('段索引')
    ax3.set_ylabel('特征值 (归一化)')
    ax3.set_title('各特征对比 (前20段)')
    ax3.set_xlim(-1, min(20, len(frequencies)))
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 子图4: 特征相关性散点图
    ax4.scatter(std_curvatures, std_slopes, c=multi_feature_scores, cmap='viridis',
               s=50, alpha=0.7, edgecolors='black')
    ax4.set_xlabel('曲率标准差')
    ax4.set_ylabel('斜率标准差')
    ax4.set_title('曲率标准差 vs 斜率标准差 (颜色=评分)')
    ax4.grid(True, alpha=0.3)

    # 添加颜色条
    cbar = plt.colorbar(ax4.collections[0], ax=ax4)
    cbar.set_label('多特征评分')

    # 添加统计信息文本
    high_score_count = sum(1 for x in multi_feature_scores if x >= 6)
    avg_trimmed_duration = np.mean([r['trimmed_duration'] for r in segment_results])
    avg_original_duration = np.mean([r['original_duration'] for r in segment_results])

    stats_text = f"""多特征评分统计 (8%修剪):
平均评分: {mean_score:.1f}
标准差: {std_score:.1f}
最高评分: {max_score}
最低评分: {min_score}
高评分段数(≥6): {high_score_count}/{len(multi_feature_scores)}
平均修剪后时长: {avg_trimmed_duration:.2f}s
平均原始时长: {avg_original_duration:.2f}s
分类: {status}"""

    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)

    plt.tight_layout()

    # 保存图片
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_path = os.path.join(output_dir, f"{safe_filename}_multi_feature_scores.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_file_multi_features(args):
    """
    处理单个文件的包装函数，用于多进程（多特征评分版本）
    """
    wav_file, output_dir, file_index, total_files = args

    try:
        rel_path = os.path.relpath(wav_file, "test20250717")
        print(f"[{file_index}/{total_files}] 处理: {rel_path}")

        # 分析文件
        file_data = analyze_all_segments_multi_features(wav_file)

        if file_data:
            # 生成可视化
            viz_path = create_multi_feature_visualization(file_data, output_dir)

            if viz_path:
                # 计算平均多特征评分
                multi_feature_scores = [result['multi_feature_score'] for result in file_data['segment_results']]
                mean_score = np.mean(multi_feature_scores)

                # 判断是否为异常文件
                is_abnormal = any(keyword in file_data['filename'] for keyword in ['低音戳洞', '153632', '155101'])

                result = {
                    'success': True,
                    'filename': file_data['filename'],
                    'mean_score': mean_score,
                    'is_abnormal': is_abnormal,
                    'viz_path': viz_path,
                    'num_segments': len(file_data['segment_results']),
                    'high_score_count': sum(1 for x in multi_feature_scores if x >= 6)
                }

                print(f"  ✅ [{file_index}/{total_files}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 平均多特征评分: {mean_score:.1f}")

                return result
            else:
                print(f"  ❌ [{file_index}/{total_files}] 可视化失败")
                return {'success': False, 'filename': os.path.basename(wav_file), 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{file_index}/{total_files}] 分析失败")
            return {'success': False, 'filename': os.path.basename(wav_file), 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{file_index}/{total_files}] 处理异常: {e}")
        return {'success': False, 'filename': os.path.basename(wav_file), 'error': str(e)}

def main():
    """
    主函数 - 分析test20250717目录下所有wav文件的多特征评分
    """

    print("🎯 可视化test20250717所有样本的93段多特征评分")
    print("📝 使用曲率标准差、斜率标准差、峰值数、谷值数的组合评分")
    print("📝 每段去掉开头和结尾8%后进行计算")
    print("="*70)

    # 创建输出目录
    output_dir = "multi_feature_scores_analysis"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 查找所有wav文件
    test_dir = "test20250717"
    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return

    # 递归查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))

    if not wav_files:
        print(f"❌ 在{test_dir}目录下未找到wav文件")
        return

    print(f"🔍 找到{len(wav_files)}个wav文件")

    # 确定进程数
    num_processes = min(cpu_count(), len(wav_files), 8)  # 最多8个进程
    print(f"🚀 使用{num_processes}个进程并行处理")

    # 准备参数
    process_args = [(wav_file, output_dir, i+1, len(wav_files)) for i, wav_file in enumerate(wav_files)]

    # 多进程处理
    start_time = time.time()
    successful_count = 0
    failed_count = 0
    abnormal_files = []
    normal_files = []

    print(f"\n⏱️  开始并行处理...")

    with Pool(processes=num_processes) as pool:
        results = pool.map(process_single_file_multi_features, process_args)

    # 处理结果
    for result in results:
        if result['success']:
            successful_count += 1

            if result['is_abnormal']:
                abnormal_files.append((result['filename'], result['mean_score'], result['high_score_count']))
            else:
                normal_files.append((result['filename'], result['mean_score'], result['high_score_count']))
        else:
            failed_count += 1

    end_time = time.time()
    processing_time = end_time - start_time

    # 生成汇总统计
    print("\n" + "="*70)
    print(f"📊 多进程分析完成统计 (多特征评分版本):")
    print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
    print(f"  🚀 平均每文件: {processing_time/len(wav_files):.1f}秒")
    print(f"  ✅ 成功: {successful_count}个文件")
    print(f"  ❌ 失败: {failed_count}个文件")
    print(f"  📁 输出目录: {output_dir}")
    print(f"  📊 生成图片: {successful_count}张")

    print(f"\n🎯 异常文件汇总 ({len(abnormal_files)}个):")
    abnormal_files.sort(key=lambda x: x[1], reverse=True)  # 按平均评分排序
    for filename, mean_score, high_score_count in abnormal_files:
        short_name = filename[:50] + '...' if len(filename) > 50 else filename
        print(f"  📈 {short_name}: 平均{mean_score:.1f}分, 高分段{high_score_count}个")

    print(f"\n✅ 正常文件汇总 (前10个最高评分):")
    normal_files.sort(key=lambda x: x[1], reverse=True)  # 按平均评分排序
    for filename, mean_score, high_score_count in normal_files[:10]:
        short_name = filename[:50] + '...' if len(filename) > 50 else filename
        print(f"  📊 {short_name}: 平均{mean_score:.1f}分, 高分段{high_score_count}个")

    # 统计分析
    if abnormal_files and normal_files:
        abnormal_scores = [x[1] for x in abnormal_files]
        normal_scores = [x[1] for x in normal_files]

        print(f"\n📈 统计对比 (多特征评分版本):")
        print(f"  异常文件平均评分: {np.mean(abnormal_scores):.1f} ± {np.std(abnormal_scores):.1f}")
        print(f"  正常文件平均评分: {np.mean(normal_scores):.1f} ± {np.std(normal_scores):.1f}")
        print(f"  评分差异: {np.mean(abnormal_scores) - np.mean(normal_scores):.1f}")

    print("="*70)
    print("🎯 多特征评分可视化分析完成！")

if __name__ == "__main__":
    main()
