#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析批量检测结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_batch_results():
    """分析批量检测结果"""
    # 读取结果
    df = pd.read_csv('batch_detection_results.csv')
    
    print("🎯 竖线检测器批量测试结果分析")
    print("="*60)
    
    # 基本统计
    total_samples = len(df)
    successful_samples = len(df[df['detection_status'] == 'success'])
    
    print(f"📊 总体统计:")
    print(f"  总样本数: {total_samples}")
    print(f"  成功检测: {successful_samples}")
    print(f"  检测成功率: {successful_samples/total_samples*100:.1f}%")
    
    # 按样本类型分析
    print(f"\n📈 按样本类型详细分析:")
    
    # 成功检测的样本
    success_df = df[df['detection_status'] == 'success'].copy()
    
    for sample_type in ['positive', 'negative', 'unknown']:
        type_df = success_df[success_df['sample_type'] == sample_type]
        
        if len(type_df) > 0:
            print(f"\n  {sample_type.upper()}样本 ({len(type_df)}个成功检测):")
            print(f"    异常率统计:")
            print(f"      平均值: {type_df['anomaly_rate'].mean():.1f}%")
            print(f"      中位数: {type_df['anomaly_rate'].median():.1f}%")
            print(f"      标准差: {type_df['anomaly_rate'].std():.1f}%")
            print(f"      范围: {type_df['anomaly_rate'].min():.1f}% - {type_df['anomaly_rate'].max():.1f}%")
            
            print(f"    竖线数统计:")
            print(f"      平均值: {type_df['line_count'].mean():.1f}")
            print(f"      中位数: {type_df['line_count'].median():.1f}")
            print(f"      范围: {type_df['line_count'].min()} - {type_df['line_count'].max()}")
            
            # 异常率分布
            zero_anomaly = len(type_df[type_df['anomaly_rate'] == 0])
            low_anomaly = len(type_df[(type_df['anomaly_rate'] > 0) & (type_df['anomaly_rate'] <= 10)])
            mid_anomaly = len(type_df[(type_df['anomaly_rate'] > 10) & (type_df['anomaly_rate'] <= 30)])
            high_anomaly = len(type_df[type_df['anomaly_rate'] > 30])
            
            print(f"    异常率分布:")
            print(f"      0%异常: {zero_anomaly}个 ({zero_anomaly/len(type_df)*100:.1f}%)")
            print(f"      1-10%异常: {low_anomaly}个 ({low_anomaly/len(type_df)*100:.1f}%)")
            print(f"      11-30%异常: {mid_anomaly}个 ({mid_anomaly/len(type_df)*100:.1f}%)")
            print(f"      >30%异常: {high_anomaly}个 ({high_anomaly/len(type_df)*100:.1f}%)")
    
    # 检测性能分析
    print(f"\n🎯 检测性能分析:")
    
    # 正样本检测效果
    pos_df = success_df[success_df['sample_type'] == 'positive']
    if len(pos_df) > 0:
        high_anomaly_pos = len(pos_df[pos_df['anomaly_rate'] > 20])
        print(f"  正样本检测效果:")
        print(f"    高异常率(>20%)样本: {high_anomaly_pos}/{len(pos_df)} ({high_anomaly_pos/len(pos_df)*100:.1f}%)")
        
        # 找出高异常率的正样本
        high_pos = pos_df[pos_df['anomaly_rate'] > 20].sort_values('anomaly_rate', ascending=False)
        print(f"    高异常率正样本TOP5:")
        for i, (_, row) in enumerate(high_pos.head().iterrows()):
            print(f"      {i+1}. {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条竖线)")
    
    # 负样本检测效果
    neg_df = success_df[success_df['sample_type'] == 'negative']
    if len(neg_df) > 0:
        zero_anomaly_neg = len(neg_df[neg_df['anomaly_rate'] == 0])
        low_anomaly_neg = len(neg_df[neg_df['anomaly_rate'] <= 5])
        print(f"  负样本检测效果:")
        print(f"    零异常样本: {zero_anomaly_neg}/{len(neg_df)} ({zero_anomaly_neg/len(neg_df)*100:.1f}%)")
        print(f"    低异常率(≤5%)样本: {low_anomaly_neg}/{len(neg_df)} ({low_anomaly_neg/len(neg_df)*100:.1f}%)")
        
        # 找出误报的负样本
        false_pos = neg_df[neg_df['anomaly_rate'] > 10].sort_values('anomaly_rate', ascending=False)
        if len(false_pos) > 0:
            print(f"    可能误报的负样本:")
            for i, (_, row) in enumerate(false_pos.iterrows()):
                print(f"      {i+1}. {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条竖线)")
    
    # 参数效果分析
    print(f"\n⚙️ 参数效果分析:")
    print(f"  标准差预筛选阈值: 2000")
    print(f"  峰值突出度要求: 0.2σ")
    print(f"  峰值高度要求: 均值+0.3σ")
    print(f"  最小竖线强度: 0.1")
    
    # 区分度分析
    if len(pos_df) > 0 and len(neg_df) > 0:
        pos_mean = pos_df['anomaly_rate'].mean()
        neg_mean = neg_df['anomaly_rate'].mean()
        separation = pos_mean - neg_mean
        
        print(f"\n📏 正负样本区分度:")
        print(f"  正样本平均异常率: {pos_mean:.1f}%")
        print(f"  负样本平均异常率: {neg_mean:.1f}%")
        print(f"  区分度: {separation:.1f}% (越大越好)")
        
        # 重叠分析
        pos_max = pos_df['anomaly_rate'].max()
        neg_max = neg_df['anomaly_rate'].max()
        pos_min = pos_df['anomaly_rate'].min()
        neg_min = neg_df['anomaly_rate'].min()
        
        print(f"  正样本异常率范围: {pos_min:.1f}% - {pos_max:.1f}%")
        print(f"  负样本异常率范围: {neg_min:.1f}% - {neg_max:.1f}%")
        
        # 建议阈值
        if separation > 5:
            suggested_threshold = (pos_mean + neg_mean) / 2
            print(f"  建议分类阈值: {suggested_threshold:.1f}%")
        else:
            print(f"  ⚠️ 正负样本重叠较多，需要优化参数")
    
    return df

def create_visualization(df):
    """创建可视化图表"""
    success_df = df[df['detection_status'] == 'success']
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('竖线检测器批量测试结果分析', fontsize=16)
    
    # 1. 异常率分布
    ax1 = axes[0, 0]
    for sample_type in ['positive', 'negative']:
        type_data = success_df[success_df['sample_type'] == sample_type]['anomaly_rate']
        if len(type_data) > 0:
            ax1.hist(type_data, alpha=0.7, label=f'{sample_type} ({len(type_data)}个)', bins=20)
    ax1.set_xlabel('异常率 (%)')
    ax1.set_ylabel('样本数量')
    ax1.set_title('异常率分布对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 竖线数分布
    ax2 = axes[0, 1]
    for sample_type in ['positive', 'negative']:
        type_data = success_df[success_df['sample_type'] == sample_type]['line_count']
        if len(type_data) > 0:
            ax2.hist(type_data, alpha=0.7, label=f'{sample_type} ({len(type_data)}个)', bins=20)
    ax2.set_xlabel('竖线数量')
    ax2.set_ylabel('样本数量')
    ax2.set_title('竖线数量分布对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 箱线图对比
    ax3 = axes[1, 0]
    box_data = []
    box_labels = []
    for sample_type in ['positive', 'negative']:
        type_data = success_df[success_df['sample_type'] == sample_type]['anomaly_rate']
        if len(type_data) > 0:
            box_data.append(type_data)
            box_labels.append(f'{sample_type}\n({len(type_data)}个)')
    
    if box_data:
        ax3.boxplot(box_data, labels=box_labels)
        ax3.set_ylabel('异常率 (%)')
        ax3.set_title('异常率箱线图对比')
        ax3.grid(True, alpha=0.3)
    
    # 4. 散点图
    ax4 = axes[1, 1]
    for sample_type in ['positive', 'negative']:
        type_df = success_df[success_df['sample_type'] == sample_type]
        if len(type_df) > 0:
            ax4.scatter(type_df['anomaly_rate'], type_df['line_count'], 
                       alpha=0.7, label=f'{sample_type} ({len(type_df)}个)', s=50)
    ax4.set_xlabel('异常率 (%)')
    ax4.set_ylabel('竖线数量')
    ax4.set_title('异常率 vs 竖线数量')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('batch_detection_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n📊 可视化图表已保存: batch_detection_analysis.png")

if __name__ == "__main__":
    df = analyze_batch_results()
    create_visualization(df)
