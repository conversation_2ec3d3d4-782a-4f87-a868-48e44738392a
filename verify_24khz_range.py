#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证频率范围是否扩展到24kHz
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def verify_24khz_range():
    """
    验证频率范围扩展到24kHz的效果
    """
    print("🔍 验证频率范围扩展到24kHz")
    print("="*60)
    
    # 检查输出目录
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    print(f"✅ 输出目录存在: {output_dir}")
    
    # 检查PNG文件
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    print(f"📊 找到 {len(png_files)} 个对数频谱图像文件")
    
    if len(png_files) != 93:
        print(f"⚠️  警告: 期望93个文件，实际找到{len(png_files)}个")
    else:
        print("✅ 文件数量正确: 93个频段")
    
    # 显示文件大小统计
    if png_files:
        file_sizes = []
        for png_file in png_files:
            size = os.path.getsize(png_file)
            file_sizes.append(size)
        
        avg_size = sum(file_sizes) / len(file_sizes)
        min_size = min(file_sizes)
        max_size = max(file_sizes)
        total_size = sum(file_sizes)
        
        print(f"\n📈 文件大小统计:")
        print(f"  平均大小: {avg_size/1024:.1f} KB")
        print(f"  最小文件: {min_size/1024:.1f} KB")
        print(f"  最大文件: {max_size/1024:.1f} KB")
        print(f"  总大小: {total_size/1024/1024:.1f} MB")
    
    # 检查图像规格
    if png_files:
        try:
            with Image.open(png_files[0]) as img:
                width, height = img.size
                print(f"\n🖼️  图像规格:")
                print(f"  尺寸: {width}×{height}px")
                print(f"  模式: {img.mode}")
                print(f"  格式: {img.format}")
        except Exception as e:
            print(f"  ❌ 无法读取图像信息: {e}")
    
    # 显示频率范围覆盖情况
    print(f"\n🎵 频率范围覆盖:")
    frequency_ranges = [
        ("低频段", [0, 10, 20]),
        ("中低频段", [30, 40, 50]),
        ("中频段", [60, 70, 80]),
        ("高频段", [85, 90, 92])
    ]
    
    for range_name, indices in frequency_ranges:
        print(f"  {range_name}:")
        for idx in indices:
            if idx < len(png_files):
                filename = os.path.basename(png_files[idx])
                freq_str = filename.split('_')[3].replace('Hz.png', '')
                file_size = os.path.getsize(png_files[idx]) / 1024
                print(f"    段 {idx:2d}: {freq_str:>6}Hz - {file_size:.1f}KB")
    
    print(f"\n✅ 验证完成!")
    print(f"📁 所有文件保存在: {output_dir}")
    print(f"🎯 当前版本特点:")
    print(f"   ✅ 使用semilogx对数频率轴")
    print(f"   ✅ 频率范围: 10Hz ~ 24000Hz (扩展到24kHz)")
    print(f"   ✅ 纵坐标范围: -80dB ~ 40dB")
    print(f"   ✅ 每段去除首尾8%数据")
    print(f"   ✅ 显示动态噪声阈值曲线 (红色线)")
    print(f"   ✅ 频谱曲线 (黑色线)")
    print(f"   ✅ 主频标记 (蓝色圆点)")
    print(f"   ✅ 多进程加速 (8进程)")
    print(f"   ✅ 与harmonic_detector_api_fast.py参数一致")

def create_24khz_comparison():
    """
    创建24kHz频率范围对比图
    """
    print(f"\n🎨 创建24kHz频率范围对比概览...")
    
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    # 选择6个代表性频段 - 特别关注高频段
    selected_indices = [0, 20, 40, 70, 85, 92]
    
    if len(png_files) >= max(selected_indices) + 1:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, seg_idx in enumerate(selected_indices):
            if i < len(axes) and seg_idx < len(png_files):
                try:
                    img = mpimg.imread(png_files[seg_idx])
                    axes[i].imshow(img)
                    axes[i].axis('off')
                    
                    # 从文件名提取频率
                    basename = os.path.basename(png_files[seg_idx])
                    freq_str = basename.split('_')[3].replace('Hz.png', '')
                    axes[i].set_title(f'段 {seg_idx}: {freq_str}Hz\n频率范围: 10Hz~24kHz', 
                                    fontsize=12, fontweight='bold')
                    
                except Exception as e:
                    axes[i].text(0.5, 0.5, f'无法加载\n段 {seg_idx}', 
                               ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].axis('off')
        
        plt.suptitle('扩展频率范围到24kHz效果展示', fontsize=16, fontweight='bold')
        plt.tight_layout()
        overview_file = os.path.join(output_dir, "24kHz频率范围概览.png")
        plt.savefig(overview_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 概览图已保存: {overview_file}")
    else:
        print(f"❌ 无法创建概览图，文件数量不足")

def analyze_24khz_benefits():
    """
    分析扩展到24kHz的好处
    """
    print(f"\n📊 扩展到24kHz的优势分析:")
    
    print(f"\n🎯 频率覆盖范围:")
    print(f"  ✅ 10Hz ~ 24000Hz: 完整覆盖音频频谱")
    print(f"  ✅ 超声波边缘: 捕获24kHz附近的信息")
    print(f"  ✅ 高频细节: 更好地显示高频噪声特性")
    
    print(f"\n📈 技术优势:")
    print(f"  ✅ 更宽的分析带宽")
    print(f"  ✅ 高频噪声特性可见")
    print(f"  ✅ 完整的频谱信息")
    print(f"  ✅ 符合高质量音频标准")
    
    print(f"\n🔍 应用价值:")
    print(f"  ✅ 高保真音频分析")
    print(f"  ✅ 宽带噪声检测")
    print(f"  ✅ 频谱完整性验证")
    print(f"  ✅ 高频异常识别")
    
    print(f"\n📋 对比分析:")
    print(f"  20kHz范围: 标准音频分析")
    print(f"  24kHz范围: 高质量音频分析")
    print(f"  优势: 4kHz额外带宽，更完整的频谱信息")

def frequency_range_comparison():
    """
    频率范围对比
    """
    print(f"\n⚖️  频率范围对比:")
    
    ranges = [
        ("人耳可听范围", "20Hz ~ 20kHz", "标准"),
        ("CD音质范围", "20Hz ~ 22.05kHz", "高质量"),
        ("当前分析范围", "10Hz ~ 24kHz", "超高质量"),
        ("专业音频范围", "10Hz ~ 96kHz", "专业级")
    ]
    
    print(f"\n📊 不同标准对比:")
    for name, range_str, quality in ranges:
        print(f"  {name}: {range_str} ({quality})")
    
    print(f"\n🎯 我们的选择 (10Hz ~ 24kHz):")
    print(f"  ✅ 覆盖完整的可听频率范围")
    print(f"  ✅ 包含高质量音频的全部信息")
    print(f"  ✅ 适合专业音频分析需求")
    print(f"  ✅ 平衡了分析精度和计算效率")
    
    print(f"\n📈 实际意义:")
    print(f"  - 低频扩展 (10Hz): 捕获超低频信息")
    print(f"  - 高频扩展 (24kHz): 分析高频噪声和失真")
    print(f"  - 完整覆盖: 确保没有遗漏重要频率成分")

if __name__ == "__main__":
    verify_24khz_range()
    create_24khz_comparison()
    analyze_24khz_benefits()
    frequency_range_comparison()
