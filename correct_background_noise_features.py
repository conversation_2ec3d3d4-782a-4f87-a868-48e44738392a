#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新设计正确的背景噪声特征
基于频谱图观察的真实噪声特征
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch
from scipy import stats
import seaborn as sns

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def extract_correct_background_noise_features():
    """提取正确的背景噪声特征"""
    print("🔍 提取正确的背景噪声特征")
    print("="*70)
    print("基于频谱图观察重新设计特征:")
    print("1. 真实底噪水平 - 整个频谱的最低功率")
    print("2. 频谱平坦度 - 能量分布均匀性") 
    print("3. 信号间隙噪声 - 信号频率之间的噪声")
    print("4. 全频段功率密度 - 整体频谱能量密度")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_results = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取正确的背景噪声特征
                correct_features = extract_file_correct_features(audio_path, filename)
                
                if correct_features:
                    correct_features['filename'] = filename
                    correct_features['label'] = true_label
                    correct_features['is_target'] = filename in target_files
                    all_results.append(correct_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 保存结果
    df.to_csv('correct_background_noise_features.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 正确背景噪声特征已保存: correct_background_noise_features.csv")
    
    # 分析结果
    analyze_correct_features_separation(df, target_files)
    
    return df

def extract_file_correct_features(audio_path, filename):
    """提取单个文件的正确背景噪声特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 聚焦前10个频段 (低频段，100-200Hz左右)
        target_segments = list(range(min(10, len(step_boundaries))))
        
        correct_features = {}
        
        # 1. 真实底噪水平特征
        correct_features.update(extract_true_noise_floor(y, sr, step_boundaries, target_segments))
        
        # 2. 频谱平坦度特征
        correct_features.update(extract_spectral_flatness_correct(y, sr, step_boundaries, target_segments))
        
        # 3. 信号间隙噪声特征
        correct_features.update(extract_signal_gap_noise(y, sr, step_boundaries, target_segments, freq_table))
        
        # 4. 全频段功率密度特征
        correct_features.update(extract_power_density_features(y, sr, step_boundaries, target_segments))
        
        return correct_features
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_true_noise_floor(y, sr, step_boundaries, target_segments):
    """提取真实底噪水平特征"""
    features = {}
    
    try:
        all_noise_floors = []
        all_noise_floor_stds = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # STFT分析
            f, t, Zxx = stft(segment_audio, sr, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 计算每个时间帧的真实底噪
            for frame_idx in range(power_spectrum.shape[1]):
                frame_power_db = power_db[:, frame_idx]
                
                # 真实底噪 = 最低10%的功率水平
                noise_floor_10 = np.percentile(frame_power_db, 10)
                noise_floor_5 = np.percentile(frame_power_db, 5)
                noise_floor_min = np.min(frame_power_db)
                
                all_noise_floors.extend([noise_floor_10, noise_floor_5, noise_floor_min])
                
                # 底噪的稳定性 (低功率区域的标准差)
                low_power_mask = frame_power_db <= np.percentile(frame_power_db, 20)
                if np.any(low_power_mask):
                    noise_floor_std = np.std(frame_power_db[low_power_mask])
                    all_noise_floor_stds.append(noise_floor_std)
        
        # 统计特征
        if all_noise_floors:
            features['true_noise_floor_mean'] = np.mean(all_noise_floors)
            features['true_noise_floor_median'] = np.median(all_noise_floors)
            features['true_noise_floor_std'] = np.std(all_noise_floors)
            features['true_noise_floor_max'] = np.max(all_noise_floors)
            features['true_noise_floor_min'] = np.min(all_noise_floors)
            features['true_noise_floor_p10'] = np.percentile(all_noise_floors, 10)
            features['true_noise_floor_p90'] = np.percentile(all_noise_floors, 90)
        else:
            for key in ['true_noise_floor_mean', 'true_noise_floor_median', 'true_noise_floor_std',
                       'true_noise_floor_max', 'true_noise_floor_min', 'true_noise_floor_p10', 'true_noise_floor_p90']:
                features[key] = -120
        
        if all_noise_floor_stds:
            features['noise_floor_stability_mean'] = np.mean(all_noise_floor_stds)
            features['noise_floor_stability_std'] = np.std(all_noise_floor_stds)
        else:
            features['noise_floor_stability_mean'] = 0
            features['noise_floor_stability_std'] = 0
            
    except Exception as e:
        # 设置默认值
        default_keys = ['true_noise_floor_mean', 'true_noise_floor_median', 'true_noise_floor_std',
                       'true_noise_floor_max', 'true_noise_floor_min', 'true_noise_floor_p10', 'true_noise_floor_p90',
                       'noise_floor_stability_mean', 'noise_floor_stability_std']
        for key in default_keys:
            features[key] = -120 if 'stability' not in key else 0
    
    return features

def extract_spectral_flatness_correct(y, sr, step_boundaries, target_segments):
    """提取正确的频谱平坦度特征"""
    features = {}
    
    try:
        all_flatness_values = []
        all_spectral_entropies = []
        all_spectral_spreads = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 使用Welch方法计算功率谱密度
            freqs, psd = welch(segment_audio, sr, nperseg=1024)
            
            # 聚焦低频段 (100-1000Hz)
            low_freq_mask = (freqs >= 100) & (freqs <= 1000)
            if np.any(low_freq_mask):
                low_freq_psd = psd[low_freq_mask]
                
                # 频谱平坦度 = 几何平均 / 算术平均
                if np.all(low_freq_psd > 0):
                    geometric_mean = np.exp(np.mean(np.log(low_freq_psd + 1e-12)))
                    arithmetic_mean = np.mean(low_freq_psd)
                    flatness = geometric_mean / (arithmetic_mean + 1e-12)
                    all_flatness_values.append(flatness)
                
                # 频谱熵 (衡量分布均匀性)
                psd_normalized = low_freq_psd / (np.sum(low_freq_psd) + 1e-12)
                psd_normalized = psd_normalized + 1e-12
                spectral_entropy = -np.sum(psd_normalized * np.log2(psd_normalized))
                all_spectral_entropies.append(spectral_entropy)
                
                # 频谱扩散度 (能量分布的宽度)
                freqs_low = freqs[low_freq_mask]
                spectral_centroid = np.sum(freqs_low * low_freq_psd) / (np.sum(low_freq_psd) + 1e-12)
                spectral_spread = np.sqrt(np.sum(((freqs_low - spectral_centroid) ** 2) * low_freq_psd) / (np.sum(low_freq_psd) + 1e-12))
                all_spectral_spreads.append(spectral_spread)
        
        # 统计特征
        if all_flatness_values:
            features['spectral_flatness_mean'] = np.mean(all_flatness_values)
            features['spectral_flatness_std'] = np.std(all_flatness_values)
            features['spectral_flatness_max'] = np.max(all_flatness_values)
            features['spectral_flatness_min'] = np.min(all_flatness_values)
        else:
            for key in ['spectral_flatness_mean', 'spectral_flatness_std', 'spectral_flatness_max', 'spectral_flatness_min']:
                features[key] = 0
        
        if all_spectral_entropies:
            features['spectral_entropy_mean'] = np.mean(all_spectral_entropies)
            features['spectral_entropy_std'] = np.std(all_spectral_entropies)
            features['spectral_entropy_max'] = np.max(all_spectral_entropies)
        else:
            for key in ['spectral_entropy_mean', 'spectral_entropy_std', 'spectral_entropy_max']:
                features[key] = 0
        
        if all_spectral_spreads:
            features['spectral_spread_mean'] = np.mean(all_spectral_spreads)
            features['spectral_spread_std'] = np.std(all_spectral_spreads)
            features['spectral_spread_max'] = np.max(all_spectral_spreads)
        else:
            for key in ['spectral_spread_mean', 'spectral_spread_std', 'spectral_spread_max']:
                features[key] = 0
            
    except Exception as e:
        # 设置默认值
        default_keys = ['spectral_flatness_mean', 'spectral_flatness_std', 'spectral_flatness_max', 'spectral_flatness_min',
                       'spectral_entropy_mean', 'spectral_entropy_std', 'spectral_entropy_max',
                       'spectral_spread_mean', 'spectral_spread_std', 'spectral_spread_max']
        for key in default_keys:
            features[key] = 0
    
    return features

def extract_signal_gap_noise(y, sr, step_boundaries, target_segments, freq_table):
    """提取信号间隙噪声特征"""
    features = {}
    
    try:
        all_gap_noise_levels = []
        all_signal_to_gap_ratios = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries) or seg_idx >= len(freq_table):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # FFT分析
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(len(segment_audio), 1/sr)
            magnitude = np.abs(fft)
            
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            # 定义信号间隙区域 (远离期望频率的区域)
            signal_tolerance = 50  # Hz
            gap_regions = []
            
            # 低频间隙 (50-80Hz)
            if expected_freq > 100:
                low_gap_mask = (positive_freqs >= 50) & (positive_freqs <= 80)
                if np.any(low_gap_mask):
                    gap_regions.append(positive_magnitude[low_gap_mask])
            
            # 高频间隙 (期望频率 + 100Hz 到 期望频率 + 200Hz)
            high_gap_start = expected_freq + 100
            high_gap_end = expected_freq + 200
            if high_gap_end < sr/2:
                high_gap_mask = (positive_freqs >= high_gap_start) & (positive_freqs <= high_gap_end)
                if np.any(high_gap_mask):
                    gap_regions.append(positive_magnitude[high_gap_mask])
            
            # 计算间隙噪声水平
            for gap_region in gap_regions:
                if len(gap_region) > 0:
                    gap_noise_level = np.mean(gap_region)
                    all_gap_noise_levels.append(gap_noise_level)
            
            # 信号与间隙的比值
            signal_mask = np.abs(positive_freqs - expected_freq) <= signal_tolerance
            if np.any(signal_mask) and len(all_gap_noise_levels) > 0:
                signal_power = np.max(positive_magnitude[signal_mask])
                gap_noise_power = np.mean(all_gap_noise_levels[-len(gap_regions):]) if len(gap_regions) > 0 else 1e-12
                signal_to_gap_ratio = signal_power / (gap_noise_power + 1e-12)
                all_signal_to_gap_ratios.append(signal_to_gap_ratio)
        
        # 统计特征
        if all_gap_noise_levels:
            features['gap_noise_mean'] = np.mean(all_gap_noise_levels)
            features['gap_noise_std'] = np.std(all_gap_noise_levels)
            features['gap_noise_max'] = np.max(all_gap_noise_levels)
            features['gap_noise_median'] = np.median(all_gap_noise_levels)
        else:
            for key in ['gap_noise_mean', 'gap_noise_std', 'gap_noise_max', 'gap_noise_median']:
                features[key] = 0
        
        if all_signal_to_gap_ratios:
            features['signal_to_gap_ratio_mean'] = np.mean(all_signal_to_gap_ratios)
            features['signal_to_gap_ratio_std'] = np.std(all_signal_to_gap_ratios)
            features['signal_to_gap_ratio_min'] = np.min(all_signal_to_gap_ratios)
        else:
            for key in ['signal_to_gap_ratio_mean', 'signal_to_gap_ratio_std', 'signal_to_gap_ratio_min']:
                features[key] = 0
            
    except Exception as e:
        # 设置默认值
        default_keys = ['gap_noise_mean', 'gap_noise_std', 'gap_noise_max', 'gap_noise_median',
                       'signal_to_gap_ratio_mean', 'signal_to_gap_ratio_std', 'signal_to_gap_ratio_min']
        for key in default_keys:
            features[key] = 0
    
    return features

def extract_power_density_features(y, sr, step_boundaries, target_segments):
    """提取全频段功率密度特征"""
    features = {}
    
    try:
        all_power_densities = []
        all_power_concentrations = []
        all_dynamic_ranges = []
        
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 使用Welch方法计算功率谱密度
            freqs, psd = welch(segment_audio, sr, nperseg=1024)
            
            # 聚焦低频段 (100-1000Hz)
            low_freq_mask = (freqs >= 100) & (freqs <= 1000)
            if np.any(low_freq_mask):
                low_freq_psd = psd[low_freq_mask]
                
                # 总功率密度
                total_power_density = np.sum(low_freq_psd)
                all_power_densities.append(total_power_density)
                
                # 功率集中度 (最大功率 / 平均功率)
                max_power = np.max(low_freq_psd)
                mean_power = np.mean(low_freq_psd)
                power_concentration = max_power / (mean_power + 1e-12)
                all_power_concentrations.append(power_concentration)
                
                # 动态范围 (最大功率 - 最小功率)
                min_power = np.min(low_freq_psd)
                dynamic_range = 10 * np.log10((max_power + 1e-12) / (min_power + 1e-12))
                all_dynamic_ranges.append(dynamic_range)
        
        # 统计特征
        if all_power_densities:
            features['power_density_mean'] = np.mean(all_power_densities)
            features['power_density_std'] = np.std(all_power_densities)
            features['power_density_max'] = np.max(all_power_densities)
            features['power_density_median'] = np.median(all_power_densities)
        else:
            for key in ['power_density_mean', 'power_density_std', 'power_density_max', 'power_density_median']:
                features[key] = 0
        
        if all_power_concentrations:
            features['power_concentration_mean'] = np.mean(all_power_concentrations)
            features['power_concentration_std'] = np.std(all_power_concentrations)
            features['power_concentration_min'] = np.min(all_power_concentrations)
        else:
            for key in ['power_concentration_mean', 'power_concentration_std', 'power_concentration_min']:
                features[key] = 0
        
        if all_dynamic_ranges:
            features['dynamic_range_mean'] = np.mean(all_dynamic_ranges)
            features['dynamic_range_std'] = np.std(all_dynamic_ranges)
            features['dynamic_range_min'] = np.min(all_dynamic_ranges)
            features['dynamic_range_max'] = np.max(all_dynamic_ranges)
        else:
            for key in ['dynamic_range_mean', 'dynamic_range_std', 'dynamic_range_min', 'dynamic_range_max']:
                features[key] = 0
            
    except Exception as e:
        # 设置默认值
        default_keys = ['power_density_mean', 'power_density_std', 'power_density_max', 'power_density_median',
                       'power_concentration_mean', 'power_concentration_std', 'power_concentration_min',
                       'dynamic_range_mean', 'dynamic_range_std', 'dynamic_range_min', 'dynamic_range_max']
        for key in default_keys:
            features[key] = 0
    
    return features

def analyze_correct_features_separation(df, target_files):
    """分析正确特征的分离能力"""
    print(f"\n🔍 分析正确背景噪声特征的分离能力")
    print("="*70)
    
    # 获取特征列
    feature_cols = [col for col in df.columns if col not in ['filename', 'label', 'is_target']]
    print(f"📊 正确背景噪声特征数: {len(feature_cols)}")
    
    # 分析目标样本与其他样本的分离
    target_data = df[df['is_target'] == True]
    other_data = df[df['is_target'] == False]
    
    print(f"📊 目标样本: {len(target_data)}个")
    print(f"📊 其他样本: {len(other_data)}个")
    
    separable_features = []
    
    for feature in feature_cols:
        target_values = target_data[feature].dropna()
        other_values = other_data[feature].dropna()
        
        if len(target_values) == 0 or len(other_values) == 0:
            continue
        
        target_min, target_max = np.min(target_values), np.max(target_values)
        other_min, other_max = np.min(other_values), np.max(other_values)
        
        # 检查完全分离
        if target_max < other_min:
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
        elif target_min > other_max:
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
    
    # 排序并显示结果
    separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
    
    print(f"\n✅ 找到 {len(separable_features)} 个可完全分离的正确特征:")
    
    for i, feature_info in enumerate(separable_features[:10]):
        print(f"  {i+1:2d}. {feature_info['feature']}")
        print(f"      分离类型: {feature_info['separation_type']}")
        print(f"      分离间隙: {feature_info['separation_gap']:.6f}")
        print(f"      目标范围: [{feature_info['target_range'][0]:.6f}, {feature_info['target_range'][1]:.6f}]")
        print(f"      其他范围: [{feature_info['other_range'][0]:.6f}, {feature_info['other_range'][1]:.6f}]")
        print(f"      目标均值: {feature_info['target_mean']:.6f}")
        print(f"      其他均值: {feature_info['other_mean']:.6f}")
    
    # 可视化最佳特征
    if len(separable_features) > 0:
        visualize_correct_features(df, separable_features[:5], target_files)
    
    return separable_features

def visualize_correct_features(df, top_features, target_files):
    """可视化正确的背景噪声特征"""
    print(f"\n🎨 生成正确背景噪声特征可视化...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('正确背景噪声特征分离分析', fontsize=16, fontweight='bold')
    
    # 1-4. 前4个最佳特征的分布对比
    for i in range(min(4, len(top_features))):
        ax = axes[i//2, i%2] if i < 4 else None
        if ax is None:
            continue
            
        feature_name = top_features[i]['feature']
        
        target_data = df[df['is_target'] == True]
        other_data = df[df['is_target'] == False]
        
        target_values = target_data[feature_name].dropna()
        other_values = other_data[feature_name].dropna()
        
        if len(target_values) > 0 and len(other_values) > 0:
            ax.hist(other_values, bins=30, alpha=0.6, color='blue', 
                   label=f'其他样本 (n={len(other_values)})', density=True)
            ax.hist(target_values, bins=30, alpha=0.8, color='red', 
                   label=f'噪声样本 (n={len(target_values)})', density=True)
            
            ax.set_title(f'{feature_name}')
            ax.set_xlabel('特征值')
            ax.set_ylabel('密度')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 显示分离信息
            gap = top_features[i]['separation_gap']
            ax.text(0.5, 0.95, f'分离间隙: {gap:.4f}', 
                   transform=ax.transAxes, ha='center', va='top',
                   bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    # 5. 特征重要性对比
    ax5 = axes[1, 2]
    
    if len(top_features) >= 3:
        feature_names = [f['feature'] for f in top_features[:5]]
        gaps = [f['separation_gap'] for f in top_features[:5]]
        
        bars = ax5.bar(range(len(feature_names)), gaps, color=['red', 'orange', 'yellow', 'green', 'blue'][:len(gaps)])
        ax5.set_title('特征分离能力排名')
        ax5.set_xlabel('特征')
        ax5.set_ylabel('分离间隙')
        ax5.set_xticks(range(len(feature_names)))
        ax5.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax5.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, gap in zip(bars, gaps):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{gap:.4f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('correct_background_noise_features_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 图表已保存: correct_background_noise_features_analysis.png")

if __name__ == "__main__":
    df = extract_correct_background_noise_features()
    print(f"\n✅ 正确背景噪声特征分析完成！")
