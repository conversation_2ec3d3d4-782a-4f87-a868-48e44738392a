#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化特征分析：
1. 竖线检测单独处理
2. 其他特征重新分析
3. 每个频段只要能完全分离一个负样本就视为有效
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def optimize_feature_analysis():
    """优化特征分析"""
    print("🔍 优化特征分析")
    print("="*70)
    
    # 加载特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 分离竖线检测特征
    vertical_line_features = [col for col in df.columns if col.startswith('vl_')]
    other_features = [col for col in df.columns if not col.startswith('vl_') 
                     and col not in ['label', 'folder', 'filename', 'segment_idx', 
                                    'start_time', 'end_time', 'duration', 'expected_freq']]
    
    print(f"📊 竖线检测特征: {len(vertical_line_features)}个")
    print(f"📊 其他特征: {len(other_features)}个")
    
    # 按频段分析
    segment_results = []
    
    # 获取所有负样本文件
    neg_files = df[df['label'] == 'neg']['filename'].unique()
    print(f"📊 负样本文件: {len(neg_files)}个")
    
    for segment_idx in sorted(df['segment_idx'].unique()):
        segment_data = df[df['segment_idx'] == segment_idx]
        
        pos_data = segment_data[segment_data['label'] == 'pos']
        neg_data = segment_data[segment_data['label'] == 'neg']
        
        if len(pos_data) == 0 or len(neg_data) == 0:
            continue
        
        expected_freq = segment_data['expected_freq'].iloc[0]
        
        print(f"\n📊 分析频段 {segment_idx}: {expected_freq:.1f}Hz")
        print(f"   正样本: {len(pos_data)}个, 负样本: {len(neg_data)}个")
        
        # 1. 分析竖线检测特征
        vl_results = analyze_vertical_line_features(pos_data, neg_data, vertical_line_features)
        
        # 2. 分析其他特征
        other_results = analyze_other_features(pos_data, neg_data, other_features, neg_files)
        
        # 3. 分析每个负样本文件的可分离特征
        file_separation_results = analyze_file_separation(pos_data, neg_data, other_features, neg_files)
        
        # 合并结果
        segment_result = {
            'segment_idx': segment_idx,
            'expected_freq': expected_freq,
            'pos_count': len(pos_data),
            'neg_count': len(neg_data),
            'vl_effective': vl_results['effective'],
            'vl_best_feature': vl_results['best_feature'],
            'vl_best_score': vl_results['best_score'],
            'other_effective': other_results['effective'],
            'other_best_feature': other_results['best_feature'],
            'other_best_score': other_results['best_score'],
            'separable_files': file_separation_results['separable_files'],
            'total_files': file_separation_results['total_files'],
            'file_separation_ratio': file_separation_results['separation_ratio'],
            'file_feature_map': file_separation_results['file_feature_map']
        }
        
        # 显示结果
        print(f"   竖线检测: {'✅ 有效' if vl_results['effective'] else '❌ 无效'}")
        if vl_results['effective']:
            print(f"     最佳特征: {vl_results['best_feature']}, 分离评分: {vl_results['best_score']:.3f}")
        
        print(f"   其他特征: {'✅ 有效' if other_results['effective'] else '❌ 无效'}")
        if other_results['effective']:
            print(f"     最佳特征: {other_results['best_feature']}, 分离评分: {other_results['best_score']:.3f}")
        
        print(f"   负样本文件可分离性: {file_separation_results['separable_files']}/{file_separation_results['total_files']} ({file_separation_results['separation_ratio']*100:.1f}%)")
        
        segment_results.append(segment_result)
    
    # 转换为DataFrame
    results_df = pd.DataFrame(segment_results)
    
    # 生成分析报告
    generate_analysis_report(results_df, neg_files)
    
    # 保存结果
    results_df.to_csv('optimized_feature_analysis.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 优化特征分析结果已保存: optimized_feature_analysis.csv")
    
    return results_df

def analyze_vertical_line_features(pos_data, neg_data, vl_features):
    """分析竖线检测特征"""
    best_feature = None
    best_score = 0
    effective = False
    
    for feature in vl_features:
        try:
            pos_values = pos_data[feature].dropna()
            neg_values = neg_data[feature].dropna()
            
            if len(pos_values) == 0 or len(neg_values) == 0:
                continue
            
            # 统计检验
            t_stat, p_value = stats.ttest_ind(pos_values, neg_values)
            
            # 效应大小
            pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                                 (len(neg_values) - 1) * np.var(neg_values)) / 
                                (len(pos_values) + len(neg_values) - 2))
            cohens_d = abs(np.mean(pos_values) - np.mean(neg_values)) / (pooled_std + 1e-12)
            
            # 分离度
            pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
            neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
            
            # 重叠度计算
            overlap = max(0, min(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - 
                         max(pos_mean - 2*pos_std, neg_mean - 2*neg_std))
            total_range = max(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - \
                         min(pos_mean - 2*pos_std, neg_mean - 2*neg_std)
            overlap_ratio = overlap / (total_range + 1e-12)
            
            separation_score = cohens_d * (1 - overlap_ratio)
            
            # 判断是否有效
            if separation_score > 1.0 and overlap_ratio < 0.2:
                effective = True
                if separation_score > best_score:
                    best_score = separation_score
                    best_feature = feature
            
        except Exception as e:
            continue
    
    return {
        'effective': effective,
        'best_feature': best_feature,
        'best_score': best_score
    }

def analyze_other_features(pos_data, neg_data, other_features, neg_files):
    """分析其他特征"""
    best_feature = None
    best_score = 0
    effective = False
    
    for feature in other_features:
        try:
            pos_values = pos_data[feature].dropna()
            neg_values = neg_data[feature].dropna()
            
            if len(pos_values) == 0 or len(neg_values) == 0:
                continue
            
            # 统计检验
            t_stat, p_value = stats.ttest_ind(pos_values, neg_values)
            
            # 效应大小
            pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                                 (len(neg_values) - 1) * np.var(neg_values)) / 
                                (len(pos_values) + len(neg_values) - 2))
            cohens_d = abs(np.mean(pos_values) - np.mean(neg_values)) / (pooled_std + 1e-12)
            
            # 分离度
            pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
            neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
            
            # 重叠度计算
            overlap = max(0, min(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - 
                         max(pos_mean - 2*pos_std, neg_mean - 2*neg_std))
            total_range = max(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - \
                         min(pos_mean - 2*pos_std, neg_mean - 2*neg_std)
            overlap_ratio = overlap / (total_range + 1e-12)
            
            separation_score = cohens_d * (1 - overlap_ratio)
            
            # 判断是否有效
            if separation_score > 1.0 and overlap_ratio < 0.2:
                effective = True
                if separation_score > best_score:
                    best_score = separation_score
                    best_feature = feature
            
        except Exception as e:
            continue
    
    return {
        'effective': effective,
        'best_feature': best_feature,
        'best_score': best_score
    }

def analyze_file_separation(pos_data, neg_data, features, neg_files):
    """分析每个负样本文件的可分离特征"""
    file_feature_map = {}
    separable_files = 0
    
    for neg_file in neg_files:
        file_neg_data = neg_data[neg_data['filename'] == neg_file]
        
        if len(file_neg_data) == 0:
            continue
        
        file_separable = False
        best_feature = None
        best_score = 0
        
        for feature in features:
            try:
                pos_values = pos_data[feature].dropna()
                neg_values = file_neg_data[feature].dropna()
                
                if len(pos_values) == 0 or len(neg_values) == 0:
                    continue
                
                # 统计检验
                t_stat, p_value = stats.ttest_ind(pos_values, neg_values)
                
                # 效应大小
                pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                                     (len(neg_values) - 1) * np.var(neg_values)) / 
                                    (len(pos_values) + len(neg_values) - 2))
                cohens_d = abs(np.mean(pos_values) - np.mean(neg_values)) / (pooled_std + 1e-12)
                
                # 分离度
                pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
                neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
                
                # 重叠度计算
                overlap = max(0, min(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - 
                             max(pos_mean - 2*pos_std, neg_mean - 2*neg_std))
                total_range = max(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - \
                             min(pos_mean - 2*pos_std, neg_mean - 2*neg_std)
                overlap_ratio = overlap / (total_range + 1e-12)
                
                separation_score = cohens_d * (1 - overlap_ratio)
                
                # 判断是否可分离
                if separation_score > 1.0 and overlap_ratio < 0.2:
                    file_separable = True
                    if separation_score > best_score:
                        best_score = separation_score
                        best_feature = feature
                
            except Exception as e:
                continue
        
        if file_separable:
            separable_files += 1
            file_feature_map[neg_file] = {
                'feature': best_feature,
                'score': best_score
            }
    
    return {
        'separable_files': separable_files,
        'total_files': len(neg_files),
        'separation_ratio': separable_files / len(neg_files) if len(neg_files) > 0 else 0,
        'file_feature_map': file_feature_map
    }

def generate_analysis_report(results_df, neg_files):
    """生成分析报告"""
    print(f"\n📊 优化特征分析报告")
    print("="*70)
    
    # 1. 总体统计
    total_segments = len(results_df)
    vl_effective_segments = len(results_df[results_df['vl_effective'] == True])
    other_effective_segments = len(results_df[results_df['other_effective'] == True])
    any_effective_segments = len(results_df[(results_df['vl_effective'] == True) | 
                                          (results_df['other_effective'] == True)])
    
    print(f"总频段数: {total_segments}")
    print(f"竖线检测有效频段: {vl_effective_segments} ({vl_effective_segments/total_segments*100:.1f}%)")
    print(f"其他特征有效频段: {other_effective_segments} ({other_effective_segments/total_segments*100:.1f}%)")
    print(f"任一特征有效频段: {any_effective_segments} ({any_effective_segments/total_segments*100:.1f}%)")
    
    # 2. 负样本文件分析
    file_coverage = {}
    for neg_file in neg_files:
        covered_segments = 0
        for _, row in results_df.iterrows():
            if neg_file in row['file_feature_map']:
                covered_segments += 1
        
        coverage_ratio = covered_segments / total_segments
        file_coverage[neg_file] = {
            'covered_segments': covered_segments,
            'coverage_ratio': coverage_ratio
        }
    
    print(f"\n📊 负样本文件覆盖分析:")
    print("-" * 50)
    for file, coverage in sorted(file_coverage.items(), key=lambda x: x[1]['coverage_ratio'], reverse=True):
        print(f"  {file}: {coverage['covered_segments']}/{total_segments} ({coverage['coverage_ratio']*100:.1f}%)")
    
    # 3. 频率范围分析
    frequency_ranges = [
        (100, 500, "低频"),
        (500, 2000, "中低频"),
        (2000, 8000, "中频"),
        (8000, 20000, "高频")
    ]
    
    print(f"\n📊 按频率范围分析:")
    print("-" * 50)
    
    for freq_min, freq_max, freq_name in frequency_ranges:
        range_segments = results_df[
            (results_df['expected_freq'] >= freq_min) & 
            (results_df['expected_freq'] <= freq_max)
        ]
        
        if len(range_segments) > 0:
            vl_effective = len(range_segments[range_segments['vl_effective'] == True])
            other_effective = len(range_segments[range_segments['other_effective'] == True])
            any_effective = len(range_segments[(range_segments['vl_effective'] == True) | 
                                             (range_segments['other_effective'] == True)])
            
            print(f"  {freq_name} ({freq_min}-{freq_max}Hz): {len(range_segments)}个频段")
            print(f"    竖线检测有效: {vl_effective}/{len(range_segments)} ({vl_effective/len(range_segments)*100:.1f}%)")
            print(f"    其他特征有效: {other_effective}/{len(range_segments)} ({other_effective/len(range_segments)*100:.1f}%)")
            print(f"    任一特征有效: {any_effective}/{len(range_segments)} ({any_effective/len(range_segments)*100:.1f}%)")
    
    # 4. 最佳特征分析
    vl_feature_counts = results_df[results_df['vl_effective'] == True]['vl_best_feature'].value_counts()
    other_feature_counts = results_df[results_df['other_effective'] == True]['other_best_feature'].value_counts()
    
    print(f"\n📊 最佳特征分析:")
    print("-" * 50)
    
    print(f"  竖线检测最佳特征:")
    for feature, count in vl_feature_counts.items():
        print(f"    {feature}: {count}个频段")
    
    print(f"  其他特征最佳特征:")
    for feature, count in other_feature_counts.head(10).items():
        print(f"    {feature}: {count}个频段")
    
    # 5. 文件特征映射分析
    file_feature_preference = defaultdict(lambda: defaultdict(int))
    
    for _, row in results_df.iterrows():
        for file, info in row['file_feature_map'].items():
            file_feature_preference[file][info['feature']] += 1
    
    print(f"\n📊 文件特征偏好分析:")
    print("-" * 50)
    
    for file in neg_files:
        if file in file_feature_preference:
            print(f"  {file}:")
            for feature, count in sorted(file_feature_preference[file].items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"    {feature}: {count}个频段")
    
    # 6. 可视化
    visualize_analysis_results(results_df, neg_files)

def visualize_analysis_results(results_df, neg_files):
    """可视化分析结果"""
    print(f"\n🔍 生成可视化分析")
    print("="*70)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('优化特征分析结果', fontsize=16, fontweight='bold')
    
    # 1. 频段有效性随频率变化
    ax1 = axes[0, 0]
    ax1.plot(results_df['expected_freq'], results_df['vl_effective'].astype(int), 'r-o', markersize=4, alpha=0.7, label='竖线检测')
    ax1.plot(results_df['expected_freq'], results_df['other_effective'].astype(int), 'b-o', markersize=4, alpha=0.7, label='其他特征')
    ax1.set_title('特征有效性随频率变化')
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('是否有效 (1=有效, 0=无效)')
    ax1.set_xscale('log')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. 文件可分离性随频段变化
    ax2 = axes[0, 1]
    ax2.plot(results_df['segment_idx'], results_df['file_separation_ratio'], 'g-o', markersize=4)
    ax2.set_title('文件可分离性随频段变化')
    ax2.set_xlabel('频段索引')
    ax2.set_ylabel('可分离文件比例')
    ax2.grid(True, alpha=0.3)
    
    # 3. 频率范围有效性分布
    frequency_ranges = [
        (100, 500, "低频"),
        (500, 2000, "中低频"),
        (2000, 8000, "中频"),
        (8000, 20000, "高频")
    ]
    
    range_labels = []
    vl_effective_ratios = []
    other_effective_ratios = []
    
    for freq_min, freq_max, freq_name in frequency_ranges:
        range_segments = results_df[
            (results_df['expected_freq'] >= freq_min) & 
            (results_df['expected_freq'] <= freq_max)
        ]
        
        if len(range_segments) > 0:
            range_labels.append(freq_name)
            vl_effective_ratios.append(len(range_segments[range_segments['vl_effective'] == True]) / len(range_segments))
            other_effective_ratios.append(len(range_segments[range_segments['other_effective'] == True]) / len(range_segments))
    
    ax3 = axes[1, 0]
    x = np.arange(len(range_labels))
    width = 0.35
    
    ax3.bar(x - width/2, vl_effective_ratios, width, label='竖线检测')
    ax3.bar(x + width/2, other_effective_ratios, width, label='其他特征')
    
    ax3.set_title('不同频率范围的特征有效性')
    ax3.set_xlabel('频率范围')
    ax3.set_ylabel('有效比例')
    ax3.set_xticks(x)
    ax3.set_xticklabels(range_labels)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 文件覆盖分析
    file_coverage = {}
    for neg_file in neg_files:
        covered_segments = 0
        for _, row in results_df.iterrows():
            if neg_file in row['file_feature_map']:
                covered_segments += 1
        
        coverage_ratio = covered_segments / len(results_df)
        file_coverage[neg_file] = coverage_ratio
    
    ax4 = axes[1, 1]
    sorted_files = sorted(file_coverage.items(), key=lambda x: x[1], reverse=True)
    file_names = [file[:15] + '...' if len(file) > 15 else file for file, _ in sorted_files]
    coverage_values = [coverage for _, coverage in sorted_files]
    
    ax4.bar(range(len(file_names)), coverage_values, color='purple')
    ax4.set_title('负样本文件覆盖分析')
    ax4.set_xlabel('文件')
    ax4.set_ylabel('覆盖比例')
    ax4.set_xticks(range(len(file_names)))
    ax4.set_xticklabels(file_names, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    results_df = optimize_feature_analysis()
