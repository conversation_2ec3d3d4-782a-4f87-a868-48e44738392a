#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高性能API
"""

from harmonic_detector_api_fast import FastHarmonicDetectorAPI, detect_audio_harmonic_anomaly_fast
import time

def test_fast_api():
    """测试高性能API"""
    print("🚀 高性能API测试")
    print("="*40)
    
    # 测试文件
    test_file = "../test20250722/喇叭鼓膜凹陷2.1.wav"
    
    # 方法1: 便捷函数
    print("方法1: 便捷函数")
    start = time.time()
    result = detect_audio_harmonic_anomaly_fast(test_file)
    end = time.time()
    print(f"结果: {result} ({'正常' if result == 1 else '异常'})")
    print(f"耗时: {end-start:.2f}秒")
    print()
    
    # # 方法2: 类接口
    # print("方法2: 类接口 (详细信息)")
    # detector = FastHarmonicDetectorAPI(num_workers=8)
    # details = detector.detect_with_details(test_file)
    
    # print(f"结果: {details['result']} ({'正常' if details['result'] == 1 else '异常'})")
    # print(f"异常段: {details['anomaly_count']}个")
    # print(f"耗时: {details['processing_time']:.2f}秒")
    # print(f"工作进程: {details['num_workers']}个")
    # print(f"性能提升: {details['performance_boost']}")

if __name__ == "__main__":
    test_fast_api()
