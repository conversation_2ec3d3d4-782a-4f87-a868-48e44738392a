#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量分析test20170717样本的93段谐波数量和噪声波动特征，与低音戳洞对比
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import glob
import pandas as pd

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def batch_analysis_comparison():
    """批量分析对比"""
    print("🎯 批量分析test20250717样本93段特征并与低音戳洞对比")
    print("="*70)
    
    # 低音戳洞文件
    hole_file = r'test20250717\neg\录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    # test20250717目录
    test_dir = 'test20250717'
    
    if not os.path.exists(test_dir):
        print("❌ test20170717目录不存在")
        return
    
    # 分析低音戳洞作为基准
    print("\n📊 分析低音戳洞基准...")
    hole_data = analyze_single_file_features(hole_file, "低音戳洞")
    
    if not hole_data:
        print("❌ 低音戳洞分析失败")
        return
    
    # 批量分析test20250717中的所有wav文件
    print(f"\n📊 批量分析{test_dir}中的样本...")
    batch_data = batch_analyze_directory(test_dir)
    
    if batch_data:
        # 创建对比分析
        create_comparison_analysis(hole_data, batch_data)

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def analyze_single_file_features(audio_path, file_label):
    """分析单个文件的特征"""
    try:
        print(f"  分析: {file_label}")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        harmonic_counts = []
        noise_variations = []
        stability_scores = []
        fluctuation_stds = []
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析
            fft_size = 32768  # 32k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 谐波检测
            if noise_analysis:
                harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
                
                harmonic_counts.append(len(harmonic_analysis))
                noise_variations.append(noise_analysis['noise_variation_db'])
                stability_scores.append(noise_analysis['noise_fluctuation_features']['fluctuation_stability_score'])
                fluctuation_stds.append(noise_analysis['noise_fluctuation_features']['fluctuation_std'])
            else:
                harmonic_counts.append(0)
                noise_variations.append(0)
                stability_scores.append(0)
                fluctuation_stds.append(0)
        
        return {
            'filename': os.path.basename(audio_path),
            'label': file_label,
            'harmonic_counts': harmonic_counts,
            'noise_variations': noise_variations,
            'stability_scores': stability_scores,
            'fluctuation_stds': fluctuation_stds,
            'total_harmonics': sum(harmonic_counts),
            'avg_harmonics': np.mean(harmonic_counts),
            'avg_noise_variation': np.mean(noise_variations),
            'avg_stability': np.mean(stability_scores)
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def batch_analyze_directory(directory):
    """批量分析目录中的文件"""
    
    # 查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"  找到{len(wav_files)}个wav文件")
    
    batch_results = []
    
    for i, wav_file in enumerate(wav_files):
        print(f"  处理进度: {i+1}/{len(wav_files)} - {os.path.basename(wav_file)}")
        
        # 确定文件标签
        if 'pos' in wav_file.lower() or 'good' in wav_file.lower() or 'ok' in wav_file.lower():
            label = 'Good'
        elif 'neg' in wav_file.lower() or 'bad' in wav_file.lower() or 'defect' in wav_file.lower():
            label = 'Defect'
        else:
            label = 'Unknown'
        
        result = analyze_single_file_features(wav_file, label)
        if result:
            batch_results.append(result)
    
    print(f"✅ 成功分析{len(batch_results)}个文件")
    
    return batch_results

def create_comparison_analysis(hole_data, batch_data):
    """创建对比分析"""
    print(f"\n🎨 生成对比分析...")
    
    # 分离好样本和缺陷样本
    good_samples = [data for data in batch_data if data['label'] == 'Good']
    defect_samples = [data for data in batch_data if data['label'] == 'Defect']
    
    print(f"  好样本: {len(good_samples)}个")
    print(f"  缺陷样本: {len(defect_samples)}个")
    print(f"  低音戳洞: 1个 (基准)")
    
    # 计算统计特征
    stats = calculate_comparison_statistics(hole_data, good_samples, defect_samples)
    
    # 创建可视化
    create_comparison_visualization(stats)
    
    # 打印对比报告
    print_comparison_report(stats)

def calculate_comparison_statistics(hole_data, good_samples, defect_samples):
    """计算对比统计"""
    
    def extract_features(samples):
        if not samples:
            return {
                'harmonic_counts': [],
                'noise_variations': [],
                'stability_scores': [],
                'fluctuation_stds': []
            }
        
        all_harmonic_counts = []
        all_noise_variations = []
        all_stability_scores = []
        all_fluctuation_stds = []
        
        for sample in samples:
            all_harmonic_counts.extend(sample['harmonic_counts'])
            all_noise_variations.extend(sample['noise_variations'])
            all_stability_scores.extend(sample['stability_scores'])
            all_fluctuation_stds.extend(sample['fluctuation_stds'])
        
        return {
            'harmonic_counts': all_harmonic_counts,
            'noise_variations': all_noise_variations,
            'stability_scores': all_stability_scores,
            'fluctuation_stds': all_fluctuation_stds
        }
    
    # 提取特征
    hole_features = {
        'harmonic_counts': hole_data['harmonic_counts'],
        'noise_variations': hole_data['noise_variations'],
        'stability_scores': hole_data['stability_scores'],
        'fluctuation_stds': hole_data['fluctuation_stds']
    }
    
    good_features = extract_features(good_samples)
    defect_features = extract_features(defect_samples)
    
    # 计算统计量
    def calc_stats(data):
        if not data:
            return {'min': 0, 'max': 0, 'mean': 0, 'std': 0, 'range': 0}
        return {
            'min': np.min(data),
            'max': np.max(data),
            'mean': np.mean(data),
            'std': np.std(data),
            'range': np.max(data) - np.min(data)
        }
    
    return {
        'hole': {
            'harmonic_counts': calc_stats(hole_features['harmonic_counts']),
            'noise_variations': calc_stats(hole_features['noise_variations']),
            'stability_scores': calc_stats(hole_features['stability_scores']),
            'fluctuation_stds': calc_stats(hole_features['fluctuation_stds']),
            'sample_count': len(good_samples) if good_samples else 1
        },
        'good': {
            'harmonic_counts': calc_stats(good_features['harmonic_counts']),
            'noise_variations': calc_stats(good_features['noise_variations']),
            'stability_scores': calc_stats(good_features['stability_scores']),
            'fluctuation_stds': calc_stats(good_features['fluctuation_stds']),
            'sample_count': len(good_samples)
        },
        'defect': {
            'harmonic_counts': calc_stats(defect_features['harmonic_counts']),
            'noise_variations': calc_stats(defect_features['noise_variations']),
            'stability_scores': calc_stats(defect_features['stability_scores']),
            'fluctuation_stds': calc_stats(defect_features['fluctuation_stds']),
            'sample_count': len(defect_samples)
        },
        'raw_data': {
            'hole': hole_features,
            'good': good_features,
            'defect': defect_features
        }
    }

def create_comparison_visualization(stats):
    """创建对比可视化"""
    
    # 创建图表 (2行2列)
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('Batch Analysis Comparison: Hole vs Good vs Defect Samples', 
                 fontsize=16, fontweight='bold')
    
    categories = ['Hole (Baseline)', 'Good Samples', 'Defect Samples']
    colors = ['red', 'green', 'orange']
    
    # 1. 谐波数量对比
    ax1 = axes[0, 0]
    harmonic_means = [stats['hole']['harmonic_counts']['mean'],
                     stats['good']['harmonic_counts']['mean'],
                     stats['defect']['harmonic_counts']['mean']]
    harmonic_stds = [stats['hole']['harmonic_counts']['std'],
                    stats['good']['harmonic_counts']['std'],
                    stats['defect']['harmonic_counts']['std']]
    
    bars1 = ax1.bar(categories, harmonic_means, yerr=harmonic_stds, 
                   color=colors, alpha=0.7, capsize=5, edgecolor='black')
    ax1.set_ylabel('Average Harmonics per Segment')
    ax1.set_title('Harmonic Count Comparison')
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, mean, std in zip(bars1, harmonic_means, harmonic_stds):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.1, 
                f'{mean:.1f}±{std:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 噪声变化对比
    ax2 = axes[0, 1]
    noise_means = [stats['hole']['noise_variations']['mean'],
                  stats['good']['noise_variations']['mean'],
                  stats['defect']['noise_variations']['mean']]
    noise_stds = [stats['hole']['noise_variations']['std'],
                 stats['good']['noise_variations']['std'],
                 stats['defect']['noise_variations']['std']]
    
    bars2 = ax2.bar(categories, noise_means, yerr=noise_stds, 
                   color=colors, alpha=0.7, capsize=5, edgecolor='black')
    ax2.set_ylabel('Average Noise Variation (dB)')
    ax2.set_title('Noise Variation Comparison')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, mean, std in zip(bars2, noise_means, noise_stds):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 1, 
                f'{mean:.1f}±{std:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 稳定性评分对比
    ax3 = axes[1, 0]
    stability_means = [stats['hole']['stability_scores']['mean'],
                      stats['good']['stability_scores']['mean'],
                      stats['defect']['stability_scores']['mean']]
    stability_stds = [stats['hole']['stability_scores']['std'],
                     stats['good']['stability_scores']['std'],
                     stats['defect']['stability_scores']['std']]
    
    bars3 = ax3.bar(categories, stability_means, yerr=stability_stds, 
                   color=colors, alpha=0.7, capsize=5, edgecolor='black')
    ax3.set_ylabel('Average Stability Score')
    ax3.set_title('Noise Stability Comparison')
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, mean, std in zip(bars3, stability_means, stability_stds):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01, 
                f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 4. 特征范围对比
    ax4 = axes[1, 1]
    
    # 创建范围对比表
    features = ['Harmonic Count', 'Noise Variation', 'Stability Score']
    hole_ranges = [stats['hole']['harmonic_counts']['range'],
                  stats['hole']['noise_variations']['range'],
                  stats['hole']['stability_scores']['range']]
    good_ranges = [stats['good']['harmonic_counts']['range'],
                  stats['good']['noise_variations']['range'],
                  stats['good']['stability_scores']['range']]
    defect_ranges = [stats['defect']['harmonic_counts']['range'],
                    stats['defect']['noise_variations']['range'],
                    stats['defect']['stability_scores']['range']]
    
    x = np.arange(len(features))
    width = 0.25
    
    bars4_1 = ax4.bar(x - width, hole_ranges, width, label='Hole', color='red', alpha=0.7)
    bars4_2 = ax4.bar(x, good_ranges, width, label='Good', color='green', alpha=0.7)
    bars4_3 = ax4.bar(x + width, defect_ranges, width, label='Defect', color='orange', alpha=0.7)
    
    ax4.set_ylabel('Feature Range (Max - Min)')
    ax4.set_title('Feature Range Comparison')
    ax4.set_xticks(x)
    ax4.set_xticklabels(features)
    ax4.legend()
    ax4.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'batch_comparison_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 批量对比分析可视化已保存: {filename}")
    plt.close()

def print_comparison_report(stats):
    """打印对比报告"""
    print(f"\n📊 批量对比分析报告:")
    print(f"{'='*80}")
    
    print(f"\n样本数量:")
    print(f"  低音戳洞 (基准): 1个样本")
    print(f"  好样本: {stats['good']['sample_count']}个样本")
    print(f"  缺陷样本: {stats['defect']['sample_count']}个样本")
    
    print(f"\n🎵 谐波数量特征对比:")
    print(f"  {'类别':<15} {'平均值':<10} {'标准差':<10} {'最小值':<10} {'最大值':<10} {'范围':<10}")
    print(f"  {'-'*70}")
    print(f"  {'低音戳洞':<15} {stats['hole']['harmonic_counts']['mean']:<10.2f} {stats['hole']['harmonic_counts']['std']:<10.2f} {stats['hole']['harmonic_counts']['min']:<10.0f} {stats['hole']['harmonic_counts']['max']:<10.0f} {stats['hole']['harmonic_counts']['range']:<10.0f}")
    print(f"  {'好样本':<15} {stats['good']['harmonic_counts']['mean']:<10.2f} {stats['good']['harmonic_counts']['std']:<10.2f} {stats['good']['harmonic_counts']['min']:<10.0f} {stats['good']['harmonic_counts']['max']:<10.0f} {stats['good']['harmonic_counts']['range']:<10.0f}")
    print(f"  {'缺陷样本':<15} {stats['defect']['harmonic_counts']['mean']:<10.2f} {stats['defect']['harmonic_counts']['std']:<10.2f} {stats['defect']['harmonic_counts']['min']:<10.0f} {stats['defect']['harmonic_counts']['max']:<10.0f} {stats['defect']['harmonic_counts']['range']:<10.0f}")
    
    print(f"\n🔊 噪声变化特征对比:")
    print(f"  {'类别':<15} {'平均值':<10} {'标准差':<10} {'最小值':<10} {'最大值':<10} {'范围':<10}")
    print(f"  {'-'*70}")
    print(f"  {'低音戳洞':<15} {stats['hole']['noise_variations']['mean']:<10.2f} {stats['hole']['noise_variations']['std']:<10.2f} {stats['hole']['noise_variations']['min']:<10.2f} {stats['hole']['noise_variations']['max']:<10.2f} {stats['hole']['noise_variations']['range']:<10.2f}")
    print(f"  {'好样本':<15} {stats['good']['noise_variations']['mean']:<10.2f} {stats['good']['noise_variations']['std']:<10.2f} {stats['good']['noise_variations']['min']:<10.2f} {stats['good']['noise_variations']['max']:<10.2f} {stats['good']['noise_variations']['range']:<10.2f}")
    print(f"  {'缺陷样本':<15} {stats['defect']['noise_variations']['mean']:<10.2f} {stats['defect']['noise_variations']['std']:<10.2f} {stats['defect']['noise_variations']['min']:<10.2f} {stats['defect']['noise_variations']['max']:<10.2f} {stats['defect']['noise_variations']['range']:<10.2f}")
    
    print(f"\n📈 稳定性评分特征对比:")
    print(f"  {'类别':<15} {'平均值':<10} {'标准差':<10} {'最小值':<10} {'最大值':<10} {'范围':<10}")
    print(f"  {'-'*70}")
    print(f"  {'低音戳洞':<15} {stats['hole']['stability_scores']['mean']:<10.3f} {stats['hole']['stability_scores']['std']:<10.3f} {stats['hole']['stability_scores']['min']:<10.3f} {stats['hole']['stability_scores']['max']:<10.3f} {stats['hole']['stability_scores']['range']:<10.3f}")
    print(f"  {'好样本':<15} {stats['good']['stability_scores']['mean']:<10.3f} {stats['good']['stability_scores']['std']:<10.3f} {stats['good']['stability_scores']['min']:<10.3f} {stats['good']['stability_scores']['max']:<10.3f} {stats['good']['stability_scores']['range']:<10.3f}")
    print(f"  {'缺陷样本':<15} {stats['defect']['stability_scores']['mean']:<10.3f} {stats['defect']['stability_scores']['std']:<10.3f} {stats['defect']['stability_scores']['min']:<10.3f} {stats['defect']['stability_scores']['max']:<10.3f} {stats['defect']['stability_scores']['range']:<10.3f}")
    
    print(f"\n🔍 关键发现:")
    
    # 谐波数量对比
    hole_harmonic_avg = stats['hole']['harmonic_counts']['mean']
    good_harmonic_avg = stats['good']['harmonic_counts']['mean']
    defect_harmonic_avg = stats['defect']['harmonic_counts']['mean']
    
    print(f"  谐波数量:")
    print(f"    低音戳洞 vs 好样本: {((hole_harmonic_avg - good_harmonic_avg) / good_harmonic_avg * 100):+.1f}%")
    print(f"    低音戳洞 vs 缺陷样本: {((hole_harmonic_avg - defect_harmonic_avg) / defect_harmonic_avg * 100):+.1f}%")
    print(f"    好样本 vs 缺陷样本: {((good_harmonic_avg - defect_harmonic_avg) / defect_harmonic_avg * 100):+.1f}%")
    
    # 噪声变化对比
    hole_noise_avg = stats['hole']['noise_variations']['mean']
    good_noise_avg = stats['good']['noise_variations']['mean']
    defect_noise_avg = stats['defect']['noise_variations']['mean']
    
    print(f"  噪声变化:")
    print(f"    低音戳洞 vs 好样本: {((hole_noise_avg - good_noise_avg) / good_noise_avg * 100):+.1f}%")
    print(f"    低音戳洞 vs 缺陷样本: {((hole_noise_avg - defect_noise_avg) / defect_noise_avg * 100):+.1f}%")
    print(f"    好样本 vs 缺陷样本: {((good_noise_avg - defect_noise_avg) / defect_noise_avg * 100):+.1f}%")

if __name__ == "__main__":
    batch_analysis_comparison()
