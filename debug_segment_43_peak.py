#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试第43段峰值为什么不是竖线
"""

import os
import sys
import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def debug_segment_43_peak():
    """深度调试第43段峰值"""
    print("🔍 深度调试第43段峰值为什么不是竖线")
    print("="*70)
    
    # 测试文件
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    if not os.path.exists(audio_path):
        print(f"❌ 文件不存在: {audio_path}")
        return
    
    try:
        # 1. 获取频段分割信息
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 第43段信息
        seg_start_time, seg_end_time = step_boundaries[43]
        expected_freq = freq_table[43]
        
        print(f"📊 第43段基本信息:")
        print(f"   时间范围: {seg_start_time:.3f}s - {seg_end_time:.3f}s")
        print(f"   期望频率: {expected_freq:.1f}Hz")
        
        # 2. 加载音频并进行STFT
        y, sr = librosa.load(audio_path, sr=48000)
        if np.max(np.abs(y)) > 0:
            y = y / np.max(np.abs(y)) * 0.9
        
        frequencies, times, Zxx = stft(y, sr, nperseg=2048, noverlap=1024, 
                                      window='hann', nfft=2048)
        
        # 频率范围筛选
        freq_mask = (frequencies >= 100) & (frequencies <= 20000)
        frequencies = frequencies[freq_mask]
        Zxx = Zxx[freq_mask, :]
        
        power_spectrum = np.abs(Zxx) ** 2
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 3. 提取第43段数据
        time_mask = (times >= seg_start_time) & (times <= seg_end_time)
        segment_times = times[time_mask]
        segment_power = power_db[:, time_mask]
        
        print(f"\n📊 段内数据:")
        print(f"   时间片数: {segment_power.shape[1]}")
        print(f"   频率点数: {segment_power.shape[0]}")
        
        # 4. 详细分析峰值
        total_energy = np.sum(segment_power, axis=0)
        energy_mean = np.mean(total_energy)
        energy_std = np.std(total_energy)
        
        print(f"\n📊 能量分析:")
        print(f"   平均能量: {energy_mean:.1f}")
        print(f"   标准差: {energy_std:.1f}")
        print(f"   最小值: {np.min(total_energy):.1f}")
        print(f"   最大值: {np.max(total_energy):.1f}")
        
        # 寻找峰值
        min_height = energy_mean + 0.5 * energy_std
        min_prominence = 0.3 * energy_std
        
        peaks, properties = find_peaks(total_energy, height=min_height,
                                     distance=2, prominence=min_prominence)
        
        print(f"\n📊 峰值检测:")
        print(f"   检测阈值: {min_height:.1f}")
        print(f"   突出度阈值: {min_prominence:.1f}")
        print(f"   检测到峰值: {len(peaks)}个")
        
        if len(peaks) == 0:
            print("❌ 没有检测到峰值")
            return
        
        # 5. 详细分析第一个峰值
        peak_idx = peaks[0]
        peak_time = segment_times[peak_idx]
        peak_energy = total_energy[peak_idx]
        peak_power_spectrum = segment_power[:, peak_idx]
        
        print(f"\n📊 峰值详细分析:")
        print(f"   峰值索引: {peak_idx}")
        print(f"   峰值时间: {peak_time:.3f}s")
        print(f"   峰值能量: {peak_energy:.1f}")
        
        # 6. 竖线判定详细分析
        print(f"\n📊 竖线判定详细分析:")
        print("="*50)
        
        # 分析频谱分布
        print(f"   原始频谱统计:")
        print(f"     最小值: {np.min(peak_power_spectrum):.1f}")
        print(f"     最大值: {np.max(peak_power_spectrum):.1f}")
        print(f"     平均值: {np.mean(peak_power_spectrum):.1f}")
        print(f"     标准差: {np.std(peak_power_spectrum):.1f}")
        
        # 测试不同的阈值
        thresholds = [50, 60, 70, 80, 90]
        print(f"\n   不同阈值下的高能量点分析:")
        
        for thresh_pct in thresholds:
            threshold = np.percentile(peak_power_spectrum, thresh_pct)
            high_energy_mask = peak_power_spectrum > threshold
            high_energy_indices = np.where(high_energy_mask)[0]
            
            if len(high_energy_indices) > 0:
                freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
                freq_ratio = len(high_energy_indices) / len(frequencies)
                
                # 计算线强度
                high_energy_power = peak_power_spectrum[high_energy_indices]
                background_power = np.delete(peak_power_spectrum, high_energy_indices)
                
                if len(background_power) > 0:
                    line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                else:
                    line_strength = 1.0
                
                print(f"     {thresh_pct}%阈值 ({threshold:.1f}):")
                print(f"       高能量点: {len(high_energy_indices)}个")
                print(f"       频率跨度: {freq_span:.1f}Hz")
                print(f"       频率比例: {freq_ratio:.3f}")
                print(f"       线强度: {line_strength:.3f}")
                
                # 判定条件
                freq_span_ok = freq_span >= 1000
                freq_ratio_ok = freq_ratio >= 0.1
                line_strength_ok = line_strength >= 2.0
                
                print(f"       判定: 频率跨度{'✅' if freq_span_ok else '❌'} "
                      f"频率比例{'✅' if freq_ratio_ok else '❌'} "
                      f"线强度{'✅' if line_strength_ok else '❌'}")
                
                if freq_span_ok and freq_ratio_ok and line_strength_ok:
                    print(f"       🎯 在{thresh_pct}%阈值下判定为竖线！")
            else:
                print(f"     {thresh_pct}%阈值: 无高能量点")
        
        # 7. 分析为什么线强度不够
        print(f"\n📊 线强度不足原因分析:")
        print("="*50)
        
        # 使用70%阈值进行详细分析
        threshold_70 = np.percentile(peak_power_spectrum, 70)
        high_energy_mask = peak_power_spectrum > threshold_70
        high_energy_indices = np.where(high_energy_mask)[0]
        
        if len(high_energy_indices) > 0:
            high_energy_power = peak_power_spectrum[high_energy_indices]
            background_indices = np.where(~high_energy_mask)[0]
            background_power = peak_power_spectrum[background_indices]
            
            print(f"   高能量区域分析:")
            print(f"     高能量点数: {len(high_energy_indices)}")
            print(f"     高能量平均: {np.mean(high_energy_power):.1f}")
            print(f"     高能量范围: {np.min(high_energy_power):.1f} - {np.max(high_energy_power):.1f}")
            
            print(f"   背景区域分析:")
            print(f"     背景点数: {len(background_indices)}")
            print(f"     背景平均: {np.mean(background_power):.1f}")
            print(f"     背景范围: {np.min(background_power):.1f} - {np.max(background_power):.1f}")
            
            line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
            print(f"   线强度计算:")
            print(f"     线强度 = 高能量平均 / 背景平均")
            print(f"     线强度 = {np.mean(high_energy_power):.1f} / {np.mean(background_power):.1f}")
            print(f"     线强度 = {line_strength:.3f}")
            print(f"     要求阈值: 2.0")
            print(f"     差距: {2.0 - line_strength:.3f}")
            
            # 分析频率分布
            high_energy_freqs = frequencies[high_energy_indices]
            print(f"\n   高能量频率分布:")
            print(f"     频率范围: {np.min(high_energy_freqs):.1f}Hz - {np.max(high_energy_freqs):.1f}Hz")
            print(f"     期望频率: {expected_freq:.1f}Hz")
            print(f"     是否包含期望频率: {'✅' if np.min(high_energy_freqs) <= expected_freq <= np.max(high_energy_freqs) else '❌'}")
        
        # 8. 生成详细可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'第43段峰值详细分析 - 为什么不是竖线？', fontsize=14, fontweight='bold')
        
        # 子图1: 频谱图
        ax1 = axes[0, 0]
        im1 = ax1.imshow(segment_power, aspect='auto', origin='lower', 
                        extent=[segment_times[0], segment_times[-1], 
                               frequencies[0], frequencies[-1]],
                        cmap='viridis')
        ax1.axvline(x=peak_time, color='red', linewidth=2, alpha=0.8)
        ax1.set_title('频谱图')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('频率 (Hz)')
        plt.colorbar(im1, ax=ax1, label='功率 (dB)')
        
        # 子图2: 总能量
        ax2 = axes[0, 1]
        ax2.plot(segment_times, total_energy, 'b-', linewidth=1.5)
        ax2.axhline(y=min_height, color='orange', linestyle='--', label=f'阈值 ({min_height:.1f})')
        ax2.plot(peak_time, peak_energy, 'ro', markersize=10, label='峰值')
        ax2.set_title('总能量变化')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('总能量')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 峰值时刻频谱
        ax3 = axes[1, 0]
        ax3.plot(frequencies, peak_power_spectrum, 'b-', linewidth=1.5, label='峰值频谱')
        ax3.axhline(y=threshold_70, color='orange', linestyle='--', label=f'70%阈值 ({threshold_70:.1f})')
        ax3.axvline(x=expected_freq, color='red', linestyle=':', label=f'期望频率 ({expected_freq:.1f}Hz)')
        
        # 标记高能量区域
        if len(high_energy_indices) > 0:
            ax3.fill_between(frequencies[high_energy_indices], 
                           np.min(peak_power_spectrum), 
                           peak_power_spectrum[high_energy_indices],
                           alpha=0.3, color='red', label='高能量区域')
        
        ax3.set_title('峰值时刻频谱分析')
        ax3.set_xlabel('频率 (Hz)')
        ax3.set_ylabel('功率 (dB)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 子图4: 线强度分析
        ax4 = axes[1, 1]
        thresholds_plot = np.arange(50, 95, 5)
        line_strengths = []
        
        for thresh_pct in thresholds_plot:
            threshold = np.percentile(peak_power_spectrum, thresh_pct)
            high_mask = peak_power_spectrum > threshold
            high_indices = np.where(high_mask)[0]
            
            if len(high_indices) > 0:
                high_power = peak_power_spectrum[high_indices]
                bg_power = np.delete(peak_power_spectrum, high_indices)
                if len(bg_power) > 0:
                    strength = np.mean(high_power) / (np.mean(bg_power) + 1e-12)
                else:
                    strength = 1.0
            else:
                strength = 0.0
            line_strengths.append(strength)
        
        ax4.plot(thresholds_plot, line_strengths, 'b-o', linewidth=2, markersize=6)
        ax4.axhline(y=2.0, color='red', linestyle='--', linewidth=2, label='竖线阈值 (2.0)')
        ax4.axvline(x=70, color='orange', linestyle=':', alpha=0.7, label='当前使用 (70%)')
        ax4.set_title('线强度随阈值变化')
        ax4.set_xlabel('百分位阈值 (%)')
        ax4.set_ylabel('线强度')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('segment_43_peak_debug.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n✅ 详细分析完成")
        print(f"📊 调试图表已保存: segment_43_peak_debug.png")
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    debug_segment_43_peak()
