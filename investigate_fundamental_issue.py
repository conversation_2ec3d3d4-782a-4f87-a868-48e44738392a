#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调查根本问题：为什么所有特征都无法分离
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def investigate_fundamental_issue():
    """调查根本问题"""
    print("🔍 调查根本问题：为什么所有特征都无法分离")
    print("="*70)
    print("可能的原因:")
    print("1. 样本标记错误 - '噪声'样本实际上是正常的")
    print("2. 特征计算有系统性错误")
    print("3. 数据集本身有问题")
    print("4. 噪声类型不是我们假设的类型")
    print("="*70)
    
    # 检查改进特征数据
    if not os.path.exists('improved_noise_features.csv'):
        print("❌ 未找到改进特征数据")
        return
    
    df = pd.read_csv('improved_noise_features.csv')
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 数据概览:")
    print(f"   总记录数: {len(df)}")
    print(f"   噪声样本记录数: {len(target_data)} (2个文件 × 10个频段 = 20)")
    print(f"   正常样本记录数: {len(normal_data)} (53个文件 × 10个频段 = 530)")
    print(f"   噪声样本文件: {target_data['filename'].unique()}")
    
    # 1. 检查样本标记是否正确
    print(f"\n1️⃣ 检查样本标记:")
    print("-" * 50)
    check_sample_labeling(df, target_files)
    
    # 2. 详细对比噪声样本和正常样本的特征分布
    print(f"\n2️⃣ 详细特征分布对比:")
    print("-" * 50)
    compare_feature_distributions(target_data, normal_data)
    
    # 3. 检查是否有隐藏的分离模式
    print(f"\n3️⃣ 寻找隐藏的分离模式:")
    print("-" * 50)
    find_hidden_patterns(target_data, normal_data)
    
    # 4. 分析频段差异
    print(f"\n4️⃣ 分析频段差异:")
    print("-" * 50)
    analyze_segment_differences(target_data, normal_data)
    
    # 5. 生成可视化
    print(f"\n5️⃣ 生成可视化分析:")
    print("-" * 50)
    create_investigation_visualization(target_data, normal_data)

def check_sample_labeling(df, target_files):
    """检查样本标记是否正确"""
    
    print(f"   检查目标文件标记:")
    for filename in target_files:
        file_data = df[df['filename'] == filename]
        if len(file_data) == 0:
            print(f"     ❌ {filename} 未找到")
        else:
            is_target_values = file_data['is_target'].unique()
            segment_count = len(file_data)
            print(f"     ✅ {filename}: 标记={is_target_values}, 记录数={segment_count}")
    
    # 检查是否有其他文件被误标记
    target_filenames = df[df['is_target'] == True]['filename'].unique()
    print(f"\n   所有被标记为噪声的文件:")
    for filename in target_filenames:
        count = len(df[(df['filename'] == filename) & (df['is_target'] == True)])
        if filename in target_files:
            print(f"     ✅ {filename} (正确，记录数: {count})")
        else:
            print(f"     ❌ {filename} (可能错误，记录数: {count})")

def compare_feature_distributions(target_data, normal_data):
    """详细对比特征分布"""
    
    # 关键特征
    key_features = [
        'snr_db', 'noise_power_db', 'residual_noise_mean_db',
        'spectral_concentration', 'spectral_flatness', 'thd_db'
    ]
    
    print(f"   关键特征统计对比:")
    print(f"{'特征名':>20} {'噪声均值':>12} {'正常均值':>12} {'噪声标准差':>12} {'正常标准差':>12} {'差异':>8}")
    print("-" * 85)
    
    for feature in key_features:
        if feature not in target_data.columns or feature not in normal_data.columns:
            continue
            
        target_values = target_data[feature].dropna()
        normal_values = normal_data[feature].dropna()
        
        if len(target_values) > 0 and len(normal_values) > 0:
            target_mean = np.mean(target_values)
            normal_mean = np.mean(normal_values)
            target_std = np.std(target_values)
            normal_std = np.std(normal_values)
            
            # 计算标准化差异
            pooled_std = np.sqrt((target_std**2 + normal_std**2) / 2)
            standardized_diff = (target_mean - normal_mean) / (pooled_std + 1e-12)
            
            print(f"{feature:>20} {target_mean:>12.3f} {normal_mean:>12.3f} "
                  f"{target_std:>12.3f} {normal_std:>12.3f} {standardized_diff:>+8.3f}")
    
    # 检查分布重叠程度
    print(f"\n   分布重叠分析:")
    for feature in key_features[:3]:  # 只分析前3个特征
        if feature not in target_data.columns:
            continue
            
        target_values = target_data[feature].dropna()
        normal_values = normal_data[feature].dropna()
        
        if len(target_values) > 0 and len(normal_values) > 0:
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            # 计算重叠程度
            overlap_start = max(target_min, normal_min)
            overlap_end = min(target_max, normal_max)
            
            if overlap_start <= overlap_end:
                overlap_size = overlap_end - overlap_start
                target_range = target_max - target_min
                normal_range = normal_max - normal_min
                
                overlap_ratio_target = overlap_size / (target_range + 1e-12)
                overlap_ratio_normal = overlap_size / (normal_range + 1e-12)
                
                print(f"     {feature}:")
                print(f"       噪声范围: [{target_min:.3f}, {target_max:.3f}]")
                print(f"       正常范围: [{normal_min:.3f}, {normal_max:.3f}]")
                print(f"       重叠区间: [{overlap_start:.3f}, {overlap_end:.3f}]")
                print(f"       重叠比例: 噪声{overlap_ratio_target:.1%}, 正常{overlap_ratio_normal:.1%}")
            else:
                print(f"     {feature}: 无重叠 (完全分离)")

def find_hidden_patterns(target_data, normal_data):
    """寻找隐藏的分离模式"""
    
    # 尝试组合特征
    print(f"   尝试组合特征分离:")
    
    # 组合1: SNR + 残余噪声
    if 'snr_db' in target_data.columns and 'residual_noise_mean_db' in target_data.columns:
        target_combo1 = target_data['snr_db'] - target_data['residual_noise_mean_db']
        normal_combo1 = normal_data['snr_db'] - normal_data['residual_noise_mean_db']
        
        check_separation(target_combo1, normal_combo1, "SNR - 残余噪声")
    
    # 组合2: 频谱集中度 × 信噪比
    if 'spectral_concentration' in target_data.columns and 'snr_db' in target_data.columns:
        target_combo2 = target_data['spectral_concentration'] * target_data['snr_db']
        normal_combo2 = normal_data['spectral_concentration'] * normal_data['snr_db']
        
        check_separation(target_combo2, normal_combo2, "频谱集中度 × SNR")
    
    # 组合3: 信号功率 / 噪声功率比值
    if 'signal_power_db' in target_data.columns and 'noise_power_db' in target_data.columns:
        target_combo3 = target_data['signal_power_db'] - target_data['noise_power_db']
        normal_combo3 = normal_data['signal_power_db'] - normal_data['noise_power_db']
        
        check_separation(target_combo3, normal_combo3, "信号功率 - 噪声功率")

def check_separation(target_values, normal_values, combo_name):
    """检查组合特征的分离效果"""
    
    target_clean = target_values.dropna()
    normal_clean = normal_values.dropna()
    
    if len(target_clean) == 0 or len(normal_clean) == 0:
        return
    
    target_min, target_max = np.min(target_clean), np.max(target_clean)
    normal_min, normal_max = np.min(normal_clean), np.max(normal_clean)
    
    if target_max < normal_min:
        gap = normal_min - target_max
        print(f"     ✅ {combo_name}: 完全分离 (噪声在下方，间隙 {gap:.3f})")
    elif target_min > normal_max:
        gap = target_min - normal_max
        print(f"     ✅ {combo_name}: 完全分离 (噪声在上方，间隙 {gap:.3f})")
    else:
        overlap_start = max(target_min, normal_min)
        overlap_end = min(target_max, normal_max)
        overlap_size = overlap_end - overlap_start
        print(f"     ❌ {combo_name}: 有重叠 (重叠大小 {overlap_size:.3f})")

def analyze_segment_differences(target_data, normal_data):
    """分析频段差异"""
    
    print(f"   各频段的分离情况:")
    
    segments = sorted(target_data['segment_idx'].unique())
    
    for seg_idx in segments:
        target_seg = target_data[target_data['segment_idx'] == seg_idx]
        normal_seg = normal_data[normal_data['segment_idx'] == seg_idx]
        
        if len(target_seg) == 0 or len(normal_seg) == 0:
            continue
        
        freq = target_seg['expected_freq'].iloc[0]
        
        # 检查SNR特征在该频段的分离情况
        if 'snr_db' in target_seg.columns:
            target_snr = target_seg['snr_db'].dropna()
            normal_snr = normal_seg['snr_db'].dropna()
            
            if len(target_snr) > 0 and len(normal_snr) > 0:
                target_snr_mean = np.mean(target_snr)
                normal_snr_mean = np.mean(normal_snr)
                
                target_snr_min, target_snr_max = np.min(target_snr), np.max(target_snr)
                normal_snr_min, normal_snr_max = np.min(normal_snr), np.max(normal_snr)
                
                if target_snr_max < normal_snr_min or target_snr_min > normal_snr_max:
                    status = "✅ 分离"
                else:
                    status = "❌ 重叠"
                
                print(f"     频段{seg_idx:2d} ({freq:6.1f}Hz): {status} | "
                      f"噪声SNR={target_snr_mean:6.2f}, 正常SNR={normal_snr_mean:6.2f}")

def create_investigation_visualization(target_data, normal_data):
    """创建调查可视化"""
    
    print(f"   生成调查可视化图表...")
    
    # 选择关键特征进行可视化
    key_features = ['snr_db', 'residual_noise_mean_db', 'spectral_concentration', 'thd_db']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('改进特征分离调查 - 为什么无法分离？', fontsize=16, fontweight='bold')
    
    for i, feature in enumerate(key_features):
        if feature not in target_data.columns:
            continue
            
        ax = axes[i//2, i%2]
        
        target_values = target_data[feature].dropna()
        normal_values = normal_data[feature].dropna()
        
        if len(target_values) > 0 and len(normal_values) > 0:
            # 绘制分布直方图
            ax.hist(normal_values, bins=30, alpha=0.6, color='blue', 
                   label=f'正常样本 (n={len(normal_values)})', density=True)
            ax.hist(target_values, bins=30, alpha=0.8, color='red', 
                   label=f'噪声样本 (n={len(target_values)})', density=True)
            
            # 添加统计信息
            target_mean = np.mean(target_values)
            normal_mean = np.mean(normal_values)
            
            ax.axvline(normal_mean, color='blue', linestyle='--', alpha=0.8, label=f'正常均值: {normal_mean:.3f}')
            ax.axvline(target_mean, color='red', linestyle='--', alpha=0.8, label=f'噪声均值: {target_mean:.3f}')
            
            ax.set_title(f'{feature}')
            ax.set_xlabel('特征值')
            ax.set_ylabel('密度')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 检查重叠情况
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            if target_max < normal_min or target_min > normal_max:
                separation_status = "完全分离"
                color = 'green'
            else:
                separation_status = "有重叠"
                color = 'red'
            
            ax.text(0.5, 0.95, separation_status, transform=ax.transAxes, 
                   ha='center', va='top', fontweight='bold', color=color,
                   bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('investigation_improved_features.png', dpi=300, bbox_inches='tight')
    print(f"     ✅ 可视化已保存: investigation_improved_features.png")
    plt.close()
    
    print(f"\n💡 调查结论:")
    print(f"   如果改进的科学特征都无法分离，可能的原因:")
    print(f"   1. 这两个'噪声'样本实际上是正常的")
    print(f"   2. 噪声类型不是频域可检测的类型")
    print(f"   3. 需要时域特征或其他类型的分析")
    print(f"   4. 样本标记可能有误")

if __name__ == "__main__":
    investigate_fundamental_issue()
