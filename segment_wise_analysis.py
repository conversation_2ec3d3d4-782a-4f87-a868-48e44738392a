#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按频段进行特征分析 - 简化版
保持与93个频段可视化的数据一致性
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_existing_segment_data():
    """分析已有的按频段数据"""
    print("🔍 分析已有的按频段特征数据")
    print("="*70)
    
    # 检查是否有数据文件
    if os.path.exists('top4_features_93_segments.csv'):
        print("📊 加载已有的93个频段数据...")
        df = pd.read_csv('top4_features_93_segments.csv')
    else:
        print("❌ 未找到93个频段数据文件")
        return None
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    print(f"📊 数据概览:")
    print(f"   总记录数: {len(df)}")
    print(f"   频段数: {len(df['segment_idx'].unique())}")
    print(f"   文件数: {len(df['filename'].unique())}")
    print(f"   目标文件数: {len(df[df['is_target']==True]['filename'].unique())}")
    print(f"   正常文件数: {len(df[df['is_target']==False]['filename'].unique())}")
    
    # 分析前四个特征
    top4_features = [
        'true_noise_floor_median',
        'true_noise_floor_mean', 
        'noise_floor_stability_mean',
        'noise_floor_stability_std'
    ]
    
    # 按频段分析分离能力
    analyze_segment_separation(df, top4_features, target_files)
    
    return df

def analyze_segment_separation(df, features, target_files):
    """按频段分析分离能力"""
    print(f"\n🔍 按频段分析前四个特征的分离能力")
    print("="*70)
    
    segments = sorted(df['segment_idx'].unique())
    frequencies = [df[df['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    segment_results = {}
    
    for seg_idx in segments:
        print(f"\n📊 分析频段 {seg_idx} ({frequencies[seg_idx]:.1f}Hz)")
        
        seg_data = df[df['segment_idx'] == seg_idx]
        target_data = seg_data[seg_data['is_target'] == True]
        normal_data = seg_data[seg_data['is_target'] == False]
        
        print(f"   样本数: 目标{len(target_data)}, 正常{len(normal_data)}")
        
        separable_features = []
        
        for feature in features:
            target_values = target_data[feature].dropna()
            normal_values = normal_data[feature].dropna()
            
            if len(target_values) == 0 or len(normal_values) == 0:
                continue
            
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            # 检查完全分离
            separation_gap = 0
            separation_type = 'overlap'
            
            if target_max < normal_min:
                separation_gap = normal_min - target_max
                separation_type = 'target_below_others'
            elif target_min > normal_max:
                separation_gap = target_min - normal_max
                separation_type = 'target_above_others'
            
            if separation_gap > 0:
                separable_features.append({
                    'feature': feature,
                    'separation_gap': separation_gap,
                    'separation_type': separation_type,
                    'target_range': [target_min, target_max],
                    'normal_range': [normal_min, normal_max],
                    'target_mean': np.mean(target_values),
                    'normal_mean': np.mean(normal_values)
                })
        
        # 排序
        separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
        
        segment_results[seg_idx] = {
            'frequency': frequencies[seg_idx],
            'separable_features': separable_features,
            'separable_count': len(separable_features)
        }
        
        print(f"   ✅ 可分离特征数: {len(separable_features)}")
        
        # 显示最佳特征
        if len(separable_features) > 0:
            print(f"   🏆 最佳特征:")
            for i, feature_info in enumerate(separable_features[:2]):
                print(f"     {i+1}. {feature_info['feature']}")
                print(f"        分离间隙: {feature_info['separation_gap']:.6f}")
                print(f"        分离类型: {feature_info['separation_type']}")
                print(f"        目标范围: [{feature_info['target_range'][0]:.3f}, {feature_info['target_range'][1]:.3f}]")
                print(f"        正常范围: [{feature_info['normal_range'][0]:.3f}, {feature_info['normal_range'][1]:.3f}]")
    
    # 生成总结
    generate_summary(segment_results, features)
    
    # 可视化
    visualize_results(df, segment_results, features, target_files)
    
    return segment_results

def generate_summary(segment_results, features):
    """生成总结报告"""
    print(f"\n📊 按频段分离分析总结报告")
    print("="*70)
    
    # 统计有分离能力的频段
    separable_segments = []
    total_separable_features = 0
    
    for seg_idx, result in segment_results.items():
        separable_count = result['separable_count']
        if separable_count > 0:
            separable_segments.append({
                'segment': seg_idx,
                'frequency': result['frequency'],
                'separable_count': separable_count,
                'best_feature': result['separable_features'][0]['feature'] if result['separable_features'] else 'None',
                'best_gap': result['separable_features'][0]['separation_gap'] if result['separable_features'] else 0
            })
            total_separable_features += separable_count
    
    # 排序
    separable_segments.sort(key=lambda x: x['best_gap'], reverse=True)
    
    print(f"📈 整体统计:")
    print(f"   总频段数: {len(segment_results)}")
    print(f"   有分离能力的频段数: {len(separable_segments)}")
    print(f"   分离能力频段比例: {len(separable_segments)/len(segment_results)*100:.1f}%")
    print(f"   总可分离特征数: {total_separable_features}")
    
    print(f"\n🏆 最佳分离频段 (前10个):")
    print(f"{'频段':>4} {'频率(Hz)':>8} {'可分离特征数':>12} {'最佳间隙':>12} {'最佳特征':>25}")
    print("-" * 70)
    
    for i, seg_info in enumerate(separable_segments[:10]):
        print(f"{seg_info['segment']:>4} {seg_info['frequency']:>8.1f} "
              f"{seg_info['separable_count']:>12} {seg_info['best_gap']:>12.6f} "
              f"{seg_info['best_feature'][:25]:>25}")
    
    # 特征统计
    feature_frequency = {}
    for seg_idx, result in segment_results.items():
        for feature_info in result['separable_features']:
            feature_name = feature_info['feature']
            if feature_name not in feature_frequency:
                feature_frequency[feature_name] = 0
            feature_frequency[feature_name] += 1
    
    # 排序特征
    sorted_features = sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n🎯 最有效的特征:")
    print(f"{'特征名':>30} {'出现频段数':>12} {'出现比例':>10}")
    print("-" * 55)
    
    for feature_name, count in sorted_features:
        percentage = count / len(segment_results) * 100
        print(f"{feature_name[:30]:>30} {count:>12} {percentage:>9.1f}%")

def visualize_results(df, segment_results, features, target_files):
    """可视化结果"""
    print(f"\n🎨 生成可视化...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('按频段特征分离能力分析', fontsize=16, fontweight='bold')
    
    segments = sorted(segment_results.keys())
    frequencies = [segment_results[seg]['frequency'] for seg in segments]
    separable_counts = [segment_results[seg]['separable_count'] for seg in segments]
    
    # 1. 每个频段的可分离特征数
    ax1 = axes[0, 0]
    bars = ax1.bar(segments, separable_counts, color='skyblue', alpha=0.7)
    ax1.set_title('各频段可分离特征数量')
    ax1.set_xlabel('频段索引')
    ax1.set_ylabel('可分离特征数')
    ax1.grid(True, alpha=0.3)
    
    # 标记最佳频段
    if separable_counts:
        best_seg_idx = segments[np.argmax(separable_counts)]
        best_count = max(separable_counts)
        ax1.scatter(best_seg_idx, best_count, color='red', s=100, marker='*', zorder=5)
    
    # 2. 最佳分离间隙
    ax2 = axes[0, 1]
    best_gaps = []
    for seg in segments:
        if segment_results[seg]['separable_features']:
            best_gaps.append(segment_results[seg]['separable_features'][0]['separation_gap'])
        else:
            best_gaps.append(0)
    
    ax2.plot(segments, best_gaps, 'ro-', markersize=4, linewidth=2)
    ax2.set_title('各频段最佳分离间隙')
    ax2.set_xlabel('频段索引')
    ax2.set_ylabel('分离间隙')
    ax2.grid(True, alpha=0.3)
    
    # 3. 前四个特征的分离能力对比
    ax3 = axes[1, 0]
    
    feature_counts = {}
    for feature in features:
        feature_counts[feature] = 0
        for seg_idx, result in segment_results.items():
            for feature_info in result['separable_features']:
                if feature_info['feature'] == feature:
                    feature_counts[feature] += 1
    
    feature_names = [f[:15] + '...' if len(f) > 15 else f for f in features]
    counts = [feature_counts[f] for f in features]
    
    bars = ax3.bar(feature_names, counts, color=['red', 'blue', 'green', 'orange'])
    ax3.set_title('前四个特征的分离能力对比')
    ax3.set_xlabel('特征')
    ax3.set_ylabel('可分离频段数')
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    # 4. 频率范围分析
    ax4 = axes[1, 1]
    
    # 按频率范围统计
    freq_ranges = {
        '100-500Hz': (100, 500),
        '500-1000Hz': (500, 1000),
        '1000-2000Hz': (1000, 2000),
        '2000-5000Hz': (2000, 5000),
        '5000-10000Hz': (5000, 10000),
        '10000-20000Hz': (10000, 20000)
    }
    
    range_counts = []
    range_labels = []
    
    for range_name, (freq_min, freq_max) in freq_ranges.items():
        count = 0
        for seg in segments:
            freq = segment_results[seg]['frequency']
            if freq_min <= freq < freq_max and segment_results[seg]['separable_count'] > 0:
                count += 1
        range_counts.append(count)
        range_labels.append(range_name)
    
    ax4.bar(range_labels, range_counts, color='lightgreen', alpha=0.7)
    ax4.set_title('不同频率范围的分离能力')
    ax4.set_xlabel('频率范围')
    ax4.set_ylabel('有分离能力的频段数')
    ax4.tick_params(axis='x', rotation=45)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('segment_wise_separation_analysis.png', dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存: segment_wise_separation_analysis.png")
    plt.close()

if __name__ == "__main__":
    df = analyze_existing_segment_data()
    if df is not None:
        print(f"\n✅ 按频段特征分析完成！")
    else:
        print(f"\n❌ 分析失败，请先运行93个频段数据提取！")
