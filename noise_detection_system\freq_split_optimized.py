#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的频率分割模块
通过生成参考扫频信号并与待测信号对齐来精确定位开始时间
"""

import numpy as np
import librosa
import librosa.display
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import correlate, find_peaks
from chirp import gen_freq_step
import soundfile as sf

class OptimizedFreqSplitter:
    def __init__(self, start_freq=100, stop_freq=20000, octave=12, fs=48000, 
                 min_cycles=10, min_duration=156):
        """
        初始化优化的频率分割器
        
        参数:
        start_freq: 起始频率
        stop_freq: 结束频率
        octave: 倍频程分辨率
        fs: 采样率
        min_cycles: 最小周期数
        min_duration: 最小持续时间(ms)
        """
        self.start_freq = start_freq
        self.stop_freq = stop_freq
        self.octave = octave
        self.fs = fs
        self.min_cycles = min_cycles
        self.min_duration = min_duration
        
        # 生成参考扫频信号
        self.reference_signal, self.reference_times, self.freq_dict = self._generate_reference_signal()
        self.freq_table = list(self.freq_dict.keys())
        self.step_durations = [self.freq_dict[f][2] / fs for f in self.freq_table]
        
        print(f"参考信号生成完成: {len(self.freq_table)}个频点, 总时长{self.reference_times[-1]:.2f}秒")
    
    def _generate_reference_signal(self):
        """生成参考扫频信号"""
        sine_wave, t_list, freq_ssample_dict = gen_freq_step(
            self.start_freq, self.stop_freq, self.min_cycles, 
            self.min_duration, self.octave, self.fs, A=1
        )
        return sine_wave, t_list, freq_ssample_dict
    
    def split_freq_steps_with_alignment(self, audio_path, 
                                      search_window_start=0.1, search_window_end=2.0,
                                      correlation_length=2.0, plot=True, debug=False):
        """
        使用参考信号对齐的方法分割频率步进
        
        参数:
        audio_path: 音频文件路径
        search_window_start: 搜索窗口开始时间(秒)
        search_window_end: 搜索窗口结束时间(秒)
        correlation_length: 用于相关性计算的信号长度(秒)
        plot: 是否绘制结果
        debug: 是否显示调试信息
        
        返回:
        step_boundaries: 步进边界列表
        freq_table: 频率表
        alignment_info: 对齐信息
        """
        print(f"\n🔍 使用参考信号对齐方法分析: {audio_path}")
        
        # 读取音频
        y, sr = librosa.load(audio_path, sr=self.fs)
        print(f"音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        
        # 如果采样率不匹配，重采样
        if sr != self.fs:
            y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            sr = self.fs
            print(f"重采样到{self.fs}Hz")
        
        # 在搜索窗口内寻找最佳对齐位置
        alignment_result = self._find_alignment_position(
            y, search_window_start, search_window_end, correlation_length, debug
        )
        
        if alignment_result is None:
            print("❌ 对齐失败，使用传统方法")
            return self._fallback_to_traditional_method(y, sr, plot)
        
        start_offset, correlation_score, search_info = alignment_result
        print(f"✅ 对齐成功: 开始时间={start_offset:.3f}秒, 相关性={correlation_score:.3f}")
        
        # 计算步进边界
        step_boundaries = self._calculate_step_boundaries(start_offset)
        
        # 验证对齐质量
        alignment_quality = self._validate_alignment(y, start_offset, correlation_length)
        
        # 可视化结果
        if plot:
            self._plot_alignment_results(y, sr, step_boundaries, start_offset, 
                                       correlation_score, search_info, alignment_quality)
        
        alignment_info = {
            'start_offset': start_offset,
            'correlation_score': correlation_score,
            'alignment_quality': alignment_quality,
            'search_info': search_info
        }
        
        return step_boundaries, self.freq_table, alignment_info
    
    def _find_alignment_position(self, y, search_start, search_end, corr_length, debug):
        """在搜索窗口内寻找最佳对齐位置"""
        # 确定搜索范围
        search_start_sample = int(search_start * self.fs)
        search_end_sample = int(search_end * self.fs)
        corr_length_samples = int(corr_length * self.fs)
        
        # 确保搜索范围有效
        search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
        if search_start_sample >= search_end_sample:
            print(f"❌ 搜索范围无效: {search_start:.2f}s - {search_end:.2f}s")
            return None
        
        # 准备参考信号段
        ref_length_samples = min(corr_length_samples, len(self.reference_signal))
        reference_segment = self.reference_signal[:ref_length_samples]
        
        # 在搜索窗口内滑动计算相关性
        search_length = search_end_sample - search_start_sample
        correlations = []
        positions = []
        
        print(f"在{search_start:.2f}s-{search_end:.2f}s窗口内搜索最佳对齐位置...")
        
        # 使用滑动窗口计算相关性 + 频率特征
        step_size = max(1, self.fs // 500)  # 2ms步长，提高精度
        frequency_scores = []

        for i in range(0, search_length - corr_length_samples, step_size):
            pos = search_start_sample + i
            test_segment = y[pos:pos + ref_length_samples]

            if len(test_segment) == len(reference_segment):
                # 计算归一化互相关
                correlation = self._normalized_cross_correlation(reference_segment, test_segment)
                correlations.append(correlation)
                positions.append(pos / self.fs)

                # 计算频率特征评分（检测扫频开始特征）
                freq_score = self._calculate_frequency_score(test_segment)
                frequency_scores.append(freq_score)
        
        if not correlations:
            print("❌ 未找到有效的相关性计算结果")
            return None
        
        # 智能选择：优先选择时间更靠前的高相关性点
        correlations = np.array(correlations)
        positions = np.array(positions)

        # 找到最大相关性
        max_correlation = np.max(correlations)
        max_idx = np.argmax(correlations)

        # 定义相关性门槛：最大相关性的70%，更宽松地选择早期点
        correlation_threshold = max_correlation * 0.7

        # 同时设置绝对最低阈值，确保基本质量
        absolute_threshold = 0.4  # 绝对最低相关性要求
        final_threshold = max(correlation_threshold, absolute_threshold)

        # 找到所有相关性超过阈值的点
        good_indices = np.where(correlations >= final_threshold)[0]

        if debug:
            print(f"🔍 CRITICAL DEBUG: 阈值选择分析")
            print(f"    final_threshold = {final_threshold:.3f}")
            print(f"    good_indices 数量 = {len(good_indices)}")
            if len(good_indices) > 0:
                print(f"    good_indices[:3] = {good_indices[:3]}")

        if len(good_indices) > 0:
            # 优化策略：在满足条件的点中寻找峰值点

            # 在符合条件的点中寻找局部峰值
            good_correlations = correlations[good_indices]
            good_positions = positions[good_indices]

            # 寻找峰值点（放宽参数以便检测到更多峰值）
            peaks, peak_properties = find_peaks(good_correlations,
                                              height=final_threshold*0.95,  # 稍微降低高度要求
                                              distance=3,  # 减少最小距离
                                              prominence=0.02)  # 降低突出度要求

            if len(peaks) > 0:
                # 选择第一个峰值点（时间最早的峰值）
                first_peak_idx = peaks[0]
                selected_good_idx = good_indices[first_peak_idx]
                best_position = positions[selected_good_idx]
                best_correlation = correlations[selected_good_idx]
                selection_method = "first_peak_correlation"

                if debug:
                    print(f"  🎯 峰值选择策略:")
                    print(f"    最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
                    print(f"    相关性阈值: {final_threshold:.3f}")
                    print(f"    符合条件的点: {len(good_indices)}个")
                    print(f"    检测到峰值: {len(peaks)}个")
                    print(f"    🔍 所有峰值点:")
                    for i, peak_idx in enumerate(peaks[:3]):
                        actual_idx = good_indices[peak_idx]
                        print(f"      峰值{i+1}: 时间={positions[actual_idx]:.3f}s, 相关性={correlations[actual_idx]:.3f}")
                    print(f"    ✅ 选择第一个峰值: {best_position:.3f}s (相关性: {best_correlation:.3f})")
            else:
                # 没有找到明显峰值，回退到第一个满足条件的点
                earliest_idx = good_indices[0]
                best_position = positions[earliest_idx]
                best_correlation = correlations[earliest_idx]
                selection_method = "first_good_correlation"

                if debug:
                    print(f"  ⚠️ 未检测到明显峰值，使用第一个满足条件的点:")
                    print(f"    选择位置: {best_position:.3f}s (相关性: {best_correlation:.3f})")
        else:
            # 没有满足条件的点，使用最大相关性
            best_position = positions[max_idx]
            best_correlation = correlations[max_idx]
            selection_method = "max_correlation"

            if debug:
                print(f"  选择最大相关性点: {best_position:.3f}s (相关性: {best_correlation:.3f})")
        
        if debug:
            print(f"相关性搜索结果:")
            print(f"  搜索范围: {len(positions)}个位置")
            print(f"  最大相关性: {best_correlation:.4f}")
            print(f"  最佳位置: {best_position:.3f}秒")
            
            # 显示前5个最佳位置
            sorted_indices = np.argsort(correlations)[::-1]
            print(f"  前5个最佳位置:")
            for i in range(min(5, len(sorted_indices))):
                idx = sorted_indices[i]
                print(f"    {i+1}. 时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.4f}")
        
        search_info = {
            'search_range': (search_start, search_end),
            'correlations': correlations,
            'positions': positions,
            'max_correlation': best_correlation,
            'search_points': len(positions)
        }
        
        return best_position, best_correlation, search_info

    def _detect_sweep_start_by_frequency_pattern(self, y, search_start_sample, search_end_sample, debug):
        """基于扫频频率模式检测信号开始点"""
        candidates = []

        # 计算短时傅里叶变换
        window_size = int(0.1 * self.fs)  # 100ms窗口
        hop_size = int(0.01 * self.fs)    # 10ms跳跃

        print(f"  分析扫频频率模式...")

        for pos in range(search_start_sample, search_end_sample - window_size, hop_size):
            segment = y[pos:pos + window_size]

            # 计算频谱
            fft = np.fft.fft(segment)
            freqs = np.fft.fftfreq(len(segment), 1/self.fs)
            magnitude = np.abs(fft[:len(fft)//2])
            freqs = freqs[:len(freqs)//2]

            # 检查是否符合扫频开始特征（低频能量集中）
            low_freq_mask = (freqs >= 80) & (freqs <= 200)  # 扫频开始的低频范围
            mid_freq_mask = (freqs >= 200) & (freqs <= 1000)
            high_freq_mask = (freqs >= 1000) & (freqs <= 5000)

            if np.sum(low_freq_mask) > 0 and np.sum(mid_freq_mask) > 0:
                low_freq_energy = np.sum(magnitude[low_freq_mask])
                mid_freq_energy = np.sum(magnitude[mid_freq_mask])
                high_freq_energy = np.sum(magnitude[high_freq_mask]) if np.sum(high_freq_mask) > 0 else 0

                total_energy = low_freq_energy + mid_freq_energy + high_freq_energy

                if total_energy > 0:
                    # 扫频开始应该低频能量占主导
                    low_freq_ratio = low_freq_energy / total_energy

                    # 计算频谱的"尖锐度" - 扫频信号应该有明显的峰值
                    peak_freq_idx = np.argmax(magnitude[low_freq_mask]) if np.sum(low_freq_mask) > 0 else 0
                    if peak_freq_idx < len(magnitude[low_freq_mask]):
                        peak_magnitude = magnitude[low_freq_mask][peak_freq_idx]
                        avg_magnitude = np.mean(magnitude[low_freq_mask])
                        sharpness = peak_magnitude / avg_magnitude if avg_magnitude > 0 else 0
                    else:
                        sharpness = 0

                    # 评分：低频比例 + 频谱尖锐度
                    score = low_freq_ratio * 0.7 + min(sharpness / 10, 0.3)

                    if score > 0.3:  # 阈值
                        candidates.append({
                            'position': pos / self.fs,
                            'score': score,
                            'low_freq_ratio': low_freq_ratio,
                            'sharpness': sharpness,
                            'method': 'frequency_pattern'
                        })

        # 按评分排序
        candidates.sort(key=lambda x: x['score'], reverse=True)

        if debug and candidates:
            print(f"    找到{len(candidates)}个扫频特征候选点:")
            for i, cand in enumerate(candidates[:3]):
                print(f"      {i+1}. 时间={cand['position']:.3f}s, 评分={cand['score']:.3f}, "
                      f"低频比例={cand['low_freq_ratio']:.3f}, 尖锐度={cand['sharpness']:.1f}")

        return candidates[:5]  # 返回前5个候选

    def _detect_by_correlation(self, y, search_start_sample, search_end_sample, corr_length_samples, debug):
        """传统相关性检测方法"""
        candidates = []

        # 使用较短的参考段
        initial_corr_length = min(corr_length_samples, int(0.3 * self.fs))  # 300ms
        reference_segment = self.reference_signal[:initial_corr_length]

        print(f"  相关性检测 (参考段长度: {initial_corr_length/self.fs:.2f}秒)...")

        step_size = max(1, self.fs // 500)  # 2ms步长，提高精度
        search_length = search_end_sample - search_start_sample

        for i in range(0, search_length - initial_corr_length, step_size):
            pos = search_start_sample + i
            test_segment = y[pos:pos + initial_corr_length]

            if len(test_segment) == len(reference_segment):
                correlation = self._normalized_cross_correlation(reference_segment, test_segment)

                if correlation > 0.4:  # 只保留相关性较好的候选
                    candidates.append({
                        'position': pos / self.fs,
                        'score': correlation,
                        'correlation': correlation,
                        'method': 'correlation'
                    })

        # 按相关性排序
        candidates.sort(key=lambda x: x['score'], reverse=True)

        if debug and candidates:
            print(f"    找到{len(candidates)}个相关性候选点:")
            for i, cand in enumerate(candidates[:3]):
                print(f"      {i+1}. 时间={cand['position']:.3f}s, 相关性={cand['correlation']:.3f}")

        return candidates[:5]  # 返回前5个候选

    def _select_best_alignment(self, sweep_candidates, correlation_candidates, debug):
        """综合评估选择最佳对齐位置"""

        # 策略1: 优先选择扫频特征检测的结果
        if sweep_candidates:
            best_sweep = sweep_candidates[0]

            # 检查是否有相关性验证
            for corr_cand in correlation_candidates:
                time_diff = abs(corr_cand['position'] - best_sweep['position'])
                if time_diff < 0.05:  # 50ms内认为是同一个点
                    # 扫频特征 + 相关性验证，最佳选择
                    combined_score = best_sweep['score'] * 0.6 + corr_cand['score'] * 0.4
                    if debug:
                        print(f"  ✅ 扫频特征+相关性验证: 时间={best_sweep['position']:.3f}s, "
                              f"综合评分={combined_score:.3f}")
                    return best_sweep['position'], combined_score

            # 只有扫频特征，但评分足够高
            if best_sweep['score'] > 0.5:
                if debug:
                    print(f"  ✅ 扫频特征检测: 时间={best_sweep['position']:.3f}s, "
                          f"评分={best_sweep['score']:.3f}")
                return best_sweep['position'], best_sweep['score']

        # 策略2: 使用相关性最高的结果
        if correlation_candidates:
            best_corr = correlation_candidates[0]
            if debug:
                print(f"  ✅ 相关性检测: 时间={best_corr['position']:.3f}s, "
                      f"相关性={best_corr['correlation']:.3f}")
            return best_corr['position'], best_corr['correlation']

        # 策略3: 都失败了
        if debug:
            print(f"  ❌ 所有检测方法都失败")
        return None, 0
    
    def _normalized_cross_correlation(self, ref_signal, test_signal):
        """计算归一化互相关"""
        # 确保信号长度相同
        min_len = min(len(ref_signal), len(test_signal))
        ref_signal = ref_signal[:min_len]
        test_signal = test_signal[:min_len]
        
        # 归一化
        ref_norm = ref_signal - np.mean(ref_signal)
        test_norm = test_signal - np.mean(test_signal)
        
        # 计算相关系数
        correlation = np.corrcoef(ref_norm, test_norm)[0, 1]
        
        # 处理NaN值
        if np.isnan(correlation):
            correlation = 0.0
        
        return correlation

    def _calculate_frequency_score(self, signal_segment):
        """计算信号段的频率特征评分（用于检测扫频开始）"""
        if len(signal_segment) < 1024:
            return 0.0

        # 计算频谱
        fft = np.fft.fft(signal_segment)
        freqs = np.fft.fftfreq(len(signal_segment), 1/self.fs)
        magnitude = np.abs(fft[:len(fft)//2])
        freqs = freqs[:len(freqs)//2]

        # 定义频率范围
        low_freq_mask = (freqs >= 80) & (freqs <= 300)   # 扫频开始的低频范围
        mid_freq_mask = (freqs >= 300) & (freqs <= 1000)
        high_freq_mask = (freqs >= 1000) & (freqs <= 5000)

        if np.sum(low_freq_mask) == 0:
            return 0.0

        # 计算各频段能量
        low_freq_energy = np.sum(magnitude[low_freq_mask])
        mid_freq_energy = np.sum(magnitude[mid_freq_mask]) if np.sum(mid_freq_mask) > 0 else 0
        high_freq_energy = np.sum(magnitude[high_freq_mask]) if np.sum(high_freq_mask) > 0 else 0

        total_energy = low_freq_energy + mid_freq_energy + high_freq_energy

        if total_energy == 0:
            return 0.0

        # 扫频开始特征：低频能量占主导
        low_freq_ratio = low_freq_energy / total_energy

        # 计算频谱的"尖锐度" - 扫频信号应该有明显的峰值
        if np.sum(low_freq_mask) > 0:
            peak_magnitude = np.max(magnitude[low_freq_mask])
            avg_magnitude = np.mean(magnitude[low_freq_mask])
            sharpness = peak_magnitude / avg_magnitude if avg_magnitude > 0 else 0
        else:
            sharpness = 0

        # 综合评分
        score = low_freq_ratio * 0.7 + min(sharpness / 10, 0.3)

        return min(score, 1.0)  # 限制在0-1范围内
    
    def _calculate_step_boundaries(self, start_offset):
        """根据对齐的开始时间计算步进边界"""
        step_boundaries = []
        t = start_offset
        
        for duration in self.step_durations:
            step_boundaries.append((t, t + duration))
            t += duration
        
        print(f"计算出{len(step_boundaries)}个步进区间")
        return step_boundaries
    
    def _validate_alignment(self, y, start_offset, validation_length):
        """验证对齐质量"""
        validation_samples = int(validation_length * self.fs)
        start_sample = int(start_offset * self.fs)
        
        if start_sample + validation_samples > len(y):
            validation_samples = len(y) - start_sample
        
        if validation_samples <= 0:
            return {'quality': 'poor', 'score': 0.0, 'reason': 'insufficient_data'}
        
        # 提取对应的音频段和参考段
        audio_segment = y[start_sample:start_sample + validation_samples]
        ref_segment = self.reference_signal[:validation_samples]
        
        # 计算多个质量指标
        correlation = self._normalized_cross_correlation(ref_segment, audio_segment)
        
        # 频域相似性
        freq_similarity = self._frequency_domain_similarity(ref_segment, audio_segment)
        
        # 综合评分
        overall_score = (correlation + freq_similarity) / 2
        
        if overall_score > 0.7:
            quality = 'excellent'
        elif overall_score > 0.5:
            quality = 'good'
        elif overall_score > 0.3:
            quality = 'fair'
        else:
            quality = 'poor'
        
        return {
            'quality': quality,
            'overall_score': overall_score,
            'time_correlation': correlation,
            'frequency_similarity': freq_similarity,
            'validation_length': validation_length
        }
    
    def _frequency_domain_similarity(self, ref_signal, test_signal):
        """计算频域相似性"""
        # 计算功率谱密度
        f_ref, psd_ref = signal.welch(ref_signal, fs=self.fs, nperseg=1024)
        f_test, psd_test = signal.welch(test_signal, fs=self.fs, nperseg=1024)
        
        # 归一化
        psd_ref = psd_ref / np.sum(psd_ref)
        psd_test = psd_test / np.sum(psd_test)
        
        # 计算相关性
        similarity = np.corrcoef(psd_ref, psd_test)[0, 1]
        
        if np.isnan(similarity):
            similarity = 0.0
        
        return similarity
    
    def _fallback_to_traditional_method(self, y, sr, plot):
        """回退到传统的能量阈值方法"""
        print("使用传统能量阈值方法...")
        
        # 使用RMS能量检测
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms_db = librosa.amplitude_to_db(rms, ref=np.max)
        frame_times = librosa.frames_to_time(np.arange(len(rms_db)), sr=sr, hop_length=512)
        
        # 寻找能量阈值以上的起点
        energy_threshold_db = -30
        above_threshold = np.where(rms_db > energy_threshold_db)[0]
        
        if len(above_threshold) > 0:
            start_offset = frame_times[above_threshold[0]]
        else:
            start_offset = 0.2  # 默认起点
        
        step_boundaries = self._calculate_step_boundaries(start_offset)
        
        return step_boundaries, self.freq_table, {'method': 'traditional', 'start_offset': start_offset}

    def _plot_alignment_results(self, y, sr, step_boundaries, start_offset,
                               correlation_score, search_info, alignment_quality):
        """绘制对齐结果"""
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 1. 时域波形对比
        ax1 = axes[0]
        time_axis = np.arange(len(y)) / sr
        ax1.plot(time_axis, y, 'b-', alpha=0.7, label='待测信号')

        # 显示参考信号（对齐后）
        ref_start_sample = int(start_offset * sr)
        ref_end_sample = min(ref_start_sample + len(self.reference_signal), len(y))
        ref_time_axis = time_axis[ref_start_sample:ref_end_sample]
        ref_signal_aligned = self.reference_signal[:len(ref_time_axis)]

        if len(ref_time_axis) > 0:
            # 归一化参考信号幅度
            ref_signal_normalized = ref_signal_aligned * np.max(np.abs(y)) / np.max(np.abs(ref_signal_aligned))
            ax1.plot(ref_time_axis, ref_signal_normalized, 'r-', alpha=0.8, label='参考信号(对齐)')

        # 标记步进边界
        for i, (start_t, end_t) in enumerate(step_boundaries[:10]):  # 只显示前10个
            ax1.axvline(start_t, color='cyan', linestyle='--', alpha=0.6)
            if i == 0:
                ax1.axvline(start_t, color='cyan', linestyle='--', alpha=0.6, label='步进边界')

        ax1.axvline(start_offset, color='red', linestyle='-', linewidth=2, label=f'对齐起点({start_offset:.3f}s)')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('幅度')
        ax1.set_title(f'时域对齐结果 (相关性: {correlation_score:.3f}, 质量: {alignment_quality["quality"]})')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, min(5, time_axis[-1]))  # 只显示前5秒

        # 2. 相关性搜索结果 + 智能选择策略
        ax2 = axes[1]
        if 'correlations' in search_info:
            # 绘制相关性曲线
            ax2.plot(search_info['positions'], search_info['correlations'], 'b-', linewidth=2, label='相关性')

            # 计算并显示实际使用的阈值
            max_correlation = search_info['max_correlation']
            relative_threshold = max_correlation * 0.7  # 70%阈值
            absolute_threshold = 0.4
            final_threshold = max(relative_threshold, absolute_threshold)

            # 标记最大相关性位置
            max_idx = np.argmax(search_info['correlations'])
            ax2.plot(search_info['positions'][max_idx], search_info['correlations'][max_idx],
                    'go', markersize=8, label=f'最大相关性 ({max_correlation:.3f})')

            # 标记实际选择的位置
            ax2.axvline(start_offset, color='red', linestyle='-', linewidth=2,
                       label=f'选择位置 ({start_offset:.3f}s)')

            # 标记智能选择阈值线
            ax2.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, alpha=0.8,
                       label=f'选择阈值 ({final_threshold:.3f})')
            ax2.axhline(relative_threshold, color='purple', linestyle=':', alpha=0.6,
                       label=f'70%阈值 ({relative_threshold:.3f})')
            ax2.axhline(absolute_threshold, color='brown', linestyle=':', alpha=0.6,
                       label=f'绝对阈值 ({absolute_threshold:.3f})')

            # 高亮显示符合条件的区域
            good_mask = search_info['correlations'] >= final_threshold
            if np.any(good_mask):
                ax2.fill_between(search_info['positions'], 0, search_info['correlations'],
                               where=good_mask, alpha=0.2, color='green',
                               label='符合条件区域')

        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('相关性系数')
        ax2.set_title('智能选择策略分析')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)

        # 3. 频谱图
        ax3 = axes[2]
        S = librosa.stft(y, n_fft=2048, hop_length=512)
        S_db = librosa.amplitude_to_db(np.abs(S), ref=np.max)

        img = librosa.display.specshow(S_db, sr=sr, hop_length=512, x_axis='time',
                                      y_axis='log', cmap='magma', ax=ax3)

        # 标记步进边界
        for i, (start_t, end_t) in enumerate(step_boundaries[:20]):  # 显示前20个
            ax3.axvline(start_t, color='cyan', linestyle='--', linewidth=1, alpha=0.8)

        ax3.axvline(start_offset, color='red', linestyle='-', linewidth=2, alpha=0.9)
        ax3.set_title('频谱图 + 对齐后的步进分割')
        ax3.set_xlim(0, min(10, time_axis[-1]))  # 只显示前10秒

        plt.colorbar(img, ax=ax3, format='%+2.0f dB')
        plt.tight_layout()
        plt.show()

        # 打印对齐质量报告
        print(f"\n📊 对齐质量报告:")
        print(f"  总体质量: {alignment_quality['quality']}")
        print(f"  综合评分: {alignment_quality['overall_score']:.3f}")
        print(f"  时域相关性: {alignment_quality['time_correlation']:.3f}")
        print(f"  频域相似性: {alignment_quality['frequency_similarity']:.3f}")
        print(f"  验证长度: {alignment_quality['validation_length']:.1f}秒")

def split_freq_steps_optimized(audio_path, start_freq=100, stop_freq=20000, octave=12, 
                              min_cycles=10, min_duration=156, fs=48000,
                              search_window_start=0.2, search_window_end=2.0,
                              correlation_length=2.0, plot=True, debug=False):
    """
    优化的频率步进分割函数
    
    参数:
    audio_path: 音频文件路径
    start_freq, stop_freq: 频率范围
    octave: 倍频程分辨率
    min_cycles, min_duration: 信号生成参数
    fs: 采样率
    search_window_start, search_window_end: 搜索窗口
    correlation_length: 相关性计算长度
    plot: 是否绘图
    debug: 是否显示调试信息
    
    返回:
    step_boundaries: 步进边界
    freq_table: 频率表
    alignment_info: 对齐信息
    """
    splitter = OptimizedFreqSplitter(start_freq, stop_freq, octave, fs, min_cycles, min_duration)
    return splitter.split_freq_steps_with_alignment(
        audio_path, search_window_start, search_window_end, correlation_length, plot, debug
    )

if __name__ == "__main__":
    # 测试优化的频率分割
    audio_path = r"test20250717\pos\sd卡\sd1_1.wav"
    
    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
        audio_path, 
        min_duration=153, 
        plot=True, 
        debug=True,
        search_window_start=0.1,
        search_window_end=1.5,
        correlation_length=1.0
    )
    
    print(f"\n✅ 分割完成:")
    print(f"  步进区间数: {len(step_bounds)}")
    print(f"  频率点数: {len(freq_table)}")
    print(f"  对齐信息: {alignment_info}")
