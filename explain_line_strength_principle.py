#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解释线强度阈值的原理
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import stft

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def explain_line_strength_principle():
    """解释线强度阈值的原理"""
    print("🔍 线强度阈值原理详解")
    print("="*70)
    
    # 1. 线强度的定义和计算
    print("📊 1. 线强度的定义和计算")
    print("-" * 50)
    print("线强度 (Line Strength) = 高能量区域平均功率 / 背景区域平均功率")
    print("")
    print("物理意义:")
    print("  - 衡量频谱中某些频率成分相对于背景的突出程度")
    print("  - 值越大，说明这些频率成分越突出")
    print("  - 竖线异常的特征是某些频率成分异常突出")
    
    # 2. 创建示例数据来说明原理
    print(f"\n📊 2. 通过示例数据说明原理")
    print("-" * 50)
    
    # 创建不同类型的频谱
    frequencies = np.linspace(100, 20000, 1000)
    
    # 示例1: 正常频谱 (相对平坦)
    normal_spectrum = -90 + 10 * np.random.randn(1000) + 5 * np.sin(frequencies/1000)
    
    # 示例2: 轻微异常 (某些频率稍微突出)
    mild_anomaly = normal_spectrum.copy()
    anomaly_indices = np.where((frequencies >= 1000) & (frequencies <= 5000))[0]
    mild_anomaly[anomaly_indices] += 15  # 增加15dB
    
    # 示例3: 明显竖线 (某些频率大幅突出)
    strong_line = normal_spectrum.copy()
    line_indices = np.where((frequencies >= 2000) & (frequencies <= 8000))[0]
    strong_line[line_indices] += 35  # 增加35dB
    
    # 示例4: 极强竖线 (某些频率极度突出)
    extreme_line = normal_spectrum.copy()
    extreme_indices = np.where((frequencies >= 1500) & (frequencies <= 10000))[0]
    extreme_line[extreme_indices] += 50  # 增加50dB
    
    spectrums = {
        '正常频谱': normal_spectrum,
        '轻微异常': mild_anomaly,
        '明显竖线': strong_line,
        '极强竖线': extreme_line
    }
    
    # 3. 计算每种情况的线强度
    print(f"\n📊 3. 不同情况下的线强度计算")
    print("-" * 50)
    
    results = {}
    threshold_percentile = 70  # 使用70%阈值
    
    for name, spectrum in spectrums.items():
        # 计算阈值
        threshold = np.percentile(spectrum, threshold_percentile)
        
        # 分离高能量和背景区域
        high_energy_mask = spectrum > threshold
        high_energy_power = spectrum[high_energy_mask]
        background_power = spectrum[~high_energy_mask]
        
        # 计算线强度
        if len(background_power) > 0:
            line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
        else:
            line_strength = 1.0
        
        # 计算其他统计量
        high_energy_ratio = len(high_energy_power) / len(spectrum)
        power_contrast = np.mean(high_energy_power) - np.mean(background_power)
        
        results[name] = {
            'spectrum': spectrum,
            'threshold': threshold,
            'high_energy_count': len(high_energy_power),
            'high_energy_ratio': high_energy_ratio,
            'high_energy_avg': np.mean(high_energy_power),
            'background_avg': np.mean(background_power),
            'line_strength': line_strength,
            'power_contrast': power_contrast
        }
        
        print(f"  {name}:")
        print(f"    阈值 (70%): {threshold:.1f} dB")
        print(f"    高能量点数: {len(high_energy_power)} ({high_energy_ratio:.1%})")
        print(f"    高能量平均: {np.mean(high_energy_power):.1f} dB")
        print(f"    背景平均: {np.mean(background_power):.1f} dB")
        print(f"    功率对比: {power_contrast:.1f} dB")
        print(f"    线强度: {line_strength:.3f}")
        
        # 判定结果
        if line_strength >= 2.0:
            print(f"    判定: ✅ 竖线异常 (线强度 ≥ 2.0)")
        elif line_strength >= 1.5:
            print(f"    判定: ⚠️ 可疑异常 (1.5 ≤ 线强度 < 2.0)")
        else:
            print(f"    判定: ✅ 正常 (线强度 < 1.5)")
        print()
    
    # 4. 线强度阈值的选择原理
    print(f"\n📊 4. 线强度阈值选择原理")
    print("-" * 50)
    print("阈值选择考虑因素:")
    print("")
    print("1. 物理意义:")
    print("   - 线强度 = 1.0: 高能量区域与背景相同，无异常")
    print("   - 线强度 = 2.0: 高能量区域是背景的2倍，明显异常")
    print("   - 线强度 > 3.0: 高能量区域是背景的3倍以上，严重异常")
    print("")
    print("2. 统计学意义:")
    print("   - 正常音频的线强度通常在 0.5-1.5 范围内")
    print("   - 轻微异常的线强度在 1.5-2.0 范围内")
    print("   - 明显异常的线强度在 2.0-5.0 范围内")
    print("   - 严重异常的线强度 > 5.0")
    print("")
    print("3. 实际应用考虑:")
    print("   - 阈值太低 (如1.5): 容易误报，将正常变化判定为异常")
    print("   - 阈值太高 (如5.0): 容易漏报，只能检测到极严重异常")
    print("   - 阈值2.0: 平衡误报和漏报，适合大多数应用场景")
    
    # 5. 生成可视化说明
    print(f"\n📊 5. 生成可视化说明")
    print("-" * 50)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('线强度阈值原理说明', fontsize=16, fontweight='bold')
    
    colors = ['blue', 'orange', 'red', 'purple']
    
    for i, (name, result) in enumerate(results.items()):
        ax = axes[i//2, i%2]
        spectrum = result['spectrum']
        threshold = result['threshold']
        line_strength = result['line_strength']
        
        # 绘制频谱
        ax.plot(frequencies, spectrum, color=colors[i], linewidth=1.5, label='频谱')
        ax.axhline(y=threshold, color='orange', linestyle='--', linewidth=2, 
                  label=f'70%阈值 ({threshold:.1f}dB)')
        
        # 标记高能量区域
        high_energy_mask = spectrum > threshold
        if np.any(high_energy_mask):
            ax.fill_between(frequencies, threshold, spectrum, 
                          where=high_energy_mask, alpha=0.3, color='red',
                          label='高能量区域')
        
        # 标记背景区域
        background_mask = spectrum <= threshold
        if np.any(background_mask):
            ax.fill_between(frequencies, np.min(spectrum), spectrum,
                          where=background_mask, alpha=0.2, color='gray',
                          label='背景区域')
        
        ax.set_title(f'{name}\n线强度: {line_strength:.3f}')
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加判定结果
        if line_strength >= 2.0:
            ax.text(0.02, 0.98, '✅ 竖线异常', transform=ax.transAxes, 
                   fontsize=12, fontweight='bold', color='red',
                   verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
        else:
            ax.text(0.02, 0.98, '✅ 正常', transform=ax.transAxes,
                   fontsize=12, fontweight='bold', color='green',
                   verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('line_strength_principle_explanation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 6. 线强度与dB差值的关系
    print(f"\n📊 6. 线强度与dB差值的关系")
    print("-" * 50)
    
    print("线强度的数学关系:")
    print("  线强度 = 10^(ΔdB/10)")
    print("  其中 ΔdB = 高能量平均dB - 背景平均dB")
    print("")
    print("常见对应关系:")
    db_differences = [0, 3, 6, 10, 15, 20]
    for db_diff in db_differences:
        line_strength_calc = 10**(db_diff/10)
        print(f"  ΔdB = {db_diff:2d}dB  →  线强度 = {line_strength_calc:.3f}")
    
    print("")
    print("实际案例分析:")
    for name, result in results.items():
        db_diff = result['power_contrast']
        theoretical_strength = 10**(db_diff/10)
        actual_strength = result['line_strength']
        print(f"  {name}:")
        print(f"    实际dB差值: {db_diff:.1f}dB")
        print(f"    理论线强度: {theoretical_strength:.3f}")
        print(f"    实际线强度: {actual_strength:.3f}")
        print(f"    差异原因: 百分位阈值分割的影响")
        print()
    
    # 7. 阈值设置建议
    print(f"\n📊 7. 阈值设置建议")
    print("-" * 50)
    print("根据应用场景选择阈值:")
    print("")
    print("1. 严格检测 (阈值 = 1.5):")
    print("   - 适用于高质量要求的场景")
    print("   - 能检测到轻微异常")
    print("   - 可能有较多误报")
    print("")
    print("2. 平衡检测 (阈值 = 2.0) ← 推荐:")
    print("   - 适用于大多数应用场景")
    print("   - 平衡检测精度和误报率")
    print("   - 对应约6dB的功率差异")
    print("")
    print("3. 宽松检测 (阈值 = 3.0):")
    print("   - 适用于只关注严重异常的场景")
    print("   - 误报率很低")
    print("   - 可能漏检轻微异常")
    print("")
    print("当前使用阈值 2.0 的合理性:")
    print("  ✅ 对应6dB功率差异，物理意义明确")
    print("  ✅ 能有效区分正常音频和异常竖线")
    print("  ✅ 在实际测试中表现良好")
    print("  ✅ 避免了过多的误报和漏报")
    
    print(f"\n✅ 线强度阈值原理解释完成")
    print(f"📊 原理说明图已保存: line_strength_principle_explanation.png")

if __name__ == "__main__":
    explain_line_strength_principle()
