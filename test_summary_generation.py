#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试汇总图生成
"""

import os
import sys
import shutil

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adaptive_fundamental_removal import main as thd_main
from create_summary_visualization import parse_results_file, create_summary_visualization

def test_summary_generation():
    """测试汇总图生成"""
    # 测试文件
    test_file = "test20250722/鼓膜破裂（复测1.1).wav"
    
    print(f"🎵 测试文件: {test_file}")
    
    try:
        # 1. 运行THD+N分析
        print("🔬 运行THD+N分析...")
        success = thd_main(test_file)
        
        if not success:
            print("❌ THD+N分析失败")
            return
        
        # 2. 查找分析结果
        analysis_dir = "鼓膜破裂（复测1.1)_THD+N双方法分析"
        summary_file = os.path.join(analysis_dir, "THD+N双方法分析汇总.txt")
        
        if not os.path.exists(summary_file):
            print(f"❌ 汇总文件不存在: {summary_file}")
            return
        
        print(f"✅ 找到汇总文件: {summary_file}")
        
        # 3. 解析汇总文件
        print("📊 解析汇总文件...")
        results = parse_results_file(summary_file)
        
        if not results:
            print("❌ 解析汇总文件失败")
            return
        
        print(f"✅ 解析成功，找到 {len(results)} 个频段结果")
        
        # 4. 生成汇总图
        print("🎨 生成汇总图...")
        create_summary_visualization(results, analysis_dir)
        
        # 5. 检查生成的图片
        summary_image = os.path.join(analysis_dir, "93段THD+N分析汇总.png")
        if os.path.exists(summary_image):
            print(f"✅ 汇总图生成成功: {summary_image}")
            
            # 复制到当前目录便于查看
            shutil.copy2(summary_image, "测试汇总图.png")
            print("📊 汇总图已复制到当前目录: 测试汇总图.png")
        else:
            print("❌ 汇总图生成失败")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_summary_generation()
