#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证动态噪声阈值曲线是否正确显示
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def verify_dynamic_noise_threshold():
    """
    验证动态噪声阈值曲线
    """
    print("🔍 验证动态噪声阈值曲线显示")
    print("="*60)
    
    # 检查输出目录
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    print(f"✅ 输出目录存在: {output_dir}")
    
    # 检查PNG文件
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    print(f"📊 找到 {len(png_files)} 个对数频谱图像文件")
    
    if len(png_files) != 93:
        print(f"⚠️  警告: 期望93个文件，实际找到{len(png_files)}个")
    else:
        print("✅ 文件数量正确: 93个频段")
    
    # 检查汇总文件
    summary_file = os.path.join(output_dir, "正确对数频谱分析汇总.txt")
    if os.path.exists(summary_file):
        print("✅ 汇总文件存在")
    else:
        print("❌ 汇总文件不存在")
    
    # 显示文件大小统计
    if png_files:
        file_sizes = []
        for png_file in png_files:
            size = os.path.getsize(png_file)
            file_sizes.append(size)
        
        avg_size = sum(file_sizes) / len(file_sizes)
        min_size = min(file_sizes)
        max_size = max(file_sizes)
        total_size = sum(file_sizes)
        
        print(f"\n📈 文件大小统计:")
        print(f"  平均大小: {avg_size/1024:.1f} KB")
        print(f"  最小文件: {min_size/1024:.1f} KB")
        print(f"  最大文件: {max_size/1024:.1f} KB")
        print(f"  总大小: {total_size/1024/1024:.1f} MB")
    
    # 检查图像规格
    if png_files:
        try:
            with Image.open(png_files[0]) as img:
                width, height = img.size
                print(f"\n🖼️  图像规格:")
                print(f"  尺寸: {width}×{height}px")
                print(f"  模式: {img.mode}")
                print(f"  格式: {img.format}")
        except Exception as e:
            print(f"  ❌ 无法读取图像信息: {e}")
    
    # 显示代表性频段
    print(f"\n🎵 代表性频段:")
    representative_indices = [0, 20, 40, 60, 80, 92]
    
    for idx in representative_indices:
        if idx < len(png_files):
            filename = os.path.basename(png_files[idx])
            freq_str = filename.split('_')[3].replace('Hz.png', '')
            file_size = os.path.getsize(png_files[idx]) / 1024
            print(f"  段 {idx:2d}: {freq_str:>6}Hz - {filename} ({file_size:.1f}KB)")
    
    print(f"\n✅ 验证完成!")
    print(f"📁 所有文件保存在: {output_dir}")
    print(f"🎯 当前版本特点:")
    print(f"   ✅ 使用semilogx对数频率轴")
    print(f"   ✅ 每段去除首尾8%数据")
    print(f"   ✅ 显示动态噪声阈值曲线 (红色线)")
    print(f"   ✅ 频谱曲线 (黑色线)")
    print(f"   ✅ 主频标记 (蓝色圆点)")
    print(f"   ✅ 多进程加速 (8进程)")
    print(f"   ✅ 与harmonic_detector_api_fast.py参数一致")

def create_sample_overview():
    """
    创建几个代表性频段的概览图
    """
    print(f"\n🎨 创建代表性频段概览...")
    
    output_dir = "琴身内部异物1.1_正确对数频谱分析"
    png_files = glob.glob(os.path.join(output_dir, "correct_log_segment_*.png"))
    png_files.sort()
    
    # 选择6个代表性频段
    selected_indices = [0, 15, 30, 45, 70, 92]
    
    if len(png_files) >= max(selected_indices) + 1:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, seg_idx in enumerate(selected_indices):
            if i < len(axes) and seg_idx < len(png_files):
                try:
                    img = mpimg.imread(png_files[seg_idx])
                    axes[i].imshow(img)
                    axes[i].axis('off')
                    
                    # 从文件名提取频率
                    basename = os.path.basename(png_files[seg_idx])
                    freq_str = basename.split('_')[3].replace('Hz.png', '')
                    axes[i].set_title(f'段 {seg_idx}: {freq_str}Hz\n动态噪声阈值曲线', 
                                    fontsize=12, fontweight='bold')
                    
                except Exception as e:
                    axes[i].text(0.5, 0.5, f'无法加载\n段 {seg_idx}', 
                               ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].axis('off')
        
        plt.tight_layout()
        overview_file = os.path.join(output_dir, "动态噪声阈值概览.png")
        plt.savefig(overview_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 概览图已保存: {overview_file}")
    else:
        print(f"❌ 无法创建概览图，文件数量不足")

def compare_versions():
    """
    对比不同版本的特点
    """
    print(f"\n📊 版本对比:")
    
    versions = [
        ("琴身内部异物1.1_频谱分析", "线性频谱+包络"),
        ("琴身内部异物1.1_对数频谱分析", "对数频谱+错误噪声"),
        ("琴身内部异物1.1_正确对数频谱分析", "对数频谱+动态噪声阈值")
    ]
    
    for dir_name, description in versions:
        if os.path.exists(dir_name):
            files = glob.glob(os.path.join(dir_name, "*.png"))
            # 排除概览图
            spectrum_files = [f for f in files if "概览" not in f and "overview" not in f]
            
            if spectrum_files:
                total_size = sum(os.path.getsize(f) for f in spectrum_files)
                print(f"  {description}:")
                print(f"    目录: {dir_name}")
                print(f"    文件数: {len(spectrum_files)}个")
                print(f"    总大小: {total_size/1024/1024:.1f}MB")
                print(f"    平均大小: {total_size/len(spectrum_files)/1024:.1f}KB")
            else:
                print(f"  {description}: 目录存在但无频谱文件")
        else:
            print(f"  {description}: 目录不存在")

if __name__ == "__main__":
    verify_dynamic_noise_threshold()
    create_sample_overview()
    compare_versions()
