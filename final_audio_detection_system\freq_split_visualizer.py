#!/usr/bin/env python3
"""
频段分割可视化器
Frequency Split Visualizer
专门可视化freq_split的频段分割结果
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized")
    def split_freq_steps_optimized(audio_path, **kwargs):
        return [], [], {}

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FreqSplitVisualizer:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数 - 与您的实现保持一致
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'A': 1,
            'min_cycles': 10,
            'min_duration': 153,  # 与您的测试参数一致
            'energy_threshold_db': -45,
            'min_start_time': 0.2,
            'hop_length': 512,
            'n_fft': 2048,
            'plot': False
        }
        
        print(f"频段分割可视化器初始化完成")
    
    def visualize_freq_split(self, audio_path):
        """可视化freq_split的频段分割"""
        print(f"\n分析频段分割: {os.path.basename(audio_path)}")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选 (100-20000Hz)
            freq_mask = (frequencies >= 100) & (frequencies <= 20000)
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            print(f"频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
            print(f"时间分辨率: {times[1]-times[0]:.4f}秒")
            print(f"频率分辨率: {frequencies[1]-frequencies[0]:.1f}Hz")
            
            # 使用优化的频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path,
                start_freq=100,
                stop_freq=20000,
                octave=12,
                min_cycles=10,
                min_duration=153,
                fs=self.sample_rate,
                search_window_start=0.1,
                search_window_end=1.5,
                correlation_length=1.0,
                plot=False,
                debug=False
            )
            
            if len(step_boundaries) == 0:
                print("优化频段分割失败")
                return None

            print(f"优化频段分割成功: {len(step_boundaries)}个频段")
            if alignment_info:
                print(f"对齐信息: 开始时间={alignment_info.get('start_offset', 0):.3f}s, "
                      f"相关性={alignment_info.get('correlation_score', 0):.3f}")
            
            print(f"\nfreq_split分割结果:")
            print(f"  总频段数: {len(step_boundaries)}")
            print(f"  频率范围: {min(freq_table):.1f}Hz - {max(freq_table):.1f}Hz")
            print(f"  时间范围: {step_boundaries[0][0]:.3f}s - {step_boundaries[-1][1]:.3f}s")
            
            # 显示前10个频段的详细信息
            print(f"\n前10个频段详情:")
            for i in range(min(10, len(step_boundaries))):
                start_time, end_time = step_boundaries[i]
                expected_freq = freq_table[i]
                duration = end_time - start_time
                print(f"  段{i:2d}: {start_time:.3f}s-{end_time:.3f}s ({duration:.3f}s) → {expected_freq:.1f}Hz")
            
            if len(step_boundaries) > 10:
                print(f"  ... (还有{len(step_boundaries)-10}个频段)")
            
            # 创建可视化
            self._create_freq_split_visualization(
                power_db, frequencies, times, step_boundaries, freq_table, audio_path
            )
            
            return {
                'power_db': power_db,
                'frequencies': frequencies,
                'times': times,
                'step_boundaries': step_boundaries,
                'freq_table': freq_table,
                'audio_duration': len(y)/sr,
                'total_segments': len(step_boundaries)
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _create_freq_split_visualization(self, power_db, frequencies, times, step_boundaries, freq_table, audio_path):
        """创建频段分割可视化"""
        print(f"\n生成频段分割可视化...")
        
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(16, 12))
        fig.suptitle(f'freq_split频段分割分析: {os.path.basename(audio_path)}', fontsize=16)
        
        # 1. 完整频谱图 + 频段分割线
        ax1 = axes[0]
        im1 = ax1.imshow(power_db, aspect='auto', origin='lower', 
                        extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                        cmap='viridis')
        
        # 标记频段分割线 - 简化显示
        for i, (start_time, end_time) in enumerate(step_boundaries):
            # 统一使用白色分割线，简洁清晰
            ax1.axvline(x=start_time, color='white', alpha=0.6, linewidth=0.8)
        
        ax1.set_title(f'完整频谱 + freq_split分割线 (共{len(step_boundaries)}个频段)')
        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('频率 (Hz)')
        plt.colorbar(im1, ax=ax1, label='功率 (dB)')
        
        # 2. 频段时间分布
        ax2 = axes[1]
        segment_indices = list(range(len(step_boundaries)))
        segment_start_times = [boundary[0] for boundary in step_boundaries]
        segment_end_times = [boundary[1] for boundary in step_boundaries]
        segment_durations = [end - start for start, end in step_boundaries]
        
        # 绘制频段时间范围
        for i, (start, end) in enumerate(step_boundaries):
            ax2.barh(i, end - start, left=start, alpha=0.7, 
                    color=plt.cm.viridis(i / len(step_boundaries)))
        
        ax2.set_title(f'频段时间分布 (总计{len(step_boundaries)}个频段)')
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('频段序号')
        ax2.grid(True, alpha=0.3)
        
        # 设置y轴显示
        if len(step_boundaries) > 20:
            # 如果频段太多，只显示部分刻度
            step = max(1, len(step_boundaries) // 20)
            ax2.set_yticks(range(0, len(step_boundaries), step))
        
        # 3. 期望频率分布
        ax3 = axes[2]
        
        # 绘制期望频率随时间的变化
        segment_mid_times = [(start + end) / 2 for start, end in step_boundaries]
        ax3.plot(segment_mid_times, freq_table, 'bo-', markersize=3, linewidth=1, alpha=0.7)
        
        # 标记一些关键频率点
        key_indices = [0, len(freq_table)//4, len(freq_table)//2, 3*len(freq_table)//4, len(freq_table)-1]
        for idx in key_indices:
            if idx < len(freq_table):
                ax3.annotate(f'{freq_table[idx]:.0f}Hz', 
                           xy=(segment_mid_times[idx], freq_table[idx]),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        ax3.set_title('期望频率随时间变化 (扫频曲线)')
        ax3.set_xlabel('时间 (s)')
        ax3.set_ylabel('期望频率 (Hz)')
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')  # 使用对数刻度显示频率
        
        # 添加统计信息文本框
        stats_text = f"""
频段分割统计:
• 总频段数: {len(step_boundaries)}
• 频率范围: {min(freq_table):.1f}Hz - {max(freq_table):.1f}Hz
• 时间范围: {step_boundaries[0][0]:.3f}s - {step_boundaries[-1][1]:.3f}s
• 平均频段时长: {np.mean(segment_durations):.3f}s
• 频段时长范围: {min(segment_durations):.3f}s - {max(segment_durations):.3f}s
• 频率步进方式: 对数扫频 (octave={self.freq_split_params['octave']})
"""
        
        # 在第三个子图右侧添加统计信息
        ax3.text(1.02, 0.5, stats_text, transform=ax3.transAxes, fontsize=10,
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'freq_split_visualization_{os.path.splitext(os.path.basename(audio_path))[0]}.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  频段分割可视化已保存: {output_filename}")
        
        plt.show()
        
        # 输出详细的频段信息到文件
        self._save_segment_details(step_boundaries, freq_table, audio_path)
    
    def _save_segment_details(self, step_boundaries, freq_table, audio_path):
        """保存详细的频段信息到文件"""
        import pandas as pd
        
        # 创建详细信息表
        segment_data = []
        for i, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
            segment_data.append({
                'segment_index': i,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'expected_frequency': expected_freq,
                'mid_time': (start_time + end_time) / 2
            })
        
        df = pd.DataFrame(segment_data)
        
        # 保存到CSV文件
        csv_filename = f'freq_split_segments_{os.path.splitext(os.path.basename(audio_path))[0]}.csv'
        df.to_csv(csv_filename, index=False)
        
        print(f"  频段详细信息已保存: {csv_filename}")
        
        # 显示统计信息
        print(f"\n频段统计:")
        print(f"  平均时长: {df['duration'].mean():.3f}s")
        print(f"  时长标准差: {df['duration'].std():.3f}s")
        print(f"  最短频段: {df['duration'].min():.3f}s")
        print(f"  最长频段: {df['duration'].max():.3f}s")

def main():
    """主函数"""
    # 初始化可视化器
    visualizer = FreqSplitVisualizer()
    
    # 分析低音戳洞样本的频段分割
    audio_path = "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav"
    
    # 执行频段分割可视化
    results = visualizer.visualize_freq_split(audio_path)
    
    if results:
        print(f"\n频段分割分析完成！")
        print(f"可视化图表和详细数据已生成。")
        print(f"接下来可以基于这个分割结果进行竖线检测分析。")
    else:
        print(f"频段分割分析失败！")
    
    return visualizer, results

if __name__ == "__main__":
    visualizer, results = main()
