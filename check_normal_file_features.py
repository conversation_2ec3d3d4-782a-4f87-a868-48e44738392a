#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查正常文件的特征，用于调整分类阈值
"""

import os
import sys
from analyze_noise_curve_patterns import analyze_single_file_noise_pattern, classify_noise_pattern

def main():
    """
    检查几个明显正常的文件的特征
    """
    
    print("🔍 检查正常文件的噪声特征")
    print("="*50)
    
    # 选择几个明显正常的文件
    normal_file_patterns = ['ok1.wav', 'ok2.wav', 'sd1.wav', 'sd2.wav']
    
    test_dir = "test20250717"
    normal_files = []
    
    # 查找正常文件
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if any(pattern in file for pattern in normal_file_patterns):
                normal_files.append(os.path.join(root, file))
                if len(normal_files) >= 4:  # 只检查前4个
                    break
        if len(normal_files) >= 4:
            break
    
    print(f"找到{len(normal_files)}个正常文件:")
    for i, file in enumerate(normal_files, 1):
        rel_path = os.path.relpath(file, test_dir)
        print(f"  {i}. {rel_path}")
    print()
    
    # 分析每个正常文件
    for i, normal_file in enumerate(normal_files, 1):
        filename = os.path.basename(normal_file)
        print(f"[{i}/{len(normal_files)}] 分析: {filename}")
        
        try:
            result = analyze_single_file_noise_pattern(normal_file)
            
            if result:
                # 进行分类
                classification = classify_noise_pattern(result['noise_patterns'], result['filename'])
                
                features = classification['features']
                print(f"  📊 分类结果: {classification['classification']}")
                print(f"  📈 异常得分: {classification['abnormal_score']}")
                print(f"  📊 关键特征:")
                print(f"    曲率标准差: {features['std_curvature']:.3f}")
                print(f"    斜率标准差: {features['std_slope']:.3f}")
                print(f"    峰值数: {features['num_peaks']:.0f}")
                print(f"    谷值数: {features['num_valleys']:.0f}")

                print(f"  🔍 特征检查:")
                checks = classification['feature_checks']
                print(f"    高曲率标准差(>0.3): {checks['high_curvature_std']}")
                print(f"    高斜率标准差(>0.5): {checks['high_slope_std']}")
                print(f"    峰值多(>105): {checks['many_peaks']}")
                print(f"    谷值多(>105): {checks['many_valleys']}")
                
            else:
                print(f"  ❌ 分析失败")
        
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        
        print()
    
    print("="*50)
    print("🎯 正常文件特征检查完成！")

if __name__ == "__main__":
    main()
