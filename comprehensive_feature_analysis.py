#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于频段分割的全面特征分析系统
"""

import os
import sys
import glob
import numpy as np
import pandas as pd
import librosa
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal, stats
from scipy.signal import stft, find_peaks
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized
from final_audio_detection_system.segment_vertical_line_detector import SegmentVerticalLineDetector

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveFeatureAnalyzer:
    def __init__(self):
        self.fs = 48000
        self.vertical_detector = SegmentVerticalLineDetector()
        
    def extract_segment_features(self, audio_path, segment_idx, start_time, end_time, expected_freq):
        """提取单个频段的全面特征"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            # 提取频段音频
            start_sample = int(start_time * self.fs)
            end_sample = int(end_time * self.fs)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            features = {}
            
            # 1. 基础时域特征
            features.update(self._extract_time_domain_features(segment_audio))
            
            # 2. 频域特征
            features.update(self._extract_frequency_domain_features(segment_audio, expected_freq))
            
            # 3. 频谱特征
            features.update(self._extract_spectral_features(segment_audio))
            
            # 4. THD和谐波特征
            features.update(self._extract_thd_features(segment_audio, expected_freq))
            
            # 5. 竖线检测特征
            features.update(self._extract_vertical_line_features(segment_audio))
            
            # 6. 统计特征
            features.update(self._extract_statistical_features(segment_audio))
            
            # 7. 能量分布特征
            features.update(self._extract_energy_features(segment_audio))
            
            # 添加元信息
            features['segment_idx'] = segment_idx
            features['start_time'] = start_time
            features['end_time'] = end_time
            features['duration'] = end_time - start_time
            features['expected_freq'] = expected_freq
            
            return features
            
        except Exception as e:
            print(f"提取频段{segment_idx}特征失败: {e}")
            return None
    
    def _extract_time_domain_features(self, audio):
        """提取时域特征"""
        features = {}
        
        # 基础统计量
        features['td_mean'] = np.mean(audio)
        features['td_std'] = np.std(audio)
        features['td_var'] = np.var(audio)
        features['td_rms'] = np.sqrt(np.mean(audio**2))
        features['td_max'] = np.max(np.abs(audio))
        features['td_min'] = np.min(audio)
        features['td_range'] = np.max(audio) - np.min(audio)
        
        # 高阶统计量
        features['td_skewness'] = stats.skew(audio)
        features['td_kurtosis'] = stats.kurtosis(audio)
        
        # 过零率
        zero_crossings = np.where(np.diff(np.signbit(audio)))[0]
        features['td_zcr'] = len(zero_crossings) / len(audio)
        
        return features
    
    def _extract_frequency_domain_features(self, audio, expected_freq):
        """提取频域特征"""
        features = {}
        
        # FFT分析
        fft = np.fft.fft(audio)
        freqs = np.fft.fftfreq(len(audio), 1/self.fs)
        magnitude = np.abs(fft)
        
        # 只考虑正频率
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 基础频域特征
        features['fd_peak_freq'] = positive_freqs[np.argmax(positive_magnitude)]
        features['fd_peak_magnitude'] = np.max(positive_magnitude)
        features['fd_total_energy'] = np.sum(positive_magnitude**2)
        
        # 期望频率附近的能量
        freq_tolerance = 50  # Hz
        freq_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
        if np.any(freq_mask):
            features['fd_expected_freq_energy'] = np.sum(positive_magnitude[freq_mask]**2)
            features['fd_expected_freq_ratio'] = features['fd_expected_freq_energy'] / features['fd_total_energy']
        else:
            features['fd_expected_freq_energy'] = 0
            features['fd_expected_freq_ratio'] = 0
        
        # 频率偏差
        features['fd_freq_deviation'] = abs(features['fd_peak_freq'] - expected_freq)
        features['fd_freq_deviation_ratio'] = features['fd_freq_deviation'] / expected_freq
        
        return features
    
    def _extract_spectral_features(self, audio):
        """提取频谱特征"""
        features = {}
        
        # STFT分析
        f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
        magnitude = np.abs(Zxx)
        power = magnitude**2
        
        # 频谱统计特征
        features['spec_mean'] = np.mean(magnitude)
        features['spec_std'] = np.std(magnitude)
        features['spec_max'] = np.max(magnitude)
        features['spec_energy'] = np.sum(power)
        
        # 频谱形状特征
        freq_weighted_sum = np.sum(f[:, np.newaxis] * power, axis=0)
        total_power = np.sum(power, axis=0)
        spectral_centroid = freq_weighted_sum / (total_power + 1e-12)
        features['spec_centroid_mean'] = np.mean(spectral_centroid)
        features['spec_centroid_std'] = np.std(spectral_centroid)
        
        # 频谱带宽
        freq_diff = f[:, np.newaxis] - spectral_centroid[np.newaxis, :]
        spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * power, axis=0) / (total_power + 1e-12))
        features['spec_bandwidth_mean'] = np.mean(spectral_bandwidth)
        features['spec_bandwidth_std'] = np.std(spectral_bandwidth)
        
        # 频谱滚降
        cumulative_power = np.cumsum(power, axis=0)
        total_power_per_frame = np.sum(power, axis=0)
        rolloff_threshold = 0.85 * total_power_per_frame
        rolloff_indices = np.argmax(cumulative_power >= rolloff_threshold[np.newaxis, :], axis=0)
        spectral_rolloff = f[rolloff_indices]
        features['spec_rolloff_mean'] = np.mean(spectral_rolloff)
        features['spec_rolloff_std'] = np.std(spectral_rolloff)
        
        return features
    
    def _extract_thd_features(self, audio, expected_freq):
        """提取THD和谐波特征"""
        features = {}
        
        # FFT分析
        fft = np.fft.fft(audio)
        freqs = np.fft.fftfreq(len(audio), 1/self.fs)
        magnitude = np.abs(fft)
        
        # 只考虑正频率
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 基频能量
        fundamental_tolerance = 20  # Hz
        fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
        if np.any(fundamental_mask):
            fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
        else:
            fundamental_energy = 0
        
        # 谐波能量
        harmonic_energies = []
        for harmonic in range(2, 6):  # 2-5次谐波
            harmonic_freq = expected_freq * harmonic
            if harmonic_freq < self.fs / 2:
                harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                if np.any(harmonic_mask):
                    harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                    harmonic_energies.append(harmonic_energy)
                else:
                    harmonic_energies.append(0)
        
        # THD计算
        total_harmonic_energy = sum(harmonic_energies)
        if fundamental_energy > 0:
            features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
            features['thd_db'] = 20 * np.log10(features['thd'] + 1e-12)
        else:
            features['thd'] = 0
            features['thd_db'] = -120
        
        # 各次谐波比例
        for i, energy in enumerate(harmonic_energies, 2):
            if fundamental_energy > 0:
                features[f'harmonic_{i}_ratio'] = energy / fundamental_energy
            else:
                features[f'harmonic_{i}_ratio'] = 0
        
        # 谐波总能量比例
        total_energy = np.sum(positive_magnitude**2)
        features['fundamental_ratio'] = fundamental_energy / total_energy if total_energy > 0 else 0
        features['harmonic_total_ratio'] = total_harmonic_energy / total_energy if total_energy > 0 else 0
        
        return features
    
    def _extract_vertical_line_features(self, audio):
        """提取竖线检测特征"""
        features = {}
        
        try:
            # STFT分析
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx)**2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 总能量变化
            total_energy = np.sum(power_db, axis=0)
            energy_mean = np.mean(total_energy)
            energy_std = np.std(total_energy)
            
            # 峰值检测
            min_height = energy_mean + 0.5 * energy_std
            min_prominence = 0.3 * energy_std
            
            peaks, properties = find_peaks(total_energy, 
                                         height=min_height,
                                         distance=2,
                                         prominence=min_prominence)
            
            features['vl_peak_count'] = len(peaks)
            features['vl_energy_mean'] = energy_mean
            features['vl_energy_std'] = energy_std
            features['vl_energy_cv'] = energy_std / (abs(energy_mean) + 1e-12)
            
            # 分析每个峰值的竖线特征
            line_strengths = []
            freq_spans = []
            freq_ratios = []
            
            for peak_idx in peaks:
                if peak_idx < len(t):
                    peak_spectrum = power_db[:, peak_idx]
                    threshold = np.percentile(peak_spectrum, 70)
                    high_energy_mask = peak_spectrum > threshold
                    high_energy_indices = np.where(high_energy_mask)[0]
                    
                    if len(high_energy_indices) > 0:
                        # 频率跨度
                        freq_span = f[high_energy_indices[-1]] - f[high_energy_indices[0]]
                        freq_spans.append(freq_span)
                        
                        # 频率比例
                        freq_ratio = len(high_energy_indices) / len(f)
                        freq_ratios.append(freq_ratio)
                        
                        # 线强度
                        high_energy_power = peak_spectrum[high_energy_indices]
                        background_power = np.delete(peak_spectrum, high_energy_indices)
                        if len(background_power) > 0:
                            line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                        else:
                            line_strength = 1.0
                        line_strengths.append(line_strength)
            
            # 竖线统计特征
            features['vl_max_line_strength'] = np.max(line_strengths) if line_strengths else 0
            features['vl_mean_line_strength'] = np.mean(line_strengths) if line_strengths else 0
            features['vl_max_freq_span'] = np.max(freq_spans) if freq_spans else 0
            features['vl_mean_freq_span'] = np.mean(freq_spans) if freq_spans else 0
            features['vl_max_freq_ratio'] = np.max(freq_ratios) if freq_ratios else 0
            features['vl_mean_freq_ratio'] = np.mean(freq_ratios) if freq_ratios else 0
            
            # 竖线判定
            vertical_lines = sum(1 for i, strength in enumerate(line_strengths) 
                               if strength >= 2.0 and freq_spans[i] >= 1000 and freq_ratios[i] >= 0.1)
            features['vl_vertical_line_count'] = vertical_lines
            features['vl_has_vertical_line'] = 1 if vertical_lines > 0 else 0
            
        except Exception as e:
            # 如果竖线检测失败，设置默认值
            features.update({
                'vl_peak_count': 0,
                'vl_energy_mean': 0,
                'vl_energy_std': 0,
                'vl_energy_cv': 0,
                'vl_max_line_strength': 0,
                'vl_mean_line_strength': 0,
                'vl_max_freq_span': 0,
                'vl_mean_freq_span': 0,
                'vl_max_freq_ratio': 0,
                'vl_mean_freq_ratio': 0,
                'vl_vertical_line_count': 0,
                'vl_has_vertical_line': 0
            })
        
        return features
    
    def _extract_statistical_features(self, audio):
        """提取统计特征"""
        features = {}
        
        # 分位数特征
        percentiles = [10, 25, 50, 75, 90]
        for p in percentiles:
            features[f'stat_p{p}'] = np.percentile(audio, p)
        
        # 四分位距
        features['stat_iqr'] = features['stat_p75'] - features['stat_p25']
        
        # 异常值比例
        q1, q3 = features['stat_p25'], features['stat_p75']
        iqr = features['stat_iqr']
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        outliers = (audio < lower_bound) | (audio > upper_bound)
        features['stat_outlier_ratio'] = np.sum(outliers) / len(audio)
        
        return features
    
    def _extract_energy_features(self, audio):
        """提取能量分布特征"""
        features = {}
        
        # 短时能量
        frame_length = 1024
        hop_length = 512
        frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
        frame_energy = np.sum(frames**2, axis=0)
        
        features['energy_mean'] = np.mean(frame_energy)
        features['energy_std'] = np.std(frame_energy)
        features['energy_max'] = np.max(frame_energy)
        features['energy_min'] = np.min(frame_energy)
        features['energy_range'] = features['energy_max'] - features['energy_min']
        features['energy_cv'] = features['energy_std'] / (features['energy_mean'] + 1e-12)
        
        # 能量分布的统计特征
        if len(frame_energy) > 0:
            features['energy_skewness'] = stats.skew(frame_energy)
            features['energy_kurtosis'] = stats.kurtosis(frame_energy)
        else:
            features['energy_skewness'] = 0
            features['energy_kurtosis'] = 0
        
        return features

def analyze_all_samples():
    """分析所有样本的特征"""
    print("🔍 开始全面特征分析")
    print("="*70)
    
    analyzer = ComprehensiveFeatureAnalyzer()
    
    # 定义样本文件夹
    sample_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美", 
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }
    
    all_features = []
    
    # 处理每个文件夹
    for label, folders in sample_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)
            
            print(f"\n📁 处理文件夹: {folder_name} ({label}) - {len(wav_files)}个文件")
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"  🎵 处理文件: {filename}")
                
                try:
                    # 获取频段分割
                    step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                        audio_path, start_freq=100, stop_freq=20000, octave=12,
                        min_cycles=10, min_duration=153, fs=48000,
                        search_window_start=0.1, search_window_end=1.5,
                        correlation_length=1.0, plot=False, debug=False
                    )
                    
                    # 提取每个频段的特征
                    for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                        features = analyzer.extract_segment_features(
                            audio_path, seg_idx, start_time, end_time, expected_freq
                        )
                        
                        if features:
                            features['label'] = label
                            features['folder'] = folder_name
                            features['filename'] = filename
                            all_features.append(features)
                
                except Exception as e:
                    print(f"    ❌ 处理失败: {e}")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_features)
    print(f"\n✅ 特征提取完成: {len(df)}个频段样本")
    
    # 保存原始特征数据
    df.to_csv('comprehensive_features.csv', index=False, encoding='utf-8-sig')
    print(f"💾 原始特征已保存: comprehensive_features.csv")
    
    return df

def analyze_feature_discrimination(df):
    """分析特征的区分能力"""
    print("\n🔍 分析特征区分能力")
    print("="*70)

    # 获取特征列（排除元信息列）
    meta_columns = ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    feature_columns = [col for col in df.columns if col not in meta_columns]

    print(f"📊 总特征数: {len(feature_columns)}")

    # 计算每个特征的区分能力
    discrimination_results = []

    for feature in feature_columns:
        try:
            pos_values = df[df['label'] == 'pos'][feature].dropna()
            neg_values = df[df['label'] == 'neg'][feature].dropna()

            if len(pos_values) == 0 or len(neg_values) == 0:
                continue

            # 统计检验
            t_stat, p_value = stats.ttest_ind(pos_values, neg_values)

            # 效应大小 (Cohen's d)
            pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) +
                                 (len(neg_values) - 1) * np.var(neg_values)) /
                                (len(pos_values) + len(neg_values) - 2))
            cohens_d = abs(np.mean(pos_values) - np.mean(neg_values)) / (pooled_std + 1e-12)

            # 分离度指标
            pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
            neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)

            # 重叠度计算
            overlap = max(0, min(pos_mean + 2*pos_std, neg_mean + 2*neg_std) -
                         max(pos_mean - 2*pos_std, neg_mean - 2*neg_std))
            total_range = max(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - \
                         min(pos_mean - 2*pos_std, neg_mean - 2*neg_std)
            overlap_ratio = overlap / (total_range + 1e-12)

            discrimination_results.append({
                'feature': feature,
                'pos_mean': pos_mean,
                'pos_std': pos_std,
                'neg_mean': neg_mean,
                'neg_std': neg_std,
                't_statistic': t_stat,
                'p_value': p_value,
                'cohens_d': cohens_d,
                'overlap_ratio': overlap_ratio,
                'separation_score': cohens_d * (1 - overlap_ratio)
            })

        except Exception as e:
            print(f"分析特征 {feature} 时出错: {e}")

    # 转换为DataFrame并排序
    disc_df = pd.DataFrame(discrimination_results)
    disc_df = disc_df.sort_values('separation_score', ascending=False)

    # 显示最有区分力的特征
    print(f"\n📊 最有区分力的前20个特征:")
    print("-" * 70)

    for i, (_, row) in enumerate(disc_df.head(20).iterrows()):
        significance = "***" if row['p_value'] < 0.001 else "**" if row['p_value'] < 0.01 else "*" if row['p_value'] < 0.05 else ""
        effect_size = "大" if row['cohens_d'] > 0.8 else "中" if row['cohens_d'] > 0.5 else "小"

        print(f"{i+1:2d}. {row['feature']}")
        print(f"    分离评分: {row['separation_score']:.3f}")
        print(f"    Cohen's d: {row['cohens_d']:.3f} ({effect_size})")
        print(f"    p值: {row['p_value']:.2e} {significance}")
        print(f"    正样本: {row['pos_mean']:.3f} ± {row['pos_std']:.3f}")
        print(f"    负样本: {row['neg_mean']:.3f} ± {row['neg_std']:.3f}")
        print(f"    重叠率: {row['overlap_ratio']:.3f}")
        print()

    # 保存区分能力分析结果
    disc_df.to_csv('feature_discrimination_analysis.csv', index=False, encoding='utf-8-sig')
    print(f"💾 特征区分能力分析已保存: feature_discrimination_analysis.csv")

    return disc_df

def visualize_top_features(df, disc_df, top_n=12):
    """可视化最有区分力的特征"""
    print(f"\n🔍 可视化前{top_n}个最有区分力的特征")
    print("="*70)

    top_features = disc_df.head(top_n)['feature'].tolist()

    # 创建子图
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('最有区分力的特征对比分析', fontsize=16, fontweight='bold')

    for i, feature in enumerate(top_features):
        row, col = i // 4, i % 4
        ax = axes[row, col]

        # 获取数据
        pos_data = df[df['label'] == 'pos'][feature].dropna()
        neg_data = df[df['label'] == 'neg'][feature].dropna()

        # 绘制分布
        ax.hist(pos_data, bins=30, alpha=0.7, color='green', label=f'正样本 (n={len(pos_data)})', density=True)
        ax.hist(neg_data, bins=30, alpha=0.7, color='red', label=f'负样本 (n={len(neg_data)})', density=True)

        # 添加均值线
        ax.axvline(np.mean(pos_data), color='green', linestyle='--', linewidth=2, alpha=0.8)
        ax.axvline(np.mean(neg_data), color='red', linestyle='--', linewidth=2, alpha=0.8)

        # 设置标题和标签
        feature_info = disc_df[disc_df['feature'] == feature].iloc[0]
        ax.set_title(f'{feature}\n分离评分: {feature_info["separation_score"]:.3f}', fontsize=10)
        ax.set_xlabel('特征值')
        ax.set_ylabel('密度')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('top_discriminative_features.png', dpi=300, bbox_inches='tight')
    plt.show()

def build_classification_model(df, disc_df, top_n=20):
    """构建分类模型验证特征有效性"""
    print(f"\n🔍 构建分类模型验证特征有效性")
    print("="*70)

    # 选择最有区分力的特征
    top_features = disc_df.head(top_n)['feature'].tolist()

    # 准备数据
    X = df[top_features].fillna(0)
    y = (df['label'] == 'neg').astype(int)  # 负样本为1，正样本为0

    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 训练随机森林分类器
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_scaled, y)

    # 预测和评估
    y_pred = rf.predict(X_scaled)

    print("📊 分类结果:")
    print(classification_report(y, y_pred, target_names=['正样本', '负样本']))

    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': top_features,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)

    print(f"\n📊 特征重要性排序:")
    print("-" * 50)
    for i, (_, row) in enumerate(feature_importance.head(15).iterrows()):
        print(f"{i+1:2d}. {row['feature']}: {row['importance']:.4f}")

    return rf, scaler, feature_importance

if __name__ == "__main__":
    # 1. 提取特征
    features_df = analyze_all_samples()

    # 2. 分析特征区分能力
    discrimination_df = analyze_feature_discrimination(features_df)

    # 3. 可视化最有区分力的特征
    visualize_top_features(features_df, discrimination_df)

    # 4. 构建分类模型
    model, scaler, importance_df = build_classification_model(features_df, discrimination_df)

    print(f"\n✅ 全面特征分析完成！")
    print(f"📊 生成文件:")
    print(f"  - comprehensive_features.csv: 原始特征数据")
    print(f"  - feature_discrimination_analysis.csv: 特征区分能力分析")
    print(f"  - top_discriminative_features.png: 最有区分力特征可视化")
