#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试test20250717和待定文件夹中所有文件的频段分割
为每个文件生成单独的可视化图表
"""

import os
import glob
import matplotlib.pyplot as plt
from freq_split_optimized import split_freq_steps_optimized
import numpy as np

class FreqSplitTester:
    def __init__(self):
        self.results = []
        self.output_dir = "freq_split_test_results"

        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"创建输出目录: {self.output_dir}")

    def test_all_files(self):
        """测试所有文件的频段分割"""
        print("🔍 测试test20250717和待定文件夹中所有文件的频段分割")
        print("="*70)

        # 指定要测试的文件夹
        target_folders = [
            "../test20250717",
            "../待定"
        ]

        all_files = []
        for folder in target_folders:
            if os.path.exists(folder):
                # 递归查找所有wav文件
                pattern = os.path.join(folder, "**/*.wav")
                files = glob.glob(pattern, recursive=True)
                all_files.extend(files)
                print(f"文件夹 {folder}: 找到 {len(files)} 个音频文件")
            else:
                print(f"⚠️ 文件夹不存在: {folder}")

        print(f"\n总共找到 {len(all_files)} 个音频文件")

        # 按文件夹分组显示
        self._show_file_structure(all_files)

        # 逐个测试
        for i, audio_path in enumerate(all_files):
            print(f"\n[{i+1}/{len(all_files)}] 测试频段分割: {self._get_relative_path(audio_path)}")

            try:
                # 执行频段分割测试
                result = self._test_single_file(audio_path, i+1)
                self.results.append(result)

                print(f"  ✅ 测试完成: {result['status']}")
                if result['status'] == 'success':
                    print(f"    对齐质量: {result['alignment_quality']}")
                    print(f"    相关性: {result['correlation_score']:.3f}")
                    print(f"    开始时间: {result['start_offset']:.3f}秒")
                    print(f"    步进区间数: {result['step_count']}")

            except Exception as e:
                print(f"  ❌ 测试失败: {str(e)}")
                # 记录失败的样本
                self.results.append({
                    'filepath': audio_path,
                    'filename': os.path.basename(audio_path),
                    'folder': self._get_folder_name(audio_path),
                    'status': 'failed',
                    'error': str(e),
                    'figure_path': None
                })

        # 生成测试报告
        self._generate_test_report()

        return self.results

    def _test_single_file(self, audio_path, file_index):
        """测试单个文件的频段分割"""
        filename = os.path.basename(audio_path)
        folder = self._get_folder_name(audio_path)

        # 设置matplotlib后端为非交互式
        plt.ioff()

        try:
            # 执行频段分割 - 启用可视化但不显示
            step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path,
                min_duration=153,
                plot=True,  # 启用绘图
                debug=False,  # 关闭调试信息
                search_window_start=0.1,
                search_window_end=2.0,
                correlation_length=1.0
            )

            # 保存图表
            safe_filename = self._make_safe_filename(filename)
            figure_path = os.path.join(self.output_dir, f"{file_index:02d}_{safe_filename}_freq_split.png")
            plt.savefig(figure_path, dpi=300, bbox_inches='tight')
            plt.close('all')  # 关闭所有图表

            # 提取结果信息
            result = {
                'filepath': audio_path,
                'filename': filename,
                'folder': folder,
                'status': 'success',
                'step_count': len(step_bounds),
                'freq_count': len(freq_table),
                'start_offset': alignment_info['start_offset'],
                'correlation_score': alignment_info['correlation_score'],
                'alignment_quality': alignment_info['alignment_quality']['quality'],
                'overall_score': alignment_info['alignment_quality']['overall_score'],
                'time_correlation': alignment_info['alignment_quality']['time_correlation'],
                'frequency_similarity': alignment_info['alignment_quality']['frequency_similarity'],
                'figure_path': figure_path
            }

            return result

        except Exception as e:
            plt.close('all')  # 确保关闭图表
            raise e

    def _show_file_structure(self, all_files):
        """显示文件结构"""
        print(f"\n📁 文件结构:")

        # 按文件夹分组
        folder_groups = {}
        for file_path in all_files:
            folder = self._get_folder_name(file_path)
            if folder not in folder_groups:
                folder_groups[folder] = []
            folder_groups[folder].append(os.path.basename(file_path))

        for folder, files in folder_groups.items():
            print(f"  {folder}/ ({len(files)}个文件):")
            for file in sorted(files):
                print(f"    - {file}")

    def _get_relative_path(self, file_path):
        """获取相对路径"""
        parts = file_path.replace("\\", "/").split("/")
        return "/".join(parts[-2:])

    def _get_folder_name(self, file_path):
        """获取文件夹名称"""
        if "test20250717" in file_path:
            if "pos" in file_path:
                return "test20250717/pos"
            elif "neg" in file_path:
                return "test20250717/neg"
            else:
                return "test20250717"
        elif "待定" in file_path:
            return "待定"
        else:
            return "other"

    def _make_safe_filename(self, filename):
        """创建安全的文件名"""
        # 移除或替换不安全的字符
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_."
        safe_filename = ""
        for char in filename:
            if char in safe_chars:
                safe_filename += char
            else:
                safe_filename += "_"

        # 移除文件扩展名
        if safe_filename.endswith('.wav'):
            safe_filename = safe_filename[:-4]

        return safe_filename

    def _generate_test_report(self):
        """生成测试报告"""
        print(f"\n📊 频段分割测试报告")
        print("="*70)

        # 统计结果
        total_files = len(self.results)
        successful_files = len([r for r in self.results if r['status'] == 'success'])
        failed_files = total_files - successful_files

        print(f"总文件数: {total_files}")
        print(f"成功测试: {successful_files}")
        print(f"测试失败: {failed_files}")
        print(f"成功率: {successful_files/total_files*100:.1f}%")

        # 按文件夹统计
        print(f"\n📈 按文件夹统计:")
        folder_stats = {}
        for result in self.results:
            folder = result['folder']
            if folder not in folder_stats:
                folder_stats[folder] = {'total': 0, 'success': 0, 'failed': 0}

            folder_stats[folder]['total'] += 1
            if result['status'] == 'success':
                folder_stats[folder]['success'] += 1
            else:
                folder_stats[folder]['failed'] += 1

        for folder, stats in folder_stats.items():
            success_rate = stats['success'] / stats['total'] * 100 if stats['total'] > 0 else 0
            print(f"  {folder}: {stats['success']}/{stats['total']} 成功 ({success_rate:.1f}%)")

        # 对齐质量分析
        successful_results = [r for r in self.results if r['status'] == 'success']
        if successful_results:
            print(f"\n📊 对齐质量分析:")

            # 按质量等级分组
            quality_groups = {}
            correlation_scores = []
            overall_scores = []

            for result in successful_results:
                quality = result['alignment_quality']
                if quality not in quality_groups:
                    quality_groups[quality] = 0
                quality_groups[quality] += 1

                correlation_scores.append(result['correlation_score'])
                overall_scores.append(result['overall_score'])

            for quality, count in quality_groups.items():
                percentage = count / len(successful_results) * 100
                print(f"  {quality}: {count}个文件 ({percentage:.1f}%)")

            print(f"\n  平均相关性: {np.mean(correlation_scores):.3f} ± {np.std(correlation_scores):.3f}")
            print(f"  平均综合评分: {np.mean(overall_scores):.3f} ± {np.std(overall_scores):.3f}")

        # 详细结果列表
        print(f"\n📋 详细测试结果:")
        print("-"*100)
        print(f"{'序号':<4} {'文件名':<40} {'文件夹':<15} {'状态':<8} {'质量':<10} {'相关性':<8} {'图表':<10}")
        print("-"*100)

        for i, result in enumerate(self.results, 1):
            filename = result['filename'][:37] + "..." if len(result['filename']) > 40 else result['filename']
            folder = result['folder'][:12] + "..." if len(result['folder']) > 15 else result['folder']
            status = result['status']

            if status == 'success':
                quality = result['alignment_quality']
                correlation = f"{result['correlation_score']:.3f}"
                figure_status = "✅" if result['figure_path'] else "❌"
            else:
                quality = "N/A"
                correlation = "N/A"
                figure_status = "❌"

            print(f"{i:<4} {filename:<40} {folder:<15} {status:<8} {quality:<10} {correlation:<8} {figure_status:<10}")

        print(f"\n💾 所有可视化图表已保存到: {self.output_dir}/")
        print(f"   图表命名格式: 序号_文件名_freq_split.png")

def main():
    """主函数"""
    tester = FreqSplitTester()

    # 测试所有文件
    results = tester.test_all_files()

    print(f"\n🎯 频段分割测试完成！")
    print(f"   请检查 {tester.output_dir}/ 目录中的可视化图表")


if __name__ == "__main__":
    main()
