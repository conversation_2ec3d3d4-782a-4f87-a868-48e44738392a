import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt
import librosa
import librosa.display
from scipy.signal import find_peaks
from scipy.interpolate import interp1d
from scipy.ndimage import minimum_filter1d

def get_spectrum_envelopes(mag_spectrum, freqs, peak_thresh=0.1, min_win=15):
    peaks, _ = find_peaks(mag_spectrum, height=peak_thresh * np.max(mag_spectrum))
    if len(peaks) < 4:
        return None, None, None

    interp_func = interp1d(freqs[peaks], mag_spectrum[peaks],
                           kind='cubic', fill_value='extrapolate')
    upper_env = interp_func(freqs)
    lower_env = minimum_filter1d(mag_spectrum, size=min_win)
    diff_env = upper_env - lower_env
    return upper_env, lower_env, diff_env

def analyze_wav_file(filepath, frame_step=1024, n_fft=2048, threshold_z=2.0):
    signal, fs = sf.read(filepath)
    if signal.ndim > 1:
        signal = np.mean(signal, axis=1)

    stft = librosa.stft(signal, n_fft=n_fft, hop_length=frame_step, window='hann')
    spectrogram = np.abs(stft)
    freqs = librosa.fft_frequencies(sr=fs, n_fft=n_fft)
    times = librosa.frames_to_time(np.arange(spectrogram.shape[1]), sr=fs, hop_length=frame_step)

    noise_map = []
    diff_env_list = []

    # 首先收集所有包络差用于计算全局统计
    for t in range(spectrogram.shape[1]):
        mag = spectrogram[:, t]
        _, _, diff = get_spectrum_envelopes(mag, freqs)
        if diff is None:
            diff = np.zeros_like(mag)
        diff_env_list.append(diff)

    diff_env_arr = np.array(diff_env_list).T  # shape: freq x time
    global_mean = np.mean(diff_env_arr)
    global_std = np.std(diff_env_arr) + 1e-8

    # Z-score 标准化所有帧
    z_score_map = (diff_env_arr - global_mean) / global_std
    noise_map = np.where(z_score_map > threshold_z, z_score_map, 0)

    # 可视化
    plt.figure(figsize=(12, 6))

    plt.subplot(2, 1, 1)
    librosa.display.specshow(librosa.amplitude_to_db(spectrogram, ref=np.max),
                             sr=fs, hop_length=frame_step,
                             x_axis='time', y_axis='hz')
    plt.title('Spectrogram (dB)')
    plt.colorbar()

    plt.subplot(2, 1, 2)
    librosa.display.specshow(noise_map, sr=fs, hop_length=frame_step,
                             x_axis='time', y_axis='hz', cmap='magma')
    plt.title(f'Noise Map via Envelope Diff Z-Score (Z > {threshold_z})')
    plt.colorbar()
    plt.tight_layout()
    plt.show()

# 使用你的音频路径
analyze_wav_file("your_audio_file.wav")
