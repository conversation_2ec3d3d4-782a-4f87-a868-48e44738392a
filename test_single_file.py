#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个文件的汇总图生成
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from adaptive_fundamental_removal import main as thd_main

def test_single_file():
    """测试单个文件"""
    # 测试文件
    test_file = "test20250722/鼓膜破裂（复测1.1).wav"
    output_dir = "test_single_output"
    
    print(f"🎵 测试文件: {test_file}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 调用主分析函数
        success = thd_main(test_file, output_dir)
        
        if success:
            print("✅ 分析成功")
            
            # 检查是否生成了汇总图
            summary_files = []
            for file in os.listdir(output_dir):
                if file.endswith('.png') and '汇总' in file:
                    summary_files.append(file)
            
            print(f"📊 找到汇总图: {len(summary_files)} 个")
            for f in summary_files:
                print(f"  {f}")
                
        else:
            print("❌ 分析失败")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_file()
