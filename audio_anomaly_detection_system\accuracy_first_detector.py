#!/usr/bin/env python3
"""
准确性优先的音频异常检测系统
基于最初的成功检测结果，优先保证检测准确性
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import welch, find_peaks
from scipy import stats
import os
import json
import warnings
warnings.filterwarnings('ignore')

class AccuracyFirstDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # 基于最初成功检测结果的阈值设置
        # 这些阈值在第一个版本中成功检测出了6个异常文件
        self.detection_thresholds = {
            'band_34_713Hz_spectral_rolloff': 776.0,      # 最强特征
            'band_31_599Hz_snr': 2.0,                     # SNR异常
            'band_24_400Hz_response_flatness': 0.3,       # 频响平坦度
            'band_33_673Hz_snr_db': 3.5,                  # SNR dB
            'band_31_599Hz_snr_db': 3.0,                  # SNR dB
            'band_37_848Hz_energy': 0.001,                # 能量阈值
            'band_25_424Hz_response_flatness': 0.3,       # 频响平坦度
            'band_23_378Hz_spectral_irregularity': 1.15,  # 频谱不规则性
        }
        
        # 检测权重（基于Cohen's d值）
        self.feature_weights = {
            'band_34_713Hz_spectral_rolloff': 0.25,       # 最强特征
            'band_31_599Hz_snr': 0.20,                    # 第二强
            'band_24_400Hz_response_flatness': 0.15,      # 第三强
            'band_33_673Hz_snr_db': 0.12,                 # 第四强
            'band_31_599Hz_snr_db': 0.10,                 # 第五强
            'band_37_848Hz_energy': 0.08,                 # 能量检测
            'band_25_424Hz_response_flatness': 0.05,      # 频响检测
            'band_23_378Hz_spectral_irregularity': 0.05,  # 不规则性检测
        }
        
        # 生成频段定义
        self.frequency_bands = self._generate_frequency_bands()
        
        print(f"准确性优先检测器初始化完成：{len(self.frequency_bands)}个频段，{len(self.detection_thresholds)}个检测阈值")
    
    def _generate_frequency_bands(self):
        """生成基于步进扫频的频段"""
        frequency_bands = {}
        f = 100  # 起始频率
        ratio = 2**(1/12)  # 1/12倍频程比例
        band_index = 0
        
        while f <= 20000:
            next_f = f * ratio
            if next_f > 20000:
                next_f = 20000
            
            band_name = f"band_{band_index:02d}_{f:.0f}Hz"
            frequency_bands[band_name] = {
                'center_freq': f,
                'freq_range': (f * 0.9, next_f * 1.1),
                'target_freq': f
            }
            
            f = next_f
            band_index += 1
            if f >= 20000:
                break
        
        return frequency_bands
    
    def extract_features(self, audio_path):
        """提取音频特征（使用最初成功的方法）"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            features = {}
            
            # 对每个频段进行分析
            for band_name, band_info in self.frequency_bands.items():
                band_features = self._analyze_frequency_band(
                    y, sr, band_info['center_freq'], band_info['freq_range']
                )
                
                # 添加频段前缀
                for key, value in band_features.items():
                    features[f'{band_name}_{key}'] = value
            
            # 添加全局特征
            global_features = self._analyze_global_features(y, sr)
            features.update(global_features)
            
            return features
            
        except Exception as e:
            print(f"处理音频文件失败 {audio_path}: {e}")
            return {}
    
    def _analyze_frequency_band(self, y, sr, center_freq, freq_range):
        """分析单个频段（使用最初成功的方法）"""
        features = {}
        
        # 使用Welch方法计算功率谱密度
        freqs, psd = welch(y, sr, nperseg=4096, noverlap=2048)
        
        # 提取频段内的功率谱
        low_freq, high_freq = freq_range
        band_mask = (freqs >= low_freq) & (freqs <= high_freq)
        
        if not np.any(band_mask):
            return self._get_empty_band_features()
        
        band_freqs = freqs[band_mask]
        band_psd = psd[band_mask]
        
        # 1. 基础能量特征
        features['energy'] = np.sum(band_psd)
        features['peak_power'] = np.max(band_psd)
        features['avg_power'] = np.mean(band_psd)
        features['power_std'] = np.std(band_psd)
        
        # 2. THD (总谐波失真)
        features.update(self._calculate_thd(band_freqs, band_psd, center_freq))
        
        # 3. TNR (总噪声比)
        features.update(self._calculate_tnr(band_freqs, band_psd, center_freq))
        
        # 4. SNR (信噪比)
        features.update(self._calculate_snr(band_freqs, band_psd, center_freq))
        
        # 5. 频率响应特征
        features.update(self._calculate_frequency_response(band_freqs, band_psd, center_freq))
        
        # 6. 频谱形状特征
        features.update(self._calculate_spectral_shape(band_freqs, band_psd))
        
        # 7. 异常检测特征
        features.update(self._detect_band_anomalies(band_freqs, band_psd, center_freq))
        
        return features
    
    def _calculate_thd(self, freqs, psd, center_freq):
        """计算THD"""
        features = {}
        
        # 找到基频位置
        fundamental_idx = np.argmin(np.abs(freqs - center_freq))
        fundamental_power = psd[fundamental_idx]
        
        # 估算谐波功率
        peaks, _ = find_peaks(psd, height=np.mean(psd) * 2)
        
        if len(peaks) > 1:
            harmonic_indices = peaks[peaks != fundamental_idx]
            harmonic_power = np.sum(psd[harmonic_indices]) if len(harmonic_indices) > 0 else 0
        else:
            harmonic_power = 0
        
        # THD计算
        if fundamental_power > 0:
            features['thd'] = np.sqrt(harmonic_power / fundamental_power)
            features['thd_db'] = 20 * np.log10(features['thd'] + 1e-12)
        else:
            features['thd'] = 0
            features['thd_db'] = -100
        
        features['fundamental_power'] = fundamental_power
        features['harmonic_power'] = harmonic_power
        features['peak_count'] = len(peaks)
        
        return features
    
    def _calculate_tnr(self, freqs, psd, center_freq):
        """计算TNR"""
        features = {}
        
        # 找到主要信号
        peaks, _ = find_peaks(psd, height=np.mean(psd) * 1.5)
        
        if len(peaks) > 0:
            peak_powers = psd[peaks]
            signal_count = max(1, len(peaks) // 5)
            strongest_peaks = np.argsort(peak_powers)[-signal_count:]
            signal_power = np.sum(peak_powers[strongest_peaks])
            
            total_power = np.sum(psd)
            noise_power = total_power - signal_power
        else:
            signal_power = np.max(psd)
            noise_power = np.sum(psd) - signal_power
        
        # TNR计算
        if noise_power > 0:
            features['tnr'] = signal_power / noise_power
            features['tnr_db'] = 10 * np.log10(features['tnr'])
        else:
            features['tnr'] = 1000
            features['tnr_db'] = 30
        
        features['signal_power'] = signal_power
        features['noise_power'] = noise_power
        
        return features
    
    def _calculate_snr(self, freqs, psd, center_freq):
        """计算SNR"""
        features = {}
        
        # 找到目标频率附近的功率
        target_idx = np.argmin(np.abs(freqs - center_freq))
        
        # 信号功率
        window_size = max(1, len(freqs) // 20)
        start_idx = max(0, target_idx - window_size)
        end_idx = min(len(psd), target_idx + window_size)
        signal_power = np.max(psd[start_idx:end_idx])
        
        # 噪声功率
        noise_mask = np.ones(len(psd), dtype=bool)
        noise_mask[start_idx:end_idx] = False
        noise_power = np.mean(psd[noise_mask]) if np.any(noise_mask) else 1e-12
        
        # SNR计算
        if noise_power > 0:
            features['snr'] = signal_power / noise_power
            features['snr_db'] = 10 * np.log10(features['snr'])
        else:
            features['snr'] = 1000
            features['snr_db'] = 30
        
        return features
    
    def _calculate_frequency_response(self, freqs, psd, center_freq):
        """计算频率响应特征"""
        features = {}
        
        # 频率响应平坦度
        features['response_flatness'] = np.std(psd) / (np.mean(psd) + 1e-12)
        
        # 频率响应斜率
        if len(freqs) > 2:
            slope, intercept, r_value, p_value, std_err = stats.linregress(freqs, psd)
            features['response_slope'] = slope
            features['response_linearity'] = r_value**2
        else:
            features['response_slope'] = 0
            features['response_linearity'] = 0
        
        # 频率偏移
        peak_idx = np.argmax(psd)
        actual_peak_freq = freqs[peak_idx]
        features['freq_deviation'] = actual_peak_freq - center_freq
        features['freq_deviation_percent'] = (actual_peak_freq - center_freq) / center_freq * 100
        
        # 3dB带宽
        max_power = np.max(psd)
        half_power = max_power / 2
        above_half_power = psd >= half_power
        features['bandwidth_3db'] = np.sum(above_half_power) * (freqs[1] - freqs[0]) if len(freqs) > 1 else 0
        
        return features
    
    def _calculate_spectral_shape(self, freqs, psd):
        """计算频谱形状特征"""
        features = {}
        
        # 频谱重心
        features['spectral_centroid'] = np.sum(freqs * psd) / (np.sum(psd) + 1e-12)
        
        # 频谱带宽
        centroid = features['spectral_centroid']
        features['spectral_bandwidth'] = np.sqrt(np.sum(((freqs - centroid) ** 2) * psd) / (np.sum(psd) + 1e-12))
        
        # 频谱偏度和峰度
        if len(psd) > 3:
            features['spectral_skewness'] = stats.skew(psd)
            features['spectral_kurtosis'] = stats.kurtosis(psd)
        else:
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0
        
        # 频谱滚降
        cumulative_power = np.cumsum(psd)
        total_power = cumulative_power[-1]
        rolloff_85_idx = np.where(cumulative_power >= 0.85 * total_power)[0]
        if len(rolloff_85_idx) > 0:
            features['spectral_rolloff'] = freqs[rolloff_85_idx[0]]
        else:
            features['spectral_rolloff'] = freqs[-1]
        
        return features
    
    def _detect_band_anomalies(self, freqs, psd, center_freq):
        """检测频段异常"""
        features = {}
        
        # 能量集中度
        max_power = np.max(psd)
        avg_power = np.mean(psd)
        features['energy_concentration'] = max_power / (avg_power + 1e-12)
        
        # 频谱不规则性
        if len(psd) > 1:
            psd_diff = np.diff(psd)
            features['spectral_irregularity'] = np.std(psd_diff) / (np.mean(np.abs(psd_diff)) + 1e-12)
        else:
            features['spectral_irregularity'] = 0
        
        # 异常峰值检测
        threshold = np.mean(psd) + 2 * np.std(psd)
        anomalous_peaks = np.sum(psd > threshold)
        features['anomalous_peak_count'] = anomalous_peaks
        features['anomalous_peak_ratio'] = anomalous_peaks / len(psd)
        
        return features
    
    def _analyze_global_features(self, y, sr):
        """分析全局特征"""
        features = {}
        
        # 全频段功率谱
        freqs, psd = welch(y, sr, nperseg=4096, noverlap=2048)
        
        # 频段间能量分布
        low_mask = (freqs >= 100) & (freqs <= 1000)
        mid_mask = (freqs >= 1000) & (freqs <= 8000)
        high_mask = (freqs >= 8000) & (freqs <= 20000)
        
        low_energy = np.sum(psd[low_mask]) if np.any(low_mask) else 0
        mid_energy = np.sum(psd[mid_mask]) if np.any(mid_mask) else 0
        high_energy = np.sum(psd[high_mask]) if np.any(high_mask) else 0
        total_energy = low_energy + mid_energy + high_energy
        
        if total_energy > 0:
            features['global_low_ratio'] = low_energy / total_energy
            features['global_mid_ratio'] = mid_energy / total_energy
            features['global_high_ratio'] = high_energy / total_energy
        else:
            features['global_low_ratio'] = 0
            features['global_mid_ratio'] = 0
            features['global_high_ratio'] = 0
        
        # 全局频谱特征
        features['global_spectral_centroid'] = np.sum(freqs * psd) / (np.sum(psd) + 1e-12)
        features['global_spectral_bandwidth'] = np.sqrt(np.sum(((freqs - features['global_spectral_centroid']) ** 2) * psd) / (np.sum(psd) + 1e-12))
        features['global_dynamic_range'] = 10 * np.log10(np.max(psd) / (np.mean(psd) + 1e-12))
        
        return features
    
    def _get_empty_band_features(self):
        """返回空频段的默认特征值"""
        return {
            'energy': 0, 'peak_power': 0, 'avg_power': 0, 'power_std': 0,
            'thd': 0, 'thd_db': -100, 'fundamental_power': 0, 'harmonic_power': 0, 'peak_count': 0,
            'tnr': 0, 'tnr_db': -100, 'signal_power': 0, 'noise_power': 0,
            'snr': 0, 'snr_db': -100,
            'response_flatness': 0, 'response_slope': 0, 'response_linearity': 0,
            'freq_deviation': 0, 'freq_deviation_percent': 0, 'bandwidth_3db': 0,
            'spectral_centroid': 0, 'spectral_bandwidth': 0, 'spectral_skewness': 0, 'spectral_kurtosis': 0, 'spectral_rolloff': 0,
            'energy_concentration': 0, 'spectral_irregularity': 0, 'anomalous_peak_count': 0, 'anomalous_peak_ratio': 0
        }
    
    def detect_anomaly(self, audio_path):
        """检测音频异常（使用最初成功的方法）"""
        # 提取特征
        features = self.extract_features(audio_path)
        
        if not features:
            return {
                'file_path': audio_path,
                'anomaly_detected': False,
                'confidence': 0.0,
                'anomaly_score': 0.0,
                'anomaly_details': ['特征提取失败'],
                'error': True
            }
        
        # 计算异常分数（使用最初成功的方法）
        anomaly_score = 0.0
        anomaly_details = []
        
        for feature_name, threshold in self.detection_thresholds.items():
            if feature_name in features:
                feature_value = features[feature_name]
                weight = self.feature_weights.get(feature_name, 0.01)
                
                # 根据特征类型判断异常
                is_anomaly = False
                
                if 'spectral_rolloff' in feature_name:
                    is_anomaly = feature_value > threshold
                elif 'snr' in feature_name and 'db' not in feature_name:
                    is_anomaly = feature_value > threshold
                elif 'snr_db' in feature_name:
                    is_anomaly = feature_value < threshold
                elif 'energy' in feature_name:
                    is_anomaly = feature_value < threshold
                elif 'response_flatness' in feature_name:
                    is_anomaly = feature_value > threshold
                elif 'spectral_irregularity' in feature_name:
                    is_anomaly = feature_value > threshold
                
                if is_anomaly:
                    anomaly_score += weight
                    freq_info = feature_name.split('_')[2] if len(feature_name.split('_')) > 2 else 'unknown'
                    feature_type = feature_name.split('_')[-1] if '_' in feature_name else feature_name
                    anomaly_details.append(f'{freq_info}频段{feature_type}异常 (值:{feature_value:.3f}, 阈值:{threshold:.3f})')
        
        # 判断是否异常（使用最初成功的阈值）
        anomaly_detected = anomaly_score > 0.5
        confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
        
        return {
            'file_path': audio_path,
            'anomaly_detected': anomaly_detected,
            'confidence': confidence,
            'anomaly_score': anomaly_score,
            'anomaly_details': anomaly_details,
            'error': False
        }
    
    def batch_detect(self, directories):
        """批量检测多个目录中的音频文件"""
        results = []
        
        for directory in directories:
            if not os.path.exists(directory):
                print(f"目录不存在: {directory}")
                continue
            
            print(f"\n分析目录: {directory}")
            print("-" * 50)
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.wav'):
                        file_path = os.path.join(root, file)
                        print(f"检测: {file}")
                        
                        result = self.detect_anomaly(file_path)
                        result['directory'] = directory
                        result['filename'] = file
                        results.append(result)
        
        return results
    
    def generate_report(self, results):
        """生成检测报告"""
        print("\n" + "="*80)
        print("准确性优先音频异常检测报告")
        print("="*80)
        
        # 统计信息
        total_files = len(results)
        anomaly_files = len([r for r in results if r['anomaly_detected']])
        normal_files = total_files - anomaly_files
        error_files = len([r for r in results if r['error']])
        
        print(f"\n总体统计:")
        print(f"  总文件数: {total_files}")
        print(f"  异常文件: {anomaly_files}")
        print(f"  正常文件: {normal_files}")
        print(f"  处理失败: {error_files}")
        print(f"  异常率: {anomaly_files/total_files*100:.1f}%")
        
        # 按目录分组
        by_directory = {}
        for result in results:
            directory = result['directory']
            if directory not in by_directory:
                by_directory[directory] = []
            by_directory[directory].append(result)
        
        # 详细结果
        for directory, dir_results in by_directory.items():
            print(f"\n目录: {directory}")
            print("-" * 60)
            
            dir_anomalies = [r for r in dir_results if r['anomaly_detected']]
            dir_normal = [r for r in dir_results if not r['anomaly_detected'] and not r['error']]
            dir_errors = [r for r in dir_results if r['error']]
            
            print(f"  文件数: {len(dir_results)}, 异常: {len(dir_anomalies)}, 正常: {len(dir_normal)}, 错误: {len(dir_errors)}")
            
            # 显示异常文件
            if dir_anomalies:
                print(f"\n  异常文件:")
                for result in sorted(dir_anomalies, key=lambda x: x['confidence'], reverse=True):
                    print(f"    ❌ {result['filename']} (置信度: {result['confidence']:.3f})")
                    for detail in result['anomaly_details'][:3]:  # 只显示前3个异常
                        print(f"       - {detail}")
            
            # 显示正常文件
            if dir_normal:
                print(f"\n  正常文件 (显示前5个):")
                for result in dir_normal[:5]:  # 只显示前5个
                    print(f"    ✅ {result['filename']} (置信度: {result['confidence']:.3f})")
                if len(dir_normal) > 5:
                    print(f"    ... 还有 {len(dir_normal)-5} 个正常文件")
        
        return {
            'total_files': total_files,
            'anomaly_files': anomaly_files,
            'normal_files': normal_files,
            'error_files': error_files,
            'anomaly_rate': anomaly_files/total_files*100 if total_files > 0 else 0
        }

def main():
    """主函数"""
    # 初始化准确性优先检测器
    detector = AccuracyFirstDetector()
    
    # 定义要检测的目录
    directories = [
        "test20250717",
        "待定"
    ]
    
    # 批量检测
    results = detector.batch_detect(directories)
    
    # 生成报告
    summary = detector.generate_report(results)
    
    # 保存详细结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('accuracy_first_results.csv', index=False)
    
    # 保存JSON格式结果
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.bool_)):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    with open('accuracy_first_results.json', 'w', encoding='utf-8') as f:
        json.dump(convert_numpy(results), f, ensure_ascii=False, indent=2)
    
    print(f"\n准确性优先检测结果已保存:")
    print(f"  - accuracy_first_results.csv")
    print(f"  - accuracy_first_results.json")
    
    return detector, results, summary

if __name__ == "__main__":
    detector, results, summary = main()
