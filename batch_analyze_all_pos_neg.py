#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量分析所有pos和neg文件夹的音频文件，使用高进程数提速
"""

import os
import glob
import subprocess
import time
import re
from pathlib import Path

def find_all_wav_files_by_category():
    """
    按类别查找所有wav文件
    """
    pos_files = []
    neg_files = []
    
    # 查找pos文件夹下的所有wav文件
    pos_pattern = "test20250717/pos/**/*.wav"
    pos_files = glob.glob(pos_pattern, recursive=True)
    
    # 查找neg文件夹下的所有wav文件
    neg_pattern = "test20250717/neg/*.wav"
    neg_files = glob.glob(neg_pattern, recursive=True)
    
    return pos_files, neg_files

def run_analysis_batch(wav_files, category, workers=12):
    """
    批量运行音频文件分析
    """
    print(f"\n🚀 开始批量分析 {category} 样本")
    print(f"文件数量: {len(wav_files)}")
    print(f"进程数: {workers}")
    print("="*60)
    
    successful_count = 0
    failed_count = 0
    start_time = time.time()
    
    for i, wav_file in enumerate(wav_files, 1):
        rel_path = os.path.relpath(wav_file)
        
        cmd = [
            'python', 'universal_spectrum_analyzer.py',
            rel_path,
            '--mel-scale',
            '--show-diff', 
            '--workers', str(workers),
            '--freq-range', '100', '24000'
        ]
        
        print(f"[{i}/{len(wav_files)}] 分析: {os.path.basename(wav_file)}")
        
        try:
            # 设置环境变量避免编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            # 运行分析
            result = subprocess.run(cmd, capture_output=True, text=True, cwd='.', env=env)
            
            if result.returncode == 0:
                successful_count += 1
                print(f"  ✅ 成功")
            else:
                failed_count += 1
                print(f"  ❌ 失败: {result.stderr[:100]}...")
                
        except Exception as e:
            failed_count += 1
            print(f"  ❌ 错误: {str(e)}")
        
        # 显示进度
        elapsed = time.time() - start_time
        if i > 0:
            avg_time = elapsed / i
            remaining = (len(wav_files) - i) * avg_time
            print(f"  ⏱️ 进度: {i}/{len(wav_files)}, 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
    
    total_time = time.time() - start_time
    
    print(f"\n{category} 样本分析完成:")
    print(f"  成功: {successful_count}/{len(wav_files)}")
    print(f"  失败: {failed_count}/{len(wav_files)}")
    print(f"  成功率: {successful_count/len(wav_files)*100:.1f}%")
    print(f"  总耗时: {total_time:.1f}秒")
    print(f"  平均每文件: {total_time/len(wav_files):.1f}秒")
    
    return successful_count, failed_count, total_time

def extract_diff_stats_from_summary(summary_file):
    """
    从汇总文件中提取差值统计信息
    """
    if not os.path.exists(summary_file):
        return None
    
    try:
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        stats = {}
        
        # 提取各种差值统计
        patterns = {
            'min_avg_diff': r'平均差值最小值:\s*([\d.]+)\s*dB',
            'max_avg_diff': r'平均差值最大值:\s*([\d.]+)\s*dB',
            'mean_avg_diff': r'平均差值均值:\s*([\d.]+)\s*dB',
            'std_avg_diff': r'平均差值标准差:\s*([\d.]+)\s*dB',
            'mean_max_diff': r'最大差值均值:\s*([\d.]+)\s*dB'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                stats[key] = float(match.group(1))
        
        return stats if stats else None
        
    except Exception as e:
        print(f"Error reading {summary_file}: {e}")
        return None

def collect_all_statistics():
    """
    收集所有分析结果的统计信息
    """
    print(f"\n📊 收集所有分析结果统计信息")
    print("="*60)
    
    # 查找所有分析结果目录
    analysis_dirs = glob.glob("*_梅尔频谱分析")
    
    pos_stats = {}
    neg_stats = {}
    
    for analysis_dir in analysis_dirs:
        summary_file = os.path.join(analysis_dir, "频谱分析汇总.txt")
        dir_name = os.path.basename(analysis_dir)
        
        stats = extract_diff_stats_from_summary(summary_file)
        
        if stats:
            # 根据文件名判断是pos还是neg样本
            original_name = dir_name.replace('_梅尔频谱分析', '')
            
            # 检查是否是neg样本的特征名称
            neg_keywords = ['主板隔音eva取消', '喇叭eva没贴', '录音_步进扫频', '低音戳洞', '高低音互换']
            is_neg = any(keyword in original_name for keyword in neg_keywords)
            
            if is_neg:
                neg_stats[dir_name] = stats
                print(f"NEG: {dir_name} - 最大平均差值: {stats.get('max_avg_diff', 'N/A')} dB")
            else:
                pos_stats[dir_name] = stats
                print(f"POS: {dir_name} - 最大平均差值: {stats.get('max_avg_diff', 'N/A')} dB")
    
    return pos_stats, neg_stats

def comprehensive_comparison(pos_stats, neg_stats):
    """
    综合对比所有pos和neg样本
    """
    print(f"\n" + "="*80)
    print("📈 综合对比分析 - 所有POS vs 所有NEG样本")
    print("="*80)
    
    if not pos_stats or not neg_stats:
        print("❌ 缺少pos或neg样本数据")
        return
    
    # 收集所有数据
    pos_max_avg_diffs = [s['max_avg_diff'] for s in pos_stats.values() if 'max_avg_diff' in s]
    pos_mean_avg_diffs = [s['mean_avg_diff'] for s in pos_stats.values() if 'mean_avg_diff' in s]
    pos_min_avg_diffs = [s['min_avg_diff'] for s in pos_stats.values() if 'min_avg_diff' in s]
    
    neg_max_avg_diffs = [s['max_avg_diff'] for s in neg_stats.values() if 'max_avg_diff' in s]
    neg_mean_avg_diffs = [s['mean_avg_diff'] for s in neg_stats.values() if 'mean_avg_diff' in s]
    neg_min_avg_diffs = [s['min_avg_diff'] for s in neg_stats.values() if 'min_avg_diff' in s]
    
    print(f"📊 样本数量:")
    print(f"  POS样本: {len(pos_stats)} 个")
    print(f"  NEG样本: {len(neg_stats)} 个")
    print(f"  总计: {len(pos_stats) + len(neg_stats)} 个")
    
    print(f"\n📈 最大平均差值对比:")
    print(f"{'指标':<20} {'POS样本':<15} {'NEG样本':<15} {'差异'}")
    print("-" * 65)
    
    if pos_max_avg_diffs and neg_max_avg_diffs:
        import numpy as np
        
        pos_max_mean = np.mean(pos_max_avg_diffs)
        neg_max_mean = np.mean(neg_max_avg_diffs)
        diff = neg_max_mean - pos_max_mean
        
        print(f"{'均值':<20} {pos_max_mean:<15.2f} {neg_max_mean:<15.2f} {diff:+.2f}")
        print(f"{'最小值':<20} {min(pos_max_avg_diffs):<15.2f} {min(neg_max_avg_diffs):<15.2f} {min(neg_max_avg_diffs)-min(pos_max_avg_diffs):+.2f}")
        print(f"{'最大值':<20} {max(pos_max_avg_diffs):<15.2f} {max(neg_max_avg_diffs):<15.2f} {max(neg_max_avg_diffs)-max(pos_max_avg_diffs):+.2f}")
        print(f"{'标准差':<20} {np.std(pos_max_avg_diffs):<15.2f} {np.std(neg_max_avg_diffs):<15.2f} {np.std(neg_max_avg_diffs)-np.std(pos_max_avg_diffs):+.2f}")
        print(f"{'范围':<20} {max(pos_max_avg_diffs)-min(pos_max_avg_diffs):<15.2f} {max(neg_max_avg_diffs)-min(neg_max_avg_diffs):<15.2f} {(max(neg_max_avg_diffs)-min(neg_max_avg_diffs))-(max(pos_max_avg_diffs)-min(pos_max_avg_diffs)):+.2f}")
        
        print(f"\n🎯 关键结论:")
        if neg_max_mean > pos_max_mean:
            print(f"  ❗ NEG样本平均差值比POS样本高 {diff:.2f} dB")
            print(f"  📈 NEG样本最大值: {max(neg_max_avg_diffs):.2f} dB")
            print(f"  📈 POS样本最大值: {max(pos_max_avg_diffs):.2f} dB")
        else:
            print(f"  ✅ POS样本平均差值比NEG样本高 {-diff:.2f} dB")
        
        # 建议检测阈值
        pos_threshold_low = pos_max_mean - 2 * np.std(pos_max_avg_diffs)
        neg_threshold_high = neg_max_mean + 2 * np.std(neg_max_avg_diffs)
        
        print(f"\n💡 建议检测阈值:")
        print(f"  过低异常: < {pos_threshold_low:.2f} dB (基于POS样本)")
        print(f"  过高异常: > {neg_threshold_high:.2f} dB (基于NEG样本)")
        print(f"  正常范围: {pos_threshold_low:.2f} - {neg_threshold_high:.2f} dB")

def main():
    """
    主函数
    """
    print("🎯 批量分析所有POS和NEG样本")
    print("="*80)
    
    # 查找所有音频文件
    pos_files, neg_files = find_all_wav_files_by_category()
    
    print(f"📁 文件统计:")
    print(f"  POS样本: {len(pos_files)} 个文件")
    print(f"  NEG样本: {len(neg_files)} 个文件")
    print(f"  总计: {len(pos_files) + len(neg_files)} 个文件")
    
    # 设置高进程数以提速
    workers = 12  # 可以根据CPU核心数调整
    
    total_start_time = time.time()
    
    # 分析POS样本
    if pos_files:
        pos_success, pos_fail, pos_time = run_analysis_batch(pos_files, "POS", workers)
    else:
        print("❌ 未找到POS样本文件")
        return
    
    # 分析NEG样本
    if neg_files:
        neg_success, neg_fail, neg_time = run_analysis_batch(neg_files, "NEG", workers)
    else:
        print("❌ 未找到NEG样本文件")
        return
    
    total_time = time.time() - total_start_time
    
    print(f"\n" + "="*80)
    print("✅ 所有样本分析完成!")
    print(f"总耗时: {total_time:.1f}秒")
    print(f"POS样本: {pos_success}成功/{pos_fail}失败")
    print(f"NEG样本: {neg_success}成功/{neg_fail}失败")
    
    # 收集统计信息
    pos_stats, neg_stats = collect_all_statistics()
    
    # 综合对比分析
    comprehensive_comparison(pos_stats, neg_stats)

if __name__ == "__main__":
    main()
