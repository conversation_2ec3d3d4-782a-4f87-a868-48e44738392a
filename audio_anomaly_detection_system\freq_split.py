import numpy as np
import librosa
import librosa.display
import matplotlib.pyplot as plt
from chirp import gen_freq_step

def split_freq_steps(audio_path, 
                     start_freq=100, stop_freq=20000, octave=12, fs=48000, A=1, 
                     min_cycles=10, min_duration=156, 
                     energy_threshold_db=-30, 
                     min_start_time=0.2, hop_length=512, n_fft=2048, 
                     plot=True):
    """
    步进扫频信号分割，返回step区间列表和频点列表
    """
    # 生成频点与时长
    _, _, freq_ssample_dict = gen_freq_step(start_freq, stop_freq, min_cycles, min_duration, octave, fs, A=A)
    freq_table = list(freq_ssample_dict.keys())
    dur_table = [freq_ssample_dict[f][2] / fs for f in freq_table]

    # 读取音频
    y, sr = librosa.load(audio_path, sr=None)

    # 检测有效起点（从最小开始时间开始找）
    rms = librosa.feature.rms(y=y, frame_length=n_fft, hop_length=hop_length)[0]
    rms_db = librosa.amplitude_to_db(rms, ref=np.max)
    frame_times = librosa.frames_to_time(np.arange(len(rms_db)), sr=sr, hop_length=hop_length)
    search_idx = np.where(frame_times >= min_start_time)[0]
    if len(search_idx) == 0:
        valid_idx = 0
    else:
        above_thr = np.where(rms_db[search_idx] > energy_threshold_db)[0]
        if len(above_thr) == 0:
            valid_idx = search_idx[0]
        else:
            valid_idx = search_idx[above_thr[0]]
    start_offset = frame_times[valid_idx]
    print(f"[INFO] 有效起点检测：start_offset = {start_offset:.2f} 秒")

    # 计算step切割时间
    step_boundaries = []
    t = start_offset
    for dur in dur_table:
        step_boundaries.append((t, t + dur))
        t += dur
    print(f"[INFO] 切割出 {len(step_boundaries)} 个 step 区间")

    # STFT频谱分析与可视化
    if plot:
        S = librosa.stft(y, n_fft=n_fft, hop_length=hop_length)
        S_db = librosa.amplitude_to_db(np.abs(S), ref=np.max)
        # times = librosa.frames_to_time(np.arange(S.shape[1]), sr=sr, hop_length=hop_length)
        plt.figure(figsize=(12, 6))
        librosa.display.specshow(S_db, sr=sr, hop_length=hop_length, x_axis='time', y_axis='log', cmap='magma')
        for start_t, end_t in step_boundaries:
            plt.axvline(start_t, color='cyan', linestyle='--', linewidth=1)
            plt.axvline(end_t, color='blue', linestyle='--', linewidth=1)
        plt.title("频谱图 + 固定时长 Step 切割")
        plt.tight_layout()
        plt.show()

    return step_boundaries, freq_table


if __name__ == "__main__":
    audio_path = r"test20250714\156\录音_步进扫频_100Hz至20000Hz_20250714_151915_no3_75.wav"
    # audio_path = r"test20250714\156\录音_步进扫频_100Hz至20000Hz_20250714_152023.wav"
    # audio_path = r"test20250714\153\录音_步进扫频_100Hz至20000Hz_20250714_155101.wav"
    step_bounds, freq_table = split_freq_steps(audio_path, min_duration=156, plot=True, energy_threshold_db=-45)
