#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量音频分析：为test20250717和待定文件夹的所有音频文件生成主频谐波噪声图
统一保存到analysis_results文件夹中，文件名对应音频名
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def batch_audio_analysis():
    """批量音频分析"""
    print("🎯 批量音频分析：生成主频谐波噪声图")
    print("="*70)
    print("分析目标:")
    print("1. test20250717文件夹下的所有.wav文件")
    print("2. 待定文件夹下的所有.wav文件")
    print("3. 生成主频、谐波、噪声定位图")
    print("4. 统一保存到analysis_results文件夹")
    print("="*70)
    
    # 创建结果文件夹
    output_dir = "analysis_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"✅ 创建输出文件夹: {output_dir}")
    
    # 搜索音频文件
    audio_files = []
    
    # 搜索test20250717文件夹
    test_folder = "test20250717"
    if os.path.exists(test_folder):
        test_files = glob.glob(os.path.join(test_folder, "**", "*.wav"), recursive=True)
        audio_files.extend(test_files)
        print(f"📁 找到 {len(test_files)} 个文件在 {test_folder}")
    
    # 搜索待定文件夹
    pending_folder = "待定"
    if os.path.exists(pending_folder):
        pending_files = glob.glob(os.path.join(pending_folder, "**", "*.wav"), recursive=True)
        audio_files.extend(pending_files)
        print(f"📁 找到 {len(pending_files)} 个文件在 {pending_folder}")
    
    if not audio_files:
        print("❌ 未找到任何音频文件")
        return
    
    print(f"\n📊 总共找到 {len(audio_files)} 个音频文件")
    print("开始批量分析...")
    
    # 批量处理每个音频文件
    success_count = 0
    error_count = 0
    
    for i, audio_path in enumerate(audio_files):
        print(f"\n🎵 [{i+1}/{len(audio_files)}] 分析: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        try:
            # 分析单个音频文件
            analysis_result = analyze_single_audio_file(audio_path)
            
            if analysis_result:
                # 生成可视化图
                output_filename = generate_audio_visualization(analysis_result, output_dir)
                if output_filename:
                    success_count += 1
                    print(f"   ✅ 成功生成: {output_filename}")
                else:
                    error_count += 1
                    print(f"   ❌ 可视化生成失败")
            else:
                error_count += 1
                print(f"   ❌ 分析失败")
                
        except Exception as e:
            error_count += 1
            print(f"   ❌ 处理失败: {e}")
    
    print(f"\n📊 批量分析完成:")
    print(f"   成功: {success_count} 个文件")
    print(f"   失败: {error_count} 个文件")
    print(f"   结果保存在: {output_dir} 文件夹")

def analyze_single_audio_file(audio_path):
    """分析单个音频文件"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"   📊 总频段数: {len(step_boundaries)}")
        
        segment_analyses = []
        
        # 分析每个频段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 分析单个频段
            segment_analysis = analyze_single_segment_optimized(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
            
            # 显示进度
            if seg_idx % 20 == 0:
                harmonics_count = len(segment_analysis['harmonic_analysis']) if segment_analysis else 0
                print(f"   处理频段 {seg_idx:2d}/{len(step_boundaries)} ({expected_freq:6.1f}Hz) - {harmonics_count}个谐波")
        
        return {
            'audio_path': audio_path,
            'audio_name': os.path.splitext(os.path.basename(audio_path))[0],
            'segment_analyses': segment_analyses,
            'total_segments': len(segment_analyses)
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def analyze_single_segment_optimized(audio, sr, expected_freq, seg_idx):
    """优化的单段分析"""
    
    try:
        # 高分辨率FFT
        fft_size = max(65536, len(audio))  # 64k点FFT (平衡速度和精度)
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只分析正频率
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_fast(positive_freqs, positive_power, expected_freq)
        
        # 2. 噪声估计
        noise_analysis = estimate_noise_fast(positive_freqs, positive_power, expected_freq)
        
        # 3. 谐波检测
        harmonic_analysis = detect_harmonics_fast(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_analysis, sr
        )
        
        # 4. 噪声区域分析
        noise_regions = analyze_noise_regions_fast(
            positive_freqs, positive_power, fundamental_analysis, harmonic_analysis
        )
        
        # 5. 质量指标计算
        quality_metrics = calculate_quality_metrics_fast(
            fundamental_analysis, harmonic_analysis, noise_regions, positive_power
        )
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'noise_regions': noise_regions,
            'quality_metrics': quality_metrics,
            'freq_resolution': positive_freqs[1] - positive_freqs[0]
        }
        
    except Exception as e:
        return None

def analyze_fundamental_fast(freqs, power, expected_freq):
    """快速主频分析"""
    
    # 自适应带宽
    if expected_freq <= 200:
        bandwidth = 2.0
    elif expected_freq <= 500:
        bandwidth = 3.0
    elif expected_freq <= 1000:
        bandwidth = 5.0
    elif expected_freq <= 2000:
        bandwidth = 8.0
    elif expected_freq <= 5000:
        bandwidth = 12.0
    elif expected_freq <= 10000:
        bandwidth = 20.0
    else:
        bandwidth = 30.0
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'bandwidth': bandwidth,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def estimate_noise_fast(freqs, power, fundamental_freq):
    """快速噪声估计"""
    
    # 排除主频和前5个谐波
    excluded_ranges = []
    excluded_ranges.append((fundamental_freq - 15, fundamental_freq + 15))
    
    for order in range(2, 6):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 15, harmonic_freq + 15))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        noise_floor = np.percentile(noise_powers, 20)
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_sample_count': np.sum(noise_mask)
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0
        }

def detect_harmonics_fast(freqs, power, fundamental_freq, 
                         fundamental_analysis, noise_analysis, sr):
    """快速谐波检测"""
    
    if not fundamental_analysis or not noise_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 优化的SNR阈值
    base_snr_threshold = 6.0
    
    nyquist_freq = sr / 2
    
    for order in range(2, 25):  # 检测到24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 5000:
            search_bandwidth = 40.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 60.0
        else:
            search_bandwidth = 80.0
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        snr_db = harmonic_power_db - noise_floor_db
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 2000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 相对功率和频率误差
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 检测条件
        conditions = {
            'snr_sufficient': snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -65.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.9
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'index': actual_idx
            })
    
    return detected_harmonics

def analyze_noise_regions_fast(freqs, power, fundamental_analysis, harmonic_analysis):
    """快速噪声区域分析"""
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频
    if fundamental_analysis:
        fund_freq = fundamental_analysis['freq']
        fund_bandwidth = fundamental_analysis['bandwidth']
        fund_mask = (freqs >= fund_freq - fund_bandwidth) & (freqs <= fund_freq + fund_bandwidth)
        noise_mask &= ~fund_mask
    
    # 排除谐波
    for harmonic in harmonic_analysis:
        harm_freq = harmonic['freq']
        harm_bandwidth = 15.0
        harm_mask = (freqs >= harm_freq - harm_bandwidth) & (freqs <= harm_freq + harm_bandwidth)
        noise_mask &= ~harm_mask
    
    # 计算噪声统计
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        total_noise_power = np.sum(noise_powers)
        total_signal_power = np.sum(power)
        noise_ratio = total_noise_power / total_signal_power
        
        return {
            'total_noise_power': total_noise_power,
            'total_noise_power_db': 10 * np.log10(total_noise_power + 1e-12),
            'noise_ratio': noise_ratio,
            'noise_frequency_count': np.sum(noise_mask),
            'noise_frequency_coverage': np.sum(noise_mask) / len(freqs)
        }
    else:
        return {
            'total_noise_power_db': -120,
            'noise_ratio': 0,
            'noise_frequency_count': 0,
            'noise_frequency_coverage': 0
        }

def calculate_quality_metrics_fast(fundamental_analysis, harmonic_analysis, 
                                  noise_regions, total_power):
    """快速质量指标计算"""
    
    if not fundamental_analysis:
        return {
            'snr_db': -120,
            'thd_db': -120,
            'signal_to_noise_ratio': 0,
            'harmonic_count': 0
        }
    
    # SNR (使用简化计算)
    snr_db = fundamental_analysis['power_db'] - (-30)  # 假设噪声底噪-30dB
    
    # THD (总谐波失真)
    if harmonic_analysis:
        total_harmonic_power = sum(h['power'] for h in harmonic_analysis)
        fundamental_power = fundamental_analysis['power']
        thd = np.sqrt(total_harmonic_power) / fundamental_power
        thd_db = 20 * np.log10(thd + 1e-12)
    else:
        thd_db = -120
    
    # 信噪比
    signal_power = fundamental_analysis['power'] + sum(h['power'] for h in harmonic_analysis)
    noise_power = noise_regions['total_noise_power'] if noise_regions else 1e-12
    signal_to_noise_ratio = signal_power / noise_power
    
    return {
        'snr_db': snr_db,
        'thd_db': thd_db,
        'signal_to_noise_ratio': signal_to_noise_ratio,
        'harmonic_count': len(harmonic_analysis)
    }

def generate_audio_visualization(analysis_result, output_dir):
    """生成音频可视化图"""
    
    try:
        audio_name = analysis_result['audio_name']
        segment_analyses = analysis_result['segment_analyses']
        
        if not segment_analyses:
            return None
        
        # 创建图表 (4行2列，去掉综合质量评分)
        fig = plt.figure(figsize=(20, 20))
        
        # 提取数据
        segments = [s['segment_idx'] for s in segment_analyses]
        freqs = [s['expected_freq'] for s in segment_analyses]
        snr_values = [s['quality_metrics']['snr_db'] for s in segment_analyses]
        harmonic_counts = [s['quality_metrics']['harmonic_count'] for s in segment_analyses]
        noise_ratios = [s['noise_regions']['noise_ratio'] * 100 for s in segment_analyses]
        thd_values = [s['quality_metrics']['thd_db'] for s in segment_analyses]
        
        # 1. SNR分布 (左上)
        ax1 = plt.subplot(4, 2, 1)
        scatter1 = ax1.scatter(freqs, snr_values, c=snr_values, cmap='RdYlGn', s=30, alpha=0.7)
        ax1.set_title(f'{audio_name} - 信噪比分布', fontweight='bold')
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('SNR (dB)')
        ax1.set_xscale('log')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter1, ax=ax1, label='SNR (dB)')
        ax1.axhline(y=15, color='red', linestyle='--', alpha=0.8, label='良好阈值')
        ax1.legend()
        
        # 2. 谐波数量分布 (右上)
        ax2 = plt.subplot(4, 2, 2)
        scatter2 = ax2.scatter(freqs, harmonic_counts, c=harmonic_counts, cmap='viridis', s=30, alpha=0.7)
        ax2.set_title(f'{audio_name} - 检测到的谐波数量', fontweight='bold')
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('谐波数量')
        ax2.set_xscale('log')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter2, ax=ax2, label='谐波数量')
        
        # 3. 噪声比例分布 (左中)
        ax3 = plt.subplot(4, 2, 3)
        scatter3 = ax3.scatter(freqs, noise_ratios, c=noise_ratios, cmap='Reds', s=30, alpha=0.7)
        ax3.set_title(f'{audio_name} - 噪声能量比例', fontweight='bold')
        ax3.set_xlabel('频率 (Hz)')
        ax3.set_ylabel('噪声比例 (%)')
        ax3.set_xscale('log')
        ax3.grid(True, alpha=0.3)
        plt.colorbar(scatter3, ax=ax3, label='噪声比例 (%)')
        ax3.axhline(y=10, color='red', linestyle='--', alpha=0.8, label='警告阈值')
        ax3.legend()
        
        # 4. 总谐波失真 (右中)
        ax4 = plt.subplot(4, 2, 4)
        valid_thd_mask = np.array(thd_values) > -100
        valid_freqs_thd = np.array(freqs)[valid_thd_mask]
        valid_thd = np.array(thd_values)[valid_thd_mask]
        
        if len(valid_thd) > 0:
            scatter4 = ax4.scatter(valid_freqs_thd, valid_thd, c=valid_thd, cmap='plasma', s=30, alpha=0.7)
            plt.colorbar(scatter4, ax=ax4, label='THD (dB)')
        
        ax4.set_title(f'{audio_name} - 总谐波失真 (THD)', fontweight='bold')
        ax4.set_xlabel('频率 (Hz)')
        ax4.set_ylabel('THD (dB)')
        ax4.set_xscale('log')
        ax4.grid(True, alpha=0.3)
        
        # 5. 频段质量热力图 (下方跨两列)
        ax5 = plt.subplot(4, 1, 3)
        
        # 创建质量矩阵 (3个指标 × N个频段)
        quality_matrix = np.array([
            snr_values,
            harmonic_counts,
            noise_ratios
        ])
        
        # 标准化到0-1范围
        quality_matrix_norm = np.zeros_like(quality_matrix)
        for i in range(quality_matrix.shape[0]):
            row = quality_matrix[i]
            if np.max(row) > np.min(row):
                quality_matrix_norm[i] = (row - np.min(row)) / (np.max(row) - np.min(row))
            else:
                quality_matrix_norm[i] = 0.5
        
        im = ax5.imshow(quality_matrix_norm, cmap='RdYlGn', aspect='auto', interpolation='nearest')
        ax5.set_title(f'{audio_name} - 频段质量热力图', fontweight='bold')
        ax5.set_xlabel('频段索引')
        ax5.set_ylabel('质量指标')
        ax5.set_yticks(range(3))
        ax5.set_yticklabels(['SNR', '谐波数', '噪声比例'])
        
        # 设置x轴标签
        x_ticks = range(0, len(segments), max(1, len(segments)//10))
        ax5.set_xticks(x_ticks)
        ax5.set_xticklabels([f'{segments[i]}\n{freqs[i]:.0f}Hz' for i in x_ticks], rotation=45)
        
        plt.colorbar(im, ax=ax5, label='标准化值 (0-1)')
        
        # 6. 统计摘要 (底部)
        ax6 = plt.subplot(4, 1, 4)
        ax6.axis('off')
        
        # 计算统计摘要
        summary_text = f"📊 {audio_name} 分析摘要\n"
        summary_text += f"{'='*60}\n"
        summary_text += f"总频段数: {len(segments)}\n"
        summary_text += f"SNR统计: 平均{np.mean(snr_values):.1f}dB, 范围[{np.min(snr_values):.1f}, {np.max(snr_values):.1f}]dB\n"
        summary_text += f"谐波统计: 平均{np.mean(harmonic_counts):.1f}个, 总计{np.sum(harmonic_counts)}个\n"
        summary_text += f"噪声比例: 平均{np.mean(noise_ratios):.1f}%, 范围[{np.min(noise_ratios):.1f}, {np.max(noise_ratios):.1f}]%\n"
        summary_text += f"频率范围: {np.min(freqs):.0f}Hz - {np.max(freqs):.0f}Hz\n"
        summary_text += f"分析算法: 优化SNR阈值(6-9dB), 保守噪声估计, 高分辨率FFT"
        
        ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, 
                verticalalignment='top', fontsize=11, fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图片
        output_filename = f'{audio_name}_analysis.png'
        output_path = os.path.join(output_dir, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return output_filename
        
    except Exception as e:
        print(f"   ❌ 可视化生成失败: {e}")
        return None

if __name__ == "__main__":
    batch_audio_analysis()
