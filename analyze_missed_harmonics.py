#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析漏检谐波的原因并优化检测算法
重点分析42段的8、11、12次谐波漏检问题
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_missed_harmonics():
    """分析漏检谐波的原因"""
    print("🔍 分析漏检谐波的原因 - 重点分析42段8、11、12次谐波")
    print("="*70)
    print("分析目标:")
    print("1. 详细分析42段8、11、12次谐波的频谱特征")
    print("2. 检查SNR阈值是否过于严格")
    print("3. 分析局部噪声估计是否准确")
    print("4. 优化检测算法减少漏检")
    print("="*70)
    
    # 重点分析42段
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    target_segment = 42  # 重点分析42段
    
    # 分析每个样本的42段
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 详细分析 {sample_name} 频段42: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        if os.path.exists(audio_path):
            # 详细分析42段
            analysis_result = analyze_segment_42_detailed(audio_path, sample_name, target_segment)
            
            # 创建详细的漏检分析可视化
            create_missed_harmonics_visualization(analysis_result, sample_name)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")

def analyze_segment_42_detailed(audio_path, sample_name, target_segment):
    """详细分析42段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        if target_segment >= len(step_boundaries):
            return None
            
        start_time, end_time = step_boundaries[target_segment]
        expected_freq = freq_table[target_segment]
        
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            return None
        
        # 标准化音频
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        print(f"   详细分析频段 {target_segment} ({expected_freq:.1f}Hz)")
        
        # 超高分辨率FFT分析
        analysis_result = detailed_harmonic_analysis(segment_audio, sr, expected_freq, target_segment)
        
        return analysis_result
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def detailed_harmonic_analysis(audio, sr, expected_freq, seg_idx):
    """详细的谐波分析"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(131072, len(audio))  # 更高分辨率 (128k)
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 分析完整的正频率范围
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        nyquist_freq = sr / 2
        
        print(f"     超高分辨率: {freq_resolution:.4f}Hz")
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_precise(positive_freqs, positive_power, expected_freq)
        
        # 2. 详细分析目标谐波 (8、11、12次)
        target_harmonics = [8, 11, 12]
        missed_harmonic_analysis = analyze_specific_harmonics(
            positive_freqs, positive_power, expected_freq, target_harmonics, fundamental_analysis
        )
        
        # 3. 多种噪声估计方法对比
        noise_analysis_comparison = compare_noise_estimation_methods(
            positive_freqs, positive_power, expected_freq
        )
        
        # 4. 多种SNR阈值测试
        snr_threshold_analysis = test_multiple_snr_thresholds(
            positive_freqs, positive_power, expected_freq, fundamental_analysis, noise_analysis_comparison
        )
        
        # 5. 优化的谐波检测
        optimized_harmonic_analysis = optimized_harmonic_detection(
            positive_freqs, positive_power, expected_freq, fundamental_analysis, noise_analysis_comparison, sr
        )
        
        print(f"     主频: {fundamental_analysis['freq']:.2f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"     优化检测: {len(optimized_harmonic_analysis)}个谐波")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'missed_harmonic_analysis': missed_harmonic_analysis,
            'noise_analysis_comparison': noise_analysis_comparison,
            'snr_threshold_analysis': snr_threshold_analysis,
            'optimized_harmonic_analysis': optimized_harmonic_analysis,
            'freq_resolution': freq_resolution,
            'nyquist_freq': nyquist_freq
        }
        
    except Exception as e:
        print(f"     ❌ 详细分析失败: {e}")
        return None

def analyze_fundamental_precise(freqs, power, expected_freq):
    """精确分析主频"""
    
    bandwidth = 5.0
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'expected_freq': expected_freq,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def analyze_specific_harmonics(freqs, power, fundamental_freq, target_orders, fundamental_analysis):
    """详细分析特定谐波 (8、11、12次)"""
    
    print(f"     详细分析目标谐波: {target_orders}次")
    
    harmonic_details = []
    
    for order in target_orders:
        expected_harmonic_freq = fundamental_freq * order
        
        # 宽搜索带宽
        search_bandwidth = 50.0  # 很宽的搜索范围
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        search_freqs = freqs[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 分析周围的频谱特征
        # 计算峰值宽度
        half_power = harmonic_power / 2
        left_idx = actual_idx
        right_idx = actual_idx
        
        while left_idx > 0 and power[left_idx] > half_power:
            left_idx -= 1
        while right_idx < len(power) - 1 and power[right_idx] > half_power:
            right_idx += 1
        
        peak_width_hz = freqs[right_idx] - freqs[left_idx]
        
        # 计算峰值突出度
        local_background = np.median(search_powers)
        peak_prominence = harmonic_power / local_background
        
        # 计算相对功率
        relative_power_db = harmonic_power_db - fundamental_analysis['power_db']
        
        # 频率误差
        freq_error = actual_freq - expected_harmonic_freq
        
        harmonic_details.append({
            'order': order,
            'expected_freq': expected_harmonic_freq,
            'actual_freq': actual_freq,
            'freq_error': freq_error,
            'power': harmonic_power,
            'power_db': harmonic_power_db,
            'relative_power_db': relative_power_db,
            'peak_width_hz': peak_width_hz,
            'peak_prominence': peak_prominence,
            'local_background': local_background,
            'local_background_db': 10 * np.log10(local_background + 1e-12),
            'index': actual_idx
        })
        
        print(f"       {order}次谐波详情:")
        print(f"         期望频率: {expected_harmonic_freq:.1f}Hz")
        print(f"         实际频率: {actual_freq:.1f}Hz (误差: {freq_error:.1f}Hz)")
        print(f"         功率: {harmonic_power_db:.1f}dB")
        print(f"         相对基频: {relative_power_db:.1f}dB")
        print(f"         峰值宽度: {peak_width_hz:.1f}Hz")
        print(f"         峰值突出度: {peak_prominence:.1f}")
        print(f"         局部背景: {10 * np.log10(local_background + 1e-12):.1f}dB")
    
    return harmonic_details

def compare_noise_estimation_methods(freqs, power, fundamental_freq):
    """对比多种噪声估计方法"""
    
    print(f"     对比噪声估计方法:")
    
    methods = {}
    
    # 方法1: 保守排除 (只排除主频和前5个谐波)
    excluded_ranges_conservative = []
    for order in range(1, 6):
        signal_freq = fundamental_freq * order
        if signal_freq < freqs[-1]:
            excluded_ranges_conservative.append((signal_freq - 15, signal_freq + 15))
    
    noise_mask_conservative = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges_conservative:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask_conservative &= ~exclude_mask
    
    if np.any(noise_mask_conservative):
        conservative_noise = np.percentile(power[noise_mask_conservative], 20)
        methods['conservative'] = 10 * np.log10(conservative_noise + 1e-12)
    else:
        methods['conservative'] = -120
    
    # 方法2: 激进排除 (排除前15个谐波)
    excluded_ranges_aggressive = []
    for order in range(1, 16):
        signal_freq = fundamental_freq * order
        if signal_freq < freqs[-1]:
            excluded_ranges_aggressive.append((signal_freq - 25, signal_freq + 25))
    
    noise_mask_aggressive = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges_aggressive:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask_aggressive &= ~exclude_mask
    
    if np.any(noise_mask_aggressive):
        aggressive_noise = np.percentile(power[noise_mask_aggressive], 20)
        methods['aggressive'] = 10 * np.log10(aggressive_noise + 1e-12)
    else:
        methods['aggressive'] = -120
    
    # 方法3: 频段分割噪声估计
    freq_bands = [
        (0, 5000),
        (5000, 10000),
        (10000, 15000),
        (15000, 24000)
    ]
    
    band_noise_levels = []
    for f_low, f_high in freq_bands:
        band_mask = (freqs >= f_low) & (freqs < f_high)
        
        # 在该频段内排除谐波
        band_noise_mask = band_mask.copy()
        for order in range(1, 25):
            signal_freq = fundamental_freq * order
            if f_low <= signal_freq < f_high:
                exclude_mask = (freqs >= signal_freq - 20) & (freqs <= signal_freq + 20)
                band_noise_mask &= ~exclude_mask
        
        if np.any(band_noise_mask):
            band_noise = np.percentile(power[band_noise_mask], 25)
            band_noise_db = 10 * np.log10(band_noise + 1e-12)
            band_noise_levels.append(band_noise_db)
        else:
            band_noise_levels.append(-120)
    
    methods['band_segmented'] = band_noise_levels
    
    print(f"       保守估计: {methods['conservative']:.1f}dB")
    print(f"       激进估计: {methods['aggressive']:.1f}dB")
    print(f"       分段估计: {band_noise_levels}")
    
    return methods

def test_multiple_snr_thresholds(freqs, power, fundamental_freq, fundamental_analysis, noise_methods):
    """测试多种SNR阈值"""
    
    print(f"     测试多种SNR阈值:")
    
    # 测试不同的SNR阈值
    snr_thresholds = [5.0, 8.0, 10.0, 12.0, 15.0]
    target_harmonics = [8, 11, 12]
    
    threshold_results = {}
    
    for snr_threshold in snr_thresholds:
        detected_count = 0
        detected_orders = []
        
        for order in target_harmonics:
            expected_harmonic_freq = fundamental_freq * order
            
            search_bandwidth = 30.0
            search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                         (freqs <= expected_harmonic_freq + search_bandwidth)
            
            if not np.any(search_mask):
                continue
            
            search_indices = np.where(search_mask)[0]
            search_powers = power[search_mask]
            
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            
            harmonic_power = power[actual_idx]
            harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
            
            # 使用保守噪声估计
            conservative_noise_db = noise_methods['conservative']
            snr_db = harmonic_power_db - conservative_noise_db
            
            relative_power_db = harmonic_power_db - fundamental_analysis['power_db']
            
            # 检查是否满足条件
            if (snr_db >= snr_threshold and 
                relative_power_db >= -60.0 and 
                abs(freqs[actual_idx] - expected_harmonic_freq) <= search_bandwidth * 0.8):
                detected_count += 1
                detected_orders.append(order)
        
        threshold_results[snr_threshold] = {
            'detected_count': detected_count,
            'detected_orders': detected_orders
        }
        
        print(f"       SNR阈值 {snr_threshold:.1f}dB: 检测到 {detected_count}/3 个目标谐波 {detected_orders}")
    
    return threshold_results

def optimized_harmonic_detection(freqs, power, fundamental_freq, fundamental_analysis, 
                                noise_methods, sr):
    """优化的谐波检测"""
    
    print(f"     优化谐波检测:")
    
    detected_harmonics = []
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 使用最保守的噪声估计
    noise_floor_db = noise_methods['conservative']
    
    # 使用更低的SNR阈值
    base_snr_threshold = 6.0  # 进一步降低
    
    nyquist_freq = sr / 2
    
    for order in range(2, 25):
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        # 更宽的搜索带宽
        if expected_harmonic_freq <= 5000:
            search_bandwidth = 40.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 60.0
        else:
            search_bandwidth = 80.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        snr_db = harmonic_power_db - noise_floor_db
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 2000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 相对功率
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 更宽松的条件
        conditions = {
            'snr_sufficient': snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -65.0,  # 进一步放宽
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.9  # 放宽到90%
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'search_bandwidth': search_bandwidth,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
            
            # 特别标注目标谐波
            if order in [8, 11, 12]:
                status = "✅ 目标谐波恢复"
            else:
                status = "✅"
            
            print(f"       {status} {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"SNR={snr_db:.1f}dB (阈值{adjusted_snr_threshold:.1f}dB)")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            
            # 特别关注目标谐波的失败原因
            if order in [8, 11, 12]:
                status = "❌ 目标谐波仍漏检"
                print(f"       {status} {order}次谐波: {actual_freq:.1f}Hz - 失败: {', '.join(failed_conditions)}")
                print(f"            SNR={snr_db:.1f}dB (需要{adjusted_snr_threshold:.1f}dB), "
                      f"相对功率={relative_power_db:.1f}dB, 误差={freq_error:.1f}Hz")
    
    return detected_harmonics

def create_missed_harmonics_visualization(analysis_result, sample_name):
    """创建漏检谐波分析可视化"""
    
    if not analysis_result:
        print(f"   ❌ 无有效分析数据")
        return
    
    print(f"   🎨 生成 {sample_name} 漏检谐波分析可视化...")
    
    # 创建大图 (3行1列)
    fig, axes = plt.subplots(3, 1, figsize=(24, 18))
    
    # 提取数据
    freqs = analysis_result['freqs']
    power_db = analysis_result['power_db']
    fundamental_analysis = analysis_result['fundamental_analysis']
    missed_harmonic_analysis = analysis_result['missed_harmonic_analysis']
    noise_analysis_comparison = analysis_result['noise_analysis_comparison']
    optimized_harmonic_analysis = analysis_result['optimized_harmonic_analysis']
    
    seg_idx = analysis_result['segment_idx']
    expected_freq = analysis_result['expected_freq']
    
    # 设置显示范围 (重点关注8、11、12次谐波区域)
    target_freqs = [expected_freq * order for order in [8, 11, 12]]
    freq_range = (0, 15000)  # 显示到15kHz
    
    freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
    display_freqs = freqs[freq_mask]
    display_power_db = power_db[freq_mask]
    
    # 第一个子图: 完整频谱 + 目标谐波标注
    ax1 = axes[0]
    ax1.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
    
    # 标记不同的噪声估计
    conservative_noise = noise_analysis_comparison['conservative']
    aggressive_noise = noise_analysis_comparison['aggressive']
    
    ax1.axhline(y=conservative_noise, color='blue', linestyle='--', alpha=0.8, 
               label=f'保守噪声估计 {conservative_noise:.1f}dB')
    ax1.axhline(y=aggressive_noise, color='red', linestyle='--', alpha=0.8,
               label=f'激进噪声估计 {aggressive_noise:.1f}dB')
    
    # 标记主频
    fund_freq = fundamental_analysis['freq']
    fund_power_db = fundamental_analysis['power_db']
    ax1.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
            label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
    
    # 标记目标谐波 (8、11、12次)
    target_colors = ['orange', 'green', 'purple']
    for i, harmonic in enumerate(missed_harmonic_analysis):
        color = target_colors[i]
        order = harmonic['order']
        freq = harmonic['actual_freq']
        power_db = harmonic['power_db']
        
        ax1.plot(freq, power_db, 's', color=color, markersize=10,
                label=f'{order}次谐波 {freq:.0f}Hz ({power_db:.1f}dB)')
        
        # 详细标注
        ax1.annotate(f'{order}次谐波\n{freq:.0f}Hz\n{power_db:.1f}dB\n相对基频{harmonic["relative_power_db"]:.1f}dB', 
                    xy=(freq, power_db), xytext=(freq, power_db + 15),
                    ha='center', va='bottom', fontsize=9, color=color,
                    arrowprops=dict(arrowstyle='->', color=color, lw=1.5),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))
    
    ax1.set_title(f'{sample_name} - 频段{seg_idx} 目标谐波详细分析', fontweight='bold', fontsize=14)
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('功率 (dB)')
    ax1.set_xlim(freq_range)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 第二个子图: SNR阈值测试结果
    ax2 = axes[1]
    
    snr_threshold_analysis = analysis_result['snr_threshold_analysis']
    thresholds = list(snr_threshold_analysis.keys())
    detected_counts = [snr_threshold_analysis[t]['detected_count'] for t in thresholds]
    
    bars = ax2.bar(range(len(thresholds)), detected_counts, alpha=0.7, 
                   color=['red' if c < 3 else 'orange' if c < 3 else 'green' for c in detected_counts])
    
    ax2.set_title('SNR阈值 vs 目标谐波检测数量', fontweight='bold', fontsize=14)
    ax2.set_xlabel('SNR阈值 (dB)')
    ax2.set_ylabel('检测到的目标谐波数量 (总共3个)')
    ax2.set_xticks(range(len(thresholds)))
    ax2.set_xticklabels([f'{t:.1f}' for t in thresholds])
    ax2.set_ylim(0, 3.5)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (threshold, count) in enumerate(zip(thresholds, detected_counts)):
        detected_orders = snr_threshold_analysis[threshold]['detected_orders']
        ax2.text(i, count + 0.1, f'{count}/3\n{detected_orders}', 
                ha='center', va='bottom', fontsize=9)
    
    # 第三个子图: 优化检测结果
    ax3 = axes[2]
    ax3.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
    
    # 标记优化检测的噪声底噪
    ax3.axhline(y=conservative_noise, color='blue', linestyle='--', alpha=0.8, 
               label=f'优化噪声底噪 {conservative_noise:.1f}dB')
    
    # 标记主频
    ax3.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
            label=f'主频 {fund_freq:.1f}Hz')
    
    # 标记优化检测的所有谐波
    harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta', 'olive', 'navy', 'maroon']
    
    target_orders = [8, 11, 12]
    for i, harmonic in enumerate(optimized_harmonic_analysis):
        if i < len(harmonic_colors):
            color = harmonic_colors[i]
        else:
            color = 'gray'
            
        order = harmonic['order']
        freq = harmonic['freq']
        power_db = harmonic['power_db']
        snr_db = harmonic['snr_db']
        
        if freq_range[0] <= freq <= freq_range[1]:
            # 特别标记目标谐波
            if order in target_orders:
                marker_style = 's'  # 方形标记目标谐波
                marker_size = 12
                alpha = 1.0
                label_suffix = ' (目标恢复)'
            else:
                marker_style = 'o'
                marker_size = 8
                alpha = 0.8
                label_suffix = ''
            
            ax3.plot(freq, power_db, marker_style, color=color, 
                    markersize=marker_size, alpha=alpha,
                    label=f'{order}次谐波 {freq:.0f}Hz{label_suffix}')
            
            # 标注重要谐波
            if order in target_orders or i < 8:
                ax3.annotate(f'{order}次\n{freq:.0f}Hz\nSNR={snr_db:.1f}dB', 
                            xy=(freq, power_db), xytext=(freq, power_db + 8),
                            ha='center', va='bottom', fontsize=8, color=color,
                            arrowprops=dict(arrowstyle='->', color=color, lw=1))
    
    ax3.set_title(f'优化检测结果 (检测到{len(optimized_harmonic_analysis)}个谐波)', 
                 fontweight='bold', fontsize=14)
    ax3.set_xlabel('频率 (Hz)')
    ax3.set_ylabel('功率 (dB)')
    ax3.set_xlim(freq_range)
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'missed_harmonics_analysis_{sample_name}_segment42.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 漏检分析可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    analyze_missed_harmonics()
