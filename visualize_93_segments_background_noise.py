#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用背景噪声特征可视化93个频段的对比分析
重点展示两个噪声样本与其他样本在所有频段的差异
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch
import seaborn as sns

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def extract_all_segments_background_noise():
    """提取所有93个频段的背景噪声特征"""
    print("🔍 提取所有93个频段的背景噪声特征")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_segment_data = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取所有93个频段的背景噪声特征
                segment_features = extract_file_all_segments(audio_path, filename, target_files)
                
                if segment_features:
                    all_segment_data.extend(segment_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_segment_data)
    
    # 保存结果
    df.to_csv('all_93_segments_background_noise.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 所有频段背景噪声特征已保存: all_93_segments_background_noise.csv")
    
    # 可视化分析
    visualize_93_segments_comparison(df, target_files)
    
    return df

def extract_file_all_segments(audio_path, filename, target_files):
    """提取单个文件所有93个频段的背景噪声特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_data = []
        
        # 分析所有93个频段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 提取该频段的背景噪声特征
            bg_features = extract_segment_background_noise(segment_audio, sr, expected_freq)
            
            # 添加元数据
            segment_info = {
                'filename': filename,
                'segment_idx': seg_idx,
                'expected_freq': expected_freq,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'is_target': filename in target_files,
                'sample_type': 'target' if filename in target_files else 'normal'
            }
            
            # 合并特征和元数据
            segment_info.update(bg_features)
            segment_data.append(segment_info)
        
        return segment_data
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_segment_background_noise(audio, sr, expected_freq):
    """提取单个频段的背景噪声特征"""
    features = {}
    
    try:
        # 标准化音频
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))
        
        # STFT分析
        f, t, Zxx = stft(audio, sr, nperseg=1024, noverlap=512)
        power_spectrum = np.abs(Zxx) ** 2
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 找到期望频率的索引
        expected_freq_idx = np.argmin(np.abs(f - expected_freq))
        
        # 计算背景噪声特征
        all_background_levels = []
        all_signal_levels = []
        
        for frame_idx in range(power_spectrum.shape[1]):
            frame_power_db = power_db[:, frame_idx]
            
            # 信号功率 (期望频率附近)
            signal_band_start = max(0, expected_freq_idx - 3)
            signal_band_end = min(len(f), expected_freq_idx + 3)
            signal_power = np.max(frame_power_db[signal_band_start:signal_band_end])
            all_signal_levels.append(signal_power)
            
            # 背景噪声功率 (排除信号频率)
            background_mask = np.ones(len(f), dtype=bool)
            background_mask[signal_band_start:signal_band_end] = False
            
            if np.any(background_mask):
                background_power_mean = np.mean(frame_power_db[background_mask])
                background_power_median = np.median(frame_power_db[background_mask])
                all_background_levels.append(background_power_mean)
        
        # 统计特征
        if all_background_levels:
            features['background_noise_mean'] = np.mean(all_background_levels)
            features['background_noise_median'] = np.median(all_background_levels)
            features['background_noise_std'] = np.std(all_background_levels)
            features['background_noise_max'] = np.max(all_background_levels)
            features['background_noise_min'] = np.min(all_background_levels)
        else:
            features['background_noise_mean'] = -120
            features['background_noise_median'] = -120
            features['background_noise_std'] = 0
            features['background_noise_max'] = -120
            features['background_noise_min'] = -120
        
        if all_signal_levels:
            features['signal_level_mean'] = np.mean(all_signal_levels)
            features['signal_to_background_ratio'] = np.mean(all_signal_levels) - np.mean(all_background_levels)
        else:
            features['signal_level_mean'] = -120
            features['signal_to_background_ratio'] = 0
            
    except Exception as e:
        # 设置默认值
        features = {
            'background_noise_mean': -120,
            'background_noise_median': -120,
            'background_noise_std': 0,
            'background_noise_max': -120,
            'background_noise_min': -120,
            'signal_level_mean': -120,
            'signal_to_background_ratio': 0
        }
    
    return features

def visualize_93_segments_comparison(df, target_files):
    """可视化93个频段的背景噪声对比"""
    print(f"\n🎨 生成93个频段背景噪声对比可视化...")
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 主要热力图 - background_noise_median
    ax1 = plt.subplot(3, 2, (1, 2))
    
    # 准备热力图数据
    pivot_data_median = prepare_heatmap_data(df, 'background_noise_median')
    
    # 绘制热力图
    im1 = ax1.imshow(pivot_data_median, cmap='RdYlBu_r', aspect='auto', interpolation='nearest')
    
    # 设置标签
    segments = sorted(df['segment_idx'].unique())
    files = sorted(df['filename'].unique())
    
    ax1.set_title('93个频段背景噪声中位数对比 (dB)', fontsize=14, fontweight='bold')
    ax1.set_xlabel('频段索引')
    ax1.set_ylabel('音频文件')
    
    # 设置x轴标签 (每10个频段显示一个)
    x_ticks = range(0, len(segments), 10)
    x_labels = [f'Seg{segments[i]}\n{df[df["segment_idx"]==segments[i]]["expected_freq"].iloc[0]:.0f}Hz' 
               for i in x_ticks if i < len(segments)]
    ax1.set_xticks(x_ticks)
    ax1.set_xticklabels(x_labels, rotation=45, ha='right')
    
    # 设置y轴标签 (突出显示目标文件)
    y_labels = []
    for i, filename in enumerate(files):
        if filename in target_files:
            y_labels.append(f'★ {filename[:30]}...' if len(filename) > 30 else f'★ {filename}')
        else:
            y_labels.append(filename[:30] + '...' if len(filename) > 30 else filename)
    
    ax1.set_yticks(range(len(files)))
    ax1.set_yticklabels(y_labels, fontsize=8)
    
    # 添加颜色条
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
    cbar1.set_label('背景噪声中位数 (dB)', rotation=270, labelpad=20)
    
    # 2. 主要热力图 - background_noise_mean
    ax2 = plt.subplot(3, 2, (3, 4))
    
    pivot_data_mean = prepare_heatmap_data(df, 'background_noise_mean')
    
    im2 = ax2.imshow(pivot_data_mean, cmap='RdYlBu_r', aspect='auto', interpolation='nearest')
    
    ax2.set_title('93个频段背景噪声平均值对比 (dB)', fontsize=14, fontweight='bold')
    ax2.set_xlabel('频段索引')
    ax2.set_ylabel('音频文件')
    
    ax2.set_xticks(x_ticks)
    ax2.set_xticklabels(x_labels, rotation=45, ha='right')
    ax2.set_yticks(range(len(files)))
    ax2.set_yticklabels(y_labels, fontsize=8)
    
    cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
    cbar2.set_label('背景噪声平均值 (dB)', rotation=270, labelpad=20)
    
    # 3. 频段平均对比线图
    ax3 = plt.subplot(3, 2, 5)
    
    # 计算每个频段的平均值
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    segment_stats_target = target_data.groupby('segment_idx')['background_noise_median'].agg(['mean', 'std']).reset_index()
    segment_stats_normal = normal_data.groupby('segment_idx')['background_noise_median'].agg(['mean', 'std']).reset_index()
    
    # 绘制线图
    ax3.errorbar(segment_stats_target['segment_idx'], segment_stats_target['mean'], 
                yerr=segment_stats_target['std'], label='噪声样本', color='red', 
                marker='o', markersize=4, linewidth=2, alpha=0.8)
    ax3.errorbar(segment_stats_normal['segment_idx'], segment_stats_normal['mean'], 
                yerr=segment_stats_normal['std'], label='正常样本', color='blue', 
                marker='s', markersize=3, linewidth=1, alpha=0.6)
    
    ax3.set_title('各频段背景噪声中位数对比')
    ax3.set_xlabel('频段索引')
    ax3.set_ylabel('背景噪声中位数 (dB)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加频率标签
    freq_ticks = range(0, len(segments), 20)
    freq_labels = [f'{df[df["segment_idx"]==segments[i]]["expected_freq"].iloc[0]:.0f}Hz' 
                  for i in freq_ticks if i < len(segments)]
    ax3_freq = ax3.twiny()
    ax3_freq.set_xlim(ax3.get_xlim())
    ax3_freq.set_xticks([segments[i] for i in freq_ticks])
    ax3_freq.set_xticklabels(freq_labels, rotation=45)
    ax3_freq.set_xlabel('频率 (Hz)')
    
    # 4. 分离效果统计
    ax4 = plt.subplot(3, 2, 6)
    
    # 计算每个频段的分离效果
    separation_stats = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_values = seg_data[seg_data['is_target'] == True]['background_noise_median']
        normal_values = seg_data[seg_data['is_target'] == False]['background_noise_median']
        
        if len(target_values) > 0 and len(normal_values) > 0:
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            # 检查分离
            if target_max < normal_min:
                separation_gap = normal_min - target_max
                separation_type = 'target_below'
            elif target_min > normal_max:
                separation_gap = target_min - normal_max
                separation_type = 'target_above'
            else:
                separation_gap = 0
                separation_type = 'overlap'
            
            separation_stats.append({
                'segment_idx': seg_idx,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'separable': separation_gap > 0
            })
    
    sep_df = pd.DataFrame(separation_stats)
    
    # 绘制分离间隙
    colors = ['red' if gap > 0 else 'gray' for gap in sep_df['separation_gap']]
    bars = ax4.bar(sep_df['segment_idx'], sep_df['separation_gap'], color=colors, alpha=0.7)
    
    ax4.set_title('各频段分离效果 (background_noise_median)')
    ax4.set_xlabel('频段索引')
    ax4.set_ylabel('分离间隙 (dB)')
    ax4.grid(True, alpha=0.3)
    
    # 添加统计信息
    separable_count = sum(sep_df['separable'])
    total_count = len(sep_df)
    ax4.text(0.02, 0.98, f'可分离频段: {separable_count}/{total_count} ({separable_count/total_count*100:.1f}%)', 
            transform=ax4.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('93_segments_background_noise_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: 93_segments_background_noise_comparison.png")
    
    # 生成详细统计报告
    generate_detailed_statistics(df, target_files, separation_stats)

def prepare_heatmap_data(df, feature_name):
    """准备热力图数据"""
    # 创建透视表
    pivot_table = df.pivot_table(values=feature_name, index='filename', columns='segment_idx', aggfunc='mean')
    
    # 填充缺失值
    pivot_table = pivot_table.fillna(-120)
    
    return pivot_table.values

def generate_detailed_statistics(df, target_files, separation_stats):
    """生成详细统计报告"""
    print(f"\n📊 93个频段背景噪声分析统计报告")
    print("="*70)
    
    # 整体统计
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📈 整体统计:")
    print(f"   总频段数: {len(df['segment_idx'].unique())}")
    print(f"   总文件数: {len(df['filename'].unique())}")
    print(f"   目标文件数: {len(target_files)}")
    print(f"   正常文件数: {len(df['filename'].unique()) - len(target_files)}")
    
    # 背景噪声水平对比
    print(f"\n🔊 背景噪声水平对比:")
    print(f"   噪声样本 - 中位数: {target_data['background_noise_median'].mean():.2f} ± {target_data['background_noise_median'].std():.2f} dB")
    print(f"   正常样本 - 中位数: {normal_data['background_noise_median'].mean():.2f} ± {normal_data['background_noise_median'].std():.2f} dB")
    print(f"   差异: {target_data['background_noise_median'].mean() - normal_data['background_noise_median'].mean():.2f} dB")
    
    print(f"   噪声样本 - 平均值: {target_data['background_noise_mean'].mean():.2f} ± {target_data['background_noise_mean'].std():.2f} dB")
    print(f"   正常样本 - 平均值: {normal_data['background_noise_mean'].mean():.2f} ± {normal_data['background_noise_mean'].std():.2f} dB")
    print(f"   差异: {target_data['background_noise_mean'].mean() - normal_data['background_noise_mean'].mean():.2f} dB")
    
    # 分离效果统计
    sep_df = pd.DataFrame(separation_stats)
    separable_segments = sep_df[sep_df['separable'] == True]
    
    print(f"\n🎯 分离效果统计:")
    print(f"   可完全分离的频段数: {len(separable_segments)}/{len(sep_df)} ({len(separable_segments)/len(sep_df)*100:.1f}%)")
    
    if len(separable_segments) > 0:
        print(f"   平均分离间隙: {separable_segments['separation_gap'].mean():.2f} dB")
        print(f"   最大分离间隙: {separable_segments['separation_gap'].max():.2f} dB")
        print(f"   最小分离间隙: {separable_segments['separation_gap'].min():.2f} dB")
        
        # 最佳分离频段
        best_segments = separable_segments.nlargest(5, 'separation_gap')
        print(f"\n🏆 最佳分离频段 (前5个):")
        for _, row in best_segments.iterrows():
            freq = df[df['segment_idx'] == row['segment_idx']]['expected_freq'].iloc[0]
            print(f"     频段 {row['segment_idx']:2d} ({freq:6.1f}Hz): 分离间隙 {row['separation_gap']:.2f} dB")
    
    # 频率范围分析
    print(f"\n📊 频率范围分析:")
    
    # 低频段 (100-500Hz)
    low_freq_data = df[df['expected_freq'] <= 500]
    low_freq_separable = len([s for s in separation_stats if s['separable'] and 
                             df[df['segment_idx'] == s['segment_idx']]['expected_freq'].iloc[0] <= 500])
    print(f"   低频段 (≤500Hz): {low_freq_separable}/{len(low_freq_data['segment_idx'].unique())} 可分离")
    
    # 中频段 (500-2000Hz)
    mid_freq_data = df[(df['expected_freq'] > 500) & (df['expected_freq'] <= 2000)]
    mid_freq_separable = len([s for s in separation_stats if s['separable'] and 
                             500 < df[df['segment_idx'] == s['segment_idx']]['expected_freq'].iloc[0] <= 2000])
    print(f"   中频段 (500-2000Hz): {mid_freq_separable}/{len(mid_freq_data['segment_idx'].unique())} 可分离")
    
    # 高频段 (>2000Hz)
    high_freq_data = df[df['expected_freq'] > 2000]
    high_freq_separable = len([s for s in separation_stats if s['separable'] and 
                              df[df['segment_idx'] == s['segment_idx']]['expected_freq'].iloc[0] > 2000])
    print(f"   高频段 (>2000Hz): {high_freq_separable}/{len(high_freq_data['segment_idx'].unique())} 可分离")

if __name__ == "__main__":
    df = extract_all_segments_background_noise()
    print(f"\n✅ 93个频段背景噪声对比分析完成！")
