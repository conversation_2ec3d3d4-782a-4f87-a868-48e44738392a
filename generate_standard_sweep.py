#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成标准扫频信号，用于93段主频置零分析
参考freq_split中的扫频生成方法
"""

import numpy as np
import soundfile as sf
import os

def generate_standard_sweep(start_freq=100, stop_freq=24000, octave=12, 
                          min_cycles=10, min_duration=153, fs=48000,
                          output_path="standard_sweep_93segments.wav"):
    """
    生成标准扫频信号，与freq_split_optimized中的参数一致
    
    Args:
        start_freq: 起始频率 (Hz)
        stop_freq: 结束频率 (Hz)
        octave: 每八度的频段数
        min_cycles: 每个频段的最小周期数
        min_duration: 每个频段的最小持续时间 (ms)
        fs: 采样率 (Hz)
        output_path: 输出文件路径
    
    Returns:
        signal: 生成的扫频信号
        freq_table: 频率表
        time_table: 时间表
    """
    print(f"🎵 生成标准扫频信号")
    print(f"📊 参数: {start_freq}Hz - {stop_freq}Hz, {octave}段/八度")
    print(f"⏱️  最小持续时间: {min_duration}ms, 最小周期: {min_cycles}")
    print("="*60)
    
    # 计算频率表 - 与freq_split_optimized完全一致
    octaves = np.log2(stop_freq / start_freq)
    total_steps = int(octaves * octave)
    
    freq_table = []
    for i in range(total_steps):
        freq = start_freq * (2 ** (i / octave))
        if freq <= stop_freq:
            freq_table.append(freq)
    
    print(f"✅ 生成{len(freq_table)}个频段")
    
    # 计算每个频段的持续时间
    time_table = []
    for freq in freq_table:
        # 基于周期数的持续时间
        duration_cycles = min_cycles / freq
        
        # 基于最小持续时间
        duration_min = min_duration / 1000.0  # 转换为秒
        
        # 取较大值
        duration = max(duration_cycles, duration_min)
        time_table.append(duration)
    
    # 生成信号
    signal = []
    current_time = 0
    
    print(f"🔧 生成各频段信号...")
    
    for i, (freq, duration) in enumerate(zip(freq_table, time_table)):
        # 生成时间轴
        t = np.linspace(0, duration, int(duration * fs), endpoint=False)
        
        # 生成正弦波
        segment = np.sin(2 * np.pi * freq * t)
        
        # 添加渐入渐出，避免突变
        fade_samples = int(0.01 * fs)  # 10ms渐变
        if len(segment) > 2 * fade_samples:
            # 渐入
            fade_in = np.linspace(0, 1, fade_samples)
            segment[:fade_samples] *= fade_in
            
            # 渐出
            fade_out = np.linspace(1, 0, fade_samples)
            segment[-fade_samples:] *= fade_out
        
        signal.extend(segment)
        current_time += duration
        
        if (i + 1) % 10 == 0:
            print(f"  完成 {i+1}/{len(freq_table)} 个频段")
    
    # 转换为numpy数组
    signal = np.array(signal)
    
    # 标准化到[-1, 1]
    if np.max(np.abs(signal)) > 0:
        signal = signal / np.max(np.abs(signal)) * 0.9  # 留一点余量
    
    # 保存音频文件
    sf.write(output_path, signal, fs)
    
    print(f"\n✅ 扫频信号生成完成!")
    print(f"📁 输出文件: {output_path}")
    print(f"⏱️  总时长: {len(signal)/fs:.2f}秒")
    print(f"📊 采样率: {fs}Hz")
    print(f"🔊 信号幅度: ±{np.max(np.abs(signal)):.3f}")
    
    # 打印频率表摘要
    print(f"\n📋 频率表摘要:")
    print(f"  第1段: {freq_table[0]:.1f}Hz, 时长{time_table[0]:.3f}s")
    print(f"  第10段: {freq_table[9]:.1f}Hz, 时长{time_table[9]:.3f}s") if len(freq_table) > 9 else None
    print(f"  第50段: {freq_table[49]:.1f}Hz, 时长{time_table[49]:.3f}s") if len(freq_table) > 49 else None
    print(f"  第{len(freq_table)}段: {freq_table[-1]:.1f}Hz, 时长{time_table[-1]:.3f}s")
    
    return signal, freq_table, time_table

def main():
    """
    主函数：生成标准扫频信号
    """
    try:
        # 生成标准扫频信号 - 与freq_split_optimized参数完全一致
        signal, freq_table, time_table = generate_standard_sweep(
            start_freq=100,
            stop_freq=24000,  # 扩展到24kHz
            octave=12,
            min_cycles=10,
            min_duration=153,
            fs=48000,
            output_path="standard_sweep_93segments.wav"
        )
        
        # 保存频率和时间信息
        info_file = "standard_sweep_info.txt"
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write("标准扫频信号信息\n")
            f.write("="*50 + "\n\n")
            f.write(f"总频段数: {len(freq_table)}\n")
            f.write(f"频率范围: {freq_table[0]:.1f}Hz - {freq_table[-1]:.1f}Hz\n")
            f.write(f"总时长: {len(signal)/48000:.2f}秒\n")
            f.write(f"采样率: 48000Hz\n\n")
            
            f.write("频段详情:\n")
            f.write(f"{'序号':<4} {'频率(Hz)':<10} {'时长(s)':<8} {'周期数':<8}\n")
            f.write("-" * 40 + "\n")
            
            for i, (freq, duration) in enumerate(zip(freq_table, time_table)):
                cycles = freq * duration
                f.write(f"{i+1:<4} {freq:<10.1f} {duration:<8.3f} {cycles:<8.1f}\n")
        
        print(f"📄 频段信息已保存: {info_file}")
        print(f"🎯 现在可以使用 'python thd_n_fundamental_only.py' 分析此标准扫频信号")
        
    except Exception as e:
        print(f"❌ 生成过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
