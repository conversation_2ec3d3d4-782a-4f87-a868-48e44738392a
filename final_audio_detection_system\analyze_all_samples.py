#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析所有样本数据，优化检测参数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_all_samples():
    """分析所有样本数据"""
    print("🔍 分析所有样本数据，优化检测参数")
    print("="*60)
    
    # 读取两个检测结果文件
    try:
        batch_df = pd.read_csv('batch_detection_results.csv')
        specific_df = pd.read_csv('specific_folders_detection_results.csv')
        print(f"批量检测结果: {len(batch_df)} 个样本")
        print(f"特定文件夹结果: {len(specific_df)} 个样本")
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    # 合并数据，去重
    all_df = pd.concat([batch_df, specific_df], ignore_index=True)
    all_df = all_df.drop_duplicates(subset=['filename'], keep='last')
    success_df = all_df[all_df['detection_status'] == 'success'].copy()
    
    print(f"合并后总样本数: {len(all_df)}")
    print(f"成功检测样本数: {len(success_df)}")
    
    # 重新分类样本
    success_df = reclassify_samples(success_df)
    
    # 分析真正的正负样本
    analyze_true_samples(success_df)
    
    # 分析异常率分布
    analyze_anomaly_distribution(success_df)
    
    # 提出参数优化建议
    suggest_parameter_optimization(success_df)
    
    return success_df

def reclassify_samples(df):
    """重新分类样本"""
    print(f"\n📋 重新分类样本:")
    
    # 已知的真正有竖线异常的样本（根据之前的分析）
    true_positive_patterns = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav', 
        '录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_155101.wav'
    ]
    
    # 重新标注
    df['true_label'] = 'unknown'
    
    # 标记真正的正样本
    for pattern in true_positive_patterns:
        mask = df['filename'].str.contains(pattern, na=False)
        df.loc[mask, 'true_label'] = 'true_positive'
    
    # 标记明确的负样本（正常扫频）
    negative_patterns = [
        'sweep_test_0db.wav',
        '主板隔音eva取消.wav',
        '喇叭eva没贴.wav',
        'neg1.wav', 'neg2.wav', 'neg3.wav',
        'ok.*100.wav', 'ok.*85.wav'
    ]
    
    for pattern in negative_patterns:
        mask = df['filename'].str.contains(pattern, na=False, regex=True)
        df.loc[mask, 'true_label'] = 'true_negative'
    
    # 统计重新分类结果
    true_pos = df[df['true_label'] == 'true_positive']
    true_neg = df[df['true_label'] == 'true_negative']
    unknown = df[df['true_label'] == 'unknown']
    
    print(f"  真正的正样本: {len(true_pos)} 个")
    print(f"  真正的负样本: {len(true_neg)} 个") 
    print(f"  未确定样本: {len(unknown)} 个")
    
    return df

def analyze_true_samples(df):
    """分析真正的正负样本"""
    print(f"\n📊 真正正负样本分析:")
    
    true_pos = df[df['true_label'] == 'true_positive']
    true_neg = df[df['true_label'] == 'true_negative']
    
    if len(true_pos) > 0:
        print(f"\n  真正的正样本 ({len(true_pos)}个):")
        print(f"    异常率: {true_pos['anomaly_rate'].mean():.1f}% ± {true_pos['anomaly_rate'].std():.1f}%")
        print(f"    范围: {true_pos['anomaly_rate'].min():.1f}% - {true_pos['anomaly_rate'].max():.1f}%")
        print(f"    竖线数: {true_pos['line_count'].mean():.1f} ± {true_pos['line_count'].std():.1f}")
        
        print(f"    详细数据:")
        for _, row in true_pos.iterrows():
            print(f"      {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条)")
    
    if len(true_neg) > 0:
        print(f"\n  真正的负样本 ({len(true_neg)}个):")
        print(f"    异常率: {true_neg['anomaly_rate'].mean():.1f}% ± {true_neg['anomaly_rate'].std():.1f}%")
        print(f"    范围: {true_neg['anomaly_rate'].min():.1f}% - {true_neg['anomaly_rate'].max():.1f}%")
        print(f"    竖线数: {true_neg['line_count'].mean():.1f} ± {true_neg['line_count'].std():.1f}")
        
        # 显示异常率>0的负样本
        false_positives = true_neg[true_neg['anomaly_rate'] > 0]
        if len(false_positives) > 0:
            print(f"    误报样本 ({len(false_positives)}个):")
            for _, row in false_positives.iterrows():
                print(f"      {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条)")

def analyze_anomaly_distribution(df):
    """分析异常率分布"""
    print(f"\n📈 异常率分布分析:")
    
    # 按异常率分组
    zero_anomaly = df[df['anomaly_rate'] == 0]
    low_anomaly = df[(df['anomaly_rate'] > 0) & (df['anomaly_rate'] <= 5)]
    mid_anomaly = df[(df['anomaly_rate'] > 5) & (df['anomaly_rate'] <= 15)]
    high_anomaly = df[df['anomaly_rate'] > 15]
    
    print(f"  0%异常: {len(zero_anomaly)} 个 ({len(zero_anomaly)/len(df)*100:.1f}%)")
    print(f"  1-5%异常: {len(low_anomaly)} 个 ({len(low_anomaly)/len(df)*100:.1f}%)")
    print(f"  6-15%异常: {len(mid_anomaly)} 个 ({len(mid_anomaly)/len(df)*100:.1f}%)")
    print(f"  >15%异常: {len(high_anomaly)} 个 ({len(high_anomaly)/len(df)*100:.1f}%)")
    
    # 分析高异常率样本
    if len(high_anomaly) > 0:
        print(f"\n  高异常率样本 (>15%) 分析:")
        high_sorted = high_anomaly.sort_values('anomaly_rate', ascending=False)
        
        # 区分真正的正样本和可能的误报
        true_pos_high = high_sorted[high_sorted['true_label'] == 'true_positive']
        other_high = high_sorted[high_sorted['true_label'] != 'true_positive']
        
        print(f"    真正的正样本: {len(true_pos_high)} 个")
        print(f"    其他高异常率样本: {len(other_high)} 个")
        
        if len(other_high) > 0:
            print(f"    可能的误报样本:")
            for _, row in other_high.head(10).iterrows():
                print(f"      {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条)")

def suggest_parameter_optimization(df):
    """提出参数优化建议"""
    print(f"\n💡 参数优化建议:")
    
    true_pos = df[df['true_label'] == 'true_positive']
    true_neg = df[df['true_label'] == 'true_negative']
    
    if len(true_pos) > 0 and len(true_neg) > 0:
        # 计算理想阈值
        pos_min = true_pos['anomaly_rate'].min()
        neg_max = true_neg['anomaly_rate'].max()
        
        print(f"  当前检测参数:")
        print(f"    - 标准差预筛选阈值: 2000")
        print(f"    - 峰值高度要求: 均值+0.3σ")
        print(f"    - 峰值突出度要求: 0.2σ")
        print(f"    - 最小竖线强度: 0.1")
        
        print(f"\n  真实样本特征:")
        print(f"    - 真正正样本最低异常率: {pos_min:.1f}%")
        print(f"    - 真正负样本最高异常率: {neg_max:.1f}%")
        
        if pos_min > neg_max:
            ideal_threshold = (pos_min + neg_max) / 2
            print(f"    - 理想分类阈值: {ideal_threshold:.1f}%")
            print(f"    - 当前分离度: ✅ 良好 ({pos_min - neg_max:.1f}%间隔)")
        else:
            print(f"    - ⚠️ 正负样本重叠，需要优化参数")
            
            # 建议提高检测阈值
            target_neg_rate = 2.0  # 目标：负样本异常率<2%
            current_neg_mean = true_neg['anomaly_rate'].mean()
            
            if current_neg_mean > target_neg_rate:
                reduction_factor = target_neg_rate / current_neg_mean
                print(f"\n  建议参数调整:")
                print(f"    - 当前负样本平均异常率: {current_neg_mean:.1f}%")
                print(f"    - 目标负样本异常率: <{target_neg_rate:.1f}%")
                print(f"    - 建议降低敏感度: {reduction_factor:.2f}倍")
                print(f"    - 建议调整:")
                print(f"      * 标准差预筛选阈值: 2000 → {int(2000 / reduction_factor)}")
                print(f"      * 峰值高度要求: 0.3σ → {0.3 / reduction_factor:.1f}σ")
                print(f"      * 峰值突出度要求: 0.2σ → {0.2 / reduction_factor:.1f}σ")
                print(f"      * 最小竖线强度: 0.1 → {0.1 / reduction_factor:.2f}")
    
    # 分析误报样本特征
    false_positives = true_neg[true_neg['anomaly_rate'] > 5]
    if len(false_positives) > 0:
        print(f"\n  误报样本分析:")
        print(f"    - 误报样本数: {len(false_positives)}")
        print(f"    - 误报率: {len(false_positives)/len(true_neg)*100:.1f}%")
        print(f"    - 误报样本异常率: {false_positives['anomaly_rate'].mean():.1f}% ± {false_positives['anomaly_rate'].std():.1f}%")

def create_optimization_visualization(df):
    """创建优化分析可视化"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('检测参数优化分析', fontsize=16)
    
    # 1. 真正正负样本异常率对比
    ax1 = axes[0, 0]
    true_pos = df[df['true_label'] == 'true_positive']
    true_neg = df[df['true_label'] == 'true_negative']
    
    if len(true_pos) > 0:
        ax1.hist(true_pos['anomaly_rate'], alpha=0.7, label=f'真正正样本 ({len(true_pos)}个)', 
                bins=20, color='red')
    if len(true_neg) > 0:
        ax1.hist(true_neg['anomaly_rate'], alpha=0.7, label=f'真正负样本 ({len(true_neg)}个)', 
                bins=20, color='green')
    
    ax1.set_xlabel('异常率 (%)')
    ax1.set_ylabel('样本数量')
    ax1.set_title('真正正负样本异常率分布')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 异常率分布饼图
    ax2 = axes[0, 1]
    zero_count = len(df[df['anomaly_rate'] == 0])
    low_count = len(df[(df['anomaly_rate'] > 0) & (df['anomaly_rate'] <= 5)])
    mid_count = len(df[(df['anomaly_rate'] > 5) & (df['anomaly_rate'] <= 15)])
    high_count = len(df[df['anomaly_rate'] > 15])
    
    sizes = [zero_count, low_count, mid_count, high_count]
    labels = ['0%异常', '1-5%异常', '6-15%异常', '>15%异常']
    colors = ['green', 'lightgreen', 'orange', 'red']
    
    ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax2.set_title('异常率分布')
    
    # 3. 异常率vs竖线数散点图
    ax3 = axes[1, 0]
    for label in ['true_positive', 'true_negative', 'unknown']:
        subset = df[df['true_label'] == label]
        if len(subset) > 0:
            ax3.scatter(subset['anomaly_rate'], subset['line_count'], 
                       alpha=0.7, label=f'{label} ({len(subset)}个)', s=50)
    
    ax3.set_xlabel('异常率 (%)')
    ax3.set_ylabel('竖线数量')
    ax3.set_title('异常率 vs 竖线数量')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 箱线图
    ax4 = axes[1, 1]
    box_data = []
    box_labels = []
    
    for label in ['true_positive', 'true_negative']:
        subset = df[df['true_label'] == label]
        if len(subset) > 0:
            box_data.append(subset['anomaly_rate'])
            box_labels.append(f'{label}\n({len(subset)}个)')
    
    if box_data:
        ax4.boxplot(box_data, labels=box_labels)
        ax4.set_ylabel('异常率 (%)')
        ax4.set_title('真正正负样本异常率箱线图')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('parameter_optimization_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n📊 优化分析图表已保存: parameter_optimization_analysis.png")

if __name__ == "__main__":
    df = analyze_all_samples()
    if df is not None:
        create_optimization_visualization(df)
