#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析两个低频噪声样本的特征
录音_步进扫频_100Hz至20000Hz_20250714_153632.wav
录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_specific_low_freq_samples():
    """分析特定的两个低频噪声样本"""
    print("🔍 分析特定的两个低频噪声样本")
    print("="*70)
    
    # 目标样本（使用完整的文件名）
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    print(f"目标样本:")
    for i, file in enumerate(target_files, 1):
        print(f"  {i}. {file}")
    print("="*70)
    
    # 加载特征数据
    feature_file = 'final_audio_detection_system/comprehensive_features.csv'
    if not os.path.exists(feature_file):
        feature_file = 'comprehensive_features.csv'
        if not os.path.exists(feature_file):
            print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
            return

    df = pd.read_csv(feature_file)
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 检查文件是否存在
    available_files = df['filename'].unique()
    print(f"📊 可用文件: {len(available_files)}个")
    
    # 查找匹配的文件名
    matched_files = []
    for target in target_files:
        # 尝试不同的匹配方式
        exact_match = [f for f in available_files if f == target]
        partial_match = [f for f in available_files if target.replace('.wav', '') in f]
        
        if exact_match:
            matched_files.append(exact_match[0])
            print(f"✅ 找到精确匹配: {exact_match[0]}")
        elif partial_match:
            matched_files.append(partial_match[0])
            print(f"✅ 找到部分匹配: {partial_match[0]} (目标: {target})")
        else:
            print(f"❌ 未找到匹配: {target}")
    
    if len(matched_files) == 0:
        print("❌ 未找到任何目标文件")
        print("可用的负样本文件:")
        neg_files = df[df['label'] == 'neg']['filename'].unique()
        for i, file in enumerate(neg_files, 1):
            print(f"  {i}. {file}")
        return
    
    print(f"📊 成功匹配 {len(matched_files)} 个文件")
    
    # 排除竖线检测特征和元信息
    exclude_features = [col for col in df.columns if col.startswith('vl_')] + \
                      ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    
    candidate_features = [col for col in df.columns if col not in exclude_features]
    print(f"📊 候选特征数: {len(candidate_features)}个")
    
    # 获取所有其他样本（正样本 + 其他负样本）
    other_samples = df[~df['filename'].isin(matched_files)]
    print(f"📊 其他样本数据: {len(other_samples)}个频段")
    
    # 分析每个目标样本
    for target_file in matched_files:
        print(f"\n🔍 分析目标样本: {target_file}")
        print("="*60)
        
        target_data = df[df['filename'] == target_file]
        
        # 分析低频段特征 (前20个频段，约100-315Hz)
        low_freq_results = analyze_low_freq_separation(target_data, other_samples, candidate_features, target_file)
        
        # 分析全频段特征
        full_freq_results = analyze_full_freq_separation(target_data, other_samples, candidate_features, target_file)
        
        # 可视化结果
        visualize_separation_results(target_data, other_samples, low_freq_results, full_freq_results, target_file)

def analyze_low_freq_separation(target_data, other_samples, candidate_features, target_file):
    """分析低频段分离特征"""
    print(f"   🔍 分析低频段分离特征 (前20个频段, 100-315Hz)")
    
    low_freq_segments = list(range(20))  # 前20个频段
    effective_features = []
    
    for feature in candidate_features:
        separable_segments = []
        
        for segment_idx in low_freq_segments:
            # 获取该频段的目标样本数据
            target_segment = target_data[target_data['segment_idx'] == segment_idx]
            # 获取该频段的所有其他样本数据
            other_segment = other_samples[other_samples['segment_idx'] == segment_idx]
            
            if len(target_segment) == 0 or len(other_segment) == 0:
                continue
            
            # 检查完全分离
            separation_result = check_complete_separation(other_segment, target_segment, feature, segment_idx)
            
            if separation_result['valid'] and separation_result.get('complete_separation', False):
                separable_segments.append({
                    'segment_idx': segment_idx,
                    'expected_freq': target_segment['expected_freq'].iloc[0],
                    'score': separation_result['score'],
                    'separation_type': separation_result['separation_type'],
                    'separation_gap': separation_result['separation_gap'],
                    'other_range': separation_result['other_range'],
                    'target_range': separation_result['target_range']
                })
        
        if len(separable_segments) > 0:
            avg_score = np.mean([seg['score'] for seg in separable_segments])
            effective_features.append({
                'feature': feature,
                'separable_segments': separable_segments,
                'avg_score': avg_score,
                'segment_count': len(separable_segments),
                'freq_range': 'low_freq'
            })
    
    # 排序并显示结果
    effective_features.sort(key=lambda x: x['avg_score'], reverse=True)
    
    print(f"   ✅ 在低频段找到 {len(effective_features)} 个有效特征")
    
    if len(effective_features) > 0:
        print(f"   📊 最有效的前5个低频特征:")
        for i, feature_info in enumerate(effective_features[:5]):
            best_seg = max(feature_info['separable_segments'], key=lambda x: x['score'])
            print(f"     {i+1}. {feature_info['feature']}")
            print(f"        可分离低频段数: {feature_info['segment_count']}/20")
            print(f"        平均分离评分: {feature_info['avg_score']:.1f}")
            print(f"        最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
            print(f"        分离类型: {best_seg['separation_type']}")
            print(f"        分离间隙: {best_seg['separation_gap']:.6f}")
    
    return effective_features

def analyze_full_freq_separation(target_data, other_samples, candidate_features, target_file):
    """分析全频段分离特征"""
    print(f"   🔍 分析全频段分离特征 (所有93个频段)")
    
    all_segments = list(range(93))  # 所有93个频段
    effective_features = []
    
    for feature in candidate_features:
        separable_segments = []
        
        for segment_idx in all_segments:
            # 获取该频段的目标样本数据
            target_segment = target_data[target_data['segment_idx'] == segment_idx]
            # 获取该频段的所有其他样本数据
            other_segment = other_samples[other_samples['segment_idx'] == segment_idx]
            
            if len(target_segment) == 0 or len(other_segment) == 0:
                continue
            
            # 检查完全分离
            separation_result = check_complete_separation(other_segment, target_segment, feature, segment_idx)
            
            if separation_result['valid'] and separation_result.get('complete_separation', False):
                separable_segments.append({
                    'segment_idx': segment_idx,
                    'expected_freq': target_segment['expected_freq'].iloc[0],
                    'score': separation_result['score'],
                    'separation_type': separation_result['separation_type'],
                    'separation_gap': separation_result['separation_gap'],
                    'other_range': separation_result['other_range'],
                    'target_range': separation_result['target_range']
                })
        
        if len(separable_segments) > 0:
            avg_score = np.mean([seg['score'] for seg in separable_segments])
            effective_features.append({
                'feature': feature,
                'separable_segments': separable_segments,
                'avg_score': avg_score,
                'segment_count': len(separable_segments),
                'freq_range': 'full_freq'
            })
    
    # 排序并显示结果
    effective_features.sort(key=lambda x: x['avg_score'], reverse=True)
    
    print(f"   ✅ 在全频段找到 {len(effective_features)} 个有效特征")
    
    if len(effective_features) > 0:
        print(f"   📊 最有效的前5个全频特征:")
        for i, feature_info in enumerate(effective_features[:5]):
            best_seg = max(feature_info['separable_segments'], key=lambda x: x['score'])
            print(f"     {i+1}. {feature_info['feature']}")
            print(f"        可分离频段数: {feature_info['segment_count']}/93")
            print(f"        平均分离评分: {feature_info['avg_score']:.1f}")
            print(f"        最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
            print(f"        分离类型: {best_seg['separation_type']}")
            print(f"        分离间隙: {best_seg['separation_gap']:.6f}")
    
    return effective_features

def check_complete_separation(other_segment, target_segment, feature, segment_idx):
    """检查与所有其他样本的完全分离"""
    try:
        other_values = other_segment[feature].dropna()
        target_values = target_segment[feature].dropna()
        
        if len(other_values) == 0 or len(target_values) == 0:
            return {'valid': False, 'score': 0}
        
        # 获取所有其他样本的完整取值范围
        other_min = np.min(other_values)
        other_max = np.max(other_values)
        
        # 获取目标样本的取值范围
        target_min = np.min(target_values)
        target_max = np.max(target_values)
        
        # 检查是否完全分离
        complete_separation = False
        separation_gap = 0
        separation_type = 'no_separation'
        
        if target_max < other_min:
            # 目标样本完全在其他样本下方
            complete_separation = True
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
        elif target_min > other_max:
            # 目标样本完全在其他样本上方
            complete_separation = True
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
        
        if complete_separation:
            # 计算分离质量评分
            other_range = other_max - other_min if other_max > other_min else 1e-10
            target_range = target_max - target_min if target_max > target_min else 1e-10
            total_range = max(other_max, target_max) - min(other_min, target_min)
            
            if total_range > 0:
                separation_score = (separation_gap / total_range) * 1000
            else:
                separation_score = 1000
            
            # 额外考虑分离的相对质量
            relative_gap = separation_gap / max(other_range, target_range)
            separation_score *= (1 + relative_gap)
            
            return {
                'valid': True,
                'score': separation_score,
                'complete_separation': True,
                'separation_type': separation_type,
                'separation_gap': separation_gap,
                'other_range': [other_min, other_max],
                'target_range': [target_min, target_max],
                'other_count': len(other_values),
                'target_count': len(target_values)
            }
        else:
            return {
                'valid': True,
                'score': 0,
                'complete_separation': False,
                'separation_type': 'overlap',
                'other_range': [other_min, other_max],
                'target_range': [target_min, target_max]
            }
        
    except Exception as e:
        return {'valid': False, 'score': 0, 'error': str(e)}

def visualize_separation_results(target_data, other_samples, low_freq_results, full_freq_results, target_file):
    """可视化分离结果"""
    print(f"   🎨 生成可视化图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{target_file} - 低频噪声特征分离分析', fontsize=16, fontweight='bold')
    
    # 1. 低频段vs全频段特征数量对比
    ax1 = axes[0, 0]
    
    categories = ['低频段\n(0-19)', '全频段\n(0-92)']
    feature_counts = [len(low_freq_results), len(full_freq_results)]
    
    bars = ax1.bar(categories, feature_counts, color=['red', 'blue'], alpha=0.7)
    ax1.set_title('有效特征数量对比')
    ax1.set_ylabel('特征数量')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, count in zip(bars, feature_counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{count}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    # 2. 最佳特征分离能力对比
    ax2 = axes[0, 1]
    
    if len(low_freq_results) > 0 and len(full_freq_results) > 0:
        # 选择前5个特征
        top_low_freq = low_freq_results[:5]
        top_full_freq = full_freq_results[:5]
        
        low_freq_names = [f['feature'][:15] + '...' if len(f['feature']) > 15 else f['feature'] for f in top_low_freq]
        low_freq_scores = [f['avg_score'] for f in top_low_freq]
        
        full_freq_names = [f['feature'][:15] + '...' if len(f['feature']) > 15 else f['feature'] for f in top_full_freq]
        full_freq_scores = [f['avg_score'] for f in top_full_freq]
        
        x_pos = np.arange(max(len(low_freq_names), len(full_freq_names)))
        
        if len(low_freq_names) > 0:
            ax2.barh(x_pos[:len(low_freq_names)], low_freq_scores, 
                    alpha=0.7, color='red', label='低频段特征')
        
        if len(full_freq_names) > 0:
            ax2.barh(x_pos[:len(full_freq_names)] + 0.4, full_freq_scores, 
                    alpha=0.7, color='blue', label='全频段特征')
        
        ax2.set_title('最佳特征分离评分对比')
        ax2.set_xlabel('平均分离评分')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 低频段分离频段分布
    ax3 = axes[1, 0]
    
    if len(low_freq_results) > 0:
        # 统计每个频段被多少个特征分离
        segment_separation_count = {}
        for feature_info in low_freq_results:
            for seg_info in feature_info['separable_segments']:
                seg_idx = seg_info['segment_idx']
                if seg_idx not in segment_separation_count:
                    segment_separation_count[seg_idx] = 0
                segment_separation_count[seg_idx] += 1
        
        segments = list(range(20))
        separation_counts = [segment_separation_count.get(seg, 0) for seg in segments]
        
        # 获取频率标签
        freq_labels = []
        for seg_idx in segments:
            seg_data = target_data[target_data['segment_idx'] == seg_idx]
            if len(seg_data) > 0:
                freq = seg_data['expected_freq'].iloc[0]
                freq_labels.append(f'{freq:.0f}Hz')
            else:
                freq_labels.append(f'Seg{seg_idx}')
        
        bars = ax3.bar(segments, separation_counts, color='red', alpha=0.7)
        ax3.set_title('低频段分离能力分布')
        ax3.set_xlabel('频段索引')
        ax3.set_ylabel('可分离特征数')
        ax3.set_xticks(range(0, 20, 2))
        ax3.set_xticklabels([freq_labels[i] for i in range(0, 20, 2)], rotation=45)
        ax3.grid(True, alpha=0.3)
    
    # 4. 特征类型分析
    ax4 = axes[1, 1]
    
    # 分析特征类型
    feature_types = {
        'harmonic': [],
        'spectral': [],
        'temporal': [],
        'statistical': [],
        'energy': [],
        'other': []
    }
    
    all_features = low_freq_results + full_freq_results
    for feature_info in all_features:
        feature_name = feature_info['feature'].lower()
        if 'harmonic' in feature_name or 'thd' in feature_name:
            feature_types['harmonic'].append(feature_info)
        elif 'spec' in feature_name or 'centroid' in feature_name or 'bandwidth' in feature_name:
            feature_types['spectral'].append(feature_info)
        elif 'td_' in feature_name or 'zcr' in feature_name:
            feature_types['temporal'].append(feature_info)
        elif 'stat_' in feature_name or 'skew' in feature_name or 'kurt' in feature_name:
            feature_types['statistical'].append(feature_info)
        elif 'energy' in feature_name or 'rms' in feature_name:
            feature_types['energy'].append(feature_info)
        else:
            feature_types['other'].append(feature_info)
    
    type_names = list(feature_types.keys())
    type_counts = [len(features) for features in feature_types.values()]
    
    colors = ['red', 'orange', 'yellow', 'green', 'blue', 'purple']
    wedges, texts, autotexts = ax4.pie(type_counts, labels=type_names, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax4.set_title('有效特征类型分布')
    
    plt.tight_layout()
    
    # 保存图片
    safe_filename = target_file.replace('.wav', '').replace('_', '-').replace(' ', '-')
    plt.savefig(f'{safe_filename}_low_freq_separation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"   ✅ 图表已保存: {safe_filename}_low_freq_separation_analysis.png")

if __name__ == "__main__":
    analyze_specific_low_freq_samples()
    print(f"\n✅ 特定低频噪声样本分析完成！")
