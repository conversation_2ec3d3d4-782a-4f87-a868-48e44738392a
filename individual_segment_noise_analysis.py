#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对前十个频段各自单独分析噪声特征
针对两个低频噪声样本的逐频段分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_individual_segments():
    """对前十个频段各自单独分析"""
    print("🔍 对前十个频段各自单独分析噪声特征")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    # 收集所有文件的频段数据
    all_segment_data = {}
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取前10个频段的特征
                segment_features = extract_segment_features(audio_path, filename)
                
                if segment_features:
                    for seg_idx, features in segment_features.items():
                        if seg_idx not in all_segment_data:
                            all_segment_data[seg_idx] = []
                        
                        features['filename'] = filename
                        features['label'] = true_label
                        features['is_target'] = filename in target_files
                        all_segment_data[seg_idx].append(features)
    
    # 分析每个频段
    segment_results = {}
    for seg_idx in range(10):
        if seg_idx in all_segment_data:
            print(f"\n🔍 分析频段 {seg_idx}")
            print("-" * 50)
            
            segment_df = pd.DataFrame(all_segment_data[seg_idx])
            segment_results[seg_idx] = analyze_segment_separation(segment_df, seg_idx, target_files)
    
    # 生成综合报告
    generate_segment_analysis_report(segment_results, target_files)
    
    # 可视化结果
    visualize_segment_analysis(segment_results, target_files)
    
    return segment_results

def extract_segment_features(audio_path, filename):
    """提取单个文件前10个频段的特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_features = {}
        
        # 分析前10个频段
        for seg_idx in range(min(10, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 提取该频段的噪声特征
            features = extract_single_segment_noise_features(segment_audio, sr, expected_freq, seg_idx)
            features['expected_freq'] = expected_freq
            features['start_time'] = start_time
            features['end_time'] = end_time
            features['duration'] = end_time - start_time
            
            segment_features[seg_idx] = features
        
        return segment_features
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_single_segment_noise_features(audio, sr, expected_freq, seg_idx):
    """提取单个频段的噪声特征"""
    features = {}
    
    try:
        # 标准化音频
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))
        
        # 1. 时域噪声特征
        features['time_noise_variance'] = np.var(audio)
        features['time_noise_std'] = np.std(audio)
        features['time_noise_rms'] = np.sqrt(np.mean(audio**2))
        features['time_noise_peak'] = np.max(np.abs(audio))
        
        # 2. 频域噪声特征
        fft = np.fft.fft(audio)
        freqs = np.fft.fftfreq(len(audio), 1/sr)
        magnitude = np.abs(fft)
        
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 期望频率附近的能量
        freq_tolerance = 20
        expected_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
        
        if np.any(expected_mask):
            signal_energy = np.max(positive_magnitude[expected_mask])
            
            # 噪声能量 (排除期望频率)
            noise_mask = np.abs(positive_freqs - expected_freq) > freq_tolerance * 2
            if np.any(noise_mask):
                noise_energy = np.mean(positive_magnitude[noise_mask])
                total_noise_energy = np.sum(positive_magnitude[noise_mask])
                
                features['freq_signal_energy'] = signal_energy
                features['freq_noise_energy'] = noise_energy
                features['freq_total_noise_energy'] = total_noise_energy
                features['freq_snr'] = signal_energy / (noise_energy + 1e-12)
                features['freq_snr_db'] = 10 * np.log10(signal_energy / (noise_energy + 1e-12))
            else:
                features['freq_signal_energy'] = signal_energy
                features['freq_noise_energy'] = 0
                features['freq_total_noise_energy'] = 0
                features['freq_snr'] = float('inf')
                features['freq_snr_db'] = 100
        else:
            features['freq_signal_energy'] = 0
            features['freq_noise_energy'] = 0
            features['freq_total_noise_energy'] = 0
            features['freq_snr'] = 0
            features['freq_snr_db'] = -100
        
        # 3. STFT噪声特征
        f, t, Zxx = stft(audio, sr, nperseg=512, noverlap=256)
        power_spectrum = np.abs(Zxx) ** 2
        
        # 计算每个时间帧的噪声特征
        frame_noise_powers = []
        frame_noise_entropies = []
        
        for frame_idx in range(power_spectrum.shape[1]):
            frame_power = power_spectrum[:, frame_idx]
            
            # 噪声功率 (非峰值频率)
            power_threshold = np.percentile(frame_power, 85)
            noise_mask = frame_power < power_threshold
            
            if np.any(noise_mask):
                frame_noise_power = np.mean(frame_power[noise_mask])
                frame_noise_powers.append(frame_noise_power)
                
                # 噪声分布熵
                noise_power_normalized = frame_power[noise_mask] / (np.sum(frame_power[noise_mask]) + 1e-12)
                noise_power_normalized = noise_power_normalized + 1e-12
                frame_noise_entropy = -np.sum(noise_power_normalized * np.log2(noise_power_normalized))
                frame_noise_entropies.append(frame_noise_entropy)
        
        if frame_noise_powers:
            features['stft_noise_power_mean'] = np.mean(frame_noise_powers)
            features['stft_noise_power_std'] = np.std(frame_noise_powers)
            features['stft_noise_power_max'] = np.max(frame_noise_powers)
            features['stft_noise_power_ratio'] = np.mean(frame_noise_powers) / (np.max(frame_noise_powers) + 1e-12)
        else:
            features['stft_noise_power_mean'] = 0
            features['stft_noise_power_std'] = 0
            features['stft_noise_power_max'] = 0
            features['stft_noise_power_ratio'] = 0
        
        if frame_noise_entropies:
            features['stft_noise_entropy_mean'] = np.mean(frame_noise_entropies)
            features['stft_noise_entropy_std'] = np.std(frame_noise_entropies)
            features['stft_noise_entropy_max'] = np.max(frame_noise_entropies)
        else:
            features['stft_noise_entropy_mean'] = 0
            features['stft_noise_entropy_std'] = 0
            features['stft_noise_entropy_max'] = 0
        
        # 4. 频谱平坦度
        if len(positive_magnitude) > 0 and np.all(positive_magnitude > 0):
            geometric_mean = np.exp(np.mean(np.log(positive_magnitude + 1e-12)))
            arithmetic_mean = np.mean(positive_magnitude)
            features['spectral_flatness'] = geometric_mean / (arithmetic_mean + 1e-12)
        else:
            features['spectral_flatness'] = 0
        
        # 5. 频谱质心和带宽
        if np.sum(positive_magnitude) > 0:
            spectral_centroid = np.sum(positive_freqs * positive_magnitude) / np.sum(positive_magnitude)
            features['spectral_centroid'] = spectral_centroid
            
            # 频谱带宽
            freq_diff = positive_freqs - spectral_centroid
            spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * positive_magnitude) / np.sum(positive_magnitude))
            features['spectral_bandwidth'] = spectral_bandwidth
        else:
            features['spectral_centroid'] = 0
            features['spectral_bandwidth'] = 0
        
    except Exception as e:
        # 设置默认值
        default_features = [
            'time_noise_variance', 'time_noise_std', 'time_noise_rms', 'time_noise_peak',
            'freq_signal_energy', 'freq_noise_energy', 'freq_total_noise_energy', 'freq_snr', 'freq_snr_db',
            'stft_noise_power_mean', 'stft_noise_power_std', 'stft_noise_power_max', 'stft_noise_power_ratio',
            'stft_noise_entropy_mean', 'stft_noise_entropy_std', 'stft_noise_entropy_max',
            'spectral_flatness', 'spectral_centroid', 'spectral_bandwidth'
        ]
        for feature in default_features:
            features[feature] = 0
    
    return features

def analyze_segment_separation(segment_df, seg_idx, target_files):
    """分析单个频段的分离能力"""
    print(f"   频段 {seg_idx}: {len(segment_df)} 个样本")
    
    if len(segment_df) == 0:
        return None
    
    # 获取频率信息
    expected_freq = segment_df['expected_freq'].iloc[0] if 'expected_freq' in segment_df.columns else 0
    print(f"   期望频率: {expected_freq:.1f} Hz")
    
    # 分离目标样本和其他样本
    target_data = segment_df[segment_df['is_target'] == True]
    other_data = segment_df[segment_df['is_target'] == False]
    
    print(f"   目标样本: {len(target_data)} 个")
    print(f"   其他样本: {len(other_data)} 个")
    
    # 获取特征列
    feature_cols = [col for col in segment_df.columns 
                   if col not in ['filename', 'label', 'is_target', 'expected_freq', 'start_time', 'end_time', 'duration']]
    
    separable_features = []
    
    for feature in feature_cols:
        target_values = target_data[feature].dropna()
        other_values = other_data[feature].dropna()
        
        if len(target_values) == 0 or len(other_values) == 0:
            continue
        
        target_min, target_max = np.min(target_values), np.max(target_values)
        other_min, other_max = np.min(other_values), np.max(other_values)
        
        # 检查完全分离
        if target_max < other_min:
            separation_gap = other_min - target_max
            separation_type = 'target_below_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
        elif target_min > other_max:
            separation_gap = target_min - other_max
            separation_type = 'target_above_others'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'other_range': [other_min, other_max],
                'target_mean': np.mean(target_values),
                'other_mean': np.mean(other_values)
            })
    
    # 排序
    separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
    
    print(f"   ✅ 找到 {len(separable_features)} 个可分离特征")
    
    if len(separable_features) > 0:
        print(f"   📊 最佳特征:")
        for i, feature_info in enumerate(separable_features[:3]):
            print(f"     {i+1}. {feature_info['feature']}")
            print(f"        分离间隙: {feature_info['separation_gap']:.6f}")
            print(f"        分离类型: {feature_info['separation_type']}")
    
    return {
        'segment_idx': seg_idx,
        'expected_freq': expected_freq,
        'separable_features': separable_features,
        'total_samples': len(segment_df),
        'target_samples': len(target_data),
        'other_samples': len(other_data)
    }

def generate_segment_analysis_report(segment_results, target_files):
    """生成频段分析报告"""
    print(f"\n📊 频段分析报告")
    print("="*70)
    
    # 统计每个频段的分离能力
    segment_summary = []
    
    for seg_idx in range(10):
        if seg_idx in segment_results and segment_results[seg_idx]:
            result = segment_results[seg_idx]
            segment_summary.append({
                'segment_idx': seg_idx,
                'expected_freq': result['expected_freq'],
                'separable_feature_count': len(result['separable_features']),
                'best_separation_gap': result['separable_features'][0]['separation_gap'] if result['separable_features'] else 0,
                'best_feature': result['separable_features'][0]['feature'] if result['separable_features'] else 'None'
            })
        else:
            segment_summary.append({
                'segment_idx': seg_idx,
                'expected_freq': 0,
                'separable_feature_count': 0,
                'best_separation_gap': 0,
                'best_feature': 'None'
            })
    
    # 显示摘要
    print(f"频段分离能力摘要:")
    print("-" * 70)
    print(f"{'频段':>4} {'频率(Hz)':>8} {'可分离特征数':>12} {'最佳间隙':>12} {'最佳特征':>20}")
    print("-" * 70)
    
    for summary in segment_summary:
        print(f"{summary['segment_idx']:>4} {summary['expected_freq']:>8.1f} "
              f"{summary['separable_feature_count']:>12} {summary['best_separation_gap']:>12.6f} "
              f"{summary['best_feature'][:20]:>20}")
    
    # 保存详细结果
    detailed_results = []
    for seg_idx, result in segment_results.items():
        if result:
            for feature_info in result['separable_features']:
                detailed_results.append({
                    'segment_idx': seg_idx,
                    'expected_freq': result['expected_freq'],
                    'feature': feature_info['feature'],
                    'separation_gap': feature_info['separation_gap'],
                    'separation_type': feature_info['separation_type'],
                    'target_min': feature_info['target_range'][0],
                    'target_max': feature_info['target_range'][1],
                    'other_min': feature_info['other_range'][0],
                    'other_max': feature_info['other_range'][1],
                    'target_mean': feature_info['target_mean'],
                    'other_mean': feature_info['other_mean']
                })
    
    if detailed_results:
        detailed_df = pd.DataFrame(detailed_results)
        detailed_df.to_csv('individual_segment_analysis.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 详细结果已保存: individual_segment_analysis.csv")

def visualize_segment_analysis(segment_results, target_files):
    """可视化频段分析结果"""
    print(f"\n🎨 生成频段分析可视化...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('前十个频段噪声特征分离分析', fontsize=16, fontweight='bold')
    
    # 1. 每个频段的可分离特征数量
    ax1 = axes[0, 0]
    
    segments = list(range(10))
    feature_counts = []
    freqs = []
    
    for seg_idx in segments:
        if seg_idx in segment_results and segment_results[seg_idx]:
            feature_counts.append(len(segment_results[seg_idx]['separable_features']))
            freqs.append(segment_results[seg_idx]['expected_freq'])
        else:
            feature_counts.append(0)
            freqs.append(100 + seg_idx * 10)  # 估算频率
    
    bars = ax1.bar(segments, feature_counts, color='skyblue', alpha=0.7)
    ax1.set_title('各频段可分离特征数量')
    ax1.set_xlabel('频段索引')
    ax1.set_ylabel('可分离特征数')
    ax1.set_xticks(segments)
    ax1.grid(True, alpha=0.3)
    
    # 添加频率标签
    for i, (bar, freq) in enumerate(zip(bars, freqs)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{freq:.0f}Hz', ha='center', va='bottom', fontsize=8, rotation=45)
    
    # 2. 最佳分离间隙
    ax2 = axes[0, 1]
    
    best_gaps = []
    for seg_idx in segments:
        if seg_idx in segment_results and segment_results[seg_idx] and segment_results[seg_idx]['separable_features']:
            best_gaps.append(segment_results[seg_idx]['separable_features'][0]['separation_gap'])
        else:
            best_gaps.append(0)
    
    ax2.plot(segments, best_gaps, 'ro-', markersize=6)
    ax2.set_title('各频段最佳分离间隙')
    ax2.set_xlabel('频段索引')
    ax2.set_ylabel('分离间隙')
    ax2.set_xticks(segments)
    ax2.grid(True, alpha=0.3)
    
    # 3. 特征类型分布
    ax3 = axes[1, 0]
    
    feature_types = {
        'time_noise': 0,
        'freq_noise': 0,
        'stft_noise': 0,
        'spectral': 0
    }
    
    for seg_idx, result in segment_results.items():
        if result:
            for feature_info in result['separable_features']:
                feature_name = feature_info['feature']
                if 'time_noise' in feature_name:
                    feature_types['time_noise'] += 1
                elif 'freq_' in feature_name:
                    feature_types['freq_noise'] += 1
                elif 'stft_noise' in feature_name:
                    feature_types['stft_noise'] += 1
                elif 'spectral' in feature_name:
                    feature_types['spectral'] += 1
    
    type_names = list(feature_types.keys())
    type_counts = list(feature_types.values())
    
    colors = ['red', 'orange', 'yellow', 'green']
    wedges, texts, autotexts = ax3.pie(type_counts, labels=type_names, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax3.set_title('有效特征类型分布')
    
    # 4. 频段分离热力图
    ax4 = axes[1, 1]
    
    # 创建热力图数据
    heatmap_data = []
    feature_names = set()
    
    for seg_idx in segments:
        if seg_idx in segment_results and segment_results[seg_idx]:
            for feature_info in segment_results[seg_idx]['separable_features'][:5]:  # 前5个特征
                feature_names.add(feature_info['feature'])
    
    feature_names = sorted(list(feature_names))[:10]  # 最多10个特征
    
    for seg_idx in segments:
        row_data = []
        for feature_name in feature_names:
            gap = 0
            if seg_idx in segment_results and segment_results[seg_idx]:
                for feature_info in segment_results[seg_idx]['separable_features']:
                    if feature_info['feature'] == feature_name:
                        gap = feature_info['separation_gap']
                        break
            row_data.append(gap)
        heatmap_data.append(row_data)
    
    if heatmap_data and feature_names:
        heatmap_data = np.array(heatmap_data)
        
        im = ax4.imshow(heatmap_data, cmap='Reds', aspect='auto')
        ax4.set_title('频段-特征分离热力图')
        ax4.set_xlabel('特征')
        ax4.set_ylabel('频段索引')
        
        # 设置标签
        ax4.set_xticks(range(len(feature_names)))
        ax4.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax4.set_yticks(range(len(segments)))
        ax4.set_yticklabels([f'Seg{i}' for i in segments])
        
        # 添加颜色条
        plt.colorbar(im, ax=ax4, label='分离间隙')
    
    plt.tight_layout()
    plt.savefig('individual_segment_noise_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: individual_segment_noise_analysis.png")

if __name__ == "__main__":
    segment_results = analyze_individual_segments()
    print(f"\n✅ 前十个频段单独分析完成！")
