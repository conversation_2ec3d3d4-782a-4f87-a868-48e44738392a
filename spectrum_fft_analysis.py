#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频谱FFT分析脚本
对93段频谱再做FFT进行可视化，检测频谱中的周期性和不规律性
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Deja<PERSON><PERSON> Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_spectrum_fft(audio_segment, sr):
    """
    计算音频段的频谱，然后对频谱做FFT
    """
    
    # 标准化
    if np.max(np.abs(audio_segment)) > 0:
        audio_segment = audio_segment / np.max(np.abs(audio_segment))
    
    # 高分辨率FFT
    fft_size = 131072
    if len(audio_segment) < fft_size:
        audio_segment = np.pad(audio_segment, (0, fft_size - len(audio_segment)), 'constant')
    else:
        audio_segment = audio_segment[:fft_size]
    
    # 应用窗函数
    window = np.hanning(len(audio_segment))
    audio_segment = audio_segment * window
    
    # 第一次FFT：得到频谱
    fft1 = np.fft.fft(audio_segment)
    magnitude1 = np.abs(fft1)
    
    # 只取正频率部分
    positive_magnitude = magnitude1[:fft_size//2]
    positive_freqs = np.fft.fftfreq(fft_size, 1/sr)[:fft_size//2]
    
    # 计算功率谱（仅用于显示）
    power_spectrum = positive_magnitude ** 2
    power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)

    # 对频谱幅度做第二次FFT（不是功率谱）
    # 直接使用频谱幅度，进行归一化
    normalized_magnitude = (positive_magnitude - np.min(positive_magnitude)) / (np.max(positive_magnitude) - np.min(positive_magnitude) + 1e-12)

    # 对归一化频谱幅度做FFT
    spectrum_fft = np.fft.fft(normalized_magnitude)
    spectrum_fft_magnitude = np.abs(spectrum_fft)
    
    # 频谱FFT的"频率"轴（表示频谱中的周期性）
    spectrum_freq_axis = np.fft.fftfreq(len(normalized_magnitude), d=positive_freqs[1]-positive_freqs[0])

    # 只取正频率部分
    positive_spectrum_fft = spectrum_fft_magnitude[:len(spectrum_fft_magnitude)//2]
    positive_spectrum_freqs = spectrum_freq_axis[:len(spectrum_freq_axis)//2]

    return (power_spectrum_db, positive_freqs,
            positive_spectrum_fft, positive_spectrum_freqs,
            normalized_magnitude)

def analyze_segment_spectrum_fft(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的频谱FFT
    """
    
    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage
            
            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration
            
            if trimmed_end_time <= trimmed_start_time:
                print(f"  ⚠️  段{seg_idx}: 修剪后时长不足")
                return None
            
            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
        
        # 检查边界
        if start_sample >= len(y) or end_sample > len(y) or start_sample >= end_sample:
            print(f"  ⚠️  段{seg_idx}: 时间边界超出音频范围")
            return None
        
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"  ⚠️  段{seg_idx}: 音频段为空")
            return None
        
        if len(segment_audio) < 1024:
            print(f"  ⚠️  段{seg_idx}: 音频段太短")
            return None
        
        # 计算频谱FFT
        power_spectrum_db, freqs, spectrum_fft, spectrum_freqs, normalized_magnitude = calculate_spectrum_fft(segment_audio, sr)
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = 10**(power_spectrum_db[search_mask]/10)
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 使用harmonic_detection_system检测谐波
        power_linear = 10**(power_spectrum_db/10)
        noise_analysis = {
            'global_noise_floor_db': -60,
            'noise_variation_db': 10,
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }
        
        harmonic_analysis = detect_harmonics_for_segment(freqs, power_linear, fundamental_freq, noise_analysis)
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'power_spectrum_db': power_spectrum_db,
            'freqs': freqs,
            'spectrum_fft': spectrum_fft,
            'spectrum_freqs': spectrum_freqs,
            'normalized_magnitude': normalized_magnitude,
            'harmonic_analysis': harmonic_analysis
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_spectrum_fft_visualization(segment_data, output_dir, filename=""):
    """
    创建频谱FFT可视化
    """
    
    if not segment_data:
        return None
    
    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    power_spectrum_db = segment_data['power_spectrum_db']
    freqs = segment_data['freqs']
    spectrum_fft = segment_data['spectrum_fft']
    spectrum_freqs = segment_data['spectrum_freqs']
    normalized_magnitude = segment_data['normalized_magnitude']
    harmonic_analysis = segment_data['harmonic_analysis']
    
    # 创建图形 - 3个子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n频谱FFT分析', 
                 fontsize=16, fontweight='bold')
    
    # 第一个子图：原始功率谱
    ax1.plot(freqs, power_spectrum_db, 'blue', linewidth=1, alpha=0.7, label='功率谱')
    
    # 标记主频
    ax1.axvline(fundamental_freq, color='red', linestyle='-', linewidth=3, 
               alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')
    
    # 标记检测到的谐波
    if harmonic_analysis:
        harmonic_freqs = [h['freq'] for h in harmonic_analysis]
        harmonic_powers_db = [h['power_db'] for h in harmonic_analysis]
        ax1.scatter(harmonic_freqs, harmonic_powers_db, c='green', s=50, alpha=0.8, 
                   marker='^', label=f'谐波({len(harmonic_analysis)}个)')
        
        # 标注谐波次数
        for i, (freq, power_db) in enumerate(zip(harmonic_freqs, harmonic_powers_db)):
            order = round(freq / fundamental_freq)
            ax1.annotate(f'{order}', (freq, power_db), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8, color='green')
    
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('功率 (dB)')
    ax1.set_xlim(50, 20000)
    ax1.set_xscale('log')
    ax1.set_ylim(-80, 40)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_title('原始功率谱与谐波检测')
    
    # 第二个子图：归一化频谱幅度
    ax2.plot(freqs, normalized_magnitude, 'purple', linewidth=1, alpha=0.7, label='归一化频谱幅度')
    ax2.axvline(fundamental_freq, color='red', linestyle='--', linewidth=2,
               alpha=0.6, label=f'主频: {fundamental_freq:.1f}Hz')

    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('归一化幅度')
    ax2.set_xlim(50, 20000)
    ax2.set_xscale('log')
    ax2.set_ylim(0, 1)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_title('归一化频谱幅度（用于FFT输入）')
    
    # 第三个子图：频谱的FFT
    # 限制显示范围，避免DC分量
    display_mask = (spectrum_freqs > 0) & (spectrum_freqs < 0.1)  # 显示有意义的频率范围
    
    if np.any(display_mask):
        display_spectrum_freqs = spectrum_freqs[display_mask]
        display_spectrum_fft = spectrum_fft[display_mask]
        
        ax3.plot(display_spectrum_freqs, display_spectrum_fft, 'orange', linewidth=1.5, label='频谱FFT')
        
        # 寻找峰值
        if len(display_spectrum_fft) > 10:
            # 简单峰值检测
            peak_threshold = np.mean(display_spectrum_fft) + 2 * np.std(display_spectrum_fft)
            peak_indices = []
            for i in range(1, len(display_spectrum_fft)-1):
                if (display_spectrum_fft[i] > display_spectrum_fft[i-1] and 
                    display_spectrum_fft[i] > display_spectrum_fft[i+1] and
                    display_spectrum_fft[i] > peak_threshold):
                    peak_indices.append(i)
            
            if peak_indices:
                peak_freqs = display_spectrum_freqs[peak_indices]
                peak_values = display_spectrum_fft[peak_indices]
                ax3.scatter(peak_freqs, peak_values, c='red', s=60, alpha=0.8, 
                           marker='o', label=f'峰值({len(peak_indices)}个)')
                
                # 标注前3个最高峰值
                sorted_peaks = sorted(zip(peak_values, peak_freqs), reverse=True)[:3]
                for i, (value, freq) in enumerate(sorted_peaks):
                    ax3.annotate(f'{freq:.3f}', (freq, value), xytext=(5, 5), 
                                textcoords='offset points', fontsize=8, color='red')
    
    ax3.set_xlabel('频谱频率 (1/Hz)')
    ax3.set_ylabel('FFT幅度')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.set_title('频谱的FFT（检测频谱周期性和不规律性）')
    
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(output_dir, f"spectrum_fft_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def process_single_segment_spectrum_fft(args):
    """
    处理单个段的频谱FFT分析（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 分析段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_spectrum_fft(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_spectrum_fft_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 计算频谱FFT特征
                spectrum_fft = segment_data['spectrum_fft']
                spectrum_freqs = segment_data['spectrum_freqs']

                # 计算频谱FFT的统计特征
                fft_energy = np.sum(spectrum_fft**2)
                fft_peak = np.max(spectrum_fft)
                fft_mean = np.mean(spectrum_fft)
                fft_std = np.std(spectrum_fft)

                # 计算频谱的不规律性指标
                irregularity_score = fft_std / (fft_mean + 1e-12)

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_freq': segment_data['fundamental_freq'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'fft_energy': fft_energy,
                    'fft_peak': fft_peak,
                    'irregularity_score': irregularity_score
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 谐波: {result['harmonic_count']}个, 不规律性: {irregularity_score:.3f}")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 对琴身内部异物文件的93段频谱做FFT分析
    """

    print("🎯 琴身内部异物文件频谱FFT分析")
    print("📝 对93段频谱再做FFT，检测频谱中的周期性和不规律性")
    print("📝 每段去掉开头结尾8%比例再计算")
    print("="*80)

    # 查找琴身内部异物文件
    target_file = "test20250722/琴身内部异物1.1.wav"

    if not os.path.exists(target_file):
        print(f"❌ 未找到目标文件: {target_file}")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_spectrum_fft_analysis"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行频谱FFT分析...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_spectrum_fft, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件频谱FFT分析完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        # 频谱FFT特征统计
        if successful_results:
            harmonic_counts = [r['harmonic_count'] for r in successful_results]
            fft_energies = [r['fft_energy'] for r in successful_results]
            fft_peaks = [r['fft_peak'] for r in successful_results]
            irregularity_scores = [r['irregularity_score'] for r in successful_results]

            print(f"\n📊 频谱FFT特征统计:")
            print(f"  平均谐波数: {np.mean(harmonic_counts):.1f}个")
            print(f"  谐波数范围: {np.min(harmonic_counts)} ~ {np.max(harmonic_counts)}个")
            print(f"  平均FFT能量: {np.mean(fft_energies):.1f}")
            print(f"  FFT能量范围: {np.min(fft_energies):.1f} ~ {np.max(fft_energies):.1f}")
            print(f"  平均不规律性: {np.mean(irregularity_scores):.3f}")
            print(f"  不规律性范围: {np.min(irregularity_scores):.3f} ~ {np.max(irregularity_scores):.3f}")

            # 高不规律性段
            high_irregularity_threshold = np.mean(irregularity_scores) + np.std(irregularity_scores)
            high_irregularity_segments = [(r['seg_idx'], r['expected_freq'], r['irregularity_score'])
                                         for r in successful_results if r['irregularity_score'] > high_irregularity_threshold]

            print(f"\n🎯 高不规律性段（>均值+1σ）:")
            if high_irregularity_segments:
                for seg_idx, freq, score in high_irregularity_segments:
                    print(f"  📈 段{seg_idx} ({freq:.1f}Hz): 不规律性 {score:.3f}")
            else:
                print("  ✅ 无明显高不规律性段")

        print("="*80)
        print("🎯 频谱FFT分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
