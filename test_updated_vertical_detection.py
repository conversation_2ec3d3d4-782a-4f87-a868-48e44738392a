#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的竖线检测（使用优化的频段分割）
"""

import os
import sys
sys.path.append('.')

from final_audio_detection_system.segment_vertical_line_detector import SegmentVerticalLineDetector
from final_audio_detection_system.integrated_vertical_line_detector import IntegratedVerticalLineDetector
from final_audio_detection_system.freq_split_visualizer import FreqSplitVisualizer

def test_updated_vertical_detection():
    """测试更新后的竖线检测"""
    print("🔍 测试更新后的竖线检测（使用优化的频段分割）")
    print("="*70)
    
    # 测试文件
    test_files = [
        r"../test20250717/pos/sd卡/sd1_1.wav",
        r"../test20250717/neg/主板隔音eva取消.wav",
        r"../待定/主板边缘eva消除.wav"
    ]
    
    # 创建检测器
    segment_detector = SegmentVerticalLineDetector()
    integrated_detector = IntegratedVerticalLineDetector()
    visualizer = FreqSplitVisualizer()
    
    for i, audio_path in enumerate(test_files):
        if not os.path.exists(audio_path):
            print(f"❌ 文件不存在: {audio_path}")
            continue
            
        filename = os.path.basename(audio_path)
        folder_path = os.path.dirname(audio_path)
        folder_name = os.path.basename(folder_path)
        
        print(f"\n{'='*70}")
        print(f"🎵 [{i+1}/{len(test_files)}] 测试文件: {folder_name}/{filename}")
        print("="*70)
        
        try:
            # 1. 测试频段分割可视化器
            print(f"\n📊 1. 测试频段分割可视化器")
            print("-" * 50)
            visualizer_result = visualizer.visualize_freq_split(audio_path)
            if visualizer_result:
                print(f"✅ 频段分割可视化成功")
            else:
                print(f"❌ 频段分割可视化失败")
            
            # 2. 测试分段竖线检测器
            print(f"\n📊 2. 测试分段竖线检测器")
            print("-" * 50)
            segment_result = segment_detector.detect_vertical_lines_in_segments(audio_path)
            if segment_result:
                print(f"✅ 分段竖线检测成功")
                print(f"   检测到 {segment_result.get('total_vertical_lines', 0)} 条竖线")
                print(f"   异常频段: {segment_result.get('anomalous_segments', 0)}")
                print(f"   总频段数: {segment_result.get('total_segments', 0)}")
            else:
                print(f"❌ 分段竖线检测失败")
            
            # 3. 测试集成竖线检测器
            print(f"\n📊 3. 测试集成竖线检测器")
            print("-" * 50)
            integrated_result = integrated_detector.detect_integrated_anomalies(audio_path)
            if integrated_result:
                print(f"✅ 集成竖线检测成功")
                print(f"   检测到 {integrated_result.get('total_vertical_lines', 0)} 条竖线")
                print(f"   异常频段: {integrated_result.get('anomalous_segments', 0)}")
                print(f"   总频段数: {integrated_result.get('total_segments', 0)}")
                print(f"   最大线强度: {integrated_result.get('max_line_strength', 0):.3f}")
            else:
                print(f"❌ 集成竖线检测失败")
            
            print(f"\n🎯 文件 {filename} 测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
        
        print("-" * 70)
    
    print(f"\n🎉 所有测试完成！")
    print(f"📊 更新后的竖线检测已使用优化的频段分割")

def test_optimization_comparison():
    """对比优化前后的效果"""
    print("\n🔍 对比优化前后的频段分割效果")
    print("="*70)
    
    # 测试文件
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    if not os.path.exists(audio_path):
        print(f"❌ 测试文件不存在: {audio_path}")
        return
    
    try:
        # 使用优化的频段分割
        from freq_split_optimized import split_freq_steps_optimized
        
        print(f"📊 使用优化的频段分割测试: {os.path.basename(audio_path)}")
        
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path,
            start_freq=100,
            stop_freq=20000,
            octave=12,
            min_cycles=10,
            min_duration=153,
            fs=48000,
            search_window_start=0.1,
            search_window_end=1.5,
            correlation_length=1.0,
            plot=False,
            debug=True
        )
        
        print(f"\n📈 优化频段分割结果:")
        print(f"   频段数量: {len(step_boundaries)}")
        print(f"   频率点数: {len(freq_table)}")
        print(f"   开始时间: {alignment_info.get('start_offset', 0):.3f}s")
        print(f"   相关性: {alignment_info.get('correlation_score', 0):.3f}")
        
        if alignment_info.get('alignment_quality'):
            quality = alignment_info['alignment_quality']
            print(f"   整体质量: {quality.get('overall_quality', 'unknown')}")
            print(f"   综合评分: {quality.get('composite_score', 0):.3f}")
        
        print(f"\n✅ 优化的频段分割在竖线检测中应用成功！")
        
    except Exception as e:
        print(f"❌ 对比测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_updated_vertical_detection()
    test_optimization_comparison()
