# spk_test 项目 Python 依赖库
# Generated for Audio Analysis and Anomaly Detection Project

# ========== 核心科学计算库 ==========
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# ========== 音频处理库 ==========
librosa>=0.9.0
soundfile>=0.10.0

# ========== 机器学习库 ==========
scikit-learn>=1.0.0

# ========== 可视化库 ==========
matplotlib>=3.5.0
seaborn>=0.11.0

# ========== Jupyter 支持 (可选) ==========
# 如果需要运行 .ipynb 文件，取消注释以下行
# jupyter>=1.0.0
# ipython>=7.0.0
# notebook>=6.0.0

# ========== 其他工具库 ==========
tqdm>=4.62.0

# ========== 检测到的所有第三方依赖 ==========
# 基于项目代码自动检测的完整依赖列表:
# librosa, matplotlib, numpy, pandas, scipy, seaborn, sklearn, tqdm

# ========== 系统依赖说明 ==========
# 以下是项目中使用的所有第三方库的详细说明：

# numpy - 数值计算基础库
#   用于: 数组操作、数学运算、信号处理
#   文件: 所有 .py 文件

# pandas - 数据分析库  
#   用于: 数据框操作、CSV读写、特征分析
#   文件: step_chirp_test.py, feature_analysis.py, easy_detector.py

# scipy - 科学计算库
#   用于: 信号处理、统计分析、滤波器设计
#   模块: scipy.signal, scipy.stats, scipy.fft, scipy.ndimage
#   文件: 大部分分析脚本

# librosa - 音频分析库
#   用于: 音频加载、STFT、特征提取、频谱分析
#   文件: 所有音频处理相关文件

# soundfile - 音频文件I/O
#   用于: WAV文件读写
#   文件: chirp.py, square_analysis.py

# scikit-learn - 机器学习库
#   用于: 分类器、特征选择、模型评估
#   文件: feature_analysis.py, easy_detector.py, optimized_classifier.py

# matplotlib - 绘图库
#   用于: 频谱图、波形图、分析结果可视化
#   文件: 所有包含绘图的文件

# seaborn - 统计可视化库
#   用于: 相关性矩阵、特征分布图
#   文件: feature_analysis.py

# ========== 版本兼容性说明 ==========
# Python 版本要求: >= 3.7
# 推荐 Python 版本: 3.8 - 3.11

# ========== 安装说明 ==========
# 1. 基础安装:
#    pip install -r requirements.txt
#
# 2. 如果遇到安装问题，可以尝试:
#    pip install --upgrade pip
#    pip install -r requirements.txt --no-cache-dir
#
# 3. 对于 conda 用户:
#    conda install numpy pandas scipy matplotlib seaborn scikit-learn
#    pip install librosa soundfile tqdm

# ========== 可选依赖 ==========
# 以下库在某些功能中可能需要，但不是必需的:

# IPython - 交互式Python (用于 .ipynb 文件)
# ipython>=7.0.0

# Jupyter - Notebook支持
# jupyter>=1.0.0
# notebook>=6.0.0

# 音频编解码支持 (如果需要处理其他格式)
# ffmpeg-python>=0.2.0

# 更好的进度条显示
# tqdm>=4.62.0

# ========== 特殊说明 ==========
# 1. librosa 依赖 ffmpeg 处理某些音频格式
# 2. 在 Windows 上可能需要安装 Microsoft Visual C++ 构建工具
# 3. 某些功能需要中文字体支持 (SimHei, Microsoft YaHei)
# 4. 如果使用 GPU 加速，可能需要安装对应的 CUDA 版本
