#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试两个文件夹样本 - 峰值选择优化版本
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

def quick_test_samples():
    """快速测试样本"""
    print("🔍 快速测试峰值选择优化后的样本")
    print("="*60)
    
    # 定义测试文件夹
    folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美", 
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }
    
    results = []
    
    # 测试所有文件夹
    for category, folder_list in folders.items():
        for folder in folder_list:
            print(f"\n📁 测试文件夹: {folder} ({category})")
            print("-" * 50)
            
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            # 获取所有wav文件
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            print(f"找到 {len(wav_files)} 个wav文件")
            
            for i, audio_path in enumerate(wav_files[:2]):  # 只测试前2个文件，加快速度
                filename = os.path.basename(audio_path)
                folder_name = os.path.basename(folder)
                
                print(f"\n🎵 [{i+1}/2] 测试文件: {filename}")
                
                try:
                    # 调用优化的频率分割算法
                    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                        audio_path,
                        min_duration=153,
                        plot=False,  # 不绘图，加快测试速度
                        debug=False,  # 减少输出
                        search_window_start=0.1,
                        search_window_end=1.5,
                        correlation_length=1.0
                    )
                    
                    # 提取关键信息
                    start_offset = alignment_info.get('start_offset', 0)
                    correlation_score = alignment_info.get('correlation_score', 0)
                    alignment_quality = alignment_info.get('alignment_quality', {})
                    
                    # 计算质量指标
                    overall_quality = alignment_quality.get('overall_quality', 'unknown')
                    composite_score = alignment_quality.get('composite_score', 0)
                    time_correlation = alignment_quality.get('time_correlation', 0)
                    freq_similarity = alignment_quality.get('freq_similarity', 0)
                    
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': start_offset,
                        'correlation_score': correlation_score,
                        'overall_quality': overall_quality,
                        'composite_score': composite_score,
                        'time_correlation': time_correlation,
                        'freq_similarity': freq_similarity,
                        'step_count': len(step_bounds),
                        'freq_count': len(freq_table),
                        'status': 'success'
                    }
                    
                    print(f"  ✅ 成功: 开始时间={start_offset:.3f}s, 相关性={correlation_score:.3f}")
                    print(f"     质量={overall_quality}, 评分={composite_score:.3f}")
                    
                except Exception as e:
                    print(f"  ❌ 失败: {str(e)}")
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': 0,
                        'correlation_score': 0,
                        'overall_quality': 'failed',
                        'composite_score': 0,
                        'time_correlation': 0,
                        'freq_similarity': 0,
                        'step_count': 0,
                        'freq_count': 0,
                        'status': 'failed',
                        'error': str(e)
                    }
                
                results.append(result)
    
    # 生成统计报告
    print("\n" + "="*60)
    print("📊 峰值选择优化后的测试结果统计")
    print("="*60)
    
    if not results:
        print("❌ 没有测试结果")
        return
    
    df = pd.DataFrame(results)
    success_df = df[df['status'] == 'success']
    
    # 按类别分组统计
    for category in df['category'].unique():
        cat_data = df[df['category'] == category]
        success_data = cat_data[cat_data['status'] == 'success']
        success_count = len(success_data)
        total_count = len(cat_data)
        
        print(f"\n📁 {category.upper()} 类别:")
        print(f"  成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count > 0:
            print(f"  开始时间统计:")
            print(f"    平均值: {success_data['start_offset'].mean():.3f}s")
            print(f"    标准差: {success_data['start_offset'].std():.3f}s")
            print(f"    范围: {success_data['start_offset'].min():.3f}s - {success_data['start_offset'].max():.3f}s")
            
            print(f"  相关性统计:")
            print(f"    平均值: {success_data['correlation_score'].mean():.3f}")
            print(f"    标准差: {success_data['correlation_score'].std():.3f}")
            print(f"    范围: {success_data['correlation_score'].min():.3f} - {success_data['correlation_score'].max():.3f}")
            
            print(f"  质量分布:")
            quality_counts = success_data['overall_quality'].value_counts()
            for quality, count in quality_counts.items():
                print(f"    {quality}: {count}个 ({count/success_count*100:.1f}%)")
    
    # 详细结果表格
    print(f"\n📋 详细结果:")
    print("-" * 80)
    for _, row in df.iterrows():
        status_icon = "✅" if row['status'] == 'success' else "❌"
        print(f"{status_icon} {row['category']}/{row['folder']}/{row['filename']}")
        if row['status'] == 'success':
            print(f"    开始时间: {row['start_offset']:.3f}s, 相关性: {row['correlation_score']:.3f}")
            print(f"    质量: {row['overall_quality']}, 评分: {row['composite_score']:.3f}")
        else:
            print(f"    错误: {row.get('error', 'Unknown error')}")
    
    # 峰值选择效果分析
    print(f"\n🎯 峰值选择效果分析:")
    print("-" * 40)
    
    if len(success_df) > 0:
        high_corr_count = len(success_df[success_df['correlation_score'] >= 0.8])
        medium_corr_count = len(success_df[(success_df['correlation_score'] >= 0.6) & (success_df['correlation_score'] < 0.8)])
        low_corr_count = len(success_df[success_df['correlation_score'] < 0.6])
        
        print(f"  高相关性 (≥0.8): {high_corr_count}个 ({high_corr_count/len(success_df)*100:.1f}%)")
        print(f"  中等相关性 (0.6-0.8): {medium_corr_count}个 ({medium_corr_count/len(success_df)*100:.1f}%)")
        print(f"  低相关性 (<0.6): {low_corr_count}个 ({low_corr_count/len(success_df)*100:.1f}%)")
        
        # 分析开始时间的一致性
        start_times = success_df['start_offset'].values
        time_std = np.std(start_times)
        print(f"\n  开始时间一致性:")
        print(f"    标准差: {time_std:.3f}s")
        if time_std < 0.01:
            print(f"    ✅ 非常一致 (标准差 < 10ms)")
        elif time_std < 0.02:
            print(f"    ✅ 较为一致 (标准差 < 20ms)")
        else:
            print(f"    ⚠️ 存在变化 (标准差 ≥ 20ms)")
    
    # 保存结果
    output_file = "peak_selection_test_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    
    return df

if __name__ == "__main__":
    results_df = quick_test_samples()
