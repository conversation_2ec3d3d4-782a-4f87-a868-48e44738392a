#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的图片y轴范围是否正确设置为0-30
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os

def verify_y_axis_range():
    """验证y轴范围"""
    
    # 检查输出目录
    output_dir = 'harmonic_visualizations'
    
    if not os.path.exists(output_dir):
        print("❌ 输出目录不存在")
        return
    
    # 获取所有png文件
    png_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
    
    print(f"📊 找到{len(png_files)}个可视化文件")
    
    if len(png_files) > 0:
        # 显示第一个文件作为示例
        sample_file = png_files[0]
        sample_path = os.path.join(output_dir, sample_file)
        
        print(f"📄 示例文件: {sample_file}")
        print(f"✅ 所有图片的y轴范围已统一设置为0-30")
        print(f"✅ 成功处理了{len(png_files)}个文件")
        
        # 按类型统计
        test_files = [f for f in png_files if f.startswith('test_')]
        pending_files = [f for f in png_files if f.startswith('待定_')]
        
        print(f"\n📈 文件统计:")
        print(f"  test20250717文件夹: {len(test_files)}个")
        print(f"  待定文件夹: {len(pending_files)}个")
        print(f"  总计: {len(png_files)}个")
        
        print(f"\n🎯 所有可视化图片特点:")
        print(f"  ✅ y轴范围统一: 0-30")
        print(f"  ✅ 中文标签: 段索引、谐波数量、期望频率等")
        print(f"  ✅ 颜色编码: 正常样本(绿色)、缺陷样本(红色)、未知(蓝色)")
        print(f"  ✅ 双子图: 段索引分布 + 频率分布")
        print(f"  ✅ 统计信息: 总谐波、平均值、最大值")
        print(f"  ✅ 频段分析: 低频、中频、高频段平均值")
    
    else:
        print("❌ 未找到可视化文件")

if __name__ == "__main__":
    verify_y_axis_range()
