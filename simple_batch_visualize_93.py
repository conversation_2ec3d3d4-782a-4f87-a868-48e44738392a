#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版：批量可视化test20250717所有文件的93段谐波数量
使用多进程加速，图中文字使用中文
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import multiprocessing as mp
from multiprocessing import Pool
import time

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib中文支持
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def batch_visualize_all_files():
    """批量可视化所有文件"""
    print("🎯 批量可视化test20250717和待定文件夹所有文件的93段谐波数量")
    print("="*60)
    
    # 要处理的目录列表
    target_dirs = ['test20250717', '待定']
    
    # 创建输出目录
    output_dir = 'harmonic_visualizations'
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    # 查找所有wav文件
    wav_files = []
    for target_dir in target_dirs:
        if os.path.exists(target_dir):
            print(f"📂 扫描目录: {target_dir}")
            for root, dirs, files in os.walk(target_dir):
                for file in files:
                    if file.lower().endswith('.wav'):
                        wav_files.append(os.path.join(root, file))
        else:
            print(f"⚠️ 目录不存在: {target_dir}")

    if len(wav_files) == 0:
        print("❌ 未找到任何wav文件")
        return
    
    print(f"📊 总共找到{len(wav_files)}个wav文件")
    
    # 准备多进程参数
    process_args = []
    for wav_file in wav_files:
        output_filename = create_output_filename(wav_file, output_dir)
        process_args.append((wav_file, output_filename))
    
    # 使用多进程处理
    start_time = time.time()
    
    # 获取CPU核心数
    num_processes = min(mp.cpu_count(), len(wav_files))
    print(f"🚀 使用{num_processes}个进程并行处理...")
    
    with Pool(processes=num_processes) as pool:
        results = pool.map(process_single_file, process_args)
    
    # 统计结果
    successful = sum(1 for r in results if r)
    failed = len(results) - successful
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n✅ 处理完成!")
    print(f"  成功: {successful}个文件")
    print(f"  失败: {failed}个文件")
    print(f"  总耗时: {processing_time:.1f}秒")
    print(f"  平均每文件: {processing_time/len(wav_files):.1f}秒")
    print(f"  输出目录: {output_dir}")

def create_output_filename(wav_file, output_dir):
    """创建输出文件名"""
    # 确定基础目录
    if 'test20250717' in wav_file:
        base_dir = 'test20250717'
    elif '待定' in wav_file:
        base_dir = '待定'
    else:
        base_dir = os.path.dirname(wav_file)

    # 获取相对路径并转换为安全的文件名
    try:
        rel_path = os.path.relpath(wav_file, base_dir)
    except:
        rel_path = os.path.basename(wav_file)

    safe_name = rel_path.replace('\\', '_').replace('/', '_').replace('.wav', '')

    # 移除特殊字符
    safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_-')

    # 添加目录前缀以区分来源
    if base_dir == '待定':
        safe_name = f"待定_{safe_name}"
    elif base_dir == 'test20250717':
        safe_name = f"test_{safe_name}"

    output_filename = os.path.join(output_dir, f"{safe_name}_93段谐波.png")
    return output_filename

def process_single_file(args):
    """处理单个文件 - 多进程工作函数"""
    wav_file, output_filename = args
    
    try:
        print(f"  处理: {os.path.basename(wav_file)}")
        
        # 分析文件
        segments_data = analyze_file_93_segments(wav_file)
        
        if segments_data:
            # 创建可视化
            create_single_file_visualization(segments_data, output_filename)
            return True
        else:
            print(f"❌ 分析失败: {os.path.basename(wav_file)}")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败: {os.path.basename(wav_file)} - {e}")
        return False

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def analyze_file_93_segments(audio_path):
    """分析单个文件的93段"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                segments_data.append({
                    'seg_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'harmonic_count': 0,
                    'noise_variation': 0
                })
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072  # 128k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 谐波检测
            if noise_analysis:
                harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
                harmonic_count = len(harmonic_analysis)
                noise_variation = noise_analysis['noise_variation_db']
            else:
                harmonic_count = 0
                noise_variation = 0
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'harmonic_count': harmonic_count,
                'noise_variation': noise_variation
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'filepath': audio_path,
            'segments': segments_data,
            'total_harmonics': sum([s['harmonic_count'] for s in segments_data]),
            'avg_harmonics': np.mean([s['harmonic_count'] for s in segments_data])
        }
        
    except Exception as e:
        return None

def create_single_file_visualization(segments_data, output_filename):
    """为单个文件创建93段可视化"""
    
    # 提取数据
    segment_indices = [s['seg_idx'] for s in segments_data['segments']]
    expected_freqs = [s['expected_freq'] for s in segments_data['segments']]
    harmonic_counts = [s['harmonic_count'] for s in segments_data['segments']]
    noise_variations = [s['noise_variation'] for s in segments_data['segments']]
    
    # 创建图表 (2行1列)
    fig, axes = plt.subplots(2, 1, figsize=(16, 12))
    
    # 确定文件类型和颜色
    filepath = segments_data['filepath']
    if 'pos' in filepath.lower() or 'good' in filepath.lower() or 'ok' in filepath.lower():
        file_type = '正常样本'
        color = 'green'
    elif 'neg' in filepath.lower() or 'bad' in filepath.lower() or 'defect' in filepath.lower():
        file_type = '缺陷样本'
        color = 'red'
    else:
        file_type = '未知类型'
        color = 'blue'
    
    fig.suptitle(f'93段谐波分析\n文件: {segments_data["filename"]} ({file_type})', 
                 fontsize=14, fontweight='bold')
    
    # 1. 段索引 vs 谐波数量
    ax1 = axes[0]
    bars1 = ax1.bar(segment_indices, harmonic_counts, color=color, alpha=0.7, edgecolor='black', linewidth=0.5)
    ax1.set_xlabel('段索引')
    ax1.set_ylabel('谐波数量')
    ax1.set_title('各段谐波数量分布')
    ax1.set_ylim(0, 30)  # 统一y轴范围0-30
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 设置x轴刻度
    ax1.set_xticks(range(0, len(segment_indices), 10))
    ax1.set_xticklabels([f'第{i}段' for i in range(0, len(segment_indices), 10)])
    
    # 添加统计信息
    total_harmonics = segments_data['total_harmonics']
    avg_harmonics = segments_data['avg_harmonics']
    max_harmonics = max(harmonic_counts)
    
    ax1.text(0.02, 0.98, f'总谐波: {total_harmonics}个\n平均: {avg_harmonics:.1f}个/段\n最大: {max_harmonics}个', 
             transform=ax1.transAxes, verticalalignment='top', fontsize=10,
             bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # 2. 频率 vs 谐波数量
    ax2 = axes[1]
    scatter = ax2.scatter(expected_freqs, harmonic_counts, c=noise_variations,
                         cmap='viridis', s=30, alpha=0.7, edgecolors='black', linewidth=0.5)
    ax2.set_xlabel('期望频率 (Hz)')
    ax2.set_ylabel('谐波数量')
    ax2.set_title('谐波数量随频率变化 (颜色表示噪声变化)')
    ax2.set_ylim(0, 30)  # 统一y轴范围0-30
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax2)
    cbar.set_label('噪声变化 (dB)')
    
    # 频率段分析
    low_freq_segments = [s for s in segments_data['segments'] if s['expected_freq'] <= 1000]
    mid_freq_segments = [s for s in segments_data['segments'] if 1000 < s['expected_freq'] <= 5000]
    high_freq_segments = [s for s in segments_data['segments'] if s['expected_freq'] > 5000]
    
    low_avg = np.mean([s['harmonic_count'] for s in low_freq_segments]) if low_freq_segments else 0
    mid_avg = np.mean([s['harmonic_count'] for s in mid_freq_segments]) if mid_freq_segments else 0
    high_avg = np.mean([s['harmonic_count'] for s in high_freq_segments]) if high_freq_segments else 0
    
    info_text = f"频段分析:\n"
    info_text += f"低频(≤1kHz): {low_avg:.1f}个/段\n"
    info_text += f"中频(1-5kHz): {mid_avg:.1f}个/段\n"
    info_text += f"高频(>5kHz): {high_avg:.1f}个/段\n"
    info_text += f"文件类型: {file_type}"
    
    ax2.text(0.02, 0.98, info_text, transform=ax2.transAxes, 
             verticalalignment='top', fontsize=10,
             bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    batch_visualize_all_files()
