#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THD+N分析第一步：可视化主频置零后的93段频谱
参考universal_spectrum_analyzer.py的主频、谐波检测以及频谱可视化方法
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
import time

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from freq_split_optimized import split_freq_steps_optimized

# 尝试导入matplotlib
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("matplotlib不可用，将保存数据而不生成图像")
    MATPLOTLIB_AVAILABLE = False

def hz_to_mel(hz):
    """
    将频率从Hz转换为梅尔刻度
    """
    return 2595 * np.log10(1 + hz / 700)

def mel_to_hz(mel):
    """
    将梅尔刻度转换为Hz
    """
    return 700 * (10**(mel / 2595) - 1)

def calculate_zero_bandwidth(fundamental_freq):
    """
    根据主频计算合适的置零带宽
    低频用较大带宽，高频用较小带宽

    Args:
        fundamental_freq: 主频频率 (Hz)

    Returns:
        bandwidth: 置零带宽 (Hz)
    """
    if fundamental_freq < 200:
        bandwidth = 10.0      # 低频用较大带宽
    elif fundamental_freq < 1000:
        bandwidth = 5.0       # 中频用中等带宽
    else:
        bandwidth = 2.0       # 高频用较小带宽

    return bandwidth

def zero_fundamental_and_harmonics(freqs, power_spectrum, fundamental_freq):
    """
    将主频及其谐波置零，使用频率自适应带宽

    Args:
        freqs: 频率数组
        power_spectrum: 功率谱
        fundamental_freq: 主频频率

    Returns:
        zeroed_spectrum: 主频和谐波置零后的功率谱
        zero_mask: 置零区域的掩码
        bandwidth_info: 带宽信息字典
    """
    zeroed_spectrum = power_spectrum.copy()
    zero_mask = np.zeros(len(freqs), dtype=bool)

    # 计算主频带宽
    main_bandwidth = calculate_zero_bandwidth(fundamental_freq)

    # 主频置零
    main_mask = np.abs(freqs - fundamental_freq) <= main_bandwidth / 2
    zero_mask |= main_mask

    # 谐波置零 (2-10次谐波)
    harmonic_bandwidths = []
    for harmonic in range(2, 11):
        harmonic_freq = fundamental_freq * harmonic
        if harmonic_freq <= np.max(freqs):  # 在频率范围内
            # 每个谐波使用自己的带宽
            harmonic_bandwidth = calculate_zero_bandwidth(harmonic_freq)
            harmonic_bandwidths.append((harmonic, harmonic_freq, harmonic_bandwidth))

            harmonic_mask = np.abs(freqs - harmonic_freq) <= harmonic_bandwidth / 2
            zero_mask |= harmonic_mask

    # 将置零区域设为0
    zeroed_spectrum[zero_mask] = 0

    # 带宽信息
    bandwidth_info = {
        'main_freq': fundamental_freq,
        'main_bandwidth': main_bandwidth,
        'harmonic_bandwidths': harmonic_bandwidths
    }

    return zeroed_spectrum, zero_mask, bandwidth_info

def analyze_segment_fundamental_zeroed(args):
    """
    分析单个频段，主频和谐波置零后的频谱
    参考universal_spectrum_analyzer.py的方法
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, output_dir, use_mel_scale, start_freq, stop_freq = args
    
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频 - 参考universal_spectrum_analyzer.py
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = audio_data[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"    警告: 段 {seg_idx} 音频长度为0")
            return False
        
        # 去除开头和结尾8% - 与universal_spectrum_analyzer.py保持一致
        trim_length = int(len(segment_audio) * 0.08)
        if len(segment_audio) > 2 * trim_length:
            segment_audio = segment_audio[trim_length:-trim_length]
            actual_start_time = start_time + trim_length / sr
            actual_end_time = end_time - trim_length / sr
        else:
            actual_start_time = start_time
            actual_end_time = end_time
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与universal_spectrum_analyzer.py完全一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到24kHz
        freq_mask = positive_freqs <= 24000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频 - 与universal_spectrum_analyzer.py一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 主频和谐波置零（使用频率自适应带宽）
        zeroed_spectrum, zero_mask, bandwidth_info = zero_fundamental_and_harmonics(
            display_freqs, display_power, fundamental_freq
        )
        zeroed_db = 10 * np.log10(zeroed_spectrum + 1e-12)
        
        # 计算THD+N相关指标
        # 主频功率
        fundamental_power = display_power[np.argmin(np.abs(display_freqs - fundamental_freq))]
        
        # 谐波功率总和
        harmonic_power_sum = 0
        harmonic_freqs = []
        harmonic_powers = []
        for harmonic in range(2, 11):
            harmonic_freq = fundamental_freq * harmonic
            if harmonic_freq <= np.max(display_freqs):
                harmonic_idx = np.argmin(np.abs(display_freqs - harmonic_freq))
                harmonic_power = display_power[harmonic_idx]
                harmonic_power_sum += harmonic_power
                harmonic_freqs.append(harmonic_freq)
                harmonic_powers.append(harmonic_power)
        
        # 噪声功率（主频和谐波置零后的剩余功率）
        noise_power = np.sum(zeroed_spectrum)
        
        # THD+N计算
        thd_n_power = harmonic_power_sum + noise_power
        thd_n_ratio = thd_n_power / fundamental_power if fundamental_power > 0 else 0
        thd_n_db = 10 * np.log10(thd_n_ratio + 1e-12)
        thd_n_percent = np.sqrt(thd_n_ratio) * 100
        
        # 可视化（如果matplotlib可用）
        if MATPLOTLIB_AVAILABLE:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

            # 设置频率显示范围 - 参考universal_spectrum_analyzer.py
            min_freq_display = max(start_freq, np.min(display_freqs[display_freqs > 0]))
            max_freq_display = min(stop_freq, np.max(display_freqs))

            if use_mel_scale:
                # 梅尔刻度绘图 - 完全参考universal_spectrum_analyzer.py
                mel_freqs = hz_to_mel(display_freqs)

                # 上图：原始频谱
                ax1.plot(mel_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='原始频谱')

                # 标记主频和置零区域
                fundamental_idx = np.argmin(np.abs(display_freqs - fundamental_freq))
                fundamental_mel = hz_to_mel(fundamental_freq)
                main_bandwidth = bandwidth_info['main_bandwidth']
                ax1.plot(fundamental_mel, power_db[fundamental_idx], 'o', color='blue', markersize=8,
                         markeredgecolor='darkblue', markeredgewidth=1.5,
                         label=f'主频 {fundamental_freq:.1f}Hz (±{main_bandwidth/2:.1f}Hz)')

                # 显示主频置零区域
                main_freq_low = fundamental_freq - main_bandwidth/2
                main_freq_high = fundamental_freq + main_bandwidth/2
                ax1.axvspan(hz_to_mel(main_freq_low), hz_to_mel(main_freq_high),
                           alpha=0.2, color='blue', label='主频置零区域')

                # 标记谐波和置零区域
                for i, (hf, hp) in enumerate(zip(harmonic_freqs, harmonic_powers)):
                    hp_db = 10 * np.log10(hp + 1e-12)
                    harmonic_mel = hz_to_mel(hf)

                    # 找到对应的谐波带宽
                    harmonic_bandwidth = 2.0  # 默认值
                    for harm_order, harm_freq, harm_bw in bandwidth_info['harmonic_bandwidths']:
                        if abs(harm_freq - hf) < 1.0:  # 匹配谐波
                            harmonic_bandwidth = harm_bw
                            break

                    ax1.plot(harmonic_mel, hp_db, 'go', markersize=6, alpha=0.7,
                             label=f'{i+2}次谐波 (±{harmonic_bandwidth/2:.1f}Hz)' if i == 0 else "")

                    # 显示谐波置零区域
                    if i < 3:  # 只显示前3个谐波的置零区域，避免图像过于复杂
                        harm_freq_low = hf - harmonic_bandwidth/2
                        harm_freq_high = hf + harmonic_bandwidth/2
                        ax1.axvspan(hz_to_mel(harm_freq_low), hz_to_mel(harm_freq_high),
                                   alpha=0.15, color='green')

                # 设置梅尔刻度范围和刻度标签
                min_mel = hz_to_mel(min_freq_display)
                max_mel = hz_to_mel(max_freq_display)
                ax1.set_xlim(min_mel, max_mel)

                mel_ticks = np.linspace(min_mel, max_mel, 10)
                hz_ticks = mel_to_hz(mel_ticks)
                ax1.set_xticks(mel_ticks)
                ax1.set_xticklabels([f'{hz:.0f}' for hz in hz_ticks])

                ax1.set_xlabel('频率 (Hz) - 梅尔刻度', fontsize=14, fontweight='bold')

                # 下图：主频和谐波置零后的频谱
                ax2.plot(mel_freqs, zeroed_db, color='red', linewidth=0.8, alpha=0.8,
                         label='主频+谐波置零后频谱 (噪声+失真)')

                ax2.set_xlim(min_mel, max_mel)
                ax2.set_xticks(mel_ticks)
                ax2.set_xticklabels([f'{hz:.0f}' for hz in hz_ticks])
                ax2.set_xlabel('频率 (Hz) - 梅尔刻度', fontsize=14, fontweight='bold')

            else:
                # 对数刻度绘图 - 完全参考universal_spectrum_analyzer.py
                # 上图：原始频谱
                ax1.semilogx(display_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='原始频谱')

                # 标记主频和置零区域
                fundamental_idx = np.argmin(np.abs(display_freqs - fundamental_freq))
                main_bandwidth = bandwidth_info['main_bandwidth']
                ax1.plot(fundamental_freq, power_db[fundamental_idx], 'o', color='blue', markersize=8,
                         markeredgecolor='darkblue', markeredgewidth=1.5,
                         label=f'主频 {fundamental_freq:.1f}Hz (±{main_bandwidth/2:.1f}Hz)')

                # 显示主频置零区域
                main_freq_low = fundamental_freq - main_bandwidth/2
                main_freq_high = fundamental_freq + main_bandwidth/2
                ax1.axvspan(main_freq_low, main_freq_high,
                           alpha=0.2, color='blue', label='主频置零区域')

                # 标记谐波和置零区域
                for i, (hf, hp) in enumerate(zip(harmonic_freqs, harmonic_powers)):
                    hp_db = 10 * np.log10(hp + 1e-12)

                    # 找到对应的谐波带宽
                    harmonic_bandwidth = 2.0  # 默认值
                    for harm_order, harm_freq, harm_bw in bandwidth_info['harmonic_bandwidths']:
                        if abs(harm_freq - hf) < 1.0:  # 匹配谐波
                            harmonic_bandwidth = harm_bw
                            break

                    ax1.plot(hf, hp_db, 'go', markersize=6, alpha=0.7,
                             label=f'{i+2}次谐波 (±{harmonic_bandwidth/2:.1f}Hz)' if i == 0 else "")

                    # 显示谐波置零区域
                    if i < 3:  # 只显示前3个谐波的置零区域
                        harm_freq_low = hf - harmonic_bandwidth/2
                        harm_freq_high = hf + harmonic_bandwidth/2
                        ax1.axvspan(harm_freq_low, harm_freq_high,
                                   alpha=0.15, color='green')

                ax1.set_xlim(min_freq_display, max_freq_display)
                ax1.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')

                # 下图：主频和谐波置零后的频谱
                ax2.semilogx(display_freqs, zeroed_db, color='red', linewidth=0.8, alpha=0.8,
                             label='主频+谐波置零后频谱 (噪声+失真)')

                ax2.set_xlim(min_freq_display, max_freq_display)
                ax2.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')

            # 共同设置 - 与universal_spectrum_analyzer.py完全一致
            ax1.set_ylim(-80, 40)
            ax1.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            ax1.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz - 原始频谱', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
            ax1.legend(fontsize=11, loc='upper right', framealpha=0.9)

            ax2.set_ylim(-80, 40)
            ax2.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            ax2.set_title(f'频段 {seg_idx:02d}: THD+N={thd_n_percent:.3f}% ({thd_n_db:.1f}dB) - 主频带宽±{bandwidth_info["main_bandwidth"]/2:.1f}Hz',
                          fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
            ax2.legend(fontsize=11, loc='upper right', framealpha=0.9)
            
            plt.tight_layout()
            
            # 保存图像
            output_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_THD_N_step1.png')
            plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"    保存图像: {output_filename}")
        
        # 保存数据
        data_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_THD_N_data.npz')
        np.savez(data_filename,
                 freqs=display_freqs,
                 original_spectrum=display_power,
                 zeroed_spectrum=zeroed_spectrum,
                 zero_mask=zero_mask,
                 fundamental_freq=fundamental_freq,
                 harmonic_freqs=harmonic_freqs,
                 harmonic_powers=harmonic_powers,
                 thd_n_ratio=thd_n_ratio,
                 thd_n_db=thd_n_db,
                 thd_n_percent=thd_n_percent,
                 main_bandwidth=bandwidth_info['main_bandwidth'],
                 harmonic_bandwidths=bandwidth_info['harmonic_bandwidths'])
        
        print(f"    THD+N: {thd_n_percent:.3f}% ({thd_n_db:.1f}dB)")
        
        return True, {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'thd_n_percent': thd_n_percent,
            'thd_n_db': thd_n_db,
            'harmonic_count': len(harmonic_freqs)
        }
        
    except Exception as e:
        print(f"    错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """
    主函数：THD+N分析第一步 - 主频置零频谱可视化
    """
    # 音频文件路径和参数设置
    audio_path = "test20250722/鼓膜破裂（复测1.1).wav"
    use_mel_scale = True  # 使用梅尔刻度，与universal_spectrum_analyzer.py保持一致
    start_freq = 100     # 起始频率
    stop_freq = 20000    # 结束频率

    scale_type = "梅尔刻度" if use_mel_scale else "对数刻度"
    print(f"🎯 THD+N分析第一步：主频置零频谱分析")
    print(f"📁 音频文件: {audio_path}")
    print(f"🔧 分析参数: {start_freq}Hz - {stop_freq}Hz, {scale_type}")
    print("="*70)
    
    # 检查文件是否存在
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return
    
    # 创建输出目录
    audio_name = Path(audio_path).stem
    scale_suffix = "_梅尔刻度" if use_mel_scale else "_对数刻度"
    output_dir = f"{audio_name}_THD_N_第一步分析{scale_suffix}"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 获取频段分割 - 参考universal_spectrum_analyzer.py的参数
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        print(f"✅ 频段分割完成，共{len(step_boundaries)}段")
        
        # 加载音频
        print("🎵 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000
        
        print(f"✅ 音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")
        
        # 准备多进程任务参数
        print(f"🚀 准备多进程分析...")
        tasks = []
        
        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = freq_table[i]
            tasks.append((
                i+1,  # seg_idx
                start_time,
                end_time,
                expected_freq,
                y,  # 共享音频数据
                sr,
                output_dir,
                use_mel_scale,
                start_freq,
                stop_freq
            ))
        
        # 多进程并行处理
        num_workers = 4
        print(f"📊 使用{num_workers}个进程并行分析{len(step_boundaries)}个频段...")
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            results = list(executor.map(analyze_segment_fundamental_zeroed, tasks))
        
        # 统计结果
        successful_count = 0
        thd_n_results = []
        
        for result in results:
            if isinstance(result, tuple):
                success, data = result
                if success and data:
                    successful_count += 1
                    thd_n_results.append(data)
            else:
                if result:
                    successful_count += 1
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n" + "="*70)
        print(f"✅ THD+N第一步分析完成!")
        print(f"  成功分析: {successful_count}/{len(step_boundaries)} 个频段")
        print(f"  总耗时: {processing_time:.1f}秒")
        print(f"  输出目录: {output_dir}")
        
        # 保存THD+N汇总结果
        if thd_n_results:
            summary_file = os.path.join(output_dir, "THD_N_第一步汇总.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"THD+N分析第一步汇总报告\n")
                f.write(f"="*50 + "\n\n")
                f.write(f"音频文件: {audio_path}\n")
                f.write(f"分析方法: 主频和谐波置零后计算THD+N\n")
                f.write(f"成功分析: {successful_count}/{len(step_boundaries)} 个频段\n\n")
                
                f.write(f"THD+N结果:\n")
                f.write(f"{'序号':<4} {'频率(Hz)':<10} {'实际主频(Hz)':<12} {'THD+N(%)':<10} {'THD+N(dB)':<10} {'谐波数':<6}\n")
                f.write(f"-" * 70 + "\n")
                
                for result in thd_n_results:
                    f.write(f"{result['segment_idx']:<4} {result['expected_freq']:<10.1f} "
                           f"{result['fundamental_freq']:<12.1f} {result['thd_n_percent']:<10.3f} "
                           f"{result['thd_n_db']:<10.1f} {result['harmonic_count']:<6}\n")
                
                # 统计信息
                thd_n_values = [r['thd_n_percent'] for r in thd_n_results]
                f.write(f"\n统计信息:\n")
                f.write(f"  THD+N最小值: {min(thd_n_values):.3f}%\n")
                f.write(f"  THD+N最大值: {max(thd_n_values):.3f}%\n")
                f.write(f"  THD+N平均值: {np.mean(thd_n_values):.3f}%\n")
                f.write(f"  THD+N标准差: {np.std(thd_n_values):.3f}%\n")
            
            print(f"📄 THD+N汇总已保存: {summary_file}")
            print(f"💡 下一步: 基于这些数据进行更详细的THD+N分析")
        
    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
