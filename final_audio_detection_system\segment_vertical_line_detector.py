#!/usr/bin/env python3
"""
频段竖线检测器
Segment Vertical Line Detector
基于正确的freq_split分割，对各个频段进行竖线检测并可视化
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft, find_peaks
from scipy.ndimage import gaussian_filter1d
from mpl_toolkits.axes_grid1 import make_axes_locatable
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized")
    def split_freq_steps_optimized(audio_path, **kwargs):
        return [], [], {}

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SegmentVerticalLineDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        # 优化后的竖线检测参数 - 基于真实样本数据分析
        self.detection_params = {
            'edge_exclude_time': 0.012,             # 开头结尾去除12ms

            # 优化后的核心检测参数 - 降低误报率
            'min_peak_prominence_std_ratio': 0.5,   # 提高突出度要求 (0.2→0.5)
            'min_peak_height_std_ratio': 0.8,       # 提高峰值高度要求 (0.3→0.8)
            'min_line_strength': 0.2,               # 提高最小竖线强度 (0.1→0.2)
            'peak_distance': 2,                     # 增加峰值间最小距离 (1→2)
            'min_energy_change': 30,                # 提高最小能量变化阈值 (10→30)

            # 优化后的标准差预筛选 - 更严格的筛选
            'std_prescreening': {
                'enable': True,
                'min_std_value': 3000,              # 提高阈值 (2000→3000)
            }
        }
        
        print(f"基于突出度的竖线检测器初始化完成")
        print(f"检测策略: 正确freq_split分割 → 统一突出度检测 → 各频段竖线检测 → 边界处理")

    def _self_check_analysis(self, all_segment_results, freq_table):
        """自检分析 - 详细分析关键频段数据特征"""
        print(f"\n🔍 详细数据分析:")
        print("="*60)

        # 分析关键频段的实际数据
        key_segments = [0, 1, 2, 3, 32, 33, 34]  # 已知的关键频段

        print(f"📊 关键频段能量统计分析:")
        print(f"{'段号':<4} {'频率':<8} {'均值':<8} {'标准差':<8} {'CV':<8} {'范围':<8} {'Q90':<8} {'Q95':<8}")
        print("-" * 60)

        segment_data = []
        for seg_idx in key_segments:
            if seg_idx < len(all_segment_results):
                seg = all_segment_results[seg_idx]
                expected_freq = freq_table[seg_idx] if seg_idx < len(freq_table) else 0
                stats = seg['segment_stats']

                data = {
                    'segment': seg_idx,
                    'frequency': expected_freq,
                    'mean': stats['energy_mean'],
                    'std': stats['energy_std'],
                    'cv': stats['energy_cv'],
                    'range': stats['energy_range'],
                    'q90': stats['energy_q90'],
                    'q95': stats['energy_q95'],
                    'has_lines': len(seg['vertical_lines']) > 0
                }
                segment_data.append(data)

                print(f"{seg_idx:<4} {expected_freq:<8.0f} {stats['energy_mean']:<8.1f} "
                      f"{stats['energy_std']:<8.1f} {stats['energy_cv']:<8.3f} "
                      f"{stats['energy_range']:<8.1f} {stats['energy_q90']:<8.1f} {stats['energy_q95']:<8.1f}")

        # 分析正常段和异常段的特征差异
        normal_segments = [d for d in segment_data if not d['has_lines']]
        anomalous_segments = [d for d in segment_data if d['has_lines']]

        print(f"\n📈 正常段统计特征:")
        if normal_segments:
            normal_stds = [d['std'] for d in normal_segments]
            normal_cvs = [d['cv'] for d in normal_segments]
            normal_ranges = [d['range'] for d in normal_segments]

            print(f"  标准差: {np.mean(normal_stds):.1f} ± {np.std(normal_stds):.1f} (范围: {min(normal_stds):.1f}-{max(normal_stds):.1f})")
            print(f"  变异系数: {np.mean(normal_cvs):.3f} ± {np.std(normal_cvs):.3f} (范围: {min(normal_cvs):.3f}-{max(normal_cvs):.3f})")
            print(f"  能量范围: {np.mean(normal_ranges):.1f} ± {np.std(normal_ranges):.1f}")

        print(f"\n📈 异常段统计特征:")
        if anomalous_segments:
            anom_stds = [d['std'] for d in anomalous_segments]
            anom_cvs = [d['cv'] for d in anomalous_segments]
            anom_ranges = [d['range'] for d in anomalous_segments]

            print(f"  标准差: {np.mean(anom_stds):.1f} ± {np.std(anom_stds):.1f} (范围: {min(anom_stds):.1f}-{max(anom_stds):.1f})")
            print(f"  变异系数: {np.mean(anom_cvs):.3f} ± {np.std(anom_cvs):.3f} (范围: {min(anom_cvs):.3f}-{max(anom_cvs):.3f})")
            print(f"  能量范围: {np.mean(anom_ranges):.1f} ± {np.std(anom_ranges):.1f}")
        else:
            print(f"  无异常段数据")

        # 基于实际数据给出阈值建议
        print(f"\n💡 基于实际数据的阈值建议:")
        if normal_segments:
            max_normal_std = max([d['std'] for d in normal_segments])
            max_normal_cv = max([d['cv'] for d in normal_segments])
            max_normal_range = max([d['range'] for d in normal_segments])

            print(f"  当前参数:")
            print(f"    - 峰值高度阈值: 均值 + {self.detection_params['min_peak_height_std_ratio']}σ")
            print(f"    - 突出度阈值: {self.detection_params['min_peak_prominence_std_ratio']}σ")
            print(f"    - 标准差预筛选: 最小{self.detection_params['std_prescreening']['min_std_value']}")

            print(f"  建议调整:")
            print(f"    - 标准差预筛选阈值: >{max_normal_std*1.2:.1f} (正常段最大值的1.2倍)")
            print(f"    - CV预筛选阈值: >{max_normal_cv*1.2:.3f} (正常段最大值的1.2倍)")
            print(f"    - 峰值高度阈值: 均值 + {max_normal_std/np.mean([d['std'] for d in normal_segments])*0.8:.1f}σ")
            print(f"    - 突出度阈值: {max_normal_std/np.mean([d['std'] for d in normal_segments])*0.6:.1f}σ")

        # 分析前几个低频段和中高频段
        low_freq_segments = [seg for seg in all_segment_results[:10] if seg['analysis_success']]
        low_freq_anomalies = [seg for seg in low_freq_segments if seg['vertical_lines']]

        mid_high_segments = [seg for seg in all_segment_results[30:40] if seg['analysis_success']]
        mid_high_anomalies = [seg for seg in mid_high_segments if seg['vertical_lines']]

        print(f"\n📊 频段分布分析:")
        print(f"  低频段 (0-9): {len(low_freq_anomalies)}/10 异常")
        print(f"  中高频段 (30-39): {len(mid_high_anomalies)}/10 异常")
    
    def detect_vertical_lines_in_segments(self, audio_path, max_display_segments=24):
        """对各个频段进行竖线检测"""
        print(f"\n频段竖线检测: {os.path.basename(audio_path)}")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )

            # 频率范围筛选 - 使用固定范围
            freq_mask = (frequencies >= 100) & (frequencies <= 20000)
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 计算功率谱
            power_spectrum = np.abs(Zxx) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            print(f"频谱数据: {power_db.shape[0]}个频率点 × {power_db.shape[1]}个时间片")
            
            # 使用优化的频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path,
                start_freq=100,
                stop_freq=20000,
                octave=12,
                min_cycles=10,
                min_duration=153,
                fs=self.sample_rate,
                search_window_start=0.1,
                search_window_end=1.5,
                correlation_length=1.0,
                plot=False,
                debug=False
            )
            
            if len(step_boundaries) == 0:
                print("优化频段分割失败")
                return None

            print(f"优化频段分割: {len(step_boundaries)}个频段")
            if alignment_info:
                print(f"对齐信息: 开始时间={alignment_info.get('start_offset', 0):.3f}s, "
                      f"相关性={alignment_info.get('correlation_score', 0):.3f}")
            
            # 对每个频段进行竖线检测
            all_segment_results = []
            total_vertical_lines = 0
            
            for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
                expected_freq = freq_table[seg_idx] if seg_idx < len(freq_table) else None
                
                segment_result = self._detect_lines_in_single_segment(
                    power_db, frequencies, times, seg_start_time, seg_end_time, 
                    seg_idx, expected_freq
                )
                
                all_segment_results.append(segment_result)
                
                if segment_result['analysis_success'] and segment_result['vertical_lines']:
                    total_vertical_lines += len(segment_result['vertical_lines'])
            
            # 统计结果
            successful_segments = sum(1 for seg in all_segment_results if seg['analysis_success'])
            anomalous_segments = sum(1 for seg in all_segment_results
                                   if seg['analysis_success'] and seg['vertical_lines'])

            print(f"\n检测结果统计:")
            print(f"  总频段数: {len(step_boundaries)}")
            print(f"  成功分析: {successful_segments}")
            print(f"  异常频段: {anomalous_segments}")
            print(f"  总竖线数: {total_vertical_lines}")
            print(f"  异常比例: {anomalous_segments/len(step_boundaries)*100:.1f}%")

            # 自检分析
            self._self_check_analysis(all_segment_results, freq_table)
            
            # 可视化结果 - 只有当max_display_segments不为None时才可视化
            if max_display_segments is not None:
                self._visualize_segment_detection_results(
                    power_db, frequencies, times, step_boundaries, freq_table,
                    all_segment_results, audio_path, max_display_segments
                )
            
            return {
                'power_db': power_db,
                'frequencies': frequencies,
                'times': times,
                'step_boundaries': step_boundaries,
                'freq_table': freq_table,
                'segment_results': all_segment_results,
                'total_segments': len(step_boundaries),
                'successful_segments': successful_segments,
                'anomalous_segments': anomalous_segments,
                'total_vertical_lines': total_vertical_lines
            }
            
        except Exception as e:
            print(f"检测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _detect_lines_in_single_segment(self, power_db, frequencies, times, seg_start_time, seg_end_time, seg_idx, expected_freq):
        """在单个频段中检测竖线"""
        result = {
            'segment_index': seg_idx,
            'start_time': seg_start_time,
            'end_time': seg_end_time,
            'expected_frequency': expected_freq,
            'duration': seg_end_time - seg_start_time,
            'analysis_success': False,
            'vertical_lines': [],
            'segment_stats': {},
            'error': None
        }
        
        # 找到时间段对应的索引范围
        seg_start_idx = np.argmin(np.abs(times - seg_start_time))
        seg_end_idx = np.argmin(np.abs(times - seg_end_time))
        
        if seg_end_idx <= seg_start_idx:
            result['error'] = '时间段索引无效'
            return result
        
        # 边界处理 - 去除开头结尾固定时间
        total_frames = seg_end_idx - seg_start_idx

        if total_frames <= 0:
            result['error'] = f'时间段无数据 (总帧:{total_frames})'
            return result

        # 计算要排除的帧数 - 基于固定时间
        exclude_time = self.detection_params['edge_exclude_time']  # 秒
        time_per_frame = (times[1] - times[0]) if len(times) > 1 else 0.005  # 估计每帧时间
        exclude_frames = int(exclude_time / time_per_frame)

        # 确保不会排除太多帧
        exclude_frames = min(exclude_frames, total_frames // 4)

        # 计算核心区域
        core_start_idx = seg_start_idx + exclude_frames
        core_end_idx = seg_end_idx - exclude_frames

        # 确保核心区域有效
        if core_end_idx <= core_start_idx:
            core_start_idx = seg_start_idx
            core_end_idx = seg_end_idx

        # 提取核心时间段的全频数据
        core_power = power_db[:, core_start_idx:core_end_idx]
        core_times = times[core_start_idx:core_end_idx]
        
        # 计算详细的能量统计信息用于阈值分析
        total_energy = np.sum(core_power, axis=0)
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)

        energy_mean = np.mean(smoothed_energy)
        energy_std = np.std(smoothed_energy)
        energy_cv = energy_std / energy_mean if energy_mean > 0 else 0
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)

        # 记录统计信息 - 包含详细的能量分析
        result['segment_stats'] = {
            'total_frames': total_frames,
            'excluded_frames': exclude_frames * 2,
            'core_frames': core_end_idx - core_start_idx,
            'core_duration': core_times[-1] - core_times[0] if len(core_times) > 0 else 0,
            'power_range': (np.min(core_power), np.max(core_power)),
            'power_mean': np.mean(core_power),
            'exclude_time_ms': exclude_time * 1000,

            # 详细能量统计
            'energy_mean': energy_mean,
            'energy_std': energy_std,
            'energy_cv': energy_cv,
            'energy_range': energy_range,
            'energy_min': np.min(smoothed_energy),
            'energy_max': np.max(smoothed_energy),
            'energy_median': np.median(smoothed_energy),
            'energy_q75': np.percentile(smoothed_energy, 75),
            'energy_q90': np.percentile(smoothed_energy, 90),
            'energy_q95': np.percentile(smoothed_energy, 95),
        }
        
        # 在核心时间段内检测竖线 - 使用统一突出度检测
        vertical_lines = self._detect_vertical_lines_in_core(
            core_power, frequencies, core_times, seg_idx
        )
        
        result['vertical_lines'] = vertical_lines
        result['analysis_success'] = True
        
        return result
    
    def _detect_vertical_lines_in_core(self, core_power, frequencies, core_times, seg_idx):
        """在核心时间段内检测竖线 - 基于标准差预筛选和突出度检测"""
        vertical_lines = []

        # 定义测试频段
        test_segments = [0, 1, 2, 3, 10, 20, 30, 32, 33, 34, 40, 50, 60, 70, 80, 90]

        if core_power.shape[1] < 3:  # 时间片太少
            return vertical_lines

        # 计算时间维度总能量
        total_energy = np.sum(core_power, axis=0)

        if len(total_energy) == 0:
            return vertical_lines

        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)

        # 计算能量统计特征
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        energy_mean = np.mean(smoothed_energy)
        energy_std = np.std(smoothed_energy)
        energy_cv = energy_std / energy_mean if energy_mean > 0 else 0  # 变异系数

        if energy_range < self.detection_params['min_energy_change']:
            return vertical_lines

        # 标准差预筛选 - 利用竖线会增大标准差的特征
        if self.detection_params['std_prescreening']['enable']:
            std_params = self.detection_params['std_prescreening']

            # 检查标准差是否足够大（可能存在竖线）
            if energy_std < std_params['min_std_value']:
                # 调试信息：显示预筛选过滤
                if seg_idx in test_segments:
                    print(f"\n🔍 段{seg_idx}标准差预筛选:")
                    print(f"  标准差={energy_std:.1f} < 阈值{std_params['min_std_value']}")
                    print(f"  结果: 被预筛选过滤，跳过峰值检测")
                # 能量变化太平缓，不太可能有竖线异常
                return vertical_lines
            else:
                # 调试信息：显示通过预筛选
                if seg_idx in test_segments:
                    print(f"\n🔍 段{seg_idx}标准差预筛选:")
                    print(f"  标准差={energy_std:.1f} >= 阈值{std_params['min_std_value']}")
                    print(f"  结果: 通过预筛选，进行峰值检测")

        # 降低的峰值检测阈值
        min_height = energy_mean + energy_std * self.detection_params['min_peak_height_std_ratio']
        min_prominence = energy_std * self.detection_params['min_peak_prominence_std_ratio']

        # 确保最小突出度 - 使用固定比例
        min_prominence = max(min_prominence, energy_range * 0.01)

        # 找到峰值
        peaks, properties = find_peaks(smoothed_energy,
                                     height=min_height,
                                     distance=self.detection_params['peak_distance'],
                                     prominence=min_prominence)

        # 调试信息：显示峰值检测详情 - 扩展更多测试频段
        test_segments = [0, 1, 2, 3, 10, 20, 30, 32, 33, 34, 40, 50, 60, 70, 80, 90]
        if seg_idx in test_segments:
            print(f"\n🔍 段{seg_idx}峰值检测详情:")
            print(f"  能量统计: 均值={energy_mean:.1f}, 标准差={energy_std:.1f}, 范围={energy_range:.1f}")
            print(f"  阈值设置: 高度>{min_height:.1f}, 突出度>{min_prominence:.1f}")
            print(f"  检测结果: 找到{len(peaks)}个峰值")
            if len(peaks) > 0:
                for i, p in enumerate(peaks[:3]):  # 只显示前3个峰值
                    print(f"    峰值{i+1}: 位置={p}, 能量={smoothed_energy[p]:.1f}, 突出度={properties['prominences'][i]:.1f}")

        # 分析每个峰值是否为竖线
        for peak_idx in peaks:
            if peak_idx < len(core_times):
                peak_time = core_times[peak_idx]
                peak_energy = total_energy[peak_idx]
                peak_power_spectrum = core_power[:, peak_idx]

                # 计算峰值突出度特征
                peak_prominence = properties['prominences'][list(peaks).index(peak_idx)]
                peak_snr = 20 * np.log10(peak_energy / energy_mean) if energy_mean > 0 else 0

                # 验证峰值突出度 - 简化验证
                if peak_prominence >= min_prominence:

                    # 分析该时刻的频谱是否为竖线
                    line_analysis = self._analyze_peak_for_vertical_line(
                        peak_power_spectrum, frequencies, peak_time, peak_energy,
                        seg_idx, peak_idx, peak_prominence, peak_snr
                    )

                    # 调试信息：显示竖线分析详情
                    if seg_idx in test_segments:
                        if line_analysis:
                            print(f"    竖线分析: 频率跨度={line_analysis['frequency_span']:.1f}Hz, "
                                  f"强度={line_analysis['line_strength']:.3f}, "
                                  f"阈值={self.detection_params['min_line_strength']:.3f}")
                        else:
                            print(f"    竖线分析: 未通过验证")

                    if line_analysis and line_analysis['line_strength'] >= self.detection_params['min_line_strength']:
                        # 添加标准差信息
                        line_analysis['energy_std'] = energy_std
                        line_analysis['energy_cv'] = energy_cv
                        vertical_lines.append(line_analysis)

        return vertical_lines
    
    def _analyze_peak_for_vertical_line(self, power_spectrum, frequencies, peak_time, peak_energy,
                                       seg_idx, peak_idx, peak_prominence, peak_snr):
        """分析峰值是否为竖线 - 简化版本"""
        # 使用固定阈值
        threshold = np.percentile(power_spectrum, 70)
        high_energy_mask = power_spectrum > threshold
        high_energy_indices = np.where(high_energy_mask)[0]

        # 要求适中的高能量频率点
        if len(high_energy_indices) < 3:
            return None

        # 计算频率特征
        freq_span = frequencies[high_energy_indices[-1]] - frequencies[high_energy_indices[0]]
        freq_ratio = len(high_energy_indices) / len(frequencies)

        # 简化竖线强度计算 - 主要基于突出度
        span_score = min(1.0, freq_span / 5000.0)
        ratio_score = min(1.0, freq_ratio / 0.15)
        prominence_score = min(1.0, peak_prominence / 1000.0)

        # 简化强度计算
        line_strength = (span_score + ratio_score) / 2.0 * prominence_score
        
        return {
            'segment_index': seg_idx,
            'peak_index': peak_idx,
            'time': peak_time,
            'peak_energy': peak_energy,
            'frequency_span': freq_span,
            'frequency_ratio': freq_ratio,
            'line_strength': line_strength,
            'peak_prominence': peak_prominence,
            'span_score': span_score,
            'ratio_score': ratio_score,
            'prominence_score': prominence_score
        }
    
    def _find_continuous_segments(self, indices):
        """找到连续的频率段"""
        if len(indices) == 0:
            return []
        
        segments = []
        current_segment = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_segment.append(indices[i])
            else:
                if len(current_segment) >= 2:
                    segments.append(current_segment)
                current_segment = [indices[i]]
        
        if len(current_segment) >= 2:
            segments.append(current_segment)
        
        return segments
    
    def _visualize_segment_detection_results(self, power_db, frequencies, times, step_boundaries, freq_table,
                                           all_segment_results, audio_path, max_display_segments):
        """可视化频段竖线检测结果"""
        print(f"\n生成频段竖线检测可视化...")
        
        # 选择要显示的频段 - 包含低频、中频、高频的代表性频段
        successful_segments = [seg for seg in all_segment_results if seg['analysis_success']]
        anomalous_segments = [seg for seg in successful_segments if seg['vertical_lines']]
        normal_segments = [seg for seg in successful_segments if not seg['vertical_lines']]

        # 按频率分组选择代表性频段
        total_segments = len(successful_segments)
        if total_segments > 0:
            # 选择低频段 (前1/3)
            low_freq_count = max_display_segments // 3
            low_freq_segments = successful_segments[:total_segments//3][:low_freq_count]

            # 选择中频段 (中间1/3)
            mid_freq_count = max_display_segments // 3
            mid_start = total_segments // 3
            mid_end = 2 * total_segments // 3
            mid_freq_segments = successful_segments[mid_start:mid_end][:mid_freq_count]

            # 选择高频段 (后1/3)
            high_freq_count = max_display_segments - low_freq_count - mid_freq_count
            high_freq_segments = successful_segments[2*total_segments//3:][:high_freq_count]

            display_segments = low_freq_segments + mid_freq_segments + high_freq_segments
        else:
            display_segments = []
        
        # 统计显示的频段信息
        low_count = len([s for s in display_segments if s['segment_index'] < total_segments//3])
        mid_count = len([s for s in display_segments if total_segments//3 <= s['segment_index'] < 2*total_segments//3])
        high_count = len([s for s in display_segments if s['segment_index'] >= 2*total_segments//3])
        anomaly_count = len([s for s in display_segments if s['vertical_lines']])

        print(f"显示 {len(display_segments)} 个频段: "
              f"低频:{low_count}, 中频:{mid_count}, 高频:{high_count} "
              f"(异常:{anomaly_count}, 正常:{len(display_segments)-anomaly_count})")
        
        # 创建可视化 - 为每个频段创建独立的图表
        if len(display_segments) == 0:
            print("没有频段需要显示")
            return

        cols = min(4, len(display_segments))
        if cols == 0:
            cols = 1
        rows = (len(display_segments) + cols - 1) // cols + 1  # +1 for main plot

        # 创建主图表
        fig = plt.figure(figsize=(5*cols, 6*rows))
        fig.suptitle(f'频段竖线检测结果: {os.path.basename(audio_path)}\n'
                    f'总段数:{len(all_segment_results)}, 异常段数:{len(anomalous_segments)}, '
                    f'总竖线:{sum(len(seg["vertical_lines"]) for seg in all_segment_results)}', fontsize=14)
        
        # 第一行：完整频谱图 + 检测结果
        ax_main = plt.subplot2grid((rows, cols), (0, 0), colspan=cols)
        im = ax_main.imshow(power_db, aspect='auto', origin='lower',
                           extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                           cmap='viridis')

        # 标记频段分割和检测结果
        for seg in all_segment_results:
            if seg['analysis_success']:
                # 频段边界
                color = 'red' if seg['vertical_lines'] else 'white'
                alpha = 0.8 if seg['vertical_lines'] else 0.3
                linewidth = 2 if seg['vertical_lines'] else 0.5

                ax_main.axvline(x=seg['start_time'], color=color, alpha=alpha, linewidth=linewidth)

                # 标记检测到的竖线
                for line in seg['vertical_lines']:
                    ax_main.axvline(x=line['time'], color='yellow', linewidth=3, alpha=0.9)

                    # 标记竖线的频率范围
                    if 'high_energy_indices' in line:
                        freq_indices = line['high_energy_indices']
                        freq_range = [frequencies[freq_indices[0]], frequencies[freq_indices[-1]]]
                        ax_main.plot([line['time'], line['time']], freq_range,
                                   color='yellow', linewidth=4, alpha=0.7)

        ax_main.set_title('完整频谱 + 频段分割 + 竖线检测结果')
        ax_main.set_xlabel('时间 (s)')
        ax_main.set_ylabel('频率 (Hz)')
        plt.colorbar(im, ax=ax_main, label='功率 (dB)')

        # 显示各个频段的详细分析 - 使用简化的单图模式
        for idx, segment in enumerate(display_segments):
            row = (idx // cols) + 1
            col = idx % cols

            if row < rows:
                # 为每个频段创建简单的频谱图
                ax = plt.subplot2grid((rows, cols), (row, col))
                self._plot_simple_segment_detection(ax, segment, power_db, frequencies, times)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'segment_vertical_line_detection_{os.path.splitext(os.path.basename(audio_path))[0]}.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  频段竖线检测可视化已保存: {output_filename}")
        
        plt.show()
    
    def _plot_simple_segment_detection(self, ax, segment, power_db, frequencies, times):
        """绘制单个频段的检测结果 - 包含能量峰值检测图"""
        if not segment['analysis_success']:
            ax.text(0.5, 0.5, f"段{segment['segment_index']}\n{segment.get('error', '分析失败')}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            ax.set_title(f'段{segment["segment_index"]} (失败)')
            return

        # 创建子图 - 上方频谱图，下方能量图
        divider = make_axes_locatable(ax)
        ax_energy = divider.append_axes("bottom", size="30%", pad=0.1)

        # 提取频段数据
        seg_start_idx = np.argmin(np.abs(times - segment['start_time']))
        seg_end_idx = np.argmin(np.abs(times - segment['end_time']))

        # 显示频段的频谱
        segment_power = power_db[:, seg_start_idx:seg_end_idx]
        segment_times = times[seg_start_idx:seg_end_idx]

        ax.imshow(segment_power, aspect='auto', origin='lower',
                 extent=[segment_times[0], segment_times[-1], frequencies[0], frequencies[-1]],
                 cmap='viridis')

        # 计算和显示能量峰值检测
        self._plot_energy_peak_detection(ax_energy, segment, power_db, frequencies, times)

        # 标记检测到的竖线
        for i, line in enumerate(segment['vertical_lines']):
            # 在频谱图上标记
            ax.axvline(x=line['time'], color='red', linewidth=2, alpha=0.9)
            # 在能量图上也标记
            ax_energy.axvline(x=line['time'], color='red', linewidth=2, alpha=0.9)

            # 标记竖线的频率范围
            if 'high_energy_indices' in line:
                freq_indices = line['high_energy_indices']
                freq_range = [frequencies[freq_indices[0]], frequencies[freq_indices[-1]]]
                ax.plot([line['time'], line['time']], freq_range,
                       color='red', linewidth=3, alpha=0.8)

        # 设置标题和标签
        title = f"段{segment['segment_index']}"
        if segment['expected_frequency']:
            title += f" ({segment['expected_frequency']:.0f}Hz)"
        title += f"\n竖线:{len(segment['vertical_lines'])}"

        ax.set_title(title, fontsize=9)
        ax.set_ylabel('频率 (Hz)', fontsize=7)
        ax.tick_params(axis='x', labelbottom=False)  # 隐藏x轴标签，因为下方有能量图

        # 显示统计信息
        stats = segment['segment_stats']
        stats_text = f"排除:{stats['exclude_time_ms']:.0f}ms\n核心:{stats['core_duration']:.3f}s"
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=7,
               verticalalignment='top', bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.8))

    def _plot_energy_peak_detection(self, ax_energy, segment, power_db, frequencies, times):
        """绘制能量峰值检测图"""
        # 提取频段数据
        seg_start_idx = np.argmin(np.abs(times - segment['start_time']))
        seg_end_idx = np.argmin(np.abs(times - segment['end_time']))

        # 计算边界排除
        exclude_time = self.detection_params['edge_exclude_time']
        time_per_frame = (times[1] - times[0]) if len(times) > 1 else 0.005
        exclude_frames = int(exclude_time / time_per_frame)
        exclude_frames = min(exclude_frames, (seg_end_idx - seg_start_idx) // 4)

        core_start_idx = seg_start_idx + exclude_frames
        core_end_idx = seg_end_idx - exclude_frames

        if core_end_idx <= core_start_idx:
            core_start_idx = seg_start_idx
            core_end_idx = seg_end_idx

        # 提取核心区域数据
        core_power = power_db[:, core_start_idx:core_end_idx]
        core_times = times[core_start_idx:core_end_idx]
        segment_times = times[seg_start_idx:seg_end_idx]

        # 计算时间维度总能量
        total_energy = np.sum(core_power, axis=0)

        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)

        # 绘制能量曲线
        ax_energy.plot(core_times, total_energy, 'b-', linewidth=1, alpha=0.7, label='总能量')
        ax_energy.plot(core_times, smoothed_energy, 'g-', linewidth=2, label='平滑能量')

        # 标记边界排除区域
        if exclude_frames > 0:
            ax_energy.axvspan(segment_times[0], core_times[0], alpha=0.3, color='gray', label='排除区域')
            ax_energy.axvspan(core_times[-1], segment_times[-1], alpha=0.3, color='gray')

        # 计算峰值检测参数 - 使用更合理的阈值
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)
        energy_mean = np.mean(smoothed_energy)
        energy_std = np.std(smoothed_energy)

        if energy_range >= self.detection_params['min_energy_change']:
            # 峰值高度阈值：基于统一突出度标准
            min_height = energy_mean + energy_std * self.detection_params['min_peak_height_std_ratio']

            # 突出度：基于标准差
            min_prominence = energy_std * self.detection_params['min_peak_prominence_std_ratio']

            # 标记峰值检测阈值
            ax_energy.axhline(y=min_height, color='orange', linestyle='--', alpha=0.7, label='峰值阈值')
            ax_energy.axhline(y=energy_mean, color='blue', linestyle=':', alpha=0.5, label='均值')
            ax_energy.axhline(y=energy_mean + energy_std, color='cyan', linestyle=':', alpha=0.5, label='均值+1σ')

            # 找到峰值
            peaks, _ = find_peaks(smoothed_energy,
                                height=min_height,
                                distance=self.detection_params['peak_distance'],
                                prominence=min_prominence)

            # 标记检测到的峰值
            if len(peaks) > 0:
                peak_times = [core_times[p] for p in peaks if p < len(core_times)]
                peak_energies = [smoothed_energy[p] for p in peaks if p < len(smoothed_energy)]
                ax_energy.scatter(peak_times, peak_energies, color='red', s=30, zorder=5, label=f'峰值({len(peak_times)})')

        ax_energy.set_xlabel('时间 (s)', fontsize=7)
        ax_energy.set_ylabel('能量', fontsize=7)
        ax_energy.legend(fontsize=6, loc='upper right')
        ax_energy.grid(True, alpha=0.3)
        ax_energy.tick_params(axis='both', labelsize=6)

    def _plot_single_segment_detection(self, ax, segment, power_db, frequencies, times):
        """绘制单个频段的检测结果 - 添加能量峰值检测图"""
        if not segment['analysis_success']:
            ax.text(0.5, 0.5, f"段{segment['segment_index']}\n{segment.get('error', '分析失败')}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            ax.set_title(f'段{segment["segment_index"]} (失败)')
            return

        # 创建子图布局 - 上方为频谱图，下方为能量图
        gs = ax.get_gridspec()
        subgs = gs[ax.get_subplotspec()].subgridspec(2, 1, height_ratios=[2, 1])

        # 移除原来的ax
        ax.remove()

        # 创建两个新的子图
        ax_spec = plt.subplot(subgs[0])  # 频谱图
        ax_energy = plt.subplot(subgs[1])  # 能量图

        # 提取频段数据
        seg_start_idx = np.argmin(np.abs(times - segment['start_time']))
        seg_end_idx = np.argmin(np.abs(times - segment['end_time']))

        # 显示频段的频谱
        segment_power = power_db[:, seg_start_idx:seg_end_idx]
        segment_times = times[seg_start_idx:seg_end_idx]

        im = ax_spec.imshow(segment_power, aspect='auto', origin='lower',
                          extent=[segment_times[0], segment_times[-1], frequencies[0], frequencies[-1]],
                          cmap='viridis')

        # 计算时间维度总能量
        total_energy = np.sum(segment_power, axis=0)

        # 平滑处理
        smoothed_energy = gaussian_filter1d(total_energy, sigma=1.0)

        # 绘制能量曲线
        ax_energy.plot(segment_times, total_energy, 'b-', linewidth=1, alpha=0.7, label='总能量')
        ax_energy.plot(segment_times, smoothed_energy, 'g-', linewidth=2, label='平滑能量')

        # 计算峰值检测参数
        energy_range = np.max(smoothed_energy) - np.min(smoothed_energy)

        if energy_range >= self.detection_params['min_energy_change']:
            min_height = np.min(smoothed_energy) + energy_range * self.detection_params['min_peak_height_ratio']
            min_prominence = energy_range * self.detection_params['min_peak_prominence_ratio']

            # 标记峰值检测阈值
            ax_energy.axhline(y=min_height, color='orange', linestyle='--', alpha=0.7, label='峰值阈值')

            # 找到峰值
            peaks, properties = find_peaks(smoothed_energy,
                                         height=min_height,
                                         distance=self.detection_params['peak_distance'],
                                         prominence=min_prominence)

            # 标记检测到的峰值
            if len(peaks) > 0:
                peak_times = [segment_times[p] for p in peaks if p < len(segment_times)]
                peak_energies = [smoothed_energy[p] for p in peaks if p < len(smoothed_energy)]
                ax_energy.scatter(peak_times, peak_energies, color='red', s=50, zorder=5, label=f'峰值({len(peak_times)})')

        # 标记检测到的竖线
        for i, line in enumerate(segment['vertical_lines']):
            # 在频谱图上标记竖线
            ax_spec.axvline(x=line['time'], color='red', linewidth=3, alpha=0.9)

            # 在能量图上也标记竖线
            ax_energy.axvline(x=line['time'], color='red', linewidth=3, alpha=0.9)

            # 标记竖线的频率范围
            if 'high_energy_indices' in line:
                freq_indices = line['high_energy_indices']
                freq_range = [frequencies[freq_indices[0]], frequencies[freq_indices[-1]]]
                ax_spec.plot([line['time'], line['time']], freq_range,
                           color='red', linewidth=5, alpha=0.8)

                # 添加竖线信息
                ax_spec.text(line['time'], frequencies[-1]*0.9, f"L{i+1}\n{line['line_strength']:.2f}",
                           ha='center', va='top', fontsize=8, color='white',
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="red", alpha=0.8))

        # 设置频谱图标题和标签
        title = f"段{segment['segment_index']}"
        if segment['expected_frequency']:
            title += f" ({segment['expected_frequency']:.0f}Hz)"
        title += f"\n竖线:{len(segment['vertical_lines'])}"

        ax_spec.set_title(title, fontsize=10)
        ax_spec.set_ylabel('频率 (Hz)', fontsize=8)

        # 设置能量图标签
        ax_energy.set_xlabel('时间 (s)', fontsize=8)
        ax_energy.set_ylabel('能量', fontsize=8)
        ax_energy.legend(fontsize=6, loc='upper right')
        ax_energy.grid(True, alpha=0.3)

        # 显示统计信息
        stats = segment['segment_stats']
        stats_text = f"总帧:{stats['total_frames']}\n时长:{stats['core_duration']:.3f}s"
        ax_spec.text(0.02, 0.98, stats_text, transform=ax_spec.transAxes, fontsize=8,
                   verticalalignment='top', bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.8))

def main():
    """主函数"""
    # 初始化检测器
    detector = SegmentVerticalLineDetector()
    
    # 分析低音戳洞样本
    audio_path = r"..\test20250717\pos\sd卡\sd1_1.wav"
    
    # 执行频段竖线检测
    results = detector.detect_vertical_lines_in_segments(audio_path, max_display_segments=12)
    
    if results:
        print(f"\n频段竖线检测完成！")
        print(f"详细的检测结果已可视化。")
    else:
        print(f"检测失败！")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
