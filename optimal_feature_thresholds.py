#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
挑选最有用的5个特征，为每个频段给出检测阈值和综合检测结果
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import roc_curve, auc

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def select_top_features():
    """选择最有用的5个特征"""
    print("🔍 选择最有用的5个特征")
    print("="*70)
    
    # 加载特征区分能力分析结果
    if not os.path.exists('feature_discrimination_analysis.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py")
        return None
    
    disc_df = pd.read_csv('feature_discrimination_analysis.csv')
    
    # 选择前5个最有区分力的特征
    top_5_features = disc_df.head(5)['feature'].tolist()
    
    print("📊 选择的5个最有用特征:")
    for i, feature in enumerate(top_5_features, 1):
        feature_info = disc_df[disc_df['feature'] == feature].iloc[0]
        print(f"  {i}. {feature}")
        print(f"     分离评分: {feature_info['separation_score']:.3f}")
        print(f"     Cohen's d: {feature_info['cohens_d']:.3f}")
        print(f"     p值: {feature_info['p_value']:.2e}")
        print()
    
    return top_5_features

def calculate_optimal_threshold(pos_values, neg_values, feature_name):
    """计算最优检测阈值"""
    try:
        # 计算ROC曲线
        y_true = np.concatenate([np.zeros(len(pos_values)), np.ones(len(neg_values))])
        y_scores = np.concatenate([pos_values, neg_values])
        
        fpr, tpr, thresholds = roc_curve(y_true, y_scores)
        roc_auc = auc(fpr, tpr)
        
        # 找到最优阈值（Youden's J statistic）
        j_scores = tpr - fpr
        optimal_idx = np.argmax(j_scores)
        optimal_threshold = thresholds[optimal_idx]
        optimal_sensitivity = tpr[optimal_idx]
        optimal_specificity = 1 - fpr[optimal_idx]
        
        # 检查分离效果
        pos_above_threshold = np.sum(pos_values >= optimal_threshold)
        neg_above_threshold = np.sum(neg_values >= optimal_threshold)
        
        pos_accuracy = 1 - (pos_above_threshold / len(pos_values))  # 正样本应该在阈值以下
        neg_accuracy = neg_above_threshold / len(neg_values)        # 负样本应该在阈值以上
        
        overall_accuracy = (pos_accuracy * len(pos_values) + neg_accuracy * len(neg_values)) / (len(pos_values) + len(neg_values))
        
        return {
            'threshold': optimal_threshold,
            'roc_auc': roc_auc,
            'sensitivity': optimal_sensitivity,
            'specificity': optimal_specificity,
            'pos_accuracy': pos_accuracy,
            'neg_accuracy': neg_accuracy,
            'overall_accuracy': overall_accuracy,
            'separable': overall_accuracy >= 0.8  # 80%以上准确率认为可分离
        }
    
    except Exception as e:
        return None

def analyze_segment_thresholds(top_features):
    """分析每个频段的特征阈值"""
    print("\n🔍 分析每个频段的特征阈值")
    print("="*70)
    
    # 加载特征数据
    df = pd.read_csv('comprehensive_features.csv')
    
    segment_threshold_results = []
    
    for segment_idx in sorted(df['segment_idx'].unique()):
        segment_data = df[df['segment_idx'] == segment_idx]
        
        pos_data = segment_data[segment_data['label'] == 'pos']
        neg_data = segment_data[segment_data['label'] == 'neg']
        
        if len(pos_data) == 0 or len(neg_data) == 0:
            continue
        
        expected_freq = segment_data['expected_freq'].iloc[0]
        start_time = segment_data['start_time'].iloc[0]
        end_time = segment_data['end_time'].iloc[0]
        
        print(f"\n📊 频段 {segment_idx}: {expected_freq:.1f}Hz ({start_time:.3f}s-{end_time:.3f}s)")
        print(f"   正样本: {len(pos_data)}个, 负样本: {len(neg_data)}个")
        
        segment_result = {
            'segment_idx': segment_idx,
            'expected_freq': expected_freq,
            'start_time': start_time,
            'end_time': end_time,
            'pos_count': len(pos_data),
            'neg_count': len(neg_data)
        }
        
        # 分析每个特征
        for feature in top_features:
            pos_values = pos_data[feature].dropna().values
            neg_values = neg_data[feature].dropna().values
            
            if len(pos_values) == 0 or len(neg_values) == 0:
                continue
            
            # 基础统计
            pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
            neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
            
            # 计算最优阈值
            threshold_info = calculate_optimal_threshold(pos_values, neg_values, feature)
            
            if threshold_info and threshold_info['separable']:
                # 可分离的情况
                print(f"   ✅ {feature}: 可分离")
                print(f"      阈值: {threshold_info['threshold']:.6f}")
                print(f"      准确率: {threshold_info['overall_accuracy']:.3f}")
                print(f"      ROC AUC: {threshold_info['roc_auc']:.3f}")
                print(f"      正样本: {pos_mean:.6f}±{pos_std:.6f}")
                print(f"      负样本: {neg_mean:.6f}±{neg_std:.6f}")
                
                segment_result[f'{feature}_separable'] = True
                segment_result[f'{feature}_threshold'] = threshold_info['threshold']
                segment_result[f'{feature}_accuracy'] = threshold_info['overall_accuracy']
                segment_result[f'{feature}_roc_auc'] = threshold_info['roc_auc']
            else:
                # 不可分离的情况
                print(f"   ❌ {feature}: 不可分离")
                print(f"      正样本范围: {pos_mean:.6f}±{pos_std:.6f} [{np.min(pos_values):.6f}, {np.max(pos_values):.6f}]")
                print(f"      负样本范围: {neg_mean:.6f}±{neg_std:.6f} [{np.min(neg_values):.6f}, {np.max(neg_values):.6f}]")
                
                segment_result[f'{feature}_separable'] = False
                segment_result[f'{feature}_threshold'] = None
                segment_result[f'{feature}_accuracy'] = threshold_info['overall_accuracy'] if threshold_info else 0.5
                segment_result[f'{feature}_roc_auc'] = threshold_info['roc_auc'] if threshold_info else 0.5
            
            # 保存统计信息
            segment_result[f'{feature}_pos_mean'] = pos_mean
            segment_result[f'{feature}_pos_std'] = pos_std
            segment_result[f'{feature}_pos_min'] = np.min(pos_values)
            segment_result[f'{feature}_pos_max'] = np.max(pos_values)
            segment_result[f'{feature}_neg_mean'] = neg_mean
            segment_result[f'{feature}_neg_std'] = neg_std
            segment_result[f'{feature}_neg_min'] = np.min(neg_values)
            segment_result[f'{feature}_neg_max'] = np.max(neg_values)
        
        segment_threshold_results.append(segment_result)
    
    return pd.DataFrame(segment_threshold_results)

def comprehensive_detection_analysis(threshold_df, top_features):
    """综合检测分析"""
    print(f"\n🔍 综合检测分析")
    print("="*70)
    
    # 加载原始特征数据
    df = pd.read_csv('comprehensive_features.csv')
    
    detection_results = []
    
    # 为每个样本进行检测
    for _, sample in df.iterrows():
        segment_idx = sample['segment_idx']
        label = sample['label']
        filename = sample['filename']
        
        # 获取该频段的阈值信息
        segment_thresholds = threshold_df[threshold_df['segment_idx'] == segment_idx]
        
        if len(segment_thresholds) == 0:
            continue
        
        segment_threshold = segment_thresholds.iloc[0]
        
        # 检测结果
        detection_votes = []
        feature_results = {}
        
        for feature in top_features:
            feature_value = sample[feature]
            
            if pd.isna(feature_value):
                continue
            
            separable_key = f'{feature}_separable'
            threshold_key = f'{feature}_threshold'
            
            if separable_key in segment_threshold and segment_threshold[separable_key]:
                # 可分离特征
                threshold = segment_threshold[threshold_key]
                
                # 判定逻辑：负样本通常特征值更高
                is_anomaly = feature_value >= threshold
                detection_votes.append(is_anomaly)
                
                feature_results[feature] = {
                    'value': feature_value,
                    'threshold': threshold,
                    'is_anomaly': is_anomaly,
                    'separable': True
                }
            else:
                # 不可分离特征
                feature_results[feature] = {
                    'value': feature_value,
                    'threshold': None,
                    'is_anomaly': None,
                    'separable': False
                }
        
        # 综合判定
        if len(detection_votes) > 0:
            anomaly_ratio = sum(detection_votes) / len(detection_votes)
            predicted_label = 'neg' if anomaly_ratio >= 0.4 else 'pos'  # 40%以上特征异常就判定为负样本
        else:
            predicted_label = 'unknown'
            anomaly_ratio = 0
        
        # 判定正确性
        is_correct = predicted_label == label if predicted_label != 'unknown' else False
        
        detection_results.append({
            'segment_idx': segment_idx,
            'filename': filename,
            'true_label': label,
            'predicted_label': predicted_label,
            'anomaly_ratio': anomaly_ratio,
            'is_correct': is_correct,
            'separable_features': len(detection_votes),
            'feature_results': feature_results
        })
    
    detection_df = pd.DataFrame(detection_results)
    
    # 计算检测性能
    print(f"📊 综合检测性能:")
    print("-" * 50)
    
    valid_predictions = detection_df[detection_df['predicted_label'] != 'unknown']
    
    if len(valid_predictions) > 0:
        overall_accuracy = valid_predictions['is_correct'].mean()
        
        # 按类别统计
        pos_samples = valid_predictions[valid_predictions['true_label'] == 'pos']
        neg_samples = valid_predictions[valid_predictions['true_label'] == 'neg']
        
        pos_accuracy = pos_samples['is_correct'].mean() if len(pos_samples) > 0 else 0
        neg_accuracy = neg_samples['is_correct'].mean() if len(neg_samples) > 0 else 0
        
        print(f"总体准确率: {overall_accuracy:.3f}")
        print(f"正样本准确率: {pos_accuracy:.3f} ({len(pos_samples)}个样本)")
        print(f"负样本准确率: {neg_accuracy:.3f} ({len(neg_samples)}个样本)")
        print(f"有效预测样本: {len(valid_predictions)}/{len(detection_df)} ({len(valid_predictions)/len(detection_df)*100:.1f}%)")
    
    # 按频段统计检测效果
    print(f"\n📊 按频段统计检测效果:")
    print("-" * 50)
    
    segment_performance = []
    
    for segment_idx in sorted(detection_df['segment_idx'].unique()):
        segment_detections = detection_df[detection_df['segment_idx'] == segment_idx]
        valid_segment = segment_detections[segment_detections['predicted_label'] != 'unknown']
        
        if len(valid_segment) > 0:
            segment_accuracy = valid_segment['is_correct'].mean()
            separable_count = valid_segment['separable_features'].iloc[0] if len(valid_segment) > 0 else 0
            
            segment_info = threshold_df[threshold_df['segment_idx'] == segment_idx].iloc[0]
            expected_freq = segment_info['expected_freq']
            
            segment_performance.append({
                'segment_idx': segment_idx,
                'expected_freq': expected_freq,
                'accuracy': segment_accuracy,
                'separable_features': separable_count,
                'total_samples': len(segment_detections),
                'valid_samples': len(valid_segment)
            })
            
            if segment_accuracy >= 0.8:
                status = "✅"
            elif segment_accuracy >= 0.6:
                status = "🟡"
            else:
                status = "❌"
            
            print(f"  频段{segment_idx:2d} ({expected_freq:6.1f}Hz): {status} {segment_accuracy:.3f} ({separable_count}个可分离特征)")
    
    segment_perf_df = pd.DataFrame(segment_performance)
    
    return detection_df, segment_perf_df

def save_results(threshold_df, detection_df, segment_perf_df, top_features):
    """保存结果"""
    print(f"\n💾 保存结果")
    print("="*70)
    
    # 保存阈值结果
    threshold_df.to_csv('segment_feature_thresholds.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 频段特征阈值已保存: segment_feature_thresholds.csv")
    
    # 保存检测结果
    detection_df.to_csv('comprehensive_detection_results.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 综合检测结果已保存: comprehensive_detection_results.csv")
    
    # 保存频段性能
    segment_perf_df.to_csv('segment_detection_performance.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 频段检测性能已保存: segment_detection_performance.csv")
    
    # 生成阈值摘要报告
    generate_threshold_summary(threshold_df, top_features)

def generate_threshold_summary(threshold_df, top_features):
    """生成阈值摘要报告"""
    print(f"\n📊 生成阈值摘要报告")
    print("="*70)
    
    summary_lines = []
    summary_lines.append("# 频段特征检测阈值摘要报告")
    summary_lines.append("")
    summary_lines.append(f"## 选择的5个最有用特征:")
    for i, feature in enumerate(top_features, 1):
        summary_lines.append(f"{i}. {feature}")
    summary_lines.append("")
    
    summary_lines.append("## 各频段检测阈值:")
    summary_lines.append("")
    
    for _, row in threshold_df.iterrows():
        segment_idx = row['segment_idx']
        expected_freq = row['expected_freq']
        
        summary_lines.append(f"### 频段 {segment_idx}: {expected_freq:.1f}Hz")
        summary_lines.append("")
        
        separable_count = 0
        for feature in top_features:
            separable_key = f'{feature}_separable'
            threshold_key = f'{feature}_threshold'
            accuracy_key = f'{feature}_accuracy'
            
            if separable_key in row and row[separable_key]:
                separable_count += 1
                threshold = row[threshold_key]
                accuracy = row[accuracy_key]
                summary_lines.append(f"- **{feature}**: 阈值 = {threshold:.6f}, 准确率 = {accuracy:.3f}")
            else:
                pos_mean = row[f'{feature}_pos_mean']
                pos_std = row[f'{feature}_pos_std']
                neg_mean = row[f'{feature}_neg_mean']
                neg_std = row[f'{feature}_neg_std']
                summary_lines.append(f"- **{feature}**: 不可分离")
                summary_lines.append(f"  - 正样本: {pos_mean:.6f}±{pos_std:.6f}")
                summary_lines.append(f"  - 负样本: {neg_mean:.6f}±{neg_std:.6f}")
        
        summary_lines.append(f"- **可分离特征数**: {separable_count}/{len(top_features)}")
        summary_lines.append("")
    
    # 保存摘要报告
    with open('threshold_summary_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))
    
    print(f"✅ 阈值摘要报告已保存: threshold_summary_report.md")

if __name__ == "__main__":
    # 1. 选择最有用的5个特征
    top_features = select_top_features()
    
    if top_features is None:
        exit(1)
    
    # 2. 分析每个频段的特征阈值
    threshold_df = analyze_segment_thresholds(top_features)
    
    # 3. 综合检测分析
    detection_df, segment_perf_df = comprehensive_detection_analysis(threshold_df, top_features)
    
    # 4. 保存结果
    save_results(threshold_df, detection_df, segment_perf_df, top_features)
    
    print(f"\n✅ 最优特征阈值分析完成！")
    print(f"📊 生成文件:")
    print(f"  - segment_feature_thresholds.csv: 频段特征阈值")
    print(f"  - comprehensive_detection_results.csv: 综合检测结果")
    print(f"  - segment_detection_performance.csv: 频段检测性能")
    print(f"  - threshold_summary_report.md: 阈值摘要报告")
