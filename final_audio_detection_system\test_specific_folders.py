#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门检测test20250717和待定文件夹的样本
"""

import os
import glob
import pandas as pd
import numpy as np
from segment_vertical_line_detector import SegmentVerticalLineDetector

class SpecificFolderTester:
    def __init__(self):
        self.detector = SegmentVerticalLineDetector()
        self.results = []
        
    def detect_specific_folders(self):
        """检测指定文件夹的样本"""
        print("🎯 检测test20250717和待定文件夹")
        print("="*60)
        
        # 指定要检测的文件夹
        target_folders = [
            "../test20250717",
            "../待定"
        ]
        
        all_files = []
        for folder in target_folders:
            if os.path.exists(folder):
                # 递归查找所有wav文件
                pattern = os.path.join(folder, "**/*.wav")
                files = glob.glob(pattern, recursive=True)
                all_files.extend(files)
                print(f"文件夹 {folder}: 找到 {len(files)} 个音频文件")
            else:
                print(f"⚠️ 文件夹不存在: {folder}")
        
        print(f"\n总共找到 {len(all_files)} 个音频文件")
        
        # 按文件夹分组显示
        self._show_file_structure(all_files)
        
        # 逐个检测
        for i, audio_path in enumerate(all_files):
            print(f"\n[{i+1}/{len(all_files)}] 检测: {self._get_relative_path(audio_path)}")
            
            try:
                # 执行检测 - 跳过可视化
                result = self.detector.detect_vertical_lines_in_segments(audio_path, max_display_segments=None)
                
                # 提取关键信息
                sample_result = self._extract_sample_result(audio_path, result)
                self.results.append(sample_result)
                
                print(f"  ✅ 检测完成: {sample_result['anomaly_count']}/{sample_result['total_segments']} 异常 "
                      f"({sample_result['anomaly_rate']:.1f}%) - {sample_result['line_count']}条竖线")
                
            except Exception as e:
                print(f"  ❌ 检测失败: {str(e)}")
                # 记录失败的样本
                self.results.append({
                    'filepath': audio_path,
                    'filename': os.path.basename(audio_path),
                    'folder': self._get_folder_name(audio_path),
                    'sample_type': self._classify_sample_type(audio_path),
                    'detection_status': 'failed',
                    'error': str(e),
                    'total_segments': 0,
                    'anomaly_count': 0,
                    'anomaly_rate': 0,
                    'line_count': 0
                })
        
        return self.results
    
    def _show_file_structure(self, all_files):
        """显示文件结构"""
        print(f"\n📁 文件结构:")
        
        # 按文件夹分组
        folder_groups = {}
        for file_path in all_files:
            folder = self._get_folder_name(file_path)
            if folder not in folder_groups:
                folder_groups[folder] = []
            folder_groups[folder].append(os.path.basename(file_path))
        
        for folder, files in folder_groups.items():
            print(f"  {folder}/ ({len(files)}个文件):")
            for file in sorted(files):
                print(f"    - {file}")
    
    def _get_relative_path(self, file_path):
        """获取相对路径"""
        return file_path.replace("\\", "/").split("/")[-2:]
    
    def _get_folder_name(self, file_path):
        """获取文件夹名称"""
        if "test20250717" in file_path:
            if "pos" in file_path:
                return "test20250717/pos"
            elif "neg" in file_path:
                return "test20250717/neg"
            else:
                return "test20250717"
        elif "待定" in file_path:
            return "待定"
        else:
            return "other"
    
    def _extract_sample_result(self, audio_path, detection_result):
        """提取样本检测结果"""
        filename = os.path.basename(audio_path)
        folder = self._get_folder_name(audio_path)
        sample_type = self._classify_sample_type(audio_path)
        
        if detection_result and 'segment_results' in detection_result:
            segment_results = detection_result['segment_results']
            successful_segments = [seg for seg in segment_results if seg['analysis_success']]
            anomalous_segments = [seg for seg in successful_segments if seg['vertical_lines']]
            
            total_segments = len(successful_segments)
            anomaly_count = len(anomalous_segments)
            anomaly_rate = (anomaly_count / total_segments * 100) if total_segments > 0 else 0
            
            # 统计竖线总数
            line_count = sum(len(seg['vertical_lines']) for seg in anomalous_segments)
            
            return {
                'filepath': audio_path,
                'filename': filename,
                'folder': folder,
                'sample_type': sample_type,
                'detection_status': 'success',
                'total_segments': total_segments,
                'anomaly_count': anomaly_count,
                'anomaly_rate': anomaly_rate,
                'line_count': line_count,
                'audio_duration': detection_result.get('audio_duration', 0),
                'frequency_range': detection_result.get('frequency_range', 'unknown')
            }
        else:
            return {
                'filepath': audio_path,
                'filename': filename,
                'folder': folder,
                'sample_type': sample_type,
                'detection_status': 'no_result',
                'total_segments': 0,
                'anomaly_count': 0,
                'anomaly_rate': 0,
                'line_count': 0
            }
    
    def _classify_sample_type(self, audio_path):
        """根据文件路径和文件名分类样本类型"""
        path_lower = audio_path.lower()
        filename = os.path.basename(audio_path).lower()
        
        # 根据文件夹路径判断
        if "pos" in path_lower:
            return 'positive'
        elif "neg" in path_lower:
            return 'negative'
        
        # 根据文件名判断
        if any(keyword in filename for keyword in ['竖线', '戳洞', 'vertical', 'spike', 'click']):
            return 'positive'
        elif any(keyword in filename for keyword in ['扫频', 'sweep', '正常', 'normal', 'clean']):
            return 'negative'
        elif any(keyword in filename for keyword in ['噪声', 'noise']):
            return 'noise'
        
        # 待定文件夹的样本需要根据检测结果判断
        if "待定" in path_lower:
            return 'unknown'
        
        return 'unknown'
    
    def analyze_results(self):
        """分析检测结果"""
        if not self.results:
            print("没有检测结果可分析")
            return
        
        df = pd.DataFrame(self.results)
        
        print(f"\n📊 检测结果分析")
        print("="*60)
        
        # 总体统计
        total_samples = len(df)
        successful_detections = len(df[df['detection_status'] == 'success'])
        
        print(f"总样本数: {total_samples}")
        print(f"成功检测: {successful_detections}")
        print(f"检测成功率: {successful_detections/total_samples*100:.1f}%")
        
        # 按文件夹分析
        print(f"\n📈 按文件夹分析:")
        for folder in df['folder'].unique():
            folder_df = df[df['folder'] == folder]
            successful_folder = folder_df[folder_df['detection_status'] == 'success']
            
            print(f"\n  {folder} ({len(folder_df)}个文件):")
            print(f"    成功检测: {len(successful_folder)}/{len(folder_df)}")
            
            if len(successful_folder) > 0:
                avg_anomaly_rate = successful_folder['anomaly_rate'].mean()
                avg_line_count = successful_folder['line_count'].mean()
                
                print(f"    平均异常率: {avg_anomaly_rate:.1f}%")
                print(f"    平均竖线数: {avg_line_count:.1f}")
                print(f"    异常率范围: {successful_folder['anomaly_rate'].min():.1f}% - {successful_folder['anomaly_rate'].max():.1f}%")
                
                # 显示每个文件的详细结果
                print(f"    详细结果:")
                for _, row in successful_folder.iterrows():
                    status = "🔴异常" if row['anomaly_rate'] > 10 else "🟡轻微" if row['anomaly_rate'] > 0 else "🟢正常"
                    print(f"      {row['filename']}: {row['anomaly_rate']:.1f}% ({row['line_count']}条) {status}")
        
        # 按样本类型分析
        print(f"\n📈 按样本类型分析:")
        for sample_type in df['sample_type'].unique():
            type_df = df[df['sample_type'] == sample_type]
            successful_type = type_df[type_df['detection_status'] == 'success']
            
            if len(successful_type) > 0:
                avg_anomaly_rate = successful_type['anomaly_rate'].mean()
                avg_line_count = successful_type['line_count'].mean()
                
                print(f"\n  {sample_type.upper()}样本 ({len(type_df)}个):")
                print(f"    平均异常率: {avg_anomaly_rate:.1f}%")
                print(f"    平均竖线数: {avg_line_count:.1f}")
                print(f"    异常率范围: {successful_type['anomaly_rate'].min():.1f}% - {successful_type['anomaly_rate'].max():.1f}%")
        
        return df
    
    def save_results(self, output_file="specific_folders_detection_results.csv"):
        """保存检测结果到CSV文件"""
        if self.results:
            df = pd.DataFrame(self.results)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 检测结果已保存到: {output_file}")
            return df
        return None

def main():
    """主函数"""
    tester = SpecificFolderTester()
    
    # 检测指定文件夹
    results = tester.detect_specific_folders()
    
    # 分析结果
    df = tester.analyze_results()
    
    # 保存结果
    tester.save_results()
    
    print(f"\n🎯 指定文件夹检测完成！")

if __name__ == "__main__":
    main()
