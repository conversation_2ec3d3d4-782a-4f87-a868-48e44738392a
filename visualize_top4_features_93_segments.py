#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化前四个最佳特征在93个频段的对比
重点展示两个噪声样本与所有正常样本的差异
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import librosa
from scipy.signal import stft, welch

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def extract_top4_features_all_segments():
    """提取前四个最佳特征在所有93个频段的数据"""
    print("🔍 提取前四个最佳特征在93个频段的数据")
    print("="*70)
    print("前四个最佳特征:")
    print("1. true_noise_floor_median - 真实底噪中位数")
    print("2. true_noise_floor_mean - 真实底噪平均值")
    print("3. noise_floor_stability_mean - 底噪稳定性平均值")
    print("4. noise_floor_stability_std - 底噪稳定性标准差")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_segment_data = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取所有93个频段的前四个特征
                segment_features = extract_file_all_segments_top4(audio_path, filename, target_files)
                
                if segment_features:
                    all_segment_data.extend(segment_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_segment_data)
    
    # 保存结果
    df.to_csv('top4_features_93_segments.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 前四个特征93个频段数据已保存: top4_features_93_segments.csv")
    
    # 生成可视化
    create_top4_features_visualization(df, target_files)
    
    return df

def extract_file_all_segments_top4(audio_path, filename, target_files):
    """提取单个文件所有93个频段的前四个特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_data = []
        
        # 分析所有93个频段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 提取该频段的前四个特征
            top4_features = extract_segment_top4_features(segment_audio, sr, expected_freq)
            
            # 添加元数据
            segment_info = {
                'filename': filename,
                'segment_idx': seg_idx,
                'expected_freq': expected_freq,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'is_target': filename in target_files,
                'sample_type': 'target' if filename in target_files else 'normal'
            }
            
            # 合并特征和元数据
            segment_info.update(top4_features)
            segment_data.append(segment_info)
        
        return segment_data
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_segment_top4_features(audio, sr, expected_freq):
    """提取单个频段的前四个特征"""
    features = {}
    
    try:
        # 标准化音频
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))
        
        # STFT分析
        f, t, Zxx = stft(audio, sr, nperseg=1024, noverlap=512)
        power_spectrum = np.abs(Zxx) ** 2
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 计算前四个特征
        all_noise_floors = []
        all_noise_floor_stds = []
        
        for frame_idx in range(power_spectrum.shape[1]):
            frame_power_db = power_db[:, frame_idx]
            
            # 1. 真实底噪水平 (最低10%功率)
            noise_floor_10 = np.percentile(frame_power_db, 10)
            noise_floor_5 = np.percentile(frame_power_db, 5)
            noise_floor_min = np.min(frame_power_db)
            
            all_noise_floors.extend([noise_floor_10, noise_floor_5, noise_floor_min])
            
            # 2. 底噪稳定性 (低功率区域的标准差)
            low_power_mask = frame_power_db <= np.percentile(frame_power_db, 20)
            if np.any(low_power_mask):
                noise_floor_std = np.std(frame_power_db[low_power_mask])
                all_noise_floor_stds.append(noise_floor_std)
        
        # 计算前四个特征
        if all_noise_floors:
            features['true_noise_floor_median'] = np.median(all_noise_floors)
            features['true_noise_floor_mean'] = np.mean(all_noise_floors)
        else:
            features['true_noise_floor_median'] = -120
            features['true_noise_floor_mean'] = -120
        
        if all_noise_floor_stds:
            features['noise_floor_stability_mean'] = np.mean(all_noise_floor_stds)
            features['noise_floor_stability_std'] = np.std(all_noise_floor_stds)
        else:
            features['noise_floor_stability_mean'] = 0
            features['noise_floor_stability_std'] = 0
            
    except Exception as e:
        # 设置默认值
        features = {
            'true_noise_floor_median': -120,
            'true_noise_floor_mean': -120,
            'noise_floor_stability_mean': 0,
            'noise_floor_stability_std': 0
        }
    
    return features

def create_top4_features_visualization(df, target_files):
    """创建前四个特征的93个频段可视化"""
    print(f"\n🎨 生成前四个特征93个频段可视化...")
    
    # 准备数据
    segments = sorted(df['segment_idx'].unique())
    frequencies = [df[df['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    # 前四个特征
    top4_features = [
        'true_noise_floor_median',
        'true_noise_floor_mean', 
        'noise_floor_stability_mean',
        'noise_floor_stability_std'
    ]
    
    feature_titles = [
        '真实底噪中位数 (dB)',
        '真实底噪平均值 (dB)',
        '底噪稳定性平均值',
        '底噪稳定性标准差'
    ]
    
    # 创建大图 - 2x2布局，每个特征一个子图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('前四个最佳特征在93个频段的对比分析', fontsize=16, fontweight='bold')
    
    for i, (feature, title) in enumerate(zip(top4_features, feature_titles)):
        ax = axes[i//2, i%2]
        
        # 绘制该特征的93个频段对比
        plot_feature_93_segments(ax, df, feature, title, segments, frequencies)
    
    plt.tight_layout()
    plt.savefig('top4_features_93_segments_comparison.png', dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存: top4_features_93_segments_comparison.png")
    plt.close()
    
    # 生成热力图可视化
    create_heatmap_visualization(df, target_files, top4_features, feature_titles)
    
    # 生成统计报告
    generate_top4_statistics(df, target_files, segments, frequencies, top4_features)

def plot_feature_93_segments(ax, df, feature_name, title, segments, frequencies):
    """绘制单个特征在93个频段的对比"""
    
    target_values = []
    normal_values = []
    target_ranges = []
    normal_ranges = []
    separable_segments = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_seg_data = seg_data[seg_data['is_target'] == True][feature_name]
        normal_seg_data = seg_data[seg_data['is_target'] == False][feature_name]
        
        if len(target_seg_data) > 0 and len(normal_seg_data) > 0:
            target_mean = np.mean(target_seg_data)
            normal_mean = np.mean(normal_seg_data)
            target_min, target_max = np.min(target_seg_data), np.max(target_seg_data)
            normal_min, normal_max = np.min(normal_seg_data), np.max(normal_seg_data)
            
            target_values.append(target_mean)
            normal_values.append(normal_mean)
            target_ranges.append([target_min, target_max])
            normal_ranges.append([normal_min, normal_max])
            
            # 检查是否可分离
            if target_max < normal_min or target_min > normal_max:
                separable_segments.append(seg_idx)
        else:
            target_values.append(np.nan)
            normal_values.append(np.nan)
            target_ranges.append([np.nan, np.nan])
            normal_ranges.append([np.nan, np.nan])
    
    # 转换为数组
    target_ranges = np.array(target_ranges)
    normal_ranges = np.array(normal_ranges)
    x = np.array(segments)
    
    # 绘制范围带
    ax.fill_between(x, normal_ranges[:, 0], normal_ranges[:, 1], 
                   alpha=0.3, color='blue', label='正常样本范围')
    ax.fill_between(x, target_ranges[:, 0], target_ranges[:, 1], 
                   alpha=0.6, color='red', label='噪声样本范围')
    
    # 绘制平均值线
    ax.plot(x, normal_values, 'b-', linewidth=2, label='正常样本均值', alpha=0.8)
    ax.plot(x, target_values, 'r-', linewidth=3, label='噪声样本均值')
    
    # 标记可分离频段
    if separable_segments:
        sep_indices = [segments.index(seg) for seg in separable_segments if seg in segments]
        if sep_indices:
            ax.scatter([segments[i] for i in sep_indices], 
                      [target_values[i] for i in sep_indices],
                      color='yellow', s=100, marker='*', edgecolor='black', 
                      label=f'可分离频段 ({len(separable_segments)}个)', zorder=5)
    
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('特征值')
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    # 添加频率标签
    freq_ticks = range(0, len(segments), 20)
    ax2 = ax.twiny()
    ax2.set_xlim(ax.get_xlim())
    ax2.set_xticks([segments[i] for i in freq_ticks if i < len(segments)])
    ax2.set_xticklabels([f'{frequencies[i]:.0f}Hz' for i in freq_ticks if i < len(frequencies)], 
                       rotation=45)
    
    # 添加统计信息
    separable_count = len(separable_segments)
    total_count = len(segments)
    
    # 计算整体差异
    overall_target_mean = np.nanmean(target_values)
    overall_normal_mean = np.nanmean(normal_values)
    overall_diff = overall_target_mean - overall_normal_mean
    
    stats_text = f'可分离: {separable_count}/{total_count} ({separable_count/total_count*100:.1f}%)\n'
    stats_text += f'整体差异: {overall_diff:+.2f}'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7), fontsize=9)

def create_heatmap_visualization(df, target_files, top4_features, feature_titles):
    """创建热力图可视化"""
    print(f"\n🎨 生成热力图可视化...")
    
    # 创建热力图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('前四个特征93个频段热力图对比', fontsize=16, fontweight='bold')
    
    for i, (feature, title) in enumerate(zip(top4_features, feature_titles)):
        ax = axes[i//2, i%2]
        
        # 准备热力图数据
        pivot_data = prepare_heatmap_data(df, feature)
        
        # 绘制热力图
        im = ax.imshow(pivot_data, cmap='RdYlBu_r', aspect='auto', interpolation='nearest')
        
        # 设置标签
        segments = sorted(df['segment_idx'].unique())
        files = sorted(df['filename'].unique())
        
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.set_xlabel('频段索引')
        ax.set_ylabel('音频文件')
        
        # 设置x轴标签 (每10个频段显示一个)
        x_ticks = range(0, len(segments), 10)
        x_labels = [f'Seg{segments[i]}' for i in x_ticks if i < len(segments)]
        ax.set_xticks(x_ticks)
        ax.set_xticklabels(x_labels, rotation=45, ha='right')
        
        # 设置y轴标签 (突出显示目标文件)
        y_labels = []
        for filename in files:
            if filename in target_files:
                y_labels.append(f'★ {filename[:25]}...' if len(filename) > 25 else f'★ {filename}')
            else:
                y_labels.append(filename[:25] + '...' if len(filename) > 25 else filename)
        
        ax.set_yticks(range(len(files)))
        ax.set_yticklabels(y_labels, fontsize=6)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('特征值', rotation=270, labelpad=15)
    
    plt.tight_layout()
    plt.savefig('top4_features_heatmaps.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 热力图已保存: top4_features_heatmaps.png")

def prepare_heatmap_data(df, feature_name):
    """准备热力图数据"""
    # 创建透视表
    pivot_table = df.pivot_table(values=feature_name, index='filename', columns='segment_idx', aggfunc='mean')
    
    # 填充缺失值
    if 'noise_floor_stability' in feature_name:
        pivot_table = pivot_table.fillna(0)
    else:
        pivot_table = pivot_table.fillna(-120)
    
    return pivot_table.values

def generate_top4_statistics(df, target_files, segments, frequencies, top4_features):
    """生成前四个特征的统计报告"""
    print(f"\n📊 前四个特征93个频段统计报告")
    print("="*70)
    
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📈 整体统计:")
    print(f"   总频段数: {len(segments)}")
    print(f"   噪声样本: {len(target_data['filename'].unique())} 个文件")
    print(f"   正常样本: {len(normal_data['filename'].unique())} 个文件")
    
    # 分析每个特征
    for feature in top4_features:
        print(f"\n🎯 {feature} 分析:")
        
        # 整体对比
        target_mean = target_data[feature].mean()
        normal_mean = normal_data[feature].mean()
        target_std = target_data[feature].std()
        normal_std = normal_data[feature].std()
        
        print(f"   整体对比:")
        print(f"     噪声样本: {target_mean:.3f} ± {target_std:.3f}")
        print(f"     正常样本: {normal_mean:.3f} ± {normal_std:.3f}")
        print(f"     差异: {target_mean - normal_mean:+.3f}")
        
        # 分离分析
        separable_segments = []
        separation_gaps = []
        
        for seg_idx in segments:
            seg_data = df[df['segment_idx'] == seg_idx]
            target_values = seg_data[seg_data['is_target'] == True][feature]
            normal_values = seg_data[seg_data['is_target'] == False][feature]
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_min, target_max = np.min(target_values), np.max(target_values)
                normal_min, normal_max = np.min(normal_values), np.max(normal_values)
                
                if target_max < normal_min or target_min > normal_max:
                    gap = max(normal_min - target_max, target_min - normal_max)
                    separable_segments.append({
                        'segment': seg_idx,
                        'frequency': frequencies[seg_idx],
                        'gap': gap
                    })
                    separation_gaps.append(gap)
        
        print(f"   分离分析:")
        print(f"     可分离频段数: {len(separable_segments)}/{len(segments)} ({len(separable_segments)/len(segments)*100:.1f}%)")
        
        if separable_segments:
            separable_segments.sort(key=lambda x: x['gap'], reverse=True)
            print(f"     最大分离间隙: {max(separation_gaps):.3f}")
            print(f"     平均分离间隙: {np.mean(separation_gaps):.3f}")
            
            print(f"     前3个最佳分离频段:")
            for i, seg_info in enumerate(separable_segments[:3]):
                print(f"       {i+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): 间隙{seg_info['gap']:.3f}")

if __name__ == "__main__":
    df = extract_top4_features_all_segments()
    print(f"\n✅ 前四个特征93个频段可视化完成！")
