#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可能有问题的样本，寻找没有选择第一个满足阈值点的情况
"""

import os
import glob
from debug_threshold_analysis import analyze_threshold_selection

def find_problematic_samples():
    """寻找可能有问题的样本"""
    print("🔍 寻找可能没有选择第一个满足阈值点的样本")
    print("="*70)
    
    # 扩大搜索范围，包括更多样本
    test_folders = ["../test20250717", "../待定"]
    all_files = []
    
    for folder in test_folders:
        if os.path.exists(folder):
            pattern = os.path.join(folder, "**/*.wav")
            files = glob.glob(pattern, recursive=True)
            all_files.extend(files)
    
    print(f"找到 {len(all_files)} 个音频文件")
    
    problematic_samples = []
    successful_samples = []
    
    for i, file_path in enumerate(all_files):
        print(f"\n[{i+1}/{len(all_files)}] 测试: {os.path.basename(file_path)}")
        
        try:
            result = analyze_threshold_selection(file_path, debug=False)
            if result:
                if result['good_points_count'] == 0:
                    print(f"  ❌ 没有符合条件的点")
                    problematic_samples.append({
                        'filename': os.path.basename(file_path),
                        'filepath': file_path,
                        'max_correlation': result['max_correlation'],
                        'final_threshold': result['final_threshold'],
                        'issue': 'no_good_points'
                    })
                elif result['max_correlation'] < 0.5:
                    print(f"  ⚠️ 最大相关性很低: {result['max_correlation']:.3f}")
                    problematic_samples.append({
                        'filename': os.path.basename(file_path),
                        'filepath': file_path,
                        'max_correlation': result['max_correlation'],
                        'final_threshold': result['final_threshold'],
                        'issue': 'low_correlation'
                    })
                else:
                    print(f"  ✅ 正常: {result['good_points_count']}个符合条件的点")
                    successful_samples.append(result)
        except Exception as e:
            print(f"  ❌ 分析失败: {str(e)}")
            problematic_samples.append({
                'filename': os.path.basename(file_path),
                'filepath': file_path,
                'issue': 'analysis_failed',
                'error': str(e)
            })
    
    # 汇总报告
    print(f"\n📊 测试汇总:")
    print("="*70)
    print(f"总样本数: {len(all_files)}")
    print(f"成功样本: {len(successful_samples)}")
    print(f"问题样本: {len(problematic_samples)}")
    
    if problematic_samples:
        print(f"\n❌ 问题样本详情:")
        for sample in problematic_samples:
            print(f"  {sample['filename']}: {sample['issue']}")
            if 'max_correlation' in sample:
                print(f"    最大相关性: {sample['max_correlation']:.3f}")
                print(f"    最终阈值: {sample['final_threshold']:.3f}")
    
    if successful_samples:
        import numpy as np
        max_corrs = [s['max_correlation'] for s in successful_samples]
        good_counts = [s['good_points_count'] for s in successful_samples]
        
        print(f"\n✅ 成功样本统计:")
        print(f"  最大相关性范围: {np.min(max_corrs):.3f} - {np.max(max_corrs):.3f}")
        print(f"  平均最大相关性: {np.mean(max_corrs):.3f}")
        print(f"  平均符合条件点数: {np.mean(good_counts):.1f}")
    
    return problematic_samples, successful_samples

if __name__ == "__main__":
    problematic, successful = find_problematic_samples()
