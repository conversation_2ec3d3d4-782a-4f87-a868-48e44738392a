#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化测试峰值选择优化后的两个文件夹样本
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_peak_selection_test():
    """可视化测试峰值选择优化后的样本"""
    print("🔍 可视化测试峰值选择优化后的样本")
    print("="*60)
    
    # 定义测试文件夹
    folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美", 
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }
    
    results = []
    
    # 测试所有文件夹
    for category, folder_list in folders.items():
        for folder in folder_list:
            print(f"\n📁 测试文件夹: {folder} ({category})")
            print("-" * 50)
            
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            # 获取所有wav文件
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            print(f"找到 {len(wav_files)} 个wav文件")
            
            for i, audio_path in enumerate(wav_files[:2]):  # 测试前2个文件
                filename = os.path.basename(audio_path)
                folder_name = os.path.basename(folder)
                
                print(f"\n🎵 [{i+1}/2] 测试文件: {filename}")
                
                try:
                    # 调用优化的频率分割算法，生成可视化
                    step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                        audio_path,
                        min_duration=153,
                        plot=True,  # 生成可视化图表
                        debug=False,  # 减少输出
                        search_window_start=0.1,
                        search_window_end=1.5,
                        correlation_length=1.0
                    )
                    
                    # 提取关键信息
                    start_offset = alignment_info.get('start_offset', 0)
                    correlation_score = alignment_info.get('correlation_score', 0)
                    alignment_quality = alignment_info.get('alignment_quality', {})
                    
                    # 计算质量指标
                    overall_quality = alignment_quality.get('overall_quality', 'unknown')
                    composite_score = alignment_quality.get('composite_score', 0)
                    time_correlation = alignment_quality.get('time_correlation', 0)
                    freq_similarity = alignment_quality.get('freq_similarity', 0)
                    
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': start_offset,
                        'correlation_score': correlation_score,
                        'overall_quality': overall_quality,
                        'composite_score': composite_score,
                        'time_correlation': time_correlation,
                        'freq_similarity': freq_similarity,
                        'step_count': len(step_bounds),
                        'freq_count': len(freq_table),
                        'status': 'success'
                    }
                    
                    print(f"  ✅ 成功: 开始时间={start_offset:.3f}s, 相关性={correlation_score:.3f}")
                    print(f"     质量={overall_quality}, 评分={composite_score:.3f}")
                    
                except Exception as e:
                    print(f"  ❌ 失败: {str(e)}")
                    result = {
                        'category': category,
                        'folder': folder_name,
                        'filename': filename,
                        'start_offset': 0,
                        'correlation_score': 0,
                        'overall_quality': 'failed',
                        'composite_score': 0,
                        'time_correlation': 0,
                        'freq_similarity': 0,
                        'step_count': 0,
                        'freq_count': 0,
                        'status': 'failed',
                        'error': str(e)
                    }
                
                results.append(result)
    
    # 生成综合可视化分析
    if not results:
        print("❌ 没有测试结果")
        return
    
    df = pd.DataFrame(results)
    success_df = df[df['status'] == 'success']
    
    # 创建综合可视化图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('峰值选择优化后的音频样本测试结果可视化分析', fontsize=16, fontweight='bold')
    
    # 1. 成功率对比
    ax1 = axes[0, 0]
    success_rate = df.groupby('category')['status'].apply(lambda x: (x == 'success').mean() * 100)
    colors = ['green' if cat == 'pos' else 'red' for cat in success_rate.index]
    bars = ax1.bar(success_rate.index, success_rate.values, color=colors, alpha=0.7)
    ax1.set_title('成功率对比')
    ax1.set_ylabel('成功率 (%)')
    ax1.set_ylim(0, 100)
    for bar, rate in zip(bars, success_rate.values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 2. 开始时间分布
    ax2 = axes[0, 1]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            color = 'green' if category == 'pos' else 'red'
            ax2.hist(cat_data['start_offset'], alpha=0.6, label=f'{category} (n={len(cat_data)})', 
                    bins=8, color=color)
        ax2.set_title('开始时间分布')
        ax2.set_xlabel('开始时间 (秒)')
        ax2.set_ylabel('频次')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 相关性分布
    ax3 = axes[0, 2]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            color = 'green' if category == 'pos' else 'red'
            ax3.hist(cat_data['correlation_score'], alpha=0.6, label=f'{category} (n={len(cat_data)})', 
                    bins=8, color=color)
        ax3.set_title('相关性分布')
        ax3.set_xlabel('相关性系数')
        ax3.set_ylabel('频次')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 添加质量阈值线
        ax3.axvline(0.8, color='orange', linestyle='--', alpha=0.8, label='高质量阈值(0.8)')
        ax3.axvline(0.6, color='yellow', linestyle='--', alpha=0.8, label='中等质量阈值(0.6)')
    
    # 4. 文件夹详细对比
    ax4 = axes[1, 0]
    if len(success_df) > 0:
        folder_stats = success_df.groupby(['category', 'folder']).agg({
            'correlation_score': 'mean',
            'start_offset': 'mean'
        }).reset_index()
        
        x_pos = np.arange(len(folder_stats))
        colors = ['green' if row['category'] == 'pos' else 'red' for _, row in folder_stats.iterrows()]
        bars = ax4.bar(x_pos, folder_stats['correlation_score'], alpha=0.7, color=colors)
        ax4.set_title('各文件夹平均相关性')
        ax4.set_ylabel('平均相关性')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels([f"{row['category']}/{row['folder']}" for _, row in folder_stats.iterrows()], 
                           rotation=45, ha='right')
        
        # 添加数值标签
        for bar, score in zip(bars, folder_stats['correlation_score']):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{score:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 5. 散点图：开始时间 vs 相关性
    ax5 = axes[1, 1]
    if len(success_df) > 0:
        for category in success_df['category'].unique():
            cat_data = success_df[success_df['category'] == category]
            color = 'green' if category == 'pos' else 'red'
            ax5.scatter(cat_data['start_offset'], cat_data['correlation_score'], 
                       label=category, alpha=0.7, s=80, color=color)
        ax5.set_title('开始时间 vs 相关性')
        ax5.set_xlabel('开始时间 (秒)')
        ax5.set_ylabel('相关性系数')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 添加质量区域
        ax5.axhspan(0.8, 1.0, alpha=0.1, color='green', label='高质量区域')
        ax5.axhspan(0.6, 0.8, alpha=0.1, color='yellow', label='中等质量区域')
    
    # 6. 峰值选择效果分析
    ax6 = axes[1, 2]
    if len(success_df) > 0:
        # 相关性质量分布
        high_corr = len(success_df[success_df['correlation_score'] >= 0.8])
        medium_corr = len(success_df[(success_df['correlation_score'] >= 0.6) & 
                                   (success_df['correlation_score'] < 0.8)])
        low_corr = len(success_df[success_df['correlation_score'] < 0.6])
        
        quality_data = [high_corr, medium_corr, low_corr]
        quality_labels = ['高质量\n(≥0.8)', '中等质量\n(0.6-0.8)', '低质量\n(<0.6)']
        colors = ['green', 'orange', 'red']
        
        wedges, texts, autotexts = ax6.pie(quality_data, labels=quality_labels, colors=colors, 
                                          autopct='%1.1f%%', startangle=90)
        ax6.set_title('峰值选择质量分布')
        
        # 美化饼图
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig('peak_selection_visualization_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成详细统计报告
    print("\n" + "="*60)
    print("📊 峰值选择优化后的详细统计报告")
    print("="*60)
    
    for category in df['category'].unique():
        cat_data = df[df['category'] == category]
        success_data = cat_data[cat_data['status'] == 'success']
        
        print(f"\n📁 {category.upper()} 类别:")
        print(f"  总样本数: {len(cat_data)}")
        print(f"  成功样本数: {len(success_data)}")
        print(f"  成功率: {len(success_data)/len(cat_data)*100:.1f}%")
        
        if len(success_data) > 0:
            print(f"  开始时间统计:")
            print(f"    平均值: {success_data['start_offset'].mean():.3f}s")
            print(f"    标准差: {success_data['start_offset'].std():.3f}s")
            print(f"    范围: {success_data['start_offset'].min():.3f}s - {success_data['start_offset'].max():.3f}s")
            
            print(f"  相关性统计:")
            print(f"    平均值: {success_data['correlation_score'].mean():.3f}")
            print(f"    标准差: {success_data['correlation_score'].std():.3f}")
            print(f"    范围: {success_data['correlation_score'].min():.3f} - {success_data['correlation_score'].max():.3f}")
            
            # 质量分析
            high_quality = len(success_data[success_data['correlation_score'] >= 0.8])
            medium_quality = len(success_data[(success_data['correlation_score'] >= 0.6) & 
                                            (success_data['correlation_score'] < 0.8)])
            low_quality = len(success_data[success_data['correlation_score'] < 0.6])
            
            print(f"  质量分布:")
            print(f"    高质量 (≥0.8): {high_quality}个 ({high_quality/len(success_data)*100:.1f}%)")
            print(f"    中等质量 (0.6-0.8): {medium_quality}个 ({medium_quality/len(success_data)*100:.1f}%)")
            print(f"    低质量 (<0.6): {low_quality}个 ({low_quality/len(success_data)*100:.1f}%)")
    
    # 峰值选择效果总结
    if len(success_df) > 0:
        overall_high = len(success_df[success_df['correlation_score'] >= 0.8])
        overall_std = success_df['start_offset'].std()
        
        print(f"\n🎯 峰值选择优化效果总结:")
        print(f"  总体高质量率: {overall_high/len(success_df)*100:.1f}%")
        print(f"  时间一致性: {overall_std:.3f}s标准差")
        
        if overall_std < 0.01:
            consistency_rating = "✅ 非常一致 (< 10ms)"
        elif overall_std < 0.02:
            consistency_rating = "✅ 较为一致 (< 20ms)"
        else:
            consistency_rating = "⚠️ 存在变化 (≥ 20ms)"
        
        print(f"  一致性评级: {consistency_rating}")
        
        if overall_high/len(success_df) >= 0.8:
            quality_rating = "🎉 优秀 (≥80%高质量)"
        elif overall_high/len(success_df) >= 0.6:
            quality_rating = "✅ 良好 (≥60%高质量)"
        else:
            quality_rating = "⚠️ 需要改进 (<60%高质量)"
        
        print(f"  质量评级: {quality_rating}")
    
    # 保存结果
    output_file = "peak_selection_visual_test_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    print(f"📊 可视化图表已保存到: peak_selection_visualization_analysis.png")
    
    return df

if __name__ == "__main__":
    results_df = visualize_peak_selection_test()
