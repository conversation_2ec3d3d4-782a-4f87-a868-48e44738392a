"""
简易音频异响检测器
==================

这是一个简化的使用接口，让您可以快速检测音频文件中的异响
"""

import os
import numpy as np
import pandas as pd
import pickle
from optimized_feature_extractor import extract_features_from_audio
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class AudioAnomalyDetector:
    """
    音频异响检测器
    """
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.is_trained = False
        
        # 核心特征列表（基于重要性分析）
        self.core_features = [
            'vertical_line_count',
            'vertical_interference_ratio', 
            'avg_affected_freq_count',
            'low_freq_energy_ratio',
            'low_freq_energy_variation',
            'thd',
            'thdn',
            'noise_power_ratio',
            'spectral_energy_mean',
            'spectral_energy_kurtosis'
        ]
    
    def extract_audio_features(self, audio_path, min_duration=None):
        """
        提取音频特征
        
        参数:
        audio_path: 音频文件路径
        min_duration: 最小持续时间，None时自动判断
        
        返回:
        features_df: 特征DataFrame
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 自动判断参数
        if min_duration is None:
            min_duration = 156 if '156' in audio_path else 153
        
        print(f"[INFO] 提取特征: {os.path.basename(audio_path)}")
        
        try:
            features_df = extract_features_from_audio(
                audio_path,
                min_duration=min_duration,
                energy_threshold_db=-45
            )
            
            print(f"[SUCCESS] 成功提取 {len(features_df)} 个频段的特征")
            return features_df
            
        except Exception as e:
            print(f"[ERROR] 特征提取失败: {e}")
            return None
    
    def train_from_dataset(self, dataset_dir="dataset", save_model=True):
        """
        从数据集训练检测器
        
        参数:
        dataset_dir: 数据集目录
        save_model: 是否保存训练好的模型
        """
        from optimized_feature_extractor import process_dataset
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import classification_report
        
        print("[INFO] 开始训练检测器...")
        
        # 处理数据集
        combined_df = process_dataset(dataset_dir, "training_features")
        
        if combined_df is None:
            raise ValueError("数据集处理失败")
        
        # 准备特征
        available_features = [f for f in self.core_features if f in combined_df.columns]
        if len(available_features) == 0:
            # 如果核心特征不可用，使用所有数值特征
            feature_cols = [col for col in combined_df.columns 
                           if col not in ['label', 'filename', 'segment_id', 'time_start', 
                                         'time_end', 'frequency', 'segment_length']]
            available_features = []
            for col in feature_cols:
                try:
                    pd.to_numeric(combined_df[col], errors='raise')
                    available_features.append(col)
                except:
                    continue
        
        self.feature_names = available_features
        print(f"[INFO] 使用 {len(self.feature_names)} 个特征")
        
        X = combined_df[self.feature_names].fillna(0)
        y = combined_df['label']
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 训练模型
        self.model = RandomForestClassifier(
            n_estimators=100, 
            random_state=42,
            class_weight='balanced'  # 处理类别不平衡
        )
        self.model.fit(X_train_scaled, y_train)
        
        # 评估
        y_pred = self.model.predict(X_test_scaled)
        print("\n[INFO] 模型性能:")
        print(classification_report(y_test, y_pred, target_names=['异常', '正常']))
        
        self.is_trained = True
        
        # 保存模型
        if save_model:
            self.save_model("audio_anomaly_detector.pkl")
        
        print("[SUCCESS] 检测器训练完成!")
    
    def save_model(self, filepath):
        """保存训练好的模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'core_features': self.core_features
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"[INFO] 模型已保存到: {filepath}")
    
    def load_model(self, filepath):
        """加载训练好的模型"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")
        
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']
        self.core_features = model_data.get('core_features', self.core_features)
        self.is_trained = True
        
        print(f"[INFO] 模型已从 {filepath} 加载")
    
    def detect_anomaly(self, audio_path, detailed=False):
        """
        检测音频异响
        
        参数:
        audio_path: 音频文件路径
        detailed: 是否返回详细结果
        
        返回:
        result: 检测结果字典
        """
        if not self.is_trained:
            raise ValueError("检测器未训练，请先训练或加载模型")
        
        # 提取特征
        features_df = self.extract_audio_features(audio_path)
        if features_df is None:
            return None
        
        # 准备特征
        X = features_df[self.feature_names].fillna(0)
        X_scaled = self.scaler.transform(X)
        
        # 预测
        predictions = self.model.predict(X_scaled)
        probabilities = self.model.predict_proba(X_scaled)
        
        # 统计结果
        total_segments = len(predictions)
        normal_segments = np.sum(predictions == 1)
        anomaly_segments = np.sum(predictions == 0)
        
        normal_ratio = normal_segments / total_segments
        avg_normal_prob = np.mean(probabilities[:, 1])
        
        # 整体判断
        if avg_normal_prob > 0.7:
            overall_status = "正常"
            confidence = "高"
        elif avg_normal_prob > 0.5:
            overall_status = "正常"
            confidence = "中"
        elif avg_normal_prob > 0.3:
            overall_status = "可疑"
            confidence = "中"
        else:
            overall_status = "异常"
            confidence = "高"
        
        result = {
            'file': os.path.basename(audio_path),
            'overall_status': overall_status,
            'confidence': confidence,
            'normal_probability': avg_normal_prob,
            'normal_segments': normal_segments,
            'anomaly_segments': anomaly_segments,
            'total_segments': total_segments,
            'normal_ratio': normal_ratio
        }
        
        if detailed:
            result['segment_predictions'] = predictions
            result['segment_probabilities'] = probabilities
            result['features'] = features_df
        
        return result
    
    def batch_detect(self, audio_dir, output_file=None):
        """
        批量检测音频文件
        
        参数:
        audio_dir: 音频文件目录
        output_file: 结果保存文件
        
        返回:
        results: 检测结果列表
        """
        if not os.path.exists(audio_dir):
            raise FileNotFoundError(f"目录不存在: {audio_dir}")
        
        audio_files = [f for f in os.listdir(audio_dir) if f.lower().endswith('.wav')]
        
        if len(audio_files) == 0:
            print("[WARN] 目录中没有找到WAV文件")
            return []
        
        print(f"[INFO] 开始批量检测 {len(audio_files)} 个文件...")
        
        results = []
        for i, audio_file in enumerate(audio_files, 1):
            audio_path = os.path.join(audio_dir, audio_file)
            print(f"[{i}/{len(audio_files)}] 检测: {audio_file}")
            
            try:
                result = self.detect_anomaly(audio_path)
                if result:
                    results.append(result)
                    print(f"  结果: {result['overall_status']} (置信度: {result['confidence']})")
            except Exception as e:
                print(f"  错误: {e}")
        
        # 保存结果
        if output_file and results:
            results_df = pd.DataFrame(results)
            results_df.to_csv(output_file, index=False)
            print(f"\n[INFO] 结果已保存到: {output_file}")
        
        return results

def quick_demo():
    """
    快速演示
    """
    print("🎯 音频异响检测器演示")
    print("=" * 50)
    
    detector = AudioAnomalyDetector()
    
    # 检查是否有预训练模型
    model_file = "audio_anomaly_detector.pkl"
    
    if os.path.exists(model_file):
        print("[INFO] 发现预训练模型，正在加载...")
        detector.load_model(model_file)
    else:
        print("[INFO] 未发现预训练模型，开始训练...")
        if os.path.exists("dataset"):
            detector.train_from_dataset()
        else:
            print("[ERROR] 数据集目录不存在，无法训练")
            return
    
    # 测试检测
    test_files = [
        "dataset/pos/录音_步进扫频_100Hz至20000Hz_20250714_152023_156.wav",
        "dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n[TEST] 检测文件: {os.path.basename(test_file)}")
            result = detector.detect_anomaly(test_file)
            if result:
                print(f"  状态: {result['overall_status']}")
                print(f"  置信度: {result['confidence']}")
                print(f"  正常概率: {result['normal_probability']:.3f}")
                print(f"  正常频段: {result['normal_segments']}/{result['total_segments']}")

if __name__ == "__main__":
    # quick_demo()
    # from easy_detector import AudioAnomalyDetector
    detector = AudioAnomalyDetector()
    detector.load_model("audio_anomaly_detector.pkl")  # 加载已训练模型
    # result = detector.detect_anomaly("your_audio.wav")
    results = detector.batch_detect("dataset/pos", "results_pos.csv")
    print(results)