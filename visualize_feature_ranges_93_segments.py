#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成93个频段中两个背景噪声特征的范围可视化对比
重点展示噪声样本与正常样本的特征范围差异
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import stft

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_feature_ranges_visualization():
    """生成特征范围可视化"""
    print("🎨 生成93个频段特征范围可视化对比")
    print("="*70)
    
    # 检查是否已有数据文件
    if os.path.exists('all_93_segments_background_noise.csv'):
        print("📊 加载已有的93个频段数据...")
        df = pd.read_csv('all_93_segments_background_noise.csv')
    else:
        print("📊 提取93个频段数据...")
        df = extract_all_segments_data()
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 生成可视化
    create_comprehensive_range_visualization(df, target_files)
    
    return df

def extract_all_segments_data():
    """提取所有93个频段的数据"""
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_segment_data = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                segment_features = extract_file_segments(audio_path, filename, target_files)
                if segment_features:
                    all_segment_data.extend(segment_features)
    
    df = pd.DataFrame(all_segment_data)
    df.to_csv('all_93_segments_background_noise.csv', index=False, encoding='utf-8-sig')
    return df

def extract_file_segments(audio_path, filename, target_files):
    """提取单个文件的所有频段特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_data = []
        
        # 分析所有93个频段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 提取背景噪声特征
            bg_features = extract_background_noise_features(segment_audio, sr, expected_freq)
            
            segment_info = {
                'filename': filename,
                'segment_idx': seg_idx,
                'expected_freq': expected_freq,
                'is_target': filename in target_files,
                'sample_type': 'target' if filename in target_files else 'normal',
                'background_noise_mean': bg_features['background_noise_mean'],
                'background_noise_median': bg_features['background_noise_median']
            }
            
            segment_data.append(segment_info)
        
        return segment_data
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_background_noise_features(audio, sr, expected_freq):
    """提取背景噪声特征"""
    features = {}
    
    try:
        # 标准化音频
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))
        
        # STFT分析
        f, t, Zxx = stft(audio, sr, nperseg=1024, noverlap=512)
        power_spectrum = np.abs(Zxx) ** 2
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 找到期望频率的索引
        expected_freq_idx = np.argmin(np.abs(f - expected_freq))
        
        # 计算背景噪声特征
        all_background_levels = []
        
        for frame_idx in range(power_spectrum.shape[1]):
            frame_power_db = power_db[:, frame_idx]
            
            # 背景噪声功率 (排除信号频率)
            signal_band_start = max(0, expected_freq_idx - 3)
            signal_band_end = min(len(f), expected_freq_idx + 3)
            
            background_mask = np.ones(len(f), dtype=bool)
            background_mask[signal_band_start:signal_band_end] = False
            
            if np.any(background_mask):
                background_power_mean = np.mean(frame_power_db[background_mask])
                all_background_levels.append(background_power_mean)
        
        # 统计特征
        if all_background_levels:
            features['background_noise_mean'] = np.mean(all_background_levels)
            features['background_noise_median'] = np.median(all_background_levels)
        else:
            features['background_noise_mean'] = -120
            features['background_noise_median'] = -120
            
    except Exception as e:
        features['background_noise_mean'] = -120
        features['background_noise_median'] = -120
    
    return features

def create_comprehensive_range_visualization(df, target_files):
    """创建综合的特征范围可视化"""
    print(f"\n🎨 生成综合特征范围可视化...")
    
    # 创建大图
    fig = plt.figure(figsize=(24, 16))
    
    # 准备数据
    segments = sorted(df['segment_idx'].unique())
    frequencies = [df[df['segment_idx'] == seg]['expected_freq'].iloc[0] for seg in segments]
    
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    # 1. background_noise_median 范围对比 (主图)
    ax1 = plt.subplot(3, 2, (1, 2))
    
    plot_feature_ranges(ax1, df, 'background_noise_median', segments, frequencies, 
                       '背景噪声中位数范围对比 (93个频段)', 'background_noise_median (dB)')
    
    # 2. background_noise_mean 范围对比 (主图)
    ax2 = plt.subplot(3, 2, (3, 4))
    
    plot_feature_ranges(ax2, df, 'background_noise_mean', segments, frequencies,
                       '背景噪声平均值范围对比 (93个频段)', 'background_noise_mean (dB)')
    
    # 3. 分离间隙分析
    ax3 = plt.subplot(3, 2, 5)
    
    plot_separation_gaps(ax3, df, segments, frequencies)
    
    # 4. 特征分布对比 (选择几个关键频段)
    ax4 = plt.subplot(3, 2, 6)
    
    plot_key_segments_distribution(ax4, df, segments, frequencies)
    
    plt.tight_layout()
    plt.savefig('feature_ranges_93_segments_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: feature_ranges_93_segments_comparison.png")
    
    # 生成详细的范围统计
    generate_range_statistics(df, target_files, segments, frequencies)

def plot_feature_ranges(ax, df, feature_name, segments, frequencies, title, ylabel):
    """绘制特征范围对比图"""
    
    target_mins = []
    target_maxs = []
    normal_mins = []
    normal_maxs = []
    separation_gaps = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_values = seg_data[seg_data['is_target'] == True][feature_name]
        normal_values = seg_data[seg_data['is_target'] == False][feature_name]
        
        if len(target_values) > 0 and len(normal_values) > 0:
            target_min, target_max = np.min(target_values), np.max(target_values)
            normal_min, normal_max = np.min(normal_values), np.max(normal_values)
            
            target_mins.append(target_min)
            target_maxs.append(target_max)
            normal_mins.append(normal_min)
            normal_maxs.append(normal_max)
            
            # 计算分离间隙
            if target_max < normal_min:
                gap = normal_min - target_max
            elif target_min > normal_max:
                gap = target_min - normal_max
            else:
                gap = 0
            separation_gaps.append(gap)
        else:
            target_mins.append(np.nan)
            target_maxs.append(np.nan)
            normal_mins.append(np.nan)
            normal_maxs.append(np.nan)
            separation_gaps.append(0)
    
    # 绘制范围带
    x = np.array(segments)
    
    # 正常样本范围 (蓝色)
    ax.fill_between(x, normal_mins, normal_maxs, alpha=0.3, color='blue', label='正常样本范围')
    ax.plot(x, normal_mins, 'b-', linewidth=1, alpha=0.7, label='正常样本最小值')
    ax.plot(x, normal_maxs, 'b-', linewidth=1, alpha=0.7, label='正常样本最大值')
    
    # 噪声样本范围 (红色)
    ax.fill_between(x, target_mins, target_maxs, alpha=0.5, color='red', label='噪声样本范围')
    ax.plot(x, target_mins, 'r-', linewidth=2, label='噪声样本最小值')
    ax.plot(x, target_maxs, 'r-', linewidth=2, label='噪声样本最大值')
    
    # 标记分离的频段
    separable_segments = [i for i, gap in enumerate(separation_gaps) if gap > 0]
    if separable_segments:
        ax.scatter([segments[i] for i in separable_segments], 
                  [target_maxs[i] for i in separable_segments],
                  color='yellow', s=100, marker='*', edgecolor='black', 
                  label=f'可分离频段 ({len(separable_segments)}个)', zorder=5)
    
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel(ylabel)
    ax.legend(loc='upper right', fontsize=8)
    ax.grid(True, alpha=0.3)
    
    # 添加频率标签
    freq_ticks = range(0, len(segments), 15)
    freq_labels = [f'{frequencies[i]:.0f}Hz' for i in freq_ticks if i < len(frequencies)]
    ax2 = ax.twiny()
    ax2.set_xlim(ax.get_xlim())
    ax2.set_xticks([segments[i] for i in freq_ticks])
    ax2.set_xticklabels(freq_labels, rotation=45)
    ax2.set_xlabel('频率 (Hz)')
    
    # 添加统计信息
    separable_count = len(separable_segments)
    total_count = len(segments)
    max_gap = max(separation_gaps) if separation_gaps else 0
    
    stats_text = f'可分离: {separable_count}/{total_count} ({separable_count/total_count*100:.1f}%)\n最大间隙: {max_gap:.2f}dB'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7), fontsize=9)

def plot_separation_gaps(ax, df, segments, frequencies):
    """绘制分离间隙分析"""
    
    median_gaps = []
    mean_gaps = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_data = seg_data[seg_data['is_target'] == True]
        normal_data = seg_data[seg_data['is_target'] == False]
        
        # 计算两个特征的分离间隙
        gaps = []
        
        for feature in ['background_noise_median', 'background_noise_mean']:
            target_values = target_data[feature]
            normal_values = normal_data[feature]
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_min, target_max = np.min(target_values), np.max(target_values)
                normal_min, normal_max = np.min(normal_values), np.max(normal_values)
                
                if target_max < normal_min:
                    gap = normal_min - target_max
                elif target_min > normal_max:
                    gap = target_min - normal_max
                else:
                    gap = 0
                gaps.append(gap)
        
        if len(gaps) >= 2:
            median_gaps.append(gaps[0])  # background_noise_median
            mean_gaps.append(gaps[1])    # background_noise_mean
        else:
            median_gaps.append(0)
            mean_gaps.append(0)
    
    # 绘制分离间隙
    x = np.array(segments)
    
    ax.plot(x, median_gaps, 'ro-', linewidth=2, markersize=4, label='median分离间隙', alpha=0.8)
    ax.plot(x, mean_gaps, 'bo-', linewidth=2, markersize=4, label='mean分离间隙', alpha=0.8)
    
    # 标记最佳分离频段
    best_median_idx = np.argmax(median_gaps)
    best_mean_idx = np.argmax(mean_gaps)
    
    ax.scatter(segments[best_median_idx], median_gaps[best_median_idx], 
              color='red', s=150, marker='*', edgecolor='black', zorder=5)
    ax.scatter(segments[best_mean_idx], mean_gaps[best_mean_idx], 
              color='blue', s=150, marker='*', edgecolor='black', zorder=5)
    
    ax.set_title('各频段分离间隙对比', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段索引')
    ax.set_ylabel('分离间隙 (dB)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加最佳频段信息
    best_info = f'最佳median: 频段{segments[best_median_idx]} ({frequencies[best_median_idx]:.0f}Hz, {median_gaps[best_median_idx]:.2f}dB)\n'
    best_info += f'最佳mean: 频段{segments[best_mean_idx]} ({frequencies[best_mean_idx]:.0f}Hz, {mean_gaps[best_mean_idx]:.2f}dB)'
    
    ax.text(0.02, 0.98, best_info, transform=ax.transAxes, va='top', ha='left',
            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7), fontsize=8)

def plot_key_segments_distribution(ax, df, segments, frequencies):
    """绘制关键频段的特征分布"""
    
    # 选择几个关键频段进行详细分析
    key_segments = [0, 10, 20, 30, 40, 50, 60, 70, 80, 92]  # 代表性频段
    
    target_medians = []
    normal_medians = []
    segment_labels = []
    
    for seg_idx in key_segments:
        if seg_idx < len(segments):
            seg_data = df[df['segment_idx'] == segments[seg_idx]]
            target_values = seg_data[seg_data['is_target'] == True]['background_noise_median']
            normal_values = seg_data[seg_data['is_target'] == False]['background_noise_median']
            
            if len(target_values) > 0 and len(normal_values) > 0:
                target_medians.append(np.median(target_values))
                normal_medians.append(np.median(normal_values))
                segment_labels.append(f'Seg{segments[seg_idx]}\n{frequencies[seg_idx]:.0f}Hz')
    
    # 绘制对比柱状图
    x = np.arange(len(segment_labels))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, normal_medians, width, label='正常样本', color='blue', alpha=0.7)
    bars2 = ax.bar(x + width/2, target_medians, width, label='噪声样本', color='red', alpha=0.7)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    ax.set_title('关键频段背景噪声中位数对比', fontsize=12, fontweight='bold')
    ax.set_xlabel('频段')
    ax.set_ylabel('背景噪声中位数 (dB)')
    ax.set_xticks(x)
    ax.set_xticklabels(segment_labels, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)

def generate_range_statistics(df, target_files, segments, frequencies):
    """生成详细的范围统计"""
    print(f"\n📊 特征范围统计报告")
    print("="*70)
    
    # 计算每个频段的分离统计
    separable_median_segments = []
    separable_mean_segments = []
    
    for seg_idx in segments:
        seg_data = df[df['segment_idx'] == seg_idx]
        target_data = seg_data[seg_data['is_target'] == True]
        normal_data = seg_data[seg_data['is_target'] == False]
        
        freq = frequencies[seg_idx]
        
        # background_noise_median分离分析
        target_median_values = target_data['background_noise_median']
        normal_median_values = normal_data['background_noise_median']
        
        if len(target_median_values) > 0 and len(normal_median_values) > 0:
            target_min, target_max = np.min(target_median_values), np.max(target_median_values)
            normal_min, normal_max = np.min(normal_median_values), np.max(normal_median_values)
            
            if target_max < normal_min or target_min > normal_max:
                gap = max(normal_min - target_max, target_min - normal_max)
                separable_median_segments.append({
                    'segment': seg_idx,
                    'frequency': freq,
                    'gap': gap,
                    'target_range': [target_min, target_max],
                    'normal_range': [normal_min, normal_max]
                })
        
        # background_noise_mean分离分析
        target_mean_values = target_data['background_noise_mean']
        normal_mean_values = normal_data['background_noise_mean']
        
        if len(target_mean_values) > 0 and len(normal_mean_values) > 0:
            target_min, target_max = np.min(target_mean_values), np.max(target_mean_values)
            normal_min, normal_max = np.min(normal_mean_values), np.max(normal_mean_values)
            
            if target_max < normal_min or target_min > normal_max:
                gap = max(normal_min - target_max, target_min - normal_max)
                separable_mean_segments.append({
                    'segment': seg_idx,
                    'frequency': freq,
                    'gap': gap,
                    'target_range': [target_min, target_max],
                    'normal_range': [normal_min, normal_max]
                })
    
    # 排序并显示结果
    separable_median_segments.sort(key=lambda x: x['gap'], reverse=True)
    separable_mean_segments.sort(key=lambda x: x['gap'], reverse=True)
    
    print(f"🎯 background_noise_median 可分离频段:")
    print(f"   总数: {len(separable_median_segments)}/{len(segments)} ({len(separable_median_segments)/len(segments)*100:.1f}%)")
    
    if separable_median_segments:
        print(f"   前5个最佳分离频段:")
        for i, seg_info in enumerate(separable_median_segments[:5]):
            print(f"     {i+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): 间隙{seg_info['gap']:.2f}dB")
            print(f"        噪声范围: [{seg_info['target_range'][0]:.2f}, {seg_info['target_range'][1]:.2f}]")
            print(f"        正常范围: [{seg_info['normal_range'][0]:.2f}, {seg_info['normal_range'][1]:.2f}]")
    
    print(f"\n🎯 background_noise_mean 可分离频段:")
    print(f"   总数: {len(separable_mean_segments)}/{len(segments)} ({len(separable_mean_segments)/len(segments)*100:.1f}%)")
    
    if separable_mean_segments:
        print(f"   前5个最佳分离频段:")
        for i, seg_info in enumerate(separable_mean_segments[:5]):
            print(f"     {i+1}. 频段{seg_info['segment']:2d} ({seg_info['frequency']:6.1f}Hz): 间隙{seg_info['gap']:.2f}dB")
            print(f"        噪声范围: [{seg_info['target_range'][0]:.2f}, {seg_info['target_range'][1]:.2f}]")
            print(f"        正常范围: [{seg_info['normal_range'][0]:.2f}, {seg_info['normal_range'][1]:.2f}]")

if __name__ == "__main__":
    df = generate_feature_ranges_visualization()
    print(f"\n✅ 93个频段特征范围可视化完成！")
