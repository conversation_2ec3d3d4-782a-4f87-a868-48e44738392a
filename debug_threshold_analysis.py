#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试阈值选择逻辑，分析为什么有些样本没有选择第一个满足阈值的点
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
import os
import glob
from final_audio_detection_system.freq_split_optimized import OptimizedFreqSplitter

def analyze_threshold_selection(audio_path, debug=True):
    """分析单个文件的阈值选择逻辑"""
    print(f"\n🔍 分析阈值选择: {os.path.basename(audio_path)}")
    print("="*60)
    
    # 创建分割器
    splitter = OptimizedFreqSplitter()
    
    # 读取音频
    y, sr = librosa.load(audio_path, sr=splitter.fs)
    if sr != splitter.fs:
        y = librosa.resample(y, orig_sr=sr, target_sr=splitter.fs)
    
    # 搜索参数
    search_start = 0.1
    search_end = 2.0
    corr_length = 1.0
    
    # 执行搜索
    search_start_sample = int(search_start * splitter.fs)
    search_end_sample = int(search_end * splitter.fs)
    corr_length_samples = int(corr_length * splitter.fs)
    
    search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
    if search_start_sample >= search_end_sample:
        print("❌ 搜索范围无效")
        return None
    
    # 准备参考信号段
    initial_corr_length = min(corr_length_samples, int(0.5 * splitter.fs))
    reference_segment = splitter.reference_signal[:initial_corr_length]
    
    # 计算相关性
    search_length = search_end_sample - search_start_sample
    correlations = []
    positions = []
    
    step_size = max(1, splitter.fs // 1000)
    for i in range(0, search_length - initial_corr_length, step_size):
        pos = search_start_sample + i
        test_segment = y[pos:pos + initial_corr_length]
        
        if len(test_segment) == len(reference_segment):
            correlation = splitter._normalized_cross_correlation(reference_segment, test_segment)
            correlations.append(correlation)
            positions.append(pos / splitter.fs)
    
    if not correlations:
        print("❌ 未找到有效的相关性计算结果")
        return None
    
    correlations = np.array(correlations)
    positions = np.array(positions)
    
    # 分析阈值选择逻辑
    max_correlation = np.max(correlations)
    max_idx = np.argmax(correlations)
    
    # 当前的阈值设置
    relative_threshold = max_correlation * 0.7  # 70%
    absolute_threshold = 0.4
    final_threshold = max(relative_threshold, absolute_threshold)
    
    # 找到符合条件的点
    good_indices = np.where(correlations >= final_threshold)[0]
    
    print(f"📊 阈值分析:")
    print(f"  最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
    print(f"  70%相对阈值: {relative_threshold:.3f}")
    print(f"  绝对阈值: {absolute_threshold:.3f}")
    print(f"  最终阈值: {final_threshold:.3f}")
    print(f"  符合条件的点: {len(good_indices)}个")
    
    if len(good_indices) > 0:
        first_good_idx = good_indices[0]
        first_good_pos = positions[first_good_idx]
        first_good_corr = correlations[first_good_idx]
        
        print(f"  ✅ 选择第一个满足条件的点:")
        print(f"    位置: {first_good_pos:.3f}s")
        print(f"    相关性: {first_good_corr:.3f}")
        
        # 显示前几个符合条件的点
        print(f"  前5个符合条件的点:")
        for i, idx in enumerate(good_indices[:5]):
            print(f"    {i+1}. 时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.3f}")
    else:
        print(f"  ❌ 没有满足条件的点，将使用最大相关性点")
        print(f"    原因分析:")
        
        # 分析为什么没有满足条件的点
        if max_correlation < absolute_threshold:
            print(f"      - 最大相关性({max_correlation:.3f}) < 绝对阈值({absolute_threshold:.3f})")
            print(f"      - 建议: 降低绝对阈值到 {max_correlation * 0.8:.3f}")
        else:
            print(f"      - 相对阈值({relative_threshold:.3f}) > 绝对阈值({absolute_threshold:.3f})")
            print(f"      - 最终阈值被相对阈值主导")
            print(f"      - 建议: 降低相对阈值百分比到 60% 或 50%")
    
    # 分析相关性分布
    print(f"\n📈 相关性分布分析:")
    print(f"  相关性范围: {np.min(correlations):.3f} - {np.max(correlations):.3f}")
    print(f"  平均相关性: {np.mean(correlations):.3f}")
    print(f"  标准差: {np.std(correlations):.3f}")
    
    # 统计不同阈值下的符合条件点数
    thresholds = [0.3, 0.4, 0.5, max_correlation * 0.5, max_correlation * 0.6, max_correlation * 0.7]
    print(f"  不同阈值下的符合条件点数:")
    for thresh in thresholds:
        count = len(np.where(correlations >= thresh)[0])
        print(f"    阈值{thresh:.3f}: {count}个点")
    
    return {
        'max_correlation': max_correlation,
        'relative_threshold': relative_threshold,
        'absolute_threshold': absolute_threshold,
        'final_threshold': final_threshold,
        'good_points_count': len(good_indices),
        'correlations': correlations,
        'positions': positions
    }

def test_multiple_samples():
    """测试多个样本的阈值选择"""
    print("🔍 批量分析阈值选择逻辑")
    print("="*70)
    
    # 找到一些测试文件
    test_files = []
    
    # test20250717文件夹
    test_folders = ["../test20250717", "../待定"]
    for folder in test_folders:
        if os.path.exists(folder):
            pattern = os.path.join(folder, "**/*.wav")
            files = glob.glob(pattern, recursive=True)
            test_files.extend(files[:3])  # 每个文件夹取3个样本
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    results = []
    for i, file_path in enumerate(test_files):
        print(f"\n[{i+1}/{len(test_files)}]")
        try:
            result = analyze_threshold_selection(file_path, debug=True)
            if result:
                result['filename'] = os.path.basename(file_path)
                results.append(result)
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
    
    # 汇总分析
    print(f"\n📊 汇总分析:")
    print("="*70)
    
    if results:
        max_corrs = [r['max_correlation'] for r in results]
        good_counts = [r['good_points_count'] for r in results]
        
        print(f"样本数量: {len(results)}")
        print(f"最大相关性范围: {np.min(max_corrs):.3f} - {np.max(max_corrs):.3f}")
        print(f"平均最大相关性: {np.mean(max_corrs):.3f}")
        
        no_good_points = len([r for r in results if r['good_points_count'] == 0])
        print(f"没有符合条件点的样本: {no_good_points}/{len(results)} ({no_good_points/len(results)*100:.1f}%)")
        
        if no_good_points > 0:
            print(f"\n💡 优化建议:")
            print(f"  - 当前有 {no_good_points} 个样本没有符合条件的点")
            print(f"  - 建议降低相对阈值从70%到60%或50%")
            print(f"  - 或者降低绝对阈值从0.4到0.3")

if __name__ == "__main__":
    test_multiple_samples()
