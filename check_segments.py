#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查频段数量
"""

import pandas as pd
import numpy as np

def check_segments():
    """检查频段数量"""
    print("🔍 检查频段数量")
    print("="*50)
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"总行数: {len(df)}")
    
    print(f"\n各文件频段数:")
    print("-"*50)
    
    for filename in sorted(df['filename'].unique()):
        file_data = df[df['filename'] == filename]
        label = file_data['label'].iloc[0]
        segment_count = len(file_data)
        max_segment_idx = file_data['segment_idx'].max()
        min_segment_idx = file_data['segment_idx'].min()
        
        print(f"{filename} ({label}): {segment_count}个频段 (索引: {min_segment_idx}-{max_segment_idx})")
    
    print(f"\n频段数量统计:")
    print("-"*50)
    segment_counts = df.groupby('filename').size()
    print(f"最少频段数: {segment_counts.min()}")
    print(f"最多频段数: {segment_counts.max()}")
    print(f"平均频段数: {segment_counts.mean():.1f}")
    
    print(f"\n按标签统计:")
    print("-"*50)
    for label in ['pos', 'neg']:
        label_files = df[df['label'] == label]['filename'].unique()
        label_segments = df[df['label'] == label].groupby('filename').size()
        print(f"{label}样本: {len(label_files)}个文件, 频段数范围: {label_segments.min()}-{label_segments.max()}")
    
    print(f"\n检查是否应该有93个频段:")
    print("-"*50)
    files_with_93 = segment_counts[segment_counts == 93]
    files_with_less = segment_counts[segment_counts < 93]
    
    print(f"有93个频段的文件: {len(files_with_93)}个")
    print(f"少于93个频段的文件: {len(files_with_less)}个")
    
    if len(files_with_less) > 0:
        print(f"少于93个频段的文件详情:")
        for filename, count in files_with_less.items():
            print(f"  {filename}: {count}个频段")

if __name__ == "__main__":
    check_segments()
