#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析低音戳洞前4段的谐波和噪声特征
可视化各段的谐波、噪声阈值图
使用与谐波检测系统完全一致的方法和参数
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入完全相同的算法
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

def estimate_dynamic_noise_for_segment_with_details(freqs, power, fundamental_freq):
    """
    为单个频段估计动态噪声 - 与原始方法完全一致，但返回详细数据用于可视化
    """

    # 滑动窗口参数 - 与原始方法一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长

    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)

    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude

    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude

    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []

    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True

        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask

        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile

            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])

    if len(local_noise_levels) == 0:
        return None

    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels,
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels

    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)

    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)

    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features,
        # 额外返回用于可视化的数据
        'local_noise_levels': smoothed_noise,
        'window_centers': window_centers,
        'raw_noise_levels': local_noise_levels,
        'exclude_mask': exclude_mask
    }

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def analyze_single_segment_detailed(audio_path, seg_idx, start_time, end_time, expected_freq, y, sr):
    """
    详细分析单个频段 - 使用与谐波检测系统完全相同的方法
    
    Args:
        audio_path: 音频文件路径
        seg_idx: 段索引
        start_time: 开始时间
        end_time: 结束时间
        expected_freq: 期望频率
        y: 音频数据
        sr: 采样率
    
    Returns:
        dict: 详细分析结果
    """
    
    try:
        print(f"\n🔍 详细分析段{seg_idx} ({expected_freq:.0f}Hz)")
        print("-" * 50)
        
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print("❌ 段音频为空")
            return None
        
        print(f"📊 段时长: {len(segment_audio)/sr:.3f}秒 ({len(segment_audio)}个样本)")
        
        # 标准化 - 与谐波检测系统一致
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
            print(f"✅ 音频已标准化")
        
        # 高分辨率FFT分析 - 与谐波检测系统一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            print(f"📈 零填充到{fft_size}点")
        else:
            segment_audio = segment_audio[:fft_size]
            print(f"📈 截取到{fft_size}点")
        
        # 应用窗函数 - 与谐波检测系统一致
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        print(f"🪟 应用Hanning窗")
        
        # FFT - 与谐波检测系统一致
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率 - 与谐波检测系统一致
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz - 与谐波检测系统一致
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        print(f"📊 频谱范围: {display_freqs[0]:.1f}Hz - {display_freqs[-1]:.1f}Hz ({len(display_freqs)}个点)")
        
        # 找主频 - 与谐波检测系统一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
            fundamental_power = display_power[actual_idx]
            fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
            
            print(f"🎯 主频定位: {fundamental_freq:.2f}Hz (期望{expected_freq:.0f}Hz)")
            print(f"📊 主频功率: {fundamental_power_db:.2f}dB")
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(display_power) + 1e-12)
            print(f"⚠️  主频搜索失败，使用期望频率: {fundamental_freq:.0f}Hz")
        
        # 动态噪声分析 - 使用与谐波检测系统完全相同的算法
        print(f"🔍 开始动态噪声分析...")
        noise_analysis = estimate_dynamic_noise_for_segment_with_details(display_freqs, display_power, fundamental_freq)
        
        if noise_analysis:
            print(f"✅ 噪声分析成功:")
            print(f"  全局噪声底噪: {noise_analysis['global_noise_floor_db']:.2f}dB")
            print(f"  噪声变化范围: {noise_analysis['noise_variation_db']:.2f}dB")
            print(f"  稳定性评分: {noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']:.3f}")
            
            # 计算信噪比
            snr_db = fundamental_power_db - noise_analysis['global_noise_floor_db']
            print(f"  信噪比: {snr_db:.2f}dB")
        else:
            print(f"❌ 噪声分析失败")
            return None
        
        # 谐波检测 - 使用与谐波检测系统完全相同的算法
        print(f"🎵 开始谐波检测...")
        harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
        
        if harmonic_analysis:
            print(f"✅ 检测到{len(harmonic_analysis)}个谐波:")
            for i, harmonic in enumerate(harmonic_analysis[:5]):  # 只显示前5个
                print(f"  {harmonic['order']}次谐波: {harmonic['freq']:.1f}Hz, {harmonic['power_db']:.1f}dB, SNR:{harmonic['global_snr_db']:.1f}dB")
            if len(harmonic_analysis) > 5:
                print(f"  ... 还有{len(harmonic_analysis)-5}个谐波")
        else:
            print(f"❌ 未检测到谐波")
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'display_freqs': display_freqs,
            'display_power': display_power,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'harmonic_count': len(harmonic_analysis) if harmonic_analysis else 0,
            'snr_db': snr_db if noise_analysis else 0
        }
        
    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_segment_visualization(segment_results, output_path=None, title_prefix=""):
    """创建前4段的详细可视化"""

    if not segment_results:
        print("❌ 无有效分析结果")
        return None

    # 创建2x2子图布局
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle(f'{title_prefix}前4段谐波和噪声特征分析\n使用与谐波检测系统完全一致的算法',
                 fontsize=16, fontweight='bold')
    
    for i, result in enumerate(segment_results):
        if i >= 4:  # 只显示前4段
            break
        
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 提取数据
        freqs = result['display_freqs']
        power = result['display_power']
        power_db = 10 * np.log10(power + 1e-12)
        
        fundamental_freq = result['fundamental_freq']
        noise_analysis = result['noise_analysis']
        harmonic_analysis = result['harmonic_analysis']
        
        # 绘制频谱
        ax.plot(freqs, power_db, 'b-', linewidth=1, alpha=0.7, label='频谱')
        
        # 标记主频
        ax.axvline(fundamental_freq, color='red', linestyle='-', linewidth=2, 
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')
        
        # 绘制噪声底噪线
        if noise_analysis:
            noise_floor = noise_analysis['global_noise_floor_db']
            ax.axhline(noise_floor, color='green', linestyle='--', linewidth=2,
                      alpha=0.8, label=f'全局噪声底噪: {noise_floor:.1f}dB')

            # 绘制动态噪声阈值线
            if 'local_noise_levels' in noise_analysis and 'window_centers' in noise_analysis:
                local_noise = noise_analysis['local_noise_levels']
                window_centers = noise_analysis['window_centers']

                # 显示动态噪声曲线
                ax.plot(window_centers, local_noise, color='cyan', linestyle='-',
                       linewidth=2, alpha=0.8, label='动态噪声阈值')

                # 填充动态噪声区域
                ax.fill_between(window_centers, local_noise, noise_floor,
                               alpha=0.2, color='cyan', label='噪声变化区域')

            # 标记排除的谐波区域
            if 'exclude_mask' in noise_analysis:
                exclude_mask = noise_analysis['exclude_mask']
                excluded_freqs = freqs[exclude_mask]
                excluded_powers = power_db[exclude_mask]

                # 只显示当前频率范围内的排除点
                freq_range_mask = (excluded_freqs >= 0) & (excluded_freqs <= min(5000, freqs[-1]))
                if np.any(freq_range_mask):
                    display_excluded_freqs = excluded_freqs[freq_range_mask]
                    display_excluded_powers = excluded_powers[freq_range_mask]

                    ax.scatter(display_excluded_freqs, display_excluded_powers,
                              c='gray', s=1, alpha=0.5, label='排除区域(谐波)')

            # 绘制检测阈值线 (根据噪声变化和稳定性调整)
            noise_variation_db = noise_analysis['noise_variation_db']
            stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']

            # 使用与谐波检测相同的阈值计算逻辑
            if noise_variation_db > 10:
                base_snr_threshold = 8.0
            elif noise_variation_db > 5:
                base_snr_threshold = 10.0
            else:
                base_snr_threshold = 12.0

            if stability_score > 0.8:
                stability_adjustment = 1.0
            elif stability_score > 0.6:
                stability_adjustment = 0.0
            else:
                stability_adjustment = -1.0

            adjusted_snr_threshold = base_snr_threshold + stability_adjustment
            detection_threshold = noise_floor + adjusted_snr_threshold

            ax.axhline(detection_threshold, color='orange', linestyle='--', linewidth=1.5,
                      alpha=0.8, label=f'谐波检测阈值: {detection_threshold:.1f}dB')
        
        # 标记检测到的谐波
        if harmonic_analysis:
            harmonic_freqs = [h['freq'] for h in harmonic_analysis]
            harmonic_powers = [h['power_db'] for h in harmonic_analysis]
            harmonic_orders = [h['order'] for h in harmonic_analysis]
            
            ax.scatter(harmonic_freqs, harmonic_powers, c='red', s=50, alpha=0.8, 
                      marker='^', label=f'谐波({len(harmonic_analysis)}个)')
            
            # 标注前几个谐波的次数
            for j, (freq, power, order) in enumerate(zip(harmonic_freqs[:5], harmonic_powers[:5], harmonic_orders[:5])):
                ax.annotate(f'{order}', (freq, power), xytext=(5, 5), 
                           textcoords='offset points', fontsize=8, color='red')
        
        # 设置坐标轴
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_title(f'段{result["seg_idx"]} - {result["expected_freq"]:.0f}Hz\n'
                    f'SNR: {result["snr_db"]:.1f}dB, 谐波数: {result["harmonic_count"]}个')
        
        # 设置频率范围 (显示完整频域)
        ax.set_xlim(0, min(5000, freqs[-1]))  # 显示0-5kHz或最大频率
        
        # 设置功率范围
        if noise_analysis:
            power_min = noise_floor - 20
            power_max = result['fundamental_power_db'] + 10
            ax.set_ylim(power_min, power_max)
        
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)
    
    plt.tight_layout()
    
    # 保存图片
    if output_path is None:
        output_path = "low_freq_hole_first_4_segments_analysis.png"
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def analyze_file(file_pattern, description):
    """分析指定模式的文件"""

    print(f"🎯 分析{description}前4段的谐波和噪声特征")
    print("="*60)
    print("使用与谐波检测系统完全一致的方法和参数")
    print()

    # 查找目标文件
    target_files = []

    # 在test20250717目录中查找
    test_dir = "test20250717"
    if os.path.exists(test_dir):
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file_pattern in file and file.lower().endswith('.wav'):
                    target_files.append(os.path.join(root, file))

    if not target_files:
        print(f"❌ 未找到包含'{file_pattern}'的wav文件")
        return None

    target_file = target_files[0]
    print(f"📁 分析文件: {target_file}")
    print()
    
    try:
        # 获取频段分割 - 与谐波检测系统一致
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频 - 与谐波检测系统一致
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"📊 音频信息: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        print(f"📊 总段数: {len(step_boundaries)}段")
        print()
        
        # 分析前4段
        segment_results = []
        
        for seg_idx in range(min(4, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            result = analyze_single_segment_detailed(
                target_file, seg_idx, start_time, end_time, expected_freq, y, sr
            )
            
            if result:
                segment_results.append(result)
        
        print(f"\n✅ 成功分析{len(segment_results)}段")
        
        # 生成可视化
        if segment_results:
            viz_path = create_segment_visualization(segment_results)
            if viz_path:
                print(f"📊 可视化已保存: {viz_path}")
            
            # 打印汇总信息
            print(f"\n📊 前4段汇总:")
            print(f"{'段号':<4} {'频率(Hz)':<8} {'SNR(dB)':<8} {'谐波数':<6} {'噪声底噪(dB)':<12}")
            print("-" * 50)
            
            for result in segment_results:
                noise_floor = result['noise_analysis']['global_noise_floor_db'] if result['noise_analysis'] else 0
                print(f"{result['seg_idx']:<4} {result['expected_freq']:<8.0f} {result['snr_db']:<8.1f} "
                      f"{result['harmonic_count']:<6} {noise_floor:<12.2f}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
