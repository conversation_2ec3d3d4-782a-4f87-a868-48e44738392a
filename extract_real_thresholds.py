#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从pos文件夹分析中提取真实的93段最大值阈值
"""

import os
import sys
import numpy as np
import librosa
from scipy.signal import savgol_filter
import multiprocessing as mp
from multiprocessing import Pool

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

def extract_real_thresholds():
    """提取真实阈值"""
    print("🎯 从pos文件夹提取真实的93段最大值阈值")
    print("="*60)
    
    # pos文件夹路径
    pos_dir = os.path.join('test20250717', 'pos')
    
    if not os.path.exists(pos_dir):
        print("❌ pos文件夹不存在")
        return None
    
    # 查找pos文件夹下的所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(pos_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"📊 找到{len(wav_files)}个pos文件")
    
    if len(wav_files) == 0:
        print("❌ pos文件夹中未找到wav文件")
        return None
    
    # 使用多进程分析所有文件
    print("🚀 使用多进程分析所有pos文件...")
    
    num_processes = min(mp.cpu_count(), len(wav_files))
    with Pool(processes=num_processes) as pool:
        results = pool.map(analyze_single_pos_file, wav_files)
    
    # 过滤成功的结果
    successful_results = [r for r in results if r is not None]
    
    print(f"✅ 成功分析{len(successful_results)}个文件")
    
    if len(successful_results) == 0:
        print("❌ 没有成功分析的文件")
        return None
    
    # 计算各段的最大值
    real_thresholds = calculate_real_max_thresholds(successful_results)
    
    # 打印结果
    print_real_thresholds(real_thresholds)
    
    return real_thresholds

def analyze_single_pos_file(audio_path):
    """分析单个pos文件"""
    try:
        print(f"  分析: {os.path.basename(audio_path)}")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                segments_data.append({
                    'seg_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'harmonic_count': 0
                })
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072  # 128k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 简化的谐波检测（使用与检测器相同的逻辑）
            harmonic_count = detect_harmonics_simple(display_freqs, display_power, fundamental_freq)
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'harmonic_count': harmonic_count
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {os.path.basename(audio_path)} - {e}")
        return None

def detect_harmonics_simple(freqs, power, fundamental_freq):
    """简化的谐波检测"""
    
    detected_harmonics = []
    
    # 基础SNR阈值
    base_snr_threshold = 10.0
    
    # 估算全局噪声底噪
    power_db = 10 * np.log10(power + 1e-12)
    global_noise_floor_db = np.percentile(power_db, 20)
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        if (global_snr_db >= adjusted_snr_threshold and 
            abs(freq_error) <= search_bandwidth * 0.8):
            detected_harmonics.append(order)
    
    return len(detected_harmonics)

def calculate_real_max_thresholds(successful_results):
    """计算真实的最大值阈值"""
    
    # 确定段数
    num_segments = len(successful_results[0]['segments'])
    
    real_thresholds = []
    
    for seg_idx in range(num_segments):
        # 收集该段所有文件的谐波数量
        harmonic_counts = []
        
        for result in successful_results:
            if seg_idx < len(result['segments']):
                harmonic_counts.append(result['segments'][seg_idx]['harmonic_count'])
        
        if harmonic_counts:
            max_harmonics = max(harmonic_counts)
            real_thresholds.append(max_harmonics)
        else:
            real_thresholds.append(1)  # 默认阈值
    
    return real_thresholds

def print_real_thresholds(real_thresholds):
    """打印真实阈值"""
    
    print(f"\n📊 真实的93段最大值阈值:")
    print(f"{'='*60}")
    
    # 计算频率
    freqs = []
    for i in range(93):
        freq = 100 * (2**(i/12))
        freqs.append(freq)
    
    print("段号  频率(Hz)   阈值  |  段号  频率(Hz)   阈值  |  段号  频率(Hz)   阈值")
    print("-"*75)
    
    # 分3列显示
    for i in range(0, 93, 3):
        line = ""
        for j in range(3):
            if i+j < 93:
                seg_idx = i+j
                freq = freqs[seg_idx]
                threshold = real_thresholds[seg_idx]
                line += f"{seg_idx:3d}  {freq:7.0f}Hz   {threshold:2d}个"
                if j < 2 and i+j+1 < 93:
                    line += "  |  "
        print(line)
    
    print()
    print("📊 阈值统计:")
    print(f"  最大阈值: {max(real_thresholds)}个 (段{real_thresholds.index(max(real_thresholds))})")
    print(f"  最小阈值: {min(real_thresholds)}个")
    print(f"  平均阈值: {np.mean(real_thresholds):.1f}个")
    print(f"  阈值>10的段数: {sum(1 for t in real_thresholds if t > 10)}个")
    print(f"  阈值=0的段数: {sum(1 for t in real_thresholds if t == 0)}个")
    
    print()
    print("📋 Python代码格式的阈值列表:")
    print("real_thresholds = [")
    for i in range(0, len(real_thresholds), 10):
        line = "    "
        for j in range(10):
            if i+j < len(real_thresholds):
                line += f"{real_thresholds[i+j]:2d}"
                if i+j < len(real_thresholds)-1:
                    line += ", "
        if i+10 < len(real_thresholds):
            line += ",  # 段" + f"{i}-{min(i+9, len(real_thresholds)-1)}"
        else:
            line += "   # 段" + f"{i}-{len(real_thresholds)-1}"
        print(line)
    print("]")

if __name__ == "__main__":
    extract_real_thresholds()
