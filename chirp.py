'''导入库'''
import numpy as np
import matplotlib.pyplot as plt
import math
from matplotlib.pylab import mpl

from pylab import mpl
# 设置显示中文字体
mpl.rcParams["font.sans-serif"] = ["SimHei"]
mpl.rcParams["axes.unicode_minus"] = False

'''扫频生成函数'''

GAIN = 1


def get_duration(f, min_cycles=10, min_duration=10):
    """
    计算给定频率的持续时间（毫秒）。

    参数:
    f (float): 频率(Hz)。
    min_cycles (int): 每个频点扫描正弦周期的最小值。
    min_duration (float): 每个频点扫描的最小时间（毫秒）。

    返回:
    float: 持续时间（毫秒）。
    """
    
    def _get_duration():
        return 1 / f * math.ceil(min_duration / (1 / f * 1e3)) * 1e3
    
    # 计算持续时间
    # unit_duration = int(1 * 1e3 / f)  # 单位周期持续时间（毫秒）
    duration_of_min_cycles = 1 / f * min_cycles * 1e3  # 最小周期数对应的持续时间
    # 如果持续时间不小于最小持续时间，则返回最小持续时间，否则，计算满足条件的时长
    duration = duration_of_min_cycles if duration_of_min_cycles >= min_duration else _get_duration()
    return duration


def gen_sine_wave(f, t0, t1, A=1, fs=44100):
    # 生成单频率正弦信号
    assert t1 >= t0, "t1 must be greater than or equal to t0"
    assert f < fs / 2, "f must be less than half of the sampling rate"
    assert A >= 0, "A must be non-negative"
    assert fs > 0, "fs must be a positive number"
    assert f > 0, "f must be a positive number"
    t = np.arange(t0 * 1e-3, t1 * 1e-3, 1/fs)
    delta_t = 1 / fs * 1e3 - (t1 - t[-1] * 1e3)
    delta_t = max(0, delta_t)
    # if delta_t < 0:
    #     print('wrong delta_t!')
    y = A * np.sin(2 * np.pi * f * t)
    return y, t, delta_t


def gen_octave_freqpoints(octave, start_freq=100, stop_freq=20000):
    """
    生成倍频程的频率点。

    参数:
    Octave (int): 倍频程参数(24、12、6、3、1)。
    start_freq (float): 最小频率(Hz)。
    stop_freq (float): 最大频率(Hz)。

    返回:
    list: 包含倍频程频率点的列表。
    """
    # 计算1-20000Hz的全部倍频程频率数组
    def calculate_octave_bands():
        R_80 = [1., 1.03, 1.06, 1.09, 1.12, 1.15, 1.18, 1.22, 1.25, 1.28, 1.32, 1.36, 1.4, 1.45, 1.5, 1.55, 1.6, 1.65, 1.7, 1.75, 1.8, 1.85, 1.9, 1.95, 2., 2.06, 2.12, 2.18, 2.24, 2.3, 2.36, 2.43, 2.5, 2.58, 2.65, 2.72, 2.8, 2.9, 3., 3.07, 3.15, 3.25, 3.35, 3.45, 3.55, 3.65, 3.75, 3.87, 4., 4.12, 4.25, 4.37, 4.5, 4.62, 4.75, 4.87, 5., 5.15, 5.3, 5.45, 5.6, 5.8, 6., 6.15, 6.3, 6.5, 6.7, 6.9, 7.1, 7.3, 7.5, 7.75, 8., 8.25, 8.5, 8.75, 9., 9.25, 9.5, 9.75, 10.]
        for i in range(81, 160):
            R_80.append(round(R_80[i % 80] * 1e1, 2))
        for i in range(160, 240):
            R_80.append(round(R_80[i % 80] * 1e2, 2))
        for i in range(240, 320):
            R_80.append(round(R_80[i % 80] * 1e3, 2))
        for i in range(320, 345):
            R_80.append(round(R_80[i % 80] * 1e4, 2))
        return R_80

    # 根据Octave参数选择相应的倍频程频率数组
    R_80 = calculate_octave_bands()
    R_80_len = len(R_80)
    if octave == 24:
        R = R_80
    elif octave == 12:
        R = [R_80[i * 2] for i in range(round(R_80_len / 2) + 1)]
    elif octave == 6:
        R = [R_80[i * 4] for i in range(round(R_80_len / 4) + 1)]
    elif octave == 3:
        R = [R_80[i * 8] for i in range(round(R_80_len / 8) + 1)]
    elif octave == 1:
        R = [R_80[i * 24] for i in range(round(R_80_len / 24) + 1)]
    else:
        print("Please input the right Octave Parameter!")
        return []

    # 筛选出在范围内的频率点
    result = []
    for freq in R:
        if start_freq <= freq <= stop_freq:
            result.append(freq)
    return result


def plot_wave(y, t, title, freq_ssample_dict, figsize=(16, 5), show_segtimes=False):
    # 绘制正弦信号
    plt.figure(figsize=figsize)
    plt.plot(t, y)
    if show_segtimes:
        for f in freq_ssample_dict:
            plt.vlines(x=freq_ssample_dict[f][0],ymin=y.min()*1.1, ymax=y.max()*1.1, color='r', linestyle='-')
    plt.xlabel('时间 (秒)')
    plt.ylabel('振幅')
    plt.xlim([0, t[-1]])
    plt.title(title)
    plt.grid(True)
    plt.show()


def gen_freq_step(start_freq, stop_freq, min_cycles, min_duration, octave, fs, A=1, plot_freqs=False):
    """
    生成扫频信号
    """
    freq_ssample_dict = {}
    if_reverse = False
    if start_freq > stop_freq:
        start_freq, stop_freq = stop_freq, start_freq
        if_reverse = True

    frequency_points = gen_octave_freqpoints(octave, start_freq, stop_freq)

    if if_reverse:
        frequency_points = frequency_points[::-1]
        
    # print(f"1/{octave}倍频程的频率点:\n{frequency_points}\n共有{len(frequency_points)}个频率点")
    if plot_freqs:
        plt.plot(frequency_points, marker='.')
        plt.grid(True)

    t = 0
    sine_wave = np.array([])

    delta_t = 0
    for f in frequency_points:
        duration = get_duration(f, min_cycles, min_duration)
        t0 = t
        sine_wave_tmp, t_list_tmp, delta_t = gen_sine_wave(f, delta_t, duration, fs=fs, A=A)
        t_list_tmp = t_list_tmp + t * 1e-3
        sine_wave = np.concatenate((sine_wave, sine_wave_tmp))
        freq_ssample_dict[f] = ((t + delta_t) * 1e-3, math.ceil((t + delta_t) * 1e-3 * fs), int((duration - delta_t) * 1e-3 * fs))
        t += duration
        # print(f'{f}Hz, duration: {duration}ms, t0: {t0*1e-3}s, t: {t*1e-3}s')
    t_list = np.arange(0, t * 1e-3, 1/fs)

    # print(f'ssample:{freq_ssample_dict}')
    return sine_wave, t_list, freq_ssample_dict


if __name__ == "__main__":
    '''扫频生成'''
    octave_dict = {3:'R10', 6:'R20', 12:'R40', 24:'R80'}
    octave = 6           # 频率分辨率  例R20(1/6倍频程)
    start_freq = 100   # 开始频率
    stop_freq = 20000      # 结束频率
    A = 1                 # 振幅
    fs = 48000            # 采样率
    min_cycles = 10       # 每个频点扫描正弦周期的最小值
    min_duration = 150     # 每个频点扫描的最小时间（毫秒）

    sine_wave, t_list, freq_ssample_dict = gen_freq_step(start_freq, stop_freq, min_cycles, min_duration, octave, fs, A=A)
    print(sine_wave.shape, t_list.shape)
    plot_wave(sine_wave, t_list, '扫频信号', freq_ssample_dict)
    '''音频保存'''
    import soundfile as sf
    write_wav_path = f'./gen_chirps/sf{start_freq}-ef{stop_freq}-oct{octave_dict[octave]}-mc{min_cycles}-md{min_duration}-fs{fs}.wav'
    sf.write(write_wav_path, sine_wave, fs, 'PCM_16')
    print(f'音频已保存到: {write_wav_path}')