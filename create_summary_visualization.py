#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成93段THD+N分析结果的汇总可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import re

# 设置matplotlib和字体
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
# 设置字体和负号显示
import matplotlib.font_manager as fm

# 查找可用的中文字体
available_fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = []

# 检查常见中文字体是否可用
common_chinese = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'Arial Unicode MS']
for font in common_chinese:
    if font in available_fonts:
        chinese_fonts.append(font)

# 如果没有找到中文字体，使用默认字体
if not chinese_fonts:
    chinese_fonts = ['DejaVu Sans', 'Arial']

plt.rcParams.update({
    'font.sans-serif': chinese_fonts + ['DejaVu Sans', 'Arial', 'sans-serif'],
    'axes.unicode_minus': False,  # 解决负号显示问题
    'font.family': 'sans-serif',
    'text.usetex': False,
    'mathtext.default': 'regular',
    'font.size': 10,
})

# 清除字体缓存
try:
    fm._rebuild()
except AttributeError:
    # 新版本matplotlib可能没有_rebuild方法
    pass

def parse_results_file(file_path):
    """解析分析结果文件"""
    results = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到数据开始行
    data_start = False
    for i, line in enumerate(lines):
        if line.strip().startswith('序号'):
            data_start = True
            continue
        if line.strip().startswith('--'):
            continue
        if data_start and line.strip() and not line.strip().startswith('统计信息'):
            # 解析数据行
            parts = line.strip().split()
            if len(parts) >= 13:  # 现在有13列数据
                try:
                    # 解析带宽值
                    bandwidth_type = parts[10]
                    bandwidth_str = parts[11]
                    if bandwidth_str.endswith('%'):
                        bandwidth_value = float(bandwidth_str[:-1])
                        bandwidth_hz = float(parts[1]) * bandwidth_value / 100.0
                    else:
                        bandwidth_value = float(bandwidth_str[:-2])  # 去掉"Hz"
                        bandwidth_hz = bandwidth_value

                    result = {
                        'segment_idx': int(parts[0]),
                        'expected_freq': float(parts[1]),
                        'test_fundamental_freq': float(parts[2]),
                        'ref_fundamental_freq': float(parts[3]),
                        'freq_deviation': float(parts[4]),
                        'scaling_factor': float(parts[5]),
                        'total_test_power': float(parts[6]),
                        'total_scaled_ref_power': float(parts[7]),
                        'thd_n_method1': float(parts[8]),
                        'thd_n_method2': float(parts[9]),
                        'bandwidth_type': bandwidth_type,
                        'bandwidth_value': bandwidth_value,
                        'bandwidth_hz': bandwidth_hz,
                        'method_diff': float(parts[12])
                    }
                    results.append(result)
                except ValueError:
                    continue
        elif line.strip().startswith('统计信息'):
            break
    
    return results

def create_summary_visualization(results, output_dir, audio_filename=None):
    """创建93段结果汇总可视化 - 三张图竖向排列"""

    # 提取数据
    segments = [r['segment_idx'] for r in results]
    expected_freqs = [r['expected_freq'] for r in results]
    test_powers = [r['total_test_power'] for r in results]
    ref_powers = [r['total_scaled_ref_power'] for r in results]
    method1_thd_n = [r['thd_n_method1'] for r in results]
    method2_thd_n = [r['thd_n_method2'] for r in results]
    scaling_factors = [r['scaling_factor'] for r in results]
    freq_deviations = [r['freq_deviation'] for r in results]
    bandwidth_types = [r['bandwidth_type'] for r in results]
    bandwidth_hz_values = [r['bandwidth_hz'] for r in results]

    # 计算功率统计
    total_test_power = sum(test_powers)
    total_ref_power = sum(ref_powers)
    power_diff = total_test_power - total_ref_power
    power_ratio = total_test_power / total_ref_power if total_ref_power > 0 else 0

    # 创建三张图竖向排列
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 18))

    # 1. 方法一THD+N分布
    ax1.bar(segments, method1_thd_n, alpha=0.8, color='red',
           label='Method 1 (Time Domain Power Subtraction)')

    ax1.set_xlabel('Segment Number', fontsize=12)
    ax1.set_ylabel('THD+N (%)', fontsize=12)
    # 检查是否有正值，决定是否使用对数坐标
    if any(val > 0 for val in method1_thd_n):
        ax1.set_yscale('log')
    title1 = 'Method 1: THD+N Distribution (93 Segments)'
    if audio_filename:
        title1 = f'{audio_filename}\nMethod 1: THD+N Distribution (93 Segments)'
    ax1.set_title(title1, fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.legend()
    ax1.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    method1_avg = np.mean(method1_thd_n)
    method1_min = np.min(method1_thd_n)
    method1_max = np.max(method1_thd_n)
    ax1.text(0.02, 0.98, f'Avg: {method1_avg:.3f}%\nMin: {method1_min:.3f}%\nMax: {method1_max:.3f}%',
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))

    # 2. 方法二THD+N分布
    ax2.bar(segments, method2_thd_n, alpha=0.8, color='blue',
           label='Method 2 (Fundamental Bandwidth Exclusion, ≥100Hz)')

    ax2.set_xlabel('Segment Number', fontsize=12)
    ax2.set_ylabel('THD+N (%)', fontsize=12)
    ax2.set_yscale('log')
    title2 = 'Method 2: THD+N Distribution (93 Segments, Power ≥100Hz)'
    if audio_filename:
        title2 = f'{audio_filename}\nMethod 2: THD+N Distribution (93 Segments, Power ≥100Hz)'
    ax2.set_title(title2, fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.legend()
    ax2.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    method2_avg = np.mean(method2_thd_n)
    method2_min = np.min(method2_thd_n)
    method2_max = np.max(method2_thd_n)
    ax2.text(0.02, 0.98, f'Avg: {method2_avg:.3f}%\nMin: {method2_min:.3f}%\nMax: {method2_max:.3f}%',
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 3. 参考音频缩放系数分布
    ax3.bar(segments, scaling_factors, alpha=0.8, color='green',
           label='Reference Audio Scaling Factor')

    ax3.set_xlabel('Segment Number', fontsize=12)
    ax3.set_ylabel('Scaling Factor', fontsize=12)
    ax3.set_title('Reference Audio Scaling Factor Distribution (93 Segments)', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.legend()
    ax3.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    scaling_avg = np.mean(scaling_factors)
    scaling_min = np.min(scaling_factors)
    scaling_max = np.max(scaling_factors)
    ax3.text(0.02, 0.98, f'Avg: {scaling_avg:.6f}\nMin: {scaling_min:.6f}\nMax: {scaling_max:.6f}',
             transform=ax3.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图像
    image_path = os.path.join(output_dir, "93段THD+N分析汇总.png")
    plt.savefig(image_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 93段汇总图已保存: {image_path}")
    
    return image_path

def main():
    """主函数"""
    # 文件路径
    results_file = "鼓膜破裂（复测1.1)_THD+N双方法分析/THD+N双方法分析汇总.txt"
    output_dir = "鼓膜破裂（复测1.1)_THD+N双方法分析"
    
    if not os.path.exists(results_file):
        print(f"❌ 结果文件不存在: {results_file}")
        return
    
    print("🎯 开始生成93段THD+N分析汇总图...")
    
    # 解析结果
    results = parse_results_file(results_file)
    print(f"📊 成功解析 {len(results)} 个频段的数据")
    
    if len(results) == 0:
        print("❌ 没有找到有效数据")
        return
    
    # 创建汇总可视化
    summary_chart = create_summary_visualization(results, output_dir)
    
    print(f"\n✅ 93段汇总可视化完成!")
    print(f"📁 输出目录: {output_dir}")
    print(f"🖼️ 生成图像: 93段THD+N分析汇总.png")

if __name__ == "__main__":
    main()
