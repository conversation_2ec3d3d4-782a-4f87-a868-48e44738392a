#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量生成93段汇总图脚本 - 最终版本
调用现有的分析和汇总功能
"""

import os
import sys
import time
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入现有的分析模块
from adaptive_fundamental_removal import main as thd_main

def find_audio_files(folder_path):
    """查找文件夹中的所有音频文件"""
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.aiff', '*.m4a']
    audio_files = []
    
    folder_path = Path(folder_path)
    if not folder_path.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in audio_extensions:
        files = list(folder_path.glob(f"**/{ext}"))  # 递归搜索
        audio_files.extend(files)
    
    return sorted(audio_files)

def process_single_audio(audio_file, batch_output_dir):
    """处理单个音频文件"""
    print(f"\n🎵 处理: {audio_file.name}")
    
    try:
        # 创建临时输出目录
        temp_output = f"temp_{audio_file.stem}"
        
        # 调用THD+N分析
        print("🔬 进行THD+N分析...")
        success = thd_main(str(audio_file), temp_output)
        
        if not success:
            print(f"❌ THD+N分析失败")
            return False
        
        # 查找分析结果目录
        analysis_dir = f"{audio_file.stem}_THD+N双方法分析"

        if not os.path.exists(analysis_dir):
            print(f"❌ 分析目录不存在: {analysis_dir}")
            return False

        # 调用汇总可视化，传入正确的汇总文件路径
        summary_file = os.path.join(analysis_dir, "THD+N双方法分析汇总.txt")
        if not os.path.exists(summary_file):
            print(f"❌ 汇总文件不存在: {summary_file}")
            return False

        print("📊 生成汇总图...")
        # 直接调用create_summary_visualization模块的函数
        from create_summary_visualization import parse_results_file, create_summary_visualization

        try:
            results = parse_results_file(summary_file)
            if results:
                create_summary_visualization(results, analysis_dir)
                print(f"📊 汇总图生成完成")
            else:
                print(f"❌ 解析汇总文件失败")
                return False
        except Exception as e:
            print(f"❌ 生成汇总图失败: {str(e)}")
            return False

        # 查找生成的汇总图
        summary_files = []
        
        if os.path.exists(analysis_dir):
            for file in os.listdir(analysis_dir):
                if file.endswith('.png') and '汇总' in file:
                    summary_files.append(os.path.join(analysis_dir, file))
        
        if summary_files:
            # 复制汇总图到批量输出目录
            for summary_file in summary_files:
                dest_name = f"{audio_file.stem}_汇总图.png"
                dest_path = os.path.join(batch_output_dir, dest_name)
                shutil.copy2(summary_file, dest_path)
                print(f"📊 汇总图已保存: {dest_name}")
            
            # 清理临时目录
            if os.path.exists(temp_output):
                shutil.rmtree(temp_output)
            if os.path.exists(analysis_dir):
                shutil.rmtree(analysis_dir)
            
            return True
        else:
            print(f"❌ 未找到汇总图")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 批量生成93段汇总图工具 - 最终版本")
    print("=" * 60)
    
    # 定义要处理的文件夹
    target_folders = ["test20250717", "待定", "test20250722"]
    
    # 创建批量输出目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    batch_output_dir = f"批量汇总图_{timestamp}"
    os.makedirs(batch_output_dir, exist_ok=True)
    print(f"📁 输出目录: {batch_output_dir}")
    
    # 收集所有音频文件
    all_audio_files = []
    for folder in target_folders:
        print(f"\n📂 扫描文件夹: {folder}")
        audio_files = find_audio_files(folder)
        
        if audio_files:
            print(f"   找到 {len(audio_files)} 个音频文件")
            all_audio_files.extend(audio_files)
        else:
            print(f"   未找到音频文件")
    
    if not all_audio_files:
        print("❌ 未找到任何音频文件")
        return
    
    print(f"\n📊 总共找到 {len(all_audio_files)} 个音频文件")
    
    # 处理所有文件
    successful = 0
    failed = 0
    start_time = time.time()
    
    for i, audio_file in enumerate(all_audio_files, 1):
        print(f"\n[{i}/{len(all_audio_files)}] 来自文件夹: {audio_file.parent.name}")
        
        try:
            success = process_single_audio(audio_file, batch_output_dir)
            if success:
                successful += 1
                print(f"✅ {audio_file.name} 处理完成")
            else:
                failed += 1
                print(f"❌ {audio_file.name} 处理失败")
        except Exception as e:
            print(f"❌ {audio_file.name} 处理异常: {str(e)}")
            failed += 1
    
    # 创建处理报告
    report_file = os.path.join(batch_output_dir, "处理报告.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("批量汇总图生成报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {len(all_audio_files)}\n")
        f.write(f"成功处理: {successful}\n")
        f.write(f"处理失败: {failed}\n")
        f.write(f"成功率: {successful/len(all_audio_files)*100:.1f}%\n")
    
    # 总结
    total_time = time.time() - start_time
    print("\n" + "=" * 60)
    print("🎉 批量处理完成!")
    print(f"📊 处理统计:")
    print(f"   总文件数: {len(all_audio_files)}")
    print(f"   成功处理: {successful}")
    print(f"   处理失败: {failed}")
    print(f"   成功率: {successful/len(all_audio_files)*100:.1f}%")
    print(f"   总耗时: {total_time:.1f}秒")
    print(f"   平均耗时: {total_time/len(all_audio_files):.1f}秒/文件")
    print(f"📁 输出目录: {batch_output_dir}")
    print(f"📄 处理报告: {report_file}")
    print("=" * 60)

if __name__ == "__main__":
    main()
