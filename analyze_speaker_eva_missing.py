#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析"喇叭eva没贴"样本，寻找与正样本的区分特征
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import librosa
from scipy.signal import stft, find_peaks, welch

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class SpeakerEvaAnalyzer:
    def __init__(self):
        self.fs = 48000
        
    def extract_comprehensive_features(self, audio_path, segment_idx, start_time, end_time, expected_freq):
        """提取更全面的特征"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            # 提取频段音频
            start_sample = int(start_time * self.fs)
            end_sample = int(end_time * self.fs)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            features = {}
            
            # 1. 基础时域特征
            features.update(self._extract_time_domain_features(segment_audio))
            
            # 2. 频域特征
            features.update(self._extract_frequency_domain_features(segment_audio, expected_freq))
            
            # 3. 频谱特征
            features.update(self._extract_spectral_features(segment_audio))
            
            # 4. THD和谐波特征
            features.update(self._extract_thd_features(segment_audio, expected_freq))
            
            # 5. 竖线检测特征
            features.update(self._extract_vertical_line_features(segment_audio))
            
            # 6. 统计特征
            features.update(self._extract_statistical_features(segment_audio))
            
            # 7. 能量分布特征
            features.update(self._extract_energy_features(segment_audio))
            
            # 8. 新增特征：相位特征
            features.update(self._extract_phase_features(segment_audio, expected_freq))
            
            # 9. 新增特征：噪声特征
            features.update(self._extract_noise_features(segment_audio, expected_freq))
            
            # 10. 新增特征：动态特征
            features.update(self._extract_dynamic_features(segment_audio))
            
            # 11. 新增特征：频谱稳定性
            features.update(self._extract_stability_features(segment_audio))
            
            return features
            
        except Exception as e:
            print(f"提取频段{segment_idx}特征失败: {e}")
            return None
    
    def _extract_time_domain_features(self, audio):
        """时域特征"""
        features = {}
        features['td_mean'] = np.mean(audio)
        features['td_std'] = np.std(audio)
        features['td_var'] = np.var(audio)
        features['td_rms'] = np.sqrt(np.mean(audio**2))
        features['td_max'] = np.max(np.abs(audio))
        features['td_min'] = np.min(audio)
        features['td_range'] = np.max(audio) - np.min(audio)
        features['td_skewness'] = stats.skew(audio)
        features['td_kurtosis'] = stats.kurtosis(audio)
        
        # 过零率
        zero_crossings = np.where(np.diff(np.signbit(audio)))[0]
        features['td_zcr'] = len(zero_crossings) / len(audio)
        
        # 峰值因子
        features['td_crest_factor'] = features['td_max'] / (features['td_rms'] + 1e-12)
        
        return features
    
    def _extract_frequency_domain_features(self, audio, expected_freq):
        """频域特征"""
        features = {}
        
        # FFT分析
        fft = np.fft.fft(audio)
        freqs = np.fft.fftfreq(len(audio), 1/self.fs)
        magnitude = np.abs(fft)
        
        # 只考虑正频率
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        features['fd_peak_freq'] = positive_freqs[np.argmax(positive_magnitude)]
        features['fd_peak_magnitude'] = np.max(positive_magnitude)
        features['fd_total_energy'] = np.sum(positive_magnitude**2)
        
        # 期望频率附近的能量
        freq_tolerance = 50
        freq_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
        if np.any(freq_mask):
            features['fd_expected_freq_energy'] = np.sum(positive_magnitude[freq_mask]**2)
            features['fd_expected_freq_ratio'] = features['fd_expected_freq_energy'] / features['fd_total_energy']
        else:
            features['fd_expected_freq_energy'] = 0
            features['fd_expected_freq_ratio'] = 0
        
        features['fd_freq_deviation'] = abs(features['fd_peak_freq'] - expected_freq)
        features['fd_freq_deviation_ratio'] = features['fd_freq_deviation'] / expected_freq
        
        return features
    
    def _extract_spectral_features(self, audio):
        """频谱特征"""
        features = {}
        
        # STFT分析
        f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
        magnitude = np.abs(Zxx)
        power = magnitude**2
        
        features['spec_mean'] = np.mean(magnitude)
        features['spec_std'] = np.std(magnitude)
        features['spec_max'] = np.max(magnitude)
        features['spec_energy'] = np.sum(power)
        
        # 频谱重心和带宽
        freq_weighted_sum = np.sum(f[:, np.newaxis] * power, axis=0)
        total_power = np.sum(power, axis=0)
        spectral_centroid = freq_weighted_sum / (total_power + 1e-12)
        features['spec_centroid_mean'] = np.mean(spectral_centroid)
        features['spec_centroid_std'] = np.std(spectral_centroid)
        
        freq_diff = f[:, np.newaxis] - spectral_centroid[np.newaxis, :]
        spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * power, axis=0) / (total_power + 1e-12))
        features['spec_bandwidth_mean'] = np.mean(spectral_bandwidth)
        features['spec_bandwidth_std'] = np.std(spectral_bandwidth)
        
        return features
    
    def _extract_thd_features(self, audio, expected_freq):
        """THD和谐波特征"""
        features = {}
        
        # FFT分析
        fft = np.fft.fft(audio)
        freqs = np.fft.fftfreq(len(audio), 1/self.fs)
        magnitude = np.abs(fft)
        
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 基频能量
        fundamental_tolerance = 20
        fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
        if np.any(fundamental_mask):
            fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
        else:
            fundamental_energy = 0
        
        # 谐波能量
        harmonic_energies = []
        for harmonic in range(2, 6):
            harmonic_freq = expected_freq * harmonic
            if harmonic_freq < self.fs / 2:
                harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                if np.any(harmonic_mask):
                    harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                    harmonic_energies.append(harmonic_energy)
                else:
                    harmonic_energies.append(0)
        
        # THD计算
        total_harmonic_energy = sum(harmonic_energies)
        if fundamental_energy > 0:
            features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
            features['thd_db'] = 20 * np.log10(features['thd'] + 1e-12)
        else:
            features['thd'] = 0
            features['thd_db'] = -120
        
        # 各次谐波比例
        for i, energy in enumerate(harmonic_energies, 2):
            if fundamental_energy > 0:
                features[f'harmonic_{i}_ratio'] = energy / fundamental_energy
            else:
                features[f'harmonic_{i}_ratio'] = 0
        
        # 谐波总能量比例
        total_energy = np.sum(positive_magnitude**2)
        features['fundamental_ratio'] = fundamental_energy / total_energy if total_energy > 0 else 0
        features['harmonic_total_ratio'] = total_harmonic_energy / total_energy if total_energy > 0 else 0
        
        return features
    
    def _extract_vertical_line_features(self, audio):
        """竖线检测特征"""
        features = {}
        
        try:
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            power_spectrum = np.abs(Zxx)**2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            total_energy = np.sum(power_db, axis=0)
            energy_mean = np.mean(total_energy)
            energy_std = np.std(total_energy)
            
            features['vl_energy_mean'] = energy_mean
            features['vl_energy_std'] = energy_std
            features['vl_energy_cv'] = energy_std / (abs(energy_mean) + 1e-12)
            
            # 峰值检测
            min_height = energy_mean + 0.5 * energy_std
            min_prominence = 0.3 * energy_std
            
            peaks, properties = find_peaks(total_energy, 
                                         height=min_height,
                                         distance=2,
                                         prominence=min_prominence)
            
            features['vl_peak_count'] = len(peaks)
            
        except:
            features.update({
                'vl_energy_mean': 0,
                'vl_energy_std': 0,
                'vl_energy_cv': 0,
                'vl_peak_count': 0
            })
        
        return features
    
    def _extract_statistical_features(self, audio):
        """统计特征"""
        features = {}
        
        percentiles = [10, 25, 50, 75, 90]
        for p in percentiles:
            features[f'stat_p{p}'] = np.percentile(audio, p)
        
        features['stat_iqr'] = features['stat_p75'] - features['stat_p25']
        
        return features
    
    def _extract_energy_features(self, audio):
        """能量分布特征"""
        features = {}
        
        frame_length = 1024
        hop_length = 512
        frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
        frame_energy = np.sum(frames**2, axis=0)
        
        features['energy_mean'] = np.mean(frame_energy)
        features['energy_std'] = np.std(frame_energy)
        features['energy_max'] = np.max(frame_energy)
        features['energy_min'] = np.min(frame_energy)
        features['energy_range'] = features['energy_max'] - features['energy_min']
        features['energy_cv'] = features['energy_std'] / (features['energy_mean'] + 1e-12)
        
        return features
    
    def _extract_phase_features(self, audio, expected_freq):
        """相位特征"""
        features = {}
        
        try:
            # STFT获取相位信息
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            phase = np.angle(Zxx)
            
            # 相位稳定性
            phase_diff = np.diff(phase, axis=1)
            features['phase_stability'] = np.mean(np.std(phase_diff, axis=1))
            
            # 期望频率的相位特征
            freq_idx = np.argmin(np.abs(f - expected_freq))
            if freq_idx < len(f):
                freq_phase = phase[freq_idx, :]
                features['phase_consistency'] = np.std(np.diff(freq_phase))
            else:
                features['phase_consistency'] = 0
                
        except:
            features['phase_stability'] = 0
            features['phase_consistency'] = 0
        
        return features
    
    def _extract_noise_features(self, audio, expected_freq):
        """噪声特征"""
        features = {}
        
        try:
            # 使用Welch方法估计功率谱密度
            freqs, psd = welch(audio, self.fs, nperseg=1024)
            
            # 信噪比估计
            signal_freq_idx = np.argmin(np.abs(freqs - expected_freq))
            signal_power = psd[signal_freq_idx]
            
            # 噪声功率（排除信号频率附近）
            noise_mask = np.abs(freqs - expected_freq) > 100  # 排除100Hz范围内
            if np.any(noise_mask):
                noise_power = np.mean(psd[noise_mask])
                features['snr'] = 10 * np.log10(signal_power / (noise_power + 1e-12))
            else:
                features['snr'] = 0
            
            # 噪声底噪
            features['noise_floor'] = 10 * np.log10(np.min(psd) + 1e-12)
            
            # 频谱平坦度
            geometric_mean = np.exp(np.mean(np.log(psd + 1e-12)))
            arithmetic_mean = np.mean(psd)
            features['spectral_flatness'] = geometric_mean / (arithmetic_mean + 1e-12)
            
        except:
            features['snr'] = 0
            features['noise_floor'] = -120
            features['spectral_flatness'] = 0
        
        return features
    
    def _extract_dynamic_features(self, audio):
        """动态特征"""
        features = {}
        
        try:
            # 短时能量变化
            frame_length = 512
            hop_length = 256
            frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
            frame_energy = np.sum(frames**2, axis=0)
            
            # 能量变化率
            energy_diff = np.diff(frame_energy)
            features['energy_change_rate'] = np.std(energy_diff)
            features['energy_change_mean'] = np.mean(np.abs(energy_diff))
            
            # 动态范围
            features['dynamic_range'] = 20 * np.log10((np.max(frame_energy) + 1e-12) / (np.min(frame_energy) + 1e-12))
            
        except:
            features['energy_change_rate'] = 0
            features['energy_change_mean'] = 0
            features['dynamic_range'] = 0
        
        return features
    
    def _extract_stability_features(self, audio):
        """频谱稳定性特征"""
        features = {}
        
        try:
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            magnitude = np.abs(Zxx)
            
            # 频谱稳定性
            magnitude_std = np.std(magnitude, axis=1)  # 每个频率的时间稳定性
            features['spectral_stability'] = np.mean(magnitude_std)
            
            # 频谱一致性
            magnitude_corr = np.corrcoef(magnitude)
            features['spectral_consistency'] = np.mean(magnitude_corr[np.triu_indices_from(magnitude_corr, k=1)])
            
        except:
            features['spectral_stability'] = 0
            features['spectral_consistency'] = 0
        
        return features

def analyze_speaker_eva_missing():
    """分析喇叭eva没贴样本"""
    print("🔍 专门分析'喇叭eva没贴'样本与正样本的区分特征")
    print("="*70)
    
    analyzer = SpeakerEvaAnalyzer()
    
    # 目标文件
    target_files = [
        r"../test20250717/neg/喇叭eva没贴.wav",
        r"../test20250717/neg/喇叭eva没贴_1.wav"
    ]
    
    # 正样本参考文件（随机选择几个）
    reference_files = [
        r"../test20250717/pos/完美/ok1.wav",
        r"../test20250717/pos/完美/ok2.wav",
        r"../test20250717/pos/sd卡/sd1.wav",
        r"../test20250717/pos/sd卡/sd2.wav"
    ]
    
    all_features = []
    
    # 分析目标文件
    print("\n📊 分析目标文件（喇叭eva没贴）:")
    for audio_path in target_files:
        if not os.path.exists(audio_path):
            print(f"❌ 文件不存在: {audio_path}")
            continue
        
        filename = os.path.basename(audio_path)
        print(f"  🎵 {filename}")
        
        try:
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            # 提取每个频段的特征
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                features = analyzer.extract_comprehensive_features(
                    audio_path, seg_idx, start_time, end_time, expected_freq
                )
                
                if features:
                    features['label'] = 'neg'
                    features['filename'] = filename
                    features['segment_idx'] = seg_idx
                    features['expected_freq'] = expected_freq
                    all_features.append(features)
        
        except Exception as e:
            print(f"    ❌ 分析失败: {e}")
    
    # 分析参考文件
    print("\n📊 分析参考文件（正样本）:")
    for audio_path in reference_files:
        if not os.path.exists(audio_path):
            print(f"❌ 文件不存在: {audio_path}")
            continue
        
        filename = os.path.basename(audio_path)
        print(f"  🎵 {filename}")
        
        try:
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            # 提取每个频段的特征
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                features = analyzer.extract_comprehensive_features(
                    audio_path, seg_idx, start_time, end_time, expected_freq
                )
                
                if features:
                    features['label'] = 'pos'
                    features['filename'] = filename
                    features['segment_idx'] = seg_idx
                    features['expected_freq'] = expected_freq
                    all_features.append(features)
        
        except Exception as e:
            print(f"    ❌ 分析失败: {e}")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_features)
    print(f"\n✅ 特征提取完成: {len(df)}个频段样本")
    
    # 分析特征区分能力
    analyze_discrimination_power(df)
    
    return df

def analyze_discrimination_power(df):
    """分析特征区分能力"""
    print(f"\n🔍 分析特征区分能力")
    print("="*70)
    
    # 获取特征列
    meta_columns = ['label', 'filename', 'segment_idx', 'expected_freq']
    feature_columns = [col for col in df.columns if col not in meta_columns]
    
    print(f"📊 分析特征数: {len(feature_columns)}")
    
    discrimination_results = []
    
    for feature in feature_columns:
        try:
            pos_values = df[df['label'] == 'pos'][feature].dropna()
            neg_values = df[df['label'] == 'neg'][feature].dropna()
            
            if len(pos_values) == 0 or len(neg_values) == 0:
                continue
            
            # 统计检验
            t_stat, p_value = stats.ttest_ind(pos_values, neg_values)
            
            # 效应大小
            pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                                 (len(neg_values) - 1) * np.var(neg_values)) / 
                                (len(pos_values) + len(neg_values) - 2))
            cohens_d = abs(np.mean(pos_values) - np.mean(neg_values)) / (pooled_std + 1e-12)
            
            # 分离度
            pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
            neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
            
            # 重叠度计算
            overlap = max(0, min(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - 
                         max(pos_mean - 2*pos_std, neg_mean - 2*neg_std))
            total_range = max(pos_mean + 2*pos_std, neg_mean + 2*neg_std) - \
                         min(pos_mean - 2*pos_std, neg_mean - 2*neg_std)
            overlap_ratio = overlap / (total_range + 1e-12)
            
            separation_score = cohens_d * (1 - overlap_ratio)
            
            discrimination_results.append({
                'feature': feature,
                'pos_mean': pos_mean,
                'pos_std': pos_std,
                'neg_mean': neg_mean,
                'neg_std': neg_std,
                't_statistic': t_stat,
                'p_value': p_value,
                'cohens_d': cohens_d,
                'overlap_ratio': overlap_ratio,
                'separation_score': separation_score
            })
            
        except Exception as e:
            continue
    
    # 排序并显示结果
    disc_df = pd.DataFrame(discrimination_results)
    disc_df = disc_df.sort_values('separation_score', ascending=False)
    
    print(f"\n📊 最有区分力的前20个特征:")
    print("-" * 70)
    
    for i, (_, row) in enumerate(disc_df.head(20).iterrows()):
        significance = "***" if row['p_value'] < 0.001 else "**" if row['p_value'] < 0.01 else "*" if row['p_value'] < 0.05 else ""
        effect_size = "大" if row['cohens_d'] > 0.8 else "中" if row['cohens_d'] > 0.5 else "小"
        
        print(f"{i+1:2d}. {row['feature']}")
        print(f"    分离评分: {row['separation_score']:.3f}")
        print(f"    Cohen's d: {row['cohens_d']:.3f} ({effect_size})")
        print(f"    p值: {row['p_value']:.2e} {significance}")
        print(f"    正样本: {row['pos_mean']:.6f} ± {row['pos_std']:.6f}")
        print(f"    负样本: {row['neg_mean']:.6f} ± {row['neg_std']:.6f}")
        print(f"    重叠率: {row['overlap_ratio']:.3f}")
        
        # 判断是否可以完全区分
        if row['overlap_ratio'] < 0.1 and row['cohens_d'] > 1.0:
            print(f"    🎯 可以完全区分！")
        elif row['overlap_ratio'] < 0.3 and row['cohens_d'] > 0.8:
            print(f"    ✅ 区分效果很好")
        elif row['overlap_ratio'] < 0.5 and row['cohens_d'] > 0.5:
            print(f"    🟡 区分效果一般")
        else:
            print(f"    ❌ 区分效果较差")
        print()
    
    # 保存结果
    disc_df.to_csv('speaker_eva_discrimination_analysis.csv', index=False, encoding='utf-8-sig')
    print(f"💾 区分能力分析已保存: speaker_eva_discrimination_analysis.csv")
    
    # 生成可视化
    visualize_top_discriminative_features(df, disc_df)
    
    return disc_df

def visualize_top_discriminative_features(df, disc_df, top_n=12):
    """可视化最有区分力的特征"""
    print(f"\n🔍 可视化前{top_n}个最有区分力的特征")
    print("="*70)
    
    top_features = disc_df.head(top_n)['feature'].tolist()
    
    # 创建子图
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('喇叭eva没贴 vs 正样本 - 最有区分力的特征对比', fontsize=16, fontweight='bold')
    
    for i, feature in enumerate(top_features):
        row, col = i // 4, i % 4
        ax = axes[row, col]
        
        # 获取数据
        pos_data = df[df['label'] == 'pos'][feature].dropna()
        neg_data = df[df['label'] == 'neg'][feature].dropna()
        
        # 绘制分布
        ax.hist(pos_data, bins=30, alpha=0.7, color='green', label=f'正样本 (n={len(pos_data)})', density=True)
        ax.hist(neg_data, bins=30, alpha=0.7, color='red', label=f'喇叭eva没贴 (n={len(neg_data)})', density=True)
        
        # 添加均值线
        ax.axvline(np.mean(pos_data), color='green', linestyle='--', linewidth=2, alpha=0.8)
        ax.axvline(np.mean(neg_data), color='red', linestyle='--', linewidth=2, alpha=0.8)
        
        # 设置标题和标签
        feature_info = disc_df[disc_df['feature'] == feature].iloc[0]
        ax.set_title(f'{feature}\n分离评分: {feature_info["separation_score"]:.3f}', fontsize=10)
        ax.set_xlabel('特征值')
        ax.set_ylabel('密度')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('speaker_eva_discriminative_features.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    features_df = analyze_speaker_eva_missing()
