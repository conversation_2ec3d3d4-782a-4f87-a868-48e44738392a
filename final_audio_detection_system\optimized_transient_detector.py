#!/usr/bin/env python3
"""
优化的瞬时多频段能量突增检测器
Optimized Transient Multi-band Energy Burst Detector
基于调试结果优化的参数
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import json
import warnings
warnings.filterwarnings('ignore')

class OptimizedTransientDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # 优化后的STFT参数
        self.stft_params = {
            'nperseg': 512,         # 约10ms @48kHz
            'noverlap': 384,        # 75%重叠
            'window': 'hann',
            'nfft': 512
        }
        
        # 优化后的检测参数（基于调试结果）
        self.detection_params = {
            'energy_threshold_sigma': 1.5,     # 降低阈值
            'min_affected_bands': 0.1,         # 降低最少影响频段到10%
            'max_burst_duration': 0.2,         # 最大突增持续时间
            'frequency_range': (100, 8000),    # 分析频率范围
            'baseline_percentile': 30,          # 提高基线百分位
            'min_relative_increase': 2.0       # 最小相对增幅
        }
        
        print(f"优化瞬时突增检测器初始化完成")
        print(f"时间分辨率: {self.stft_params['nperseg']/sample_rate*1000:.1f}ms")
        print(f"检测参数: σ={self.detection_params['energy_threshold_sigma']}, 最少影响频段={self.detection_params['min_affected_bands']*100:.0f}%")
    
    def detect_transient_bursts(self, audio_path):
        """检测瞬时多频段能量突增"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # 短时傅里叶变换
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 计算功率谱
            power_spectrogram = np.abs(Zxx) ** 2
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            power_spectrogram = power_spectrogram[freq_mask, :]
            
            # 检测瞬时能量突增
            burst_analysis = self._analyze_energy_bursts(
                power_spectrogram, frequencies, times
            )
            
            # 计算异常分数
            anomaly_score = self._calculate_burst_anomaly_score(burst_analysis)
            
            # 判断是否异常
            anomaly_detected = anomaly_score > 0.3  # 降低判断阈值
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'burst_analysis': burst_analysis,
                'detection_method': 'optimized_transient_burst',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_energy_bursts(self, power_spectrogram, frequencies, times):
        """分析能量突增模式"""
        analysis = {
            'total_bursts': 0,
            'max_burst_intensity': 0.0,
            'max_affected_bands': 0.0,
            'burst_frequency_span': 0.0,
            'avg_relative_increase': 0.0,
            'burst_details': []
        }
        
        # 计算每个时间片的总能量
        time_energy = np.sum(power_spectrogram, axis=0)
        
        # 计算基线能量
        baseline_energy = np.percentile(time_energy, self.detection_params['baseline_percentile'])
        energy_std = np.std(time_energy)
        
        # 能量突增阈值
        burst_threshold = baseline_energy + self.detection_params['energy_threshold_sigma'] * energy_std
        
        # 检测能量突增时刻
        burst_indices = np.where(time_energy > burst_threshold)[0]
        
        if len(burst_indices) == 0:
            return analysis
        
        # 分析每个突增事件
        burst_groups = self._group_consecutive_indices(burst_indices)
        
        all_relative_increases = []
        
        for group in burst_groups:
            burst_start_idx = group[0]
            burst_end_idx = group[-1]
            burst_duration = (burst_end_idx - burst_start_idx + 1) * (times[1] - times[0])
            
            # 跳过过长的事件
            if burst_duration > self.detection_params['max_burst_duration']:
                continue
            
            # 分析突增的频率特征
            burst_power = power_spectrogram[:, burst_start_idx:burst_end_idx+1]
            avg_burst_power = np.mean(burst_power, axis=1)
            
            # 计算基线功率
            baseline_mask = np.ones(power_spectrogram.shape[1], dtype=bool)
            baseline_mask[burst_start_idx:burst_end_idx+1] = False
            baseline_power = np.mean(power_spectrogram[:, baseline_mask], axis=1)
            
            # 计算相对能量增幅
            relative_increase = (avg_burst_power - baseline_power) / (baseline_power + 1e-12)
            
            # 检测受影响的频段
            significant_increase_mask = relative_increase > self.detection_params['min_relative_increase']
            affected_bands_ratio = np.sum(significant_increase_mask) / len(frequencies)
            
            # 只考虑影响足够频段的突增
            if affected_bands_ratio >= self.detection_params['min_affected_bands']:
                burst_intensity = np.max(relative_increase)
                frequency_span = (np.max(frequencies[significant_increase_mask]) - 
                                np.min(frequencies[significant_increase_mask])) if np.any(significant_increase_mask) else 0
                
                analysis['total_bursts'] += 1
                analysis['max_burst_intensity'] = max(analysis['max_burst_intensity'], burst_intensity)
                analysis['max_affected_bands'] = max(analysis['max_affected_bands'], affected_bands_ratio)
                analysis['burst_frequency_span'] = max(analysis['burst_frequency_span'], frequency_span)
                
                all_relative_increases.extend(relative_increase[significant_increase_mask])
                
                analysis['burst_details'].append({
                    'time_start': times[burst_start_idx],
                    'time_end': times[burst_end_idx],
                    'duration': burst_duration,
                    'intensity': burst_intensity,
                    'affected_bands_ratio': affected_bands_ratio,
                    'frequency_span': frequency_span,
                    'peak_frequency': frequencies[np.argmax(relative_increase)]
                })
        
        # 计算平均相对增幅
        if all_relative_increases:
            analysis['avg_relative_increase'] = np.mean(all_relative_increases)
        
        return analysis
    
    def _group_consecutive_indices(self, indices):
        """将连续的索引分组"""
        if len(indices) == 0:
            return []
        
        groups = []
        current_group = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_group.append(indices[i])
            else:
                groups.append(current_group)
                current_group = [indices[i]]
        
        groups.append(current_group)
        return groups
    
    def _calculate_burst_anomaly_score(self, burst_analysis):
        """计算突增异常分数"""
        if burst_analysis['total_bursts'] == 0:
            return 0.0
        
        # 基于多个因素计算异常分数
        score_components = {
            'burst_count': min(1.0, burst_analysis['total_bursts'] / 3.0),  # 降低突增次数要求
            'intensity': min(1.0, burst_analysis['max_burst_intensity'] / 10.0),  # 降低强度要求
            'frequency_coverage': burst_analysis['max_affected_bands'] * 2,  # 提高频段覆盖权重
            'avg_increase': min(1.0, burst_analysis['avg_relative_increase'] / 5.0),  # 平均增幅
            'frequency_span': min(1.0, burst_analysis['burst_frequency_span'] / 2000.0)  # 频率跨度
        }
        
        # 加权计算最终分数
        weights = {
            'burst_count': 0.15,
            'intensity': 0.25,
            'frequency_coverage': 0.25,
            'avg_increase': 0.25,
            'frequency_span': 0.10
        }
        
        anomaly_score = sum(score_components[key] * weights[key] for key in weights)
        
        return anomaly_score
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'burst_analysis': {},
            'detection_method': 'optimized_transient_burst',
            'error': True,
            'error_message': error_msg
        }
    
    def analyze_positive_negative_samples(self):
        """分析正负样本的瞬时突增特征"""
        print("\n" + "="*80)
        print("优化后正负样本瞬时突增特征分析")
        print("="*80)
        
        # 定义正负样本路径
        positive_samples = []
        negative_samples = []
        
        # 收集正样本
        pos_dir = "../test20250717/pos"
        if os.path.exists(pos_dir):
            for root, dirs, files in os.walk(pos_dir):
                for file in files:
                    if file.endswith('.wav'):
                        positive_samples.append(os.path.join(root, file))
        
        # 收集负样本
        neg_dir = "../test20250717/neg"
        if os.path.exists(neg_dir):
            for file in os.listdir(neg_dir):
                if file.endswith('.wav'):
                    negative_samples.append(os.path.join(neg_dir, file))
        
        print(f"正样本数量: {len(positive_samples)}")
        print(f"负样本数量: {len(negative_samples)}")
        
        # 分析正样本
        print(f"\n分析正样本...")
        pos_results = []
        for sample in positive_samples:
            result = self.detect_transient_bursts(sample)
            result['sample_type'] = 'positive'
            result['filename'] = os.path.basename(sample)
            pos_results.append(result)
        
        # 分析负样本
        print(f"\n分析负样本...")
        neg_results = []
        for sample in negative_samples:
            result = self.detect_transient_bursts(sample)
            result['sample_type'] = 'negative'
            result['filename'] = os.path.basename(sample)
            neg_results.append(result)
        
        # 统计分析
        self._statistical_analysis(pos_results, neg_results)
        
        return pos_results, neg_results
    
    def _statistical_analysis(self, pos_results, neg_results):
        """统计分析正负样本差异"""
        print(f"\n优化后统计分析结果:")
        print("-" * 60)
        
        # 提取特征
        def extract_features(results):
            if len(results) == 0:
                return {
                    'anomaly_scores': [],
                    'burst_counts': [],
                    'max_intensities': [],
                    'max_affected_bands': [],
                    'detection_rate': 0.0
                }
            
            features = {
                'anomaly_scores': [r['anomaly_score'] for r in results if not r['error']],
                'burst_counts': [r['burst_analysis'].get('total_bursts', 0) for r in results if not r['error']],
                'max_intensities': [r['burst_analysis'].get('max_burst_intensity', 0) for r in results if not r['error']],
                'max_affected_bands': [r['burst_analysis'].get('max_affected_bands', 0) for r in results if not r['error']],
                'detection_rate': sum(1 for r in results if r['anomaly_detected']) / len(results) * 100
            }
            return features
        
        pos_features = extract_features(pos_results)
        neg_features = extract_features(neg_results)
        
        # 显示统计结果
        print(f"正样本统计:")
        print(f"  检出率: {pos_features['detection_rate']:.1f}%")
        if len(pos_features['anomaly_scores']) > 0:
            print(f"  平均异常分数: {np.mean(pos_features['anomaly_scores']):.3f} ± {np.std(pos_features['anomaly_scores']):.3f}")
            print(f"  平均突增次数: {np.mean(pos_features['burst_counts']):.1f} ± {np.std(pos_features['burst_counts']):.1f}")
            print(f"  平均最大强度: {np.mean(pos_features['max_intensities']):.2f} ± {np.std(pos_features['max_intensities']):.2f}")
        else:
            print(f"  无有效样本")
        
        print(f"\n负样本统计:")
        print(f"  检出率: {neg_features['detection_rate']:.1f}%")
        if len(neg_features['anomaly_scores']) > 0:
            print(f"  平均异常分数: {np.mean(neg_features['anomaly_scores']):.3f} ± {np.std(neg_features['anomaly_scores']):.3f}")
            print(f"  平均突增次数: {np.mean(neg_features['burst_counts']):.1f} ± {np.std(neg_features['burst_counts']):.1f}")
            print(f"  平均最大强度: {np.mean(neg_features['max_intensities']):.2f} ± {np.std(neg_features['max_intensities']):.2f}")
        else:
            print(f"  无有效样本")
        
        # 计算区分度
        if len(pos_features['anomaly_scores']) > 0 and len(neg_features['anomaly_scores']) > 0:
            # Cohen's d计算
            pooled_std = np.sqrt(((len(pos_features['anomaly_scores'])-1)*np.var(pos_features['anomaly_scores']) + 
                                 (len(neg_features['anomaly_scores'])-1)*np.var(neg_features['anomaly_scores'])) / 
                                (len(pos_features['anomaly_scores']) + len(neg_features['anomaly_scores']) - 2))
            
            if pooled_std > 0:
                cohens_d = (np.mean(neg_features['anomaly_scores']) - np.mean(pos_features['anomaly_scores'])) / pooled_std
                
                print(f"\n区分度分析:")
                print(f"  Cohen's d: {cohens_d:.3f}")
                if abs(cohens_d) > 0.8:
                    print(f"  效应量: 大 (区分度强)")
                elif abs(cohens_d) > 0.5:
                    print(f"  效应量: 中等")
                else:
                    print(f"  效应量: 小 (区分度弱)")
        
        # 显示具体检测结果
        print(f"\n负样本详细检测结果:")
        for result in neg_results:
            if not result['error']:
                status = "异常" if result['anomaly_detected'] else "正常"
                bursts = result['burst_analysis'].get('total_bursts', 0)
                intensity = result['burst_analysis'].get('max_burst_intensity', 0)
                print(f"  {result['filename']}: {status} (分数:{result['anomaly_score']:.3f}, 突增:{bursts}次, 强度:{intensity:.1f})")

def main():
    """主函数"""
    # 初始化优化检测器
    detector = OptimizedTransientDetector()
    
    # 分析正负样本
    pos_results, neg_results = detector.analyze_positive_negative_samples()
    
    # 保存结果
    all_results = pos_results + neg_results
    
    # 转换为DataFrame
    results_data = []
    for result in all_results:
        row = {
            'filename': result['filename'],
            'sample_type': result['sample_type'],
            'anomaly_detected': result['anomaly_detected'],
            'confidence': result['confidence'],
            'anomaly_score': result['anomaly_score'],
            'total_bursts': result['burst_analysis'].get('total_bursts', 0),
            'max_burst_intensity': result['burst_analysis'].get('max_burst_intensity', 0),
            'max_affected_bands': result['burst_analysis'].get('max_affected_bands', 0),
            'avg_relative_increase': result['burst_analysis'].get('avg_relative_increase', 0),
            'burst_frequency_span': result['burst_analysis'].get('burst_frequency_span', 0),
            'error': result['error']
        }
        results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('optimized_transient_burst_analysis.csv', index=False)
    
    print(f"\n优化瞬时突增检测结果已保存: optimized_transient_burst_analysis.csv")
    
    return detector, pos_results, neg_results

if __name__ == "__main__":
    detector, pos_results, neg_results = main()
