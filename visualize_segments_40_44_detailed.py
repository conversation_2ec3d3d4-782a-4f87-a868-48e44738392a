#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独可视化40-44频段的主频、谐波频谱定位
详细展示每个频段的频谱分析结果
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def visualize_segments_40_44_detailed():
    """详细可视化40-44频段的频谱分析"""
    print("🎨 详细可视化40-44频段的主频、谐波频谱定位")
    print("="*70)
    
    # 两个异常样本
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    target_segments = [40, 41, 42, 43, 44]  # 目标频段
    
    # 分析每个样本的目标频段
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 分析 {sample_name}: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        if os.path.exists(audio_path):
            # 分析目标频段
            segment_analyses = analyze_target_segments(audio_path, sample_name, target_segments)
            
            # 创建详细的频谱可视化
            create_detailed_spectrum_visualization(segment_analyses, sample_name, target_segments)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")

def analyze_target_segments(audio_path, sample_name, target_segments):
    """分析目标频段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_analyses = []
        
        # 分析每个目标频段
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            print(f"   分析频段 {seg_idx} ({expected_freq:.1f}Hz)")
            
            # 详细分析该频段
            segment_analysis = analyze_single_segment_detailed(
                segment_audio, sr, expected_freq, seg_idx, start_time, end_time
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
        
        return segment_analyses
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return []

def analyze_single_segment_detailed(audio, sr, expected_freq, seg_idx, start_time, end_time):
    """详细分析单个频段"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(32768, len(audio))  # 更高分辨率
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数减少频谱泄漏
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只分析正频率
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_detailed(positive_freqs, positive_power, expected_freq)
        
        # 2. 噪声底噪估计
        noise_floor_analysis = estimate_noise_floor_detailed(positive_freqs, positive_power, expected_freq)
        
        # 3. 谐波检测 (更严格)
        harmonic_analysis = detect_harmonics_detailed(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_floor_analysis, sr
        )
        
        # 4. 频谱峰值检测 (用于对比)
        all_peaks_analysis = detect_all_spectrum_peaks(positive_freqs, positive_power, power_db)
        
        # 5. 噪声区域标识
        noise_regions = identify_noise_regions_detailed(
            positive_freqs, positive_power, fundamental_analysis, harmonic_analysis
        )
        
        print(f"     主频: {fundamental_analysis['freq']:.2f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"     谐波: {len(harmonic_analysis)}个")
        print(f"     噪声底噪: {noise_floor_analysis['noise_floor_db']:.1f}dB")
        print(f"     频率分辨率: {freq_resolution:.3f}Hz")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'start_time': start_time,
            'end_time': end_time,
            'duration': end_time - start_time,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_floor_analysis': noise_floor_analysis,
            'harmonic_analysis': harmonic_analysis,
            'all_peaks_analysis': all_peaks_analysis,
            'noise_regions': noise_regions,
            'freq_resolution': freq_resolution,
            'audio_samples': len(audio)
        }
        
    except Exception as e:
        print(f"     ❌ 频段{seg_idx}分析失败: {e}")
        return None

def analyze_fundamental_detailed(freqs, power, expected_freq):
    """详细分析主频"""
    
    # 精确的搜索带宽
    if expected_freq <= 1000:
        bandwidth = 3.0
    elif expected_freq <= 2000:
        bandwidth = 5.0
    elif expected_freq <= 5000:
        bandwidth = 8.0
    else:
        bandwidth = 12.0
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    search_freqs = freqs[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    # 计算主频的峰值宽度 (3dB带宽)
    half_power = fundamental_power / 2
    left_idx = actual_idx
    right_idx = actual_idx
    
    # 向左搜索3dB点
    while left_idx > 0 and power[left_idx] > half_power:
        left_idx -= 1
    
    # 向右搜索3dB点
    while right_idx < len(power) - 1 and power[right_idx] > half_power:
        right_idx += 1
    
    bandwidth_3db = freqs[right_idx] - freqs[left_idx]
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'expected_freq': expected_freq,
        'freq_error': fundamental_freq - expected_freq,
        'search_bandwidth': bandwidth,
        'bandwidth_3db': bandwidth_3db,
        'index': actual_idx,
        'search_range': (freqs[search_indices[0]], freqs[search_indices[-1]]),
        'peak_sharpness': fundamental_power / np.mean(search_powers)  # 峰值锐度
    }

def estimate_noise_floor_detailed(freqs, power, fundamental_freq):
    """详细估计噪声底噪"""
    
    # 排除主频和谐波位置 (更宽的排除范围)
    excluded_ranges = []
    
    # 排除主频±30Hz
    excluded_ranges.append((fundamental_freq - 30, fundamental_freq + 30))
    
    # 排除前8个谐波位置±30Hz
    for order in range(2, 9):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 30, harmonic_freq + 30))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        
        # 多种噪声底噪估计
        noise_floor_10th = np.percentile(noise_powers, 10)
        noise_floor_25th = np.percentile(noise_powers, 25)
        noise_floor_median = np.median(noise_powers)
        noise_floor_mean = np.mean(noise_powers)
        
        # 使用25th percentile作为保守估计
        noise_floor = noise_floor_25th
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_floor_10th_db': 10 * np.log10(noise_floor_10th + 1e-12),
            'noise_floor_median_db': 10 * np.log10(noise_floor_median + 1e-12),
            'noise_floor_mean_db': 10 * np.log10(noise_floor_mean + 1e-12),
            'noise_sample_count': np.sum(noise_mask),
            'excluded_ranges': excluded_ranges
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0,
            'excluded_ranges': excluded_ranges
        }

def detect_harmonics_detailed(freqs, power, fundamental_freq, 
                             fundamental_analysis, noise_floor_analysis, sr):
    """详细检测谐波"""
    
    if not fundamental_analysis or not noise_floor_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_floor_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 动态SNR阈值
    base_snr_threshold = 12.0
    if fundamental_freq <= 1000:
        min_snr_db = base_snr_threshold
    elif fundamental_freq <= 3000:
        min_snr_db = base_snr_threshold + 2.0
    else:
        min_snr_db = base_snr_threshold + 5.0
    
    nyquist_freq = sr / 2
    
    print(f"       谐波检测 (SNR阈值: {min_snr_db:.1f}dB):")
    
    for order in range(2, 10):  # 检测2-9次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            print(f"         {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率")
            break
        
        if expected_harmonic_freq > freqs[-1]:
            print(f"         {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超出频谱范围")
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 2000:
            search_bandwidth = 8.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 12.0
        else:
            search_bandwidth = 20.0
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算各种指标
        harmonic_snr_db = harmonic_power_db - noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 计算谐波的峰值锐度
        peak_sharpness = harmonic_power / np.mean(search_powers)
        
        # 严格的谐波判断条件
        conditions = {
            'snr_sufficient': harmonic_snr_db >= min_snr_db,
            'power_reasonable': relative_power_db >= -50.0,  # 不能比基频弱太多
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.5,
            'peak_sharp': peak_sharpness >= 2.0  # 峰值必须足够尖锐
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': harmonic_snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'search_bandwidth': search_bandwidth,
                'peak_sharpness': peak_sharpness,
                'index': actual_idx
            })
            
            print(f"         ✅ {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"SNR={harmonic_snr_db:.1f}dB, 误差{freq_error:.1f}Hz")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"         ❌ {order}次谐波: {actual_freq:.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            SNR={harmonic_snr_db:.1f}dB, 相对功率={relative_power_db:.1f}dB, "
                  f"误差={freq_error:.1f}Hz, 锐度={peak_sharpness:.1f}")
    
    return detected_harmonics

def detect_all_spectrum_peaks(freqs, power, power_db):
    """检测所有频谱峰值 (用于对比)"""
    
    # 峰值检测参数
    min_height_db = -50
    min_prominence_db = 3
    min_distance = 5
    
    # 转换阈值
    min_height = 10 ** (min_height_db / 10)
    
    # 计算prominence阈值
    window_size = 20
    local_background = np.convolve(power, np.ones(window_size)/window_size, mode='same')
    prominence_threshold = local_background * (10 ** (min_prominence_db / 10))
    
    # 检测峰值
    peaks, properties = find_peaks(power,
                                  height=min_height,
                                  prominence=prominence_threshold,
                                  distance=min_distance)
    
    if len(peaks) > 0:
        peak_info = []
        for peak_idx in peaks:
            peak_info.append({
                'index': peak_idx,
                'freq': freqs[peak_idx],
                'power': power[peak_idx],
                'power_db': power_db[peak_idx]
            })
        
        # 按功率排序
        peak_info.sort(key=lambda x: x['power'], reverse=True)
        return peak_info[:20]  # 只保留前20个最强峰值
    else:
        return []

def identify_noise_regions_detailed(freqs, power, fundamental_analysis, harmonic_analysis):
    """详细标识噪声区域"""
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频区域
    if fundamental_analysis:
        fund_freq = fundamental_analysis['freq']
        fund_bandwidth = fundamental_analysis['search_bandwidth']
        fund_mask = (freqs >= fund_freq - fund_bandwidth) & (freqs <= fund_freq + fund_bandwidth)
        noise_mask &= ~fund_mask
    
    # 排除谐波区域
    for harmonic in harmonic_analysis:
        harm_freq = harmonic['freq']
        harm_bandwidth = harmonic['search_bandwidth']
        harm_mask = (freqs >= harm_freq - harm_bandwidth) & (freqs <= harm_freq + harm_bandwidth)
        noise_mask &= ~harm_mask
    
    return {
        'noise_mask': noise_mask,
        'noise_frequency_count': np.sum(noise_mask),
        'noise_frequency_ratio': np.sum(noise_mask) / len(freqs),
        'total_noise_power': np.sum(power[noise_mask]) if np.any(noise_mask) else 0
    }

def create_detailed_spectrum_visualization(segment_analyses, sample_name, target_segments):
    """创建详细的频谱可视化"""
    
    if not segment_analyses:
        print(f"   ❌ 无有效分析数据")
        return
    
    print(f"   🎨 生成 {sample_name} 频段40-44详细频谱可视化...")
    
    # 创建大图 (5行1列，每行一个频段)
    fig, axes = plt.subplots(5, 1, figsize=(20, 25))
    
    for i, segment_analysis in enumerate(segment_analyses):
        ax = axes[i]
        
        # 提取数据
        freqs = segment_analysis['freqs']
        power_db = segment_analysis['power_db']
        fundamental_analysis = segment_analysis['fundamental_analysis']
        harmonic_analysis = segment_analysis['harmonic_analysis']
        noise_floor_analysis = segment_analysis['noise_floor_analysis']
        all_peaks_analysis = segment_analysis['all_peaks_analysis']
        noise_regions = segment_analysis['noise_regions']
        
        seg_idx = segment_analysis['segment_idx']
        expected_freq = segment_analysis['expected_freq']
        
        # 设置显示范围 (聚焦在相关频率范围)
        if expected_freq <= 1000:
            freq_range = (0, expected_freq * 8)
        elif expected_freq <= 3000:
            freq_range = (0, expected_freq * 6)
        else:
            freq_range = (0, expected_freq * 4)
        
        freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
        display_freqs = freqs[freq_mask]
        display_power_db = power_db[freq_mask]
        
        # 绘制基础频谱
        ax.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
        
        # 标记噪声底噪线
        noise_floor_db = noise_floor_analysis['noise_floor_db']
        ax.axhline(y=noise_floor_db, color='gray', linestyle='--', alpha=0.8, 
                  label=f'噪声底噪 {noise_floor_db:.1f}dB')
        
        # SNR阈值线
        if expected_freq <= 1000:
            snr_threshold = 12.0
        elif expected_freq <= 3000:
            snr_threshold = 14.0
        else:
            snr_threshold = 17.0
        
        threshold_line_db = noise_floor_db + snr_threshold
        ax.axhline(y=threshold_line_db, color='orange', linestyle='--', alpha=0.8,
                  label=f'谐波阈值 {threshold_line_db:.1f}dB')
        
        # 标记所有检测到的峰值 (小圆点)
        if all_peaks_analysis:
            peak_freqs = [p['freq'] for p in all_peaks_analysis if freq_range[0] <= p['freq'] <= freq_range[1]]
            peak_powers = [p['power_db'] for p in all_peaks_analysis if freq_range[0] <= p['freq'] <= freq_range[1]]
            if peak_freqs:
                ax.plot(peak_freqs, peak_powers, 'go', markersize=3, alpha=0.6, label='所有峰值')
        
        # 标记主频 (大红圆)
        if fundamental_analysis:
            fund_freq = fundamental_analysis['freq']
            fund_power_db = fundamental_analysis['power_db']
            if freq_range[0] <= fund_freq <= freq_range[1]:
                ax.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
                       label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
                
                # 主频标注
                ax.annotate(f'主频\n{fund_freq:.1f}Hz\n{fund_power_db:.1f}dB', 
                           xy=(fund_freq, fund_power_db), 
                           xytext=(fund_freq, fund_power_db + 15),
                           ha='center', va='bottom', fontweight='bold', color='red',
                           arrowprops=dict(arrowstyle='->', color='red', lw=2),
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记检测到的谐波 (彩色大圆)
        harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta', 'olive']
        
        for j, harmonic in enumerate(harmonic_analysis):
            if j >= len(harmonic_colors):
                break
                
            harm_freq = harmonic['freq']
            harm_power_db = harmonic['power_db']
            harm_order = harmonic['order']
            harm_snr = harmonic['snr_db']
            
            if freq_range[0] <= harm_freq <= freq_range[1]:
                color = harmonic_colors[j]
                ax.plot(harm_freq, harm_power_db, 'o', color=color, markersize=10,
                       label=f'{harm_order}次谐波 {harm_freq:.0f}Hz (SNR={harm_snr:.1f}dB)')
                
                # 谐波标注
                ax.annotate(f'{harm_order}次谐波\n{harm_freq:.0f}Hz\nSNR={harm_snr:.1f}dB', 
                           xy=(harm_freq, harm_power_db), 
                           xytext=(harm_freq, harm_power_db + 10),
                           ha='center', va='bottom', fontsize=9, color=color,
                           arrowprops=dict(arrowstyle='->', color=color, lw=1.5))
        
        # 标记噪声区域 (半透明蓝色)
        if noise_regions and np.any(noise_regions['noise_mask']):
            noise_mask = noise_regions['noise_mask']
            noise_freqs = freqs[noise_mask]
            noise_powers = power_db[noise_mask]
            
            # 只显示在当前频率范围内的噪声
            display_noise_mask = (noise_freqs >= freq_range[0]) & (noise_freqs <= freq_range[1])
            if np.any(display_noise_mask):
                display_noise_freqs = noise_freqs[display_noise_mask]
                display_noise_powers = noise_powers[display_noise_mask]
                
                ax.fill_between(display_freqs, -150, display_power_db, 
                               where=np.isin(display_freqs, display_noise_freqs, assume_unique=False),
                               alpha=0.2, color='blue', label='噪声区域')
        
        # 设置图表属性
        ax.set_title(f'{sample_name} - 频段{seg_idx} ({expected_freq:.1f}Hz) 详细频谱分析', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(freq_range)
        ax.set_ylim(np.min(display_power_db) - 10, np.max(display_power_db) + 25)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.grid(True, alpha=0.3)
        
        # 添加频段信息文本框
        info_text = f"频段信息:\n"
        info_text += f"期望频率: {expected_freq:.1f}Hz\n"
        info_text += f"实际主频: {fundamental_analysis['freq']:.2f}Hz\n"
        info_text += f"频率误差: {fundamental_analysis['freq_error']:.2f}Hz\n"
        info_text += f"主频功率: {fundamental_analysis['power_db']:.1f}dB\n"
        info_text += f"检测谐波: {len(harmonic_analysis)}个\n"
        info_text += f"噪声底噪: {noise_floor_db:.1f}dB\n"
        info_text += f"频率分辨率: {segment_analysis['freq_resolution']:.3f}Hz"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'detailed_spectrum_segments_40_44_{sample_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 详细频谱可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    visualize_segments_40_44_detailed()
