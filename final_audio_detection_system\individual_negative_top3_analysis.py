#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为每个负样本单独分析最有效的3个特征并可视化
包括频段切分对应关系 - 要求与所有正样本完全分离
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import librosa

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_individual_top3_features():
    """为每个负样本分析最有效的3个特征"""
    print("🔍 为每个负样本单独分析最有效的3个特征")
    print("标准：与所有正样本取值范围完全分离")
    print("="*70)
    
    # 加载特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 排除竖线检测特征和元信息
    exclude_features = [col for col in df.columns if col.startswith('vl_')] + \
                      ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    
    candidate_features = [col for col in df.columns if col not in exclude_features]
    print(f"📊 候选特征数: {len(candidate_features)}个")
    
    # 获取所有负样本文件
    neg_files = df[df['label'] == 'neg']['filename'].unique()
    pos_data = df[df['label'] == 'pos']
    
    print(f"📊 负样本文件: {len(neg_files)}个")
    print(f"📊 正样本数据: {len(pos_data)}个频段")
    
    # 为每个负样本分析
    for neg_file in neg_files:
        print(f"\n🔍 分析负样本: {neg_file}")
        print("="*60)
        
        # 获取该负样本的数据
        neg_file_data = df[df['filename'] == neg_file]
        
        # 分析最有效的3个特征
        top3_features = find_top3_features_for_file(pos_data, neg_file_data, candidate_features, neg_file)
        
        # 可视化该文件的特征
        if len(top3_features) > 0:
            visualize_file_features(pos_data, neg_file_data, top3_features, neg_file)
        
        # 显示频段切分信息
        show_segment_info(neg_file_data, top3_features, neg_file)

def find_top3_features_for_file(pos_data, neg_file_data, candidate_features, neg_file):
    """为单个文件找到最有效的3个特征（只考虑完全分离的特征）"""
    print(f"   分析 {len(candidate_features)} 个特征...")
    
    feature_scores = []
    
    for feature in candidate_features:
        # 计算该特征在所有频段的完全分离能力
        separable_segments = []
        total_separation_score = 0
        
        for segment_idx in sorted(neg_file_data['segment_idx'].unique()):
            pos_segment = pos_data[pos_data['segment_idx'] == segment_idx]
            neg_segment = neg_file_data[neg_file_data['segment_idx'] == segment_idx]
            
            if len(pos_segment) == 0 or len(neg_segment) == 0:
                continue
            
            # 计算分离能力
            separation_result = calculate_separation_score(pos_segment, neg_segment, feature)
            
            if separation_result['valid'] and separation_result.get('complete_separation', False):
                separable_segments.append({
                    'segment_idx': segment_idx,
                    'score': separation_result['score'],
                    'separation_type': separation_result['separation_type'],
                    'separation_gap': separation_result['separation_gap'],
                    'pos_range': separation_result['pos_range'],
                    'neg_range': separation_result['neg_range'],
                    'expected_freq': neg_segment['expected_freq'].iloc[0] if len(neg_segment) > 0 else 0
                })
                total_separation_score += separation_result['score']
        
        # 只考虑有完全分离频段的特征
        if len(separable_segments) > 0:
            avg_separation_score = total_separation_score / len(separable_segments)
            feature_scores.append({
                'feature': feature,
                'avg_separation_score': avg_separation_score,
                'separable_segments_count': len(separable_segments),
                'separable_segments': separable_segments,
                'best_segment': max(separable_segments, key=lambda x: x['score'])
            })
    
    # 排序并选择前3个
    feature_scores.sort(key=lambda x: x['avg_separation_score'], reverse=True)
    top3 = feature_scores[:3]
    
    print(f"   ✅ 找到 {len(feature_scores)} 个完全分离特征，最有效的3个:")
    for i, feature_info in enumerate(top3, 1):
        best_seg = feature_info['best_segment']
        print(f"     {i}. {feature_info['feature']}")
        print(f"        平均分离评分: {feature_info['avg_separation_score']:.3f}")
        print(f"        完全分离频段数: {feature_info['separable_segments_count']}")
        print(f"        最佳频段: {best_seg['segment_idx']} ({best_seg['expected_freq']:.1f}Hz)")
        print(f"        分离类型: {best_seg['separation_type']}")
        print(f"        分离间隙: {best_seg['separation_gap']:.6f}")
        print(f"        正样本范围: [{best_seg['pos_range'][0]:.6f}, {best_seg['pos_range'][1]:.6f}]")
        print(f"        负样本范围: [{best_seg['neg_range'][0]:.6f}, {best_seg['neg_range'][1]:.6f}]")
    
    if len(top3) == 0:
        print(f"   ❌ 未找到完全分离的特征")
    
    return top3

def calculate_separation_score(pos_segment, neg_segment, feature):
    """计算完全分离评分 - 与所有正样本取值范围完全分离"""
    try:
        pos_values = pos_segment[feature].dropna()
        neg_values = neg_segment[feature].dropna()
        
        if len(pos_values) == 0 or len(neg_values) == 0:
            return {'valid': False, 'score': 0, 'separation_type': 'no_data'}
        
        # 获取正样本的完整取值范围
        pos_min = np.min(pos_values)
        pos_max = np.max(pos_values)
        
        # 获取负样本的完整取值范围
        neg_min = np.min(neg_values)
        neg_max = np.max(neg_values)
        
        # 检查是否完全分离
        complete_separation = False
        separation_gap = 0
        separation_type = 'no_separation'
        
        if neg_max < pos_min:
            # 负样本完全在正样本下方
            complete_separation = True
            separation_gap = pos_min - neg_max
            separation_type = 'neg_below_pos'
        elif neg_min > pos_max:
            # 负样本完全在正样本上方
            complete_separation = True
            separation_gap = neg_min - pos_max
            separation_type = 'neg_above_pos'
        
        if complete_separation:
            # 计算分离质量评分
            pos_range = pos_max - pos_min
            neg_range = neg_max - neg_min
            total_range = max(pos_max, neg_max) - min(pos_min, neg_min)
            
            # 分离评分 = 分离间隙 / 总范围 * 1000 (放大以便比较)
            if total_range > 0:
                separation_score = (separation_gap / total_range) * 1000
            else:
                separation_score = 1000  # 如果范围为0，给最高分
            
            return {
                'valid': True, 
                'score': separation_score,
                'complete_separation': True,
                'separation_type': separation_type,
                'separation_gap': separation_gap,
                'pos_range': [pos_min, pos_max],
                'neg_range': [neg_min, neg_max],
                'pos_count': len(pos_values),
                'neg_count': len(neg_values)
            }
        else:
            # 没有完全分离
            return {
                'valid': True, 
                'score': 0,
                'complete_separation': False,
                'separation_type': 'overlap',
                'pos_range': [pos_min, pos_max],
                'neg_range': [neg_min, neg_max],
                'pos_count': len(pos_values),
                'neg_count': len(neg_values)
            }
        
    except Exception as e:
        return {'valid': False, 'score': 0, 'error': str(e)}

def visualize_file_features(pos_data, neg_file_data, top3_features, neg_file):
    """可视化文件的特征"""
    print(f"   🎨 生成可视化图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{neg_file} - 最有效的3个完全分离特征', fontsize=16, fontweight='bold')
    
    # 1. 特征分布对比（第一个特征）
    ax1 = axes[0, 0]
    
    if len(top3_features) > 0:
        feature_info = top3_features[0]
        feature = feature_info['feature']
        
        # 获取所有频段的该特征值
        pos_values = pos_data[feature].dropna()
        neg_values = neg_file_data[feature].dropna()
        
        if len(pos_values) > 0 and len(neg_values) > 0:
            # 绘制分布
            ax1.hist(pos_values, bins=30, alpha=0.6, color='green', 
                    label=f'正样本 (n={len(pos_values)})', density=True)
            ax1.hist(neg_values, bins=30, alpha=0.8, color='red', 
                    label=f'{neg_file} (n={len(neg_values)})', density=True)
            
            # 标记分离区域
            pos_min, pos_max = np.min(pos_values), np.max(pos_values)
            neg_min, neg_max = np.min(neg_values), np.max(neg_values)
            
            # 添加范围标记
            ax1.axvspan(pos_min, pos_max, alpha=0.2, color='green', label='正样本范围')
            ax1.axvspan(neg_min, neg_max, alpha=0.2, color='red', label='负样本范围')
            
            ax1.set_title(f'完全分离特征分布: {feature}')
            ax1.set_xlabel('特征值')
            ax1.set_ylabel('密度')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 显示分离信息
            if neg_max < pos_min:
                separation_gap = pos_min - neg_max
                ax1.text(0.5, 0.95, f'完全分离: 负样本在下方\n分离间隙: {separation_gap:.6f}', 
                        transform=ax1.transAxes, ha='center', va='top', 
                        bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
            elif neg_min > pos_max:
                separation_gap = neg_min - pos_max
                ax1.text(0.5, 0.95, f'完全分离: 负样本在上方\n分离间隙: {separation_gap:.6f}', 
                        transform=ax1.transAxes, ha='center', va='top',
                        bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    else:
        ax1.text(0.5, 0.5, '未找到完全分离的特征', transform=ax1.transAxes, 
                ha='center', va='center', fontsize=14, color='red')
    
    # 2. 频段级特征范围对比（显示完整取值范围，不是均值）
    ax2 = axes[0, 1]

    if len(top3_features) > 0:
        segments = sorted(neg_file_data['segment_idx'].unique())
        feature1 = top3_features[0]['feature']

        pos_min_values = []
        pos_max_values = []
        neg_min_values = []
        neg_max_values = []
        valid_segments = []
        separation_flags = []

        # 确保处理所有93个频段（0-92）
        all_segments = list(range(93))  # 强制包含所有93个频段

        for segment_idx in all_segments:
            pos_seg = pos_data[pos_data['segment_idx'] == segment_idx]
            neg_seg = neg_file_data[neg_file_data['segment_idx'] == segment_idx]

            if len(pos_seg) > 0 and len(neg_seg) > 0:
                pos_values = pos_seg[feature1].dropna()
                neg_values = neg_seg[feature1].dropna()

                if len(pos_values) > 0 and len(neg_values) > 0:
                    pos_min = np.min(pos_values)
                    pos_max = np.max(pos_values)
                    neg_min = np.min(neg_values)
                    neg_max = np.max(neg_values)

                    # 检查是否完全分离
                    is_separated = (neg_max < pos_min) or (neg_min > pos_max)

                    pos_min_values.append(pos_min)
                    pos_max_values.append(pos_max)
                    neg_min_values.append(neg_min)
                    neg_max_values.append(neg_max)
                    valid_segments.append(segment_idx)
                    separation_flags.append(is_separated)
                else:
                    # 没有有效数据的频段，用NaN填充
                    pos_min_values.append(np.nan)
                    pos_max_values.append(np.nan)
                    neg_min_values.append(np.nan)
                    neg_max_values.append(np.nan)
                    valid_segments.append(segment_idx)
                    separation_flags.append(False)
            else:
                # 没有数据的频段，用NaN填充
                pos_min_values.append(np.nan)
                pos_max_values.append(np.nan)
                neg_min_values.append(np.nan)
                neg_max_values.append(np.nan)
                valid_segments.append(segment_idx)
                separation_flags.append(False)

        if valid_segments:
            # 绘制正样本范围（绿色区域）
            ax2.fill_between(valid_segments, pos_min_values, pos_max_values,
                           alpha=0.3, color='green', label='正样本范围')
            ax2.plot(valid_segments, pos_min_values, 'g-', alpha=0.7, linewidth=1)
            ax2.plot(valid_segments, pos_max_values, 'g-', alpha=0.7, linewidth=1)

            # 绘制负样本范围（红色区域）
            ax2.fill_between(valid_segments, neg_min_values, neg_max_values,
                           alpha=0.3, color='red', label=f'{neg_file}范围')
            ax2.plot(valid_segments, neg_min_values, 'r-', alpha=0.7, linewidth=1)
            ax2.plot(valid_segments, neg_max_values, 'r-', alpha=0.7, linewidth=1)

            # 标记完全分离的频段
            separated_segments = [seg for seg, flag in zip(valid_segments, separation_flags) if flag]
            if separated_segments:
                ax2.scatter(separated_segments,
                          [pos_min_values[valid_segments.index(seg)] for seg in separated_segments],
                          marker='*', s=50, color='gold', label='完全分离频段', zorder=5)

            ax2.set_title(f'频段级特征范围对比: {feature1}\n(显示完整取值范围，不是均值)')
            ax2.set_xlabel('频段索引')
            ax2.set_ylabel('特征值')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 添加分离统计信息
            separated_count = sum(separation_flags)
            total_count = len(separation_flags)
            ax2.text(0.02, 0.98, f'完全分离频段: {separated_count}/{total_count}',
                    transform=ax2.transAxes, va='top', ha='left',
                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    # 3. 特征重要性条形图
    ax3 = axes[1, 0]
    
    if len(top3_features) > 0:
        feature_names = [info['feature'] for info in top3_features]
        scores = [info['avg_separation_score'] for info in top3_features]
        segment_counts = [info['separable_segments_count'] for info in top3_features]
        
        bars = ax3.bar(range(len(feature_names)), scores, color=['red', 'orange', 'yellow'])
        ax3.set_title('完全分离特征重要性')
        ax3.set_xlabel('特征')
        ax3.set_ylabel('平均分离评分')
        ax3.set_xticks(range(len(feature_names)))
        ax3.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in feature_names], 
                           rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        
        # 在条形图上添加数值和频段数
        for i, (bar, score, count) in enumerate(zip(bars, scores, segment_counts)):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{score:.1f}\n({count}段)', ha='center', va='bottom', fontsize=8)
    else:
        ax3.text(0.5, 0.5, '无完全分离特征', transform=ax3.transAxes, 
                ha='center', va='center', fontsize=14, color='red')
    
    # 4. 完全分离频段分布（基于实际分离分析）
    ax4 = axes[1, 1]

    if len(top3_features) > 0:
        # 统计每个频段在各特征中的分离情况
        segments = sorted(neg_file_data['segment_idx'].unique())
        frequencies = []
        separation_counts = []  # 每个频段能被多少个特征完全分离

        for seg_idx in segments:
            seg_data = neg_file_data[neg_file_data['segment_idx'] == seg_idx]
            if len(seg_data) > 0:
                freq = seg_data['expected_freq'].iloc[0]
                frequencies.append(freq)

                # 计算该频段被多少个top3特征完全分离
                separation_count = 0
                for feature_info in top3_features:
                    for seg_info in feature_info['separable_segments']:
                        if seg_info['segment_idx'] == seg_idx:
                            separation_count += 1
                            break  # 每个特征最多计算一次

                separation_counts.append(separation_count)

        if frequencies:
            # 使用颜色表示分离程度
            max_count = max(separation_counts) if separation_counts else 1
            colors = []
            for count in separation_counts:
                if count == 0:
                    colors.append('lightgray')  # 无法分离
                elif count == 1:
                    colors.append('orange')     # 1个特征可分离
                elif count == 2:
                    colors.append('red')        # 2个特征可分离
                else:
                    colors.append('darkred')    # 3个特征可分离

            # 绘制散点图
            scatter = ax4.scatter(frequencies, separation_counts, c=colors, alpha=0.7, s=30)

            ax4.set_title('完全分离频段分布\n(基于Top3特征的分离能力)')
            ax4.set_xlabel('频率 (Hz)')
            ax4.set_ylabel('可分离特征数量')
            ax4.set_xscale('log')
            ax4.set_ylim(-0.1, max_count + 0.1)
            ax4.grid(True, alpha=0.3)

            # 添加图例
            ax4.scatter([], [], c='lightgray', label='无法分离', s=30)
            ax4.scatter([], [], c='orange', label='1个特征可分离', s=30)
            ax4.scatter([], [], c='red', label='2个特征可分离', s=30)
            ax4.scatter([], [], c='darkred', label='3个特征可分离', s=30)
            ax4.legend(loc='upper right')

            # 添加统计信息
            total_segments = len(separation_counts)
            separable_segments = sum(1 for count in separation_counts if count > 0)
            ax4.text(0.02, 0.98, f'可分离频段: {separable_segments}/{total_segments}\n'
                                 f'分离率: {separable_segments/total_segments*100:.1f}%',
                    transform=ax4.transAxes, va='top', ha='left',
                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    else:
        ax4.text(0.5, 0.5, '无完全分离特征', transform=ax4.transAxes,
                ha='center', va='center', fontsize=14, color='red')
    
    plt.tight_layout()
    
    # 保存图片
    safe_filename = neg_file.replace('.wav', '').replace('_', '-').replace(' ', '-')
    plt.savefig(f'{safe_filename}_top3_features.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"   ✅ 图表已保存: {safe_filename}_top3_features.png")

def show_segment_info(neg_file_data, top3_features, neg_file):
    """显示频段切分信息"""
    print(f"   📊 频段切分信息:")
    print("   " + "-"*50)
    
    segments = sorted(neg_file_data['segment_idx'].unique())
    
    print(f"   总频段数: {len(segments)}")
    print(f"   频率范围: {neg_file_data['expected_freq'].min():.1f}Hz - {neg_file_data['expected_freq'].max():.1f}Hz")
    
    # 显示前10个频段的详细信息
    print(f"   前10个频段详情:")
    for i, segment_idx in enumerate(segments[:10]):
        seg_data = neg_file_data[neg_file_data['segment_idx'] == segment_idx].iloc[0]
        print(f"     频段{segment_idx:2d}: {seg_data['expected_freq']:6.1f}Hz "
              f"({seg_data['start_time']:.3f}s-{seg_data['end_time']:.3f}s)")
    
    if len(segments) > 10:
        print(f"     ... 还有{len(segments)-10}个频段")
    
    # 显示最有效特征的完全分离频段
    print(f"   最有效特征的完全分离频段:")
    
    for i, feature_info in enumerate(top3_features, 1):
        feature = feature_info['feature']
        separable_segments = feature_info.get('separable_segments', [])
        
        print(f"     {i}. {feature} (共{len(separable_segments)}个完全分离频段):")
        
        if len(separable_segments) > 0:
            # 按分离评分排序，显示前5个最好的
            sorted_segments = sorted(separable_segments, key=lambda x: x['score'], reverse=True)
            
            for j, seg_info in enumerate(sorted_segments[:5]):
                seg_idx = seg_info['segment_idx']
                freq = seg_info['expected_freq']
                score = seg_info['score']
                sep_type = seg_info['separation_type']
                gap = seg_info['separation_gap']
                pos_range = seg_info['pos_range']
                neg_range = seg_info['neg_range']
                
                print(f"       频段{seg_idx:2d} ({freq:6.1f}Hz): 评分{score:.1f}")
                print(f"         分离类型: {sep_type}, 间隙: {gap:.6f}")
                print(f"         正样本: [{pos_range[0]:.6f}, {pos_range[1]:.6f}]")
                print(f"         负样本: [{neg_range[0]:.6f}, {neg_range[1]:.6f}]")
            
            if len(sorted_segments) > 5:
                print(f"       ... 还有{len(sorted_segments)-5}个分离频段")
        else:
            print(f"       无完全分离频段")

if __name__ == "__main__":
    # 执行分析
    analyze_individual_top3_features()
    
    print(f"\n✅ 个体负样本Top3特征分析完成！")
    print(f"📊 为每个负样本生成了:")
    print(f"  - 最有效的3个完全分离特征分析")
    print(f"  - 特征可视化图表")
    print(f"  - 频段切分信息")
    print(f"  - 完全分离频段的详细信息")
