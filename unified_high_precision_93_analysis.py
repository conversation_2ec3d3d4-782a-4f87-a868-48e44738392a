#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一高精度93段分析：使用与前4段相同的高精度算法
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def unified_high_precision_analysis():
    """统一高精度93段分析"""
    print("🎯 统一高精度93段分析 - 使用与前4段相同的算法")
    print("="*60)
    
    # 低音戳洞文件
    hole_file = r'test20250717\neg\录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析全部93段
    all_segments_data = analyze_all_segments_high_precision(hole_file)
    
    if all_segments_data:
        create_unified_visualization(all_segments_data)

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征 - 与前4段版本完全一致"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_trend': 0.0,
            'fluctuation_smoothness': 1.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 趋势 - 线性回归斜率
    if len(freq_centers) == len(noise_levels):
        try:
            freq_array = np.array(freq_centers)
            coeffs = np.polyfit(freq_array, noise_array, 1)
            fluctuation_trend = coeffs[0]
        except:
            fluctuation_trend = 0.0
    else:
        fluctuation_trend = 0.0
    
    # 5. 平滑度 - 相邻点差值的平均
    if len(noise_array) > 1:
        adjacent_diffs = np.abs(np.diff(noise_array))
        fluctuation_smoothness = 1.0 / (1.0 + np.mean(adjacent_diffs))
    else:
        fluctuation_smoothness = 1.0
    
    # 6. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_trend': fluctuation_trend,
        'fluctuation_smoothness': fluctuation_smoothness,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_high_precision(freqs, power, fundamental_analysis):
    """高精度动态噪声估计 - 与前4段版本完全一致"""
    
    if not fundamental_analysis:
        return {'global_noise_floor_db': -80}
    
    fundamental_freq = fundamental_analysis['freq']
    
    # 滑动窗口参数 - 与前4段版本一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和明显的谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            local_noise = np.percentile(window_noise_powers, 25)  # 25th percentile
            local_noise_db = 10 * np.log10(local_noise + 1e-12)
            
            local_noise_levels.append(local_noise_db)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return {'global_noise_floor_db': -80}
    
    # 平滑噪声曲线 - 与前4段版本一致
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 插值到完整频率网格
    local_noise_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'local_noise_db': local_noise_interp,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_high_precision(freqs, power, fundamental_analysis, dynamic_noise_analysis, sr):
    """高精度谐波检测 - 与前4段版本完全一致"""
    
    if not fundamental_analysis:
        return []
    
    fundamental_freq = fundamental_analysis['freq']
    fundamental_power_db = fundamental_analysis['power_db']
    global_noise_floor_db = dynamic_noise_analysis['global_noise_floor_db']
    local_noise_db = dynamic_noise_analysis['local_noise_db']
    noise_variation_db = dynamic_noise_analysis['noise_variation_db']
    
    detected_harmonics = []
    
    # 根据噪声变化和波动特征调整SNR阈值
    fluctuation_features = dynamic_noise_analysis['noise_fluctuation_features']
    stability_score = fluctuation_features['fluctuation_stability_score']
    
    # 基础SNR阈值调整
    if noise_variation_db > 10:
        base_snr_threshold = 8.0  # 噪声变化大时降低要求
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据噪声稳定性进一步调整
    if stability_score > 0.8:  # 噪声很稳定
        stability_adjustment = 1.0  # 可以提高要求
    elif stability_score > 0.6:  # 噪声中等稳定
        stability_adjustment = 0.0  # 保持基础要求
    else:  # 噪声不稳定
        stability_adjustment = -1.0  # 降低要求
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= sr/2:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 获取该频率的局部噪声
        local_noise_at_freq = local_noise_db[actual_idx]
        
        # 计算各种指标
        global_snr_db = harmonic_power_db - global_noise_floor_db
        local_snr_db = harmonic_power_db - local_noise_at_freq
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 动态谐波判断条件
        conditions = {
            'local_snr_sufficient': local_snr_db >= adjusted_snr_threshold,
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db,
                'local_snr_db': local_snr_db,
                'local_noise_db': local_noise_at_freq,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
    
    return detected_harmonics

def analyze_all_segments_high_precision(audio_path):
    """高精度分析全部93段"""
    try:
        print(f"\n使用高精度算法分析全部93段...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        all_segments_data = []
        
        print(f"开始高精度分析{len(step_boundaries)}个频段...")
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            if seg_idx % 10 == 0:
                print(f"  处理进度: {seg_idx}/{len(step_boundaries)}")
            
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析 - 与前4段版本一致
            fft_size = 131072  # 128k点FFT (与前4段一致)
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            power_db = 10 * np.log10(power + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            display_power_db = positive_power_db[freq_mask]
            
            # 1. 主频分析
            fundamental_analysis = analyze_fundamental_precise(display_freqs, display_power, expected_freq)
            
            # 2. 高精度动态噪声估计
            dynamic_noise_analysis = estimate_dynamic_noise_high_precision(display_freqs, display_power, fundamental_analysis)
            
            # 3. 高精度谐波检测
            harmonic_analysis = detect_harmonics_high_precision(
                display_freqs, display_power, fundamental_analysis, dynamic_noise_analysis, sr
            )
            
            all_segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'fundamental_analysis': fundamental_analysis,
                'harmonic_count': len(harmonic_analysis),
                'harmonic_analysis': harmonic_analysis,
                'dynamic_noise_analysis': dynamic_noise_analysis
            })
        
        print(f"✅ 完成全部{len(all_segments_data)}段高精度分析")
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': all_segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def analyze_fundamental_precise(freqs, power, expected_freq):
    """精确主频分析"""
    
    # 精确搜索范围
    search_bandwidth = 1.0  # ±1Hz
    search_mask = (freqs >= expected_freq - search_bandwidth) & (freqs <= expected_freq + search_bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def create_unified_visualization(all_data):
    """创建统一可视化"""
    print(f"\n🎨 生成统一高精度可视化...")
    
    segments = all_data['segments']
    
    # 提取数据
    expected_freqs = [seg['expected_freq'] for seg in segments]
    harmonic_counts = [seg['harmonic_count'] for seg in segments]
    
    # 噪声特征
    noise_variations = []
    stability_scores = []
    
    for seg in segments:
        if seg['dynamic_noise_analysis']:
            noise_variations.append(seg['dynamic_noise_analysis']['noise_variation_db'])
            stability_scores.append(seg['dynamic_noise_analysis']['noise_fluctuation_features']['fluctuation_stability_score'])
        else:
            noise_variations.append(0)
            stability_scores.append(0)
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle(f'Unified High Precision Analysis - 93 Segments\nFile: {all_data["filename"]}', 
                 fontsize=16, fontweight='bold')
    
    # 1. 谐波数量 vs 频率
    ax1 = axes[0, 0]
    scatter1 = ax1.scatter(expected_freqs, harmonic_counts, c=stability_scores, 
                          cmap='RdYlGn', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
    ax1.set_xlabel('Expected Frequency (Hz)')
    ax1.set_ylabel('Number of Harmonics Detected')
    ax1.set_title('High Precision: Harmonic Count vs Frequency')
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')
    
    cbar1 = plt.colorbar(scatter1, ax=ax1)
    cbar1.set_label('Noise Stability Score')
    
    # 2. 噪声变化 vs 频率
    ax2 = axes[0, 1]
    scatter2 = ax2.scatter(expected_freqs, noise_variations, c=harmonic_counts, 
                          cmap='viridis', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
    ax2.set_xlabel('Expected Frequency (Hz)')
    ax2.set_ylabel('Noise Variation (dB)')
    ax2.set_title('High Precision: Noise Variation vs Frequency')
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')
    
    cbar2 = plt.colorbar(scatter2, ax=ax2)
    cbar2.set_label('Harmonic Count')
    
    # 3. 谐波数量分布对比
    ax3 = axes[1, 0]
    ax3.hist(harmonic_counts, bins=range(0, max(harmonic_counts)+2), 
            alpha=0.7, color='blue', edgecolor='black', label='High Precision')
    ax3.set_xlabel('Number of Harmonics')
    ax3.set_ylabel('Number of Segments')
    ax3.set_title('High Precision: Harmonic Count Distribution')
    ax3.grid(True, alpha=0.3)
    
    mean_harmonics = np.mean(harmonic_counts)
    ax3.axvline(mean_harmonics, color='red', linestyle='--', linewidth=2, 
               label=f'Mean: {mean_harmonics:.1f}')
    ax3.legend()
    
    # 4. 频率段统计
    ax4 = axes[1, 1]
    
    # 频率段分析
    low_freq_segments = [seg for seg in segments if seg['expected_freq'] <= 1000]
    mid_freq_segments = [seg for seg in segments if 1000 < seg['expected_freq'] <= 5000]
    high_freq_segments = [seg for seg in segments if seg['expected_freq'] > 5000]
    
    freq_bands = ['Low (≤1kHz)', 'Mid (1-5kHz)', 'High (>5kHz)']
    avg_harmonics = [
        np.mean([s['harmonic_count'] for s in low_freq_segments]),
        np.mean([s['harmonic_count'] for s in mid_freq_segments]),
        np.mean([s['harmonic_count'] for s in high_freq_segments])
    ]
    
    bars = ax4.bar(freq_bands, avg_harmonics, color=['lightblue', 'lightgreen', 'lightcoral'], 
                   edgecolor='black', alpha=0.7)
    ax4.set_ylabel('Average Harmonics per Segment')
    ax4.set_title('High Precision: Average Harmonics by Frequency Band')
    ax4.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, value in zip(bars, avg_harmonics):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'unified_high_precision_93_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 统一高精度分析可视化已保存: {filename}")
    plt.close()
    
    # 打印对比统计
    print_comparison_summary(all_data)

def print_comparison_summary(all_data):
    """打印对比统计摘要"""
    segments = all_data['segments']
    
    harmonic_counts = [seg['harmonic_count'] for seg in segments]
    noise_variations = [seg['dynamic_noise_analysis']['noise_variation_db'] if seg['dynamic_noise_analysis'] else 0 for seg in segments]
    stability_scores = [seg['dynamic_noise_analysis']['noise_fluctuation_features']['fluctuation_stability_score'] if seg['dynamic_noise_analysis'] else 0 for seg in segments]
    
    print(f"\n📊 统一高精度分析统计摘要:")
    print(f"{'='*60}")
    print(f"文件: {all_data['filename']}")
    print(f"算法: 与前4段完全一致的高精度算法")
    print(f"FFT大小: 131072点 (128k)")
    print(f"总段数: {len(segments)}")
    
    print(f"\n谐波统计 (高精度):")
    print(f"  总检测谐波: {sum(harmonic_counts)}个")
    print(f"  平均每段: {np.mean(harmonic_counts):.1f}个")
    print(f"  最多谐波: {max(harmonic_counts)}个")
    print(f"  最少谐波: {min(harmonic_counts)}个")
    print(f"  标准差: {np.std(harmonic_counts):.1f}")
    
    print(f"\n噪声变化统计 (高精度):")
    print(f"  平均噪声变化: {np.mean(noise_variations):.1f}dB")
    print(f"  最大噪声变化: {max(noise_variations):.1f}dB")
    print(f"  最小噪声变化: {min(noise_variations):.1f}dB")
    print(f"  标准差: {np.std(noise_variations):.1f}dB")
    
    print(f"\n稳定性统计 (高精度):")
    print(f"  平均稳定性评分: {np.mean(stability_scores):.3f}")
    print(f"  最高稳定性: {max(stability_scores):.3f}")
    print(f"  最低稳定性: {min(stability_scores):.3f}")
    print(f"  标准差: {np.std(stability_scores):.3f}")
    
    # 频率段分析
    low_freq_segments = [seg for seg in segments if seg['expected_freq'] <= 1000]
    mid_freq_segments = [seg for seg in segments if 1000 < seg['expected_freq'] <= 5000]
    high_freq_segments = [seg for seg in segments if seg['expected_freq'] > 5000]
    
    print(f"\n频率段分析 (高精度):")
    print(f"  低频段 (≤1kHz): {len(low_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in low_freq_segments]):.1f}个谐波")
    print(f"  中频段 (1-5kHz): {len(mid_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in mid_freq_segments]):.1f}个谐波")
    print(f"  高频段 (>5kHz): {len(high_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in high_freq_segments]):.1f}个谐波")
    
    # 与前4段结果对比
    print(f"\n🔍 与前4段结果对比:")
    print(f"  前4段平均: 8.2个谐波/段")
    print(f"  93段平均: {np.mean(harmonic_counts):.1f}个谐波/段")
    print(f"  前4段总计: 33个谐波")
    print(f"  93段总计: {sum(harmonic_counts)}个谐波")
    
    # 前4段的期望结果
    if len(harmonic_counts) >= 4:
        first_4_avg = np.mean(harmonic_counts[:4])
        print(f"  93段中前4段平均: {first_4_avg:.1f}个谐波/段")
        print(f"  一致性检查: {'✅ 一致' if abs(first_4_avg - 8.2) < 1.0 else '❌ 不一致'}")

if __name__ == "__main__":
    unified_high_precision_analysis()
