import os
import numpy as np
import librosa
import matplotlib.pyplot as plt
import pandas as pd
from scipy.signal import windows, butter, filtfilt
from freq_split import split_freq_steps
from librosa.feature import spectral_flatness, spectral_centroid, spectral_bandwidth

def detect_vertical_line_interference(S, freqs, f0, threshold_ratio=3, min_affected_freqs=5):
    """
    检测频谱中的竖线干扰

    竖线干扰特点：某时间点很多非主频和谐波的频点能量突然变高，形成垂直线条

    参数:
    S: 频谱矩阵 (freq_bins, time_frames)
    freqs: 频率数组
    f0: 主频率
    threshold_ratio: 能量突增阈值倍数
    min_affected_freqs: 最小受影响频率点数

    返回:
    vertical_score: 竖线干扰评分
    interference_times: 检测到的干扰时间点
    affected_freq_counts: 每个时间点受影响的频率数量
    """
    if S.shape[1] < 3:  # 时间帧太少
        return 0, [], []

    # 识别主频和谐波频率（需要排除的频率）
    harmonics = []
    for h in range(1, 6):  # 前5个谐波
        harm_freq = h * f0
        if harm_freq < freqs[-1]:

            # 谐波附近的频率范围（±50Hz或±5%）
            harm_bandwidth = max(50, f0 * 0.05)
            harm_mask = np.abs(freqs - harm_freq) <= harm_bandwidth
            harmonics.extend(np.where(harm_mask)[0])

    # 去重并排序
    harmonic_indices = sorted(list(set(harmonics)))

    # 创建非主频非谐波的频率掩码
    non_harmonic_mask = np.ones(len(freqs), dtype=bool)
    non_harmonic_mask[harmonic_indices] = False

    # 提取非主频非谐波的频谱
    S_non_harmonic = S[non_harmonic_mask, :]

    if S_non_harmonic.size == 0:
        return 0, [], []

    # 计算每个时间帧的基线能量（中位数）
    baseline_energy = np.median(S_non_harmonic, axis=0)

    # 检测每个时间帧的异常能量突增
    interference_times = []
    affected_freq_counts = []

    for t in range(S.shape[1]):
        frame_energy = S_non_harmonic[:, t]
        frame_baseline = baseline_energy[t]

        # 检测能量突增的频率点
        energy_threshold = frame_baseline * threshold_ratio
        affected_freqs = np.sum(frame_energy > energy_threshold)

        # 如果受影响的频率点足够多，认为是竖线干扰
        if affected_freqs >= min_affected_freqs:
            interference_times.append(t)
            affected_freq_counts.append(affected_freqs)

    # 计算竖线干扰评分
    if len(interference_times) == 0:
        vertical_score = 0
    else:
        # 基于干扰时间帧比例和平均受影响频率数
        interference_ratio = len(interference_times) / S.shape[1]
        avg_affected_freqs = np.mean(affected_freq_counts)
        max_possible_affected = np.sum(non_harmonic_mask)

        # 综合评分
        vertical_score = min(
            interference_ratio * 0.5 +
            (avg_affected_freqs / max_possible_affected) * 0.5,
            1.0
        )

    return vertical_score, interference_times, affected_freq_counts

def detect_high_freq_nonlinear_noise(S, freqs, sr, high_freq_start=6500):
    """
    检测高频段的低能量非线性噪声

    参数:
    S: 频谱矩阵
    freqs: 频率数组
    sr: 采样率
    high_freq_start: 高频段起始频率

    返回:
    high_freq_flatness: 高频段谱平坦度
    energy_variation: 高频段能量变化率
    high_freq_ratio: 高频段能量比例
    noise_score: 综合噪声评分
    """
    high_freq_mask = freqs >= high_freq_start
    S_high = S[high_freq_mask, :]

    if S_high.size == 0:
        return 0, 0, 0, 0

    # 高频段的谱平坦度（非线性噪声通常不平坦）
    high_freq_flatness = spectral_flatness(S=S_high).mean()

    # 高频段的能量变化率（检测间歇性噪声）
    high_freq_energy_frames = S_high.sum(axis=0)
    energy_variation = np.std(high_freq_energy_frames) / (np.mean(high_freq_energy_frames) + 1e-12)

    # 高频段相对总能量的比例
    high_freq_ratio = S_high.sum() / (S.sum() + 1e-12)

    # 检测高频段的异常能量分布
    high_freq_mean = S_high.mean(axis=1)
    high_freq_std = np.std(high_freq_mean)
    high_freq_outliers = np.sum(high_freq_mean > np.mean(high_freq_mean) + 2 * high_freq_std)

    # 综合噪声评分
    noise_score = 0
    if high_freq_flatness < 0.1 and energy_variation > 0.5:  # 低平坦度 + 高变化率
        noise_score += 0.4
    if high_freq_ratio > 0.15:  # 高频能量过高
        noise_score += 0.3
    if high_freq_outliers > len(high_freq_mean) * 0.1:  # 异常频率点过多
        noise_score += 0.3

    return high_freq_flatness, energy_variation, high_freq_ratio, min(noise_score, 1.0)

def detect_harmonic_tremolo(y_seg, sr, f0, tremolo_freq_range=(3, 15), n_harmonics=5):
    """
    检测谐波震音现象

    参数:
    y_seg: 音频段
    sr: 采样率
    f0: 基频
    tremolo_freq_range: 震音频率范围 (Hz)
    n_harmonics: 检测的谐波数量

    返回:
    tremolo_strength: 震音强度
    tremolo_consistency: 震音一致性
    harmonic_count: 检测到的谐波数量
    tremolo_score: 综合震音评分
    """
    harmonics_envelopes = []
    valid_harmonics = 0

    # 提取基频和谐波的包络
    for h in range(1, n_harmonics + 1):
        harm_freq = h * f0
        if harm_freq > sr/2:
            break

        # 带通滤波提取谐波分量
        nyquist = sr / 2
        bandwidth = min(50, f0 * 0.1)  # 自适应带宽
        low = max(harm_freq - bandwidth, 1) / nyquist
        high = min(harm_freq + bandwidth, nyquist - 1) / nyquist

        if low < high and low > 0 and high < 1:
            try:
                b, a = butter(4, [low, high], btype='band')
                harm_signal = filtfilt(b, a, y_seg)

                # 计算包络（使用Hilbert变换的近似）
                # 使用STFT计算包络
                hop_length = min(256, len(y_seg) // 20)
                stft_harm = librosa.stft(harm_signal, n_fft=1024, hop_length=hop_length)
                envelope = np.mean(np.abs(stft_harm), axis=0)

                if len(envelope) > 10:  # 确保有足够的数据点
                    harmonics_envelopes.append(envelope)
                    valid_harmonics += 1
            except:
                continue  # 滤波失败时跳过

    if not harmonics_envelopes or valid_harmonics == 0:
        return 0, 0, 0, 0

    # 分析包络的调制特性
    tremolo_scores = []
    for envelope in harmonics_envelopes:
        if len(envelope) < 10:
            continue

        # 去除直流分量
        envelope_ac = envelope - np.mean(envelope)

        # FFT分析包络的调制频率
        envelope_fft = np.abs(np.fft.rfft(envelope_ac))
        envelope_freqs = np.fft.rfftfreq(len(envelope_ac), d=hop_length/sr)

        # 在震音频率范围内寻找峰值
        tremolo_mask = (envelope_freqs >= tremolo_freq_range[0]) & (envelope_freqs <= tremolo_freq_range[1])
        if np.any(tremolo_mask):
            tremolo_energy = np.sum(envelope_fft[tremolo_mask])
            total_energy = np.sum(envelope_fft) + 1e-12
            tremolo_ratio = tremolo_energy / total_energy
            tremolo_scores.append(tremolo_ratio)

    if not tremolo_scores:
        return 0, 0, valid_harmonics, 0

    tremolo_strength = np.mean(tremolo_scores)
    tremolo_consistency = 1 - np.std(tremolo_scores) if len(tremolo_scores) > 1 else 1.0

    # 综合震音评分
    tremolo_score = 0
    if tremolo_strength > 0.05:  # 基础震音强度
        tremolo_score += 0.4
    if tremolo_consistency > 0.7:  # 谐波间震音一致性
        tremolo_score += 0.3
    if valid_harmonics >= 3:  # 多谐波震音
        tremolo_score += 0.3

    return tremolo_strength, tremolo_consistency, valid_harmonics, min(tremolo_score, 1.0)

def comprehensive_anomaly_score(features_dict):
    """
    综合异响评分

    参数:
    features_dict: 包含各种特征的字典

    返回:
    score: 综合异响评分 (0-1)
    anomaly_types: 检测到的异响类型列表
    """
    score = 0
    anomaly_types = []

    # 宽频噪声权重 (30%)
    if features_dict.get('flat_out', 0) > 0.3:
        score += 0.15
        anomaly_types.append('wideband_noise_flatness')
    if features_dict.get('energy_out', 0) > 0.2:
        score += 0.15
        anomaly_types.append('wideband_noise_energy')

    # 竖线干扰权重 (25%)
    if features_dict.get('vertical_score', 0) > 0.3:
        score += 0.25
        anomaly_types.append('vertical_line_interference')

    # 高频非线性噪声权重 (20%)
    if features_dict.get('high_freq_noise_score', 0) > 0.4:
        score += 0.2
        anomaly_types.append('high_freq_nonlinear')

    # THD+N权重 (15%)
    if features_dict.get('thdn', 0) > 0.1:
        score += 0.15
        anomaly_types.append('high_distortion')

    # 震音异常权重 (10%)
    tremolo_score = features_dict.get('tremolo_score', 0)
    if tremolo_score > 0.3:  # 阈值需要根据训练数据调整
        score += 0.1
        anomaly_types.append('tremolo_anomaly')

    return min(score, 1.0), anomaly_types

def analyze_step_chirp_batch(
    audio_paths,
    result_root="features_all",  # 所有结果统一放在这个文件夹
    octave=12,
    start_freq=100,
    stop_freq=20000,
    min_cycles=10,
    min_duration=153,
    fs=48000,
    A=1,
    energy_threshold_db=-55,
    min_start_time=0.2,
    hop_length=512,
    n_fft=2048,
    plot=True
):
    """
    批量分析步进扫频音频文件，自动分段并提取特征，保存图片和csv
    :param audio_paths: 单个音频路径或音频路径列表
    :param result_root: 所有结果保存的总文件夹
    :return: 每个文件的特征DataFrame字典
    """
    if isinstance(audio_paths, str):
        audio_paths = [audio_paths]
    results = {}

    os.makedirs(result_root, exist_ok=True)  # 创建总文件夹

    for audio_path in audio_paths:
        print(f"[INFO] Processing: {audio_path}")
        step_bounds, freq_table = split_freq_steps(
            audio_path,
            start_freq=start_freq, stop_freq=stop_freq,
            octave=octave, fs=fs, A=A,
            min_cycles=min_cycles, min_duration=min_duration,
            energy_threshold_db=energy_threshold_db, min_start_time=min_start_time,
            hop_length=hop_length, n_fft=n_fft, plot=plot
        )

        def compute_thdn(y_seg, sr, f0, n_harmonics=5, n_fft=8192):
            x = y_seg * windows.hann(len(y_seg))
            N = n_fft
            x = np.pad(x, (0, max(0, N - len(x))))
            X = np.fft.rfft(x[:N], n=N)
            freqs = np.fft.rfftfreq(N, 1/sr)
            mag = np.abs(X)
            idx0 = np.argmin(np.abs(freqs - f0))
            fund_pow = mag[idx0]**2
            harm_pow = sum(mag[np.argmin(np.abs(freqs - k*f0))]**2
                           for k in range(2, n_harmonics+1))
            total_pow = np.sum(mag**2)
            noise_pow = total_pow - (fund_pow + harm_pow)
            thd  = np.sqrt(harm_pow) / (np.sqrt(fund_pow) + 1e-12)
            thdn = np.sqrt(harm_pow + noise_pow) / (np.sqrt(fund_pow) + 1e-12)
            return thd, thdn

        y, sr = librosa.load(audio_path, sr=None)
        records = []
        for i, (t0, t1) in enumerate(step_bounds):
            L = t1 - t0
            seg_start = t0 + 0.1 * L
            seg_end   = t1 - 0.1 * L
            y_seg = y[int(seg_start*sr):int(seg_end*sr)]
            f0    = freq_table[i]

            if len(y_seg) < 256:
                print(f"[WARN] Step {i}: segment too short ({len(y_seg)} samples), skipped.")
                continue

            seg_n_fft = 2**int(np.floor(np.log2(len(y_seg))))
            seg_n_fft = max(256, seg_n_fft)
            S = np.abs(librosa.stft(y_seg, n_fft=seg_n_fft, hop_length=hop_length))**2
            freqs = librosa.fft_frequencies(sr=sr, n_fft=seg_n_fft)
            spec_mean = S.mean(axis=1)
            peak_idx = np.argmax(spec_mean)
            peak_f = freqs[peak_idx]
            freq_res = freqs[1] - freqs[0]
            main_bin_width = 5
            main_bw = freq_res * main_bin_width
            main_mask = (freqs >= peak_f - main_bw) & (freqs <= peak_f + main_bw)
            S_out = S[~main_mask, :]
            flat_out = spectral_flatness(S=S_out).mean() if S_out.size > 0 else 0
            energy_out = S_out.sum() / (S.sum() + 1e-12)
            high_freq_mask = freqs > 2000
            high_freq_energy = S[high_freq_mask].sum() / (S.sum() + 1e-12)

            # 增强的频谱特征
            # 谱质心和谱扩散度
            spec_centroid = spectral_centroid(S=S, sr=sr).mean()
            spec_bandwidth = spectral_bandwidth(S=S, sr=sr).mean()

            # 子带能量方差（检测能量分布不均匀性）
            n_subbands = 8
            subband_energies = []
            freq_step = len(freqs) // n_subbands
            for sb in range(n_subbands):
                start_idx = sb * freq_step
                end_idx = min((sb + 1) * freq_step, len(freqs))
                subband_energy = S[start_idx:end_idx, :].sum()
                subband_energies.append(subband_energy)
            subband_variance = np.var(subband_energies) / (np.mean(subband_energies) + 1e-12)

            # 竖线干扰检测
            vertical_score, interference_times, affected_freq_counts = detect_vertical_line_interference(S, freqs, f0)

            # 高频非线性噪声检测
            high_freq_flatness, high_freq_variation, high_freq_ratio_new, high_freq_noise_score = detect_high_freq_nonlinear_noise(S, freqs, sr)

            # 谐波震音检测
            tremolo_strength, tremolo_consistency, harmonic_count, tremolo_score = detect_harmonic_tremolo(y_seg, sr, f0)

            # CQT特征
            C = np.abs(librosa.cqt(y_seg, sr=sr,
                                   fmin=start_freq,
                                   n_bins=octave * int(np.log2(stop_freq/start_freq)),
                                   bins_per_octave=octave))
            C_db = librosa.amplitude_to_db(C, ref=np.max)
            cqt_energy = C.sum()
            cqt_flat   = spectral_flatness(S=C).mean()
            P = C_db - C_db.min()
            P = P / (P.sum(axis=0, keepdims=True) + 1e-12)
            cqt_entropy = - (P * np.log2(P+1e-12)).sum(axis=0).mean()

            _, thdn = compute_thdn(y_seg, sr, f0)

            # 构建特征字典用于综合评分
            features_dict = {
                'flat_out': flat_out,
                'energy_out': energy_out,
                'vertical_score': vertical_score,
                'high_freq_noise_score': high_freq_noise_score,
                'thdn': thdn,
                'tremolo_score': tremolo_score
            }

            # 综合异响评分
            anomaly_score, anomaly_types = comprehensive_anomaly_score(features_dict)

            # 更新宽带噪声判定（使用更多指标）
            is_wideband_noise = (flat_out > 0.3) or (energy_out > 0.2) or (high_freq_energy > 0.2) or (subband_variance > 1.0)

            records.append({
                # 基础特征
                "freq":       f0,
                "mid_time":  (t0+t1)/2,
                "thdn":       thdn,
                "peak_freq":  peak_f,

                # 宽频噪声特征
                "flat_out":   flat_out,
                "energy_out": energy_out,
                "high_freq_energy": high_freq_energy,
                "is_wideband_noise": int(is_wideband_noise),
                "spec_centroid": spec_centroid,
                "spec_bandwidth": spec_bandwidth,
                "subband_variance": subband_variance,

                # 竖线干扰特征
                "vertical_score": vertical_score,
                "interference_time_count": len(interference_times),
                "avg_affected_freq_count": np.mean(affected_freq_counts) if affected_freq_counts else 0,

                # 高频非线性噪声特征
                "high_freq_flatness": high_freq_flatness,
                "high_freq_variation": high_freq_variation,
                "high_freq_ratio_new": high_freq_ratio_new,
                "high_freq_noise_score": high_freq_noise_score,

                # 谐波震音特征
                "tremolo_strength": tremolo_strength,
                "tremolo_consistency": tremolo_consistency,
                "harmonic_count": harmonic_count,
                "tremolo_score": tremolo_score,

                # 综合评分
                "anomaly_score": anomaly_score,
                "anomaly_types": ';'.join(anomaly_types),

                # CQT特征
                "cqt_energy": cqt_energy,
                "cqt_flat":   cqt_flat,
                "cqt_ent":    cqt_entropy
            })

        df = pd.DataFrame(records)

        # ==== 按输入文件名保存图片，保存在总文件夹下的子文件夹 ====
        audio_basename = os.path.splitext(os.path.basename(audio_path))[0]
        save_dir = os.path.join(result_root, audio_basename)
        os.makedirs(save_dir, exist_ok=True)
        csv_path = os.path.join(save_dir, "features.csv")
        df.to_csv(csv_path, index=False)

        # ==== 可视化1：综合异响评分 vs 时间 ====
        plt.figure(figsize=(12,8))

        # 子图1：综合评分和主要指标
        plt.subplot(2,2,1)
        plt.plot(df["mid_time"], df["anomaly_score"], 'ro-', linewidth=2, label="Anomaly Score")
        plt.plot(df["mid_time"], df["thdn"], 'b--', label="THD+N")
        plt.xlabel("Time (s)")
        plt.ylabel("Score")
        plt.title("Comprehensive Anomaly Score")
        plt.legend()
        plt.grid(True)
        plt.ylim(0, 1)

        # 子图2：宽频噪声特征
        plt.subplot(2,2,2)
        plt.plot(df["mid_time"], df["flat_out"], 's-', label="Spectral Flatness")
        plt.plot(df["mid_time"], df["energy_out"], 'd-', label="Out-band Energy")
        plt.plot(df["mid_time"], df["subband_variance"], '^-', label="Subband Variance")
        plt.xlabel("Time (s)")
        plt.ylabel("Value")
        plt.title("Wideband Noise Features")
        plt.legend()
        plt.grid(True)

        # 子图3：竖线干扰和高频噪声
        plt.subplot(2,2,3)
        plt.plot(df["mid_time"], df["vertical_score"], 'o-', label="Vertical Line Score")
        plt.plot(df["mid_time"], df["high_freq_noise_score"], 's-', label="High Freq Noise Score")
        plt.xlabel("Time (s)")
        plt.ylabel("Score")
        plt.title("Vertical Line & High Freq Noise")
        plt.legend()
        plt.grid(True)
        plt.ylim(0, 1)

        # 子图4：谐波震音特征
        plt.subplot(2,2,4)
        plt.plot(df["mid_time"], df["tremolo_score"], 'o-', label="Tremolo Score")
        plt.plot(df["mid_time"], df["tremolo_strength"], 's--', label="Tremolo Strength")
        plt.plot(df["mid_time"], df["tremolo_consistency"], '^--', label="Tremolo Consistency")
        plt.xlabel("Time (s)")
        plt.ylabel("Value")
        plt.title("Harmonic Tremolo Features")
        plt.legend()
        plt.grid(True)
        plt.ylim(0, 1)

        plt.tight_layout()
        img_path1 = os.path.join(save_dir, "anomaly_analysis.png")
        plt.savefig(img_path1, dpi=150)
        plt.close()

        # ==== 可视化2：频谱特征 vs 时间 ====
        plt.figure(figsize=(10,6))

        plt.subplot(2,1,1)
        plt.plot(df["mid_time"], df["spec_centroid"], 'o-', label="Spectral Centroid")
        plt.plot(df["mid_time"], df["peak_freq"], 's--', label="Peak Frequency")
        plt.xlabel("Time (s)")
        plt.ylabel("Frequency (Hz)")
        plt.title("Spectral Characteristics")
        plt.legend()
        plt.grid(True)
        plt.yscale('log')

        plt.subplot(2,1,2)
        plt.plot(df["mid_time"], df["spec_bandwidth"], 'o-', label="Spectral Bandwidth")
        plt.plot(df["mid_time"], df["high_freq_flatness"], 's-', label="High Freq Flatness")
        plt.plot(df["mid_time"], df["high_freq_variation"], '^-', label="High Freq Variation")
        plt.xlabel("Time (s)")
        plt.ylabel("Value")
        plt.title("Spectral Distribution Features")
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        img_path2 = os.path.join(save_dir, "spectral_features.png")
        plt.savefig(img_path2, dpi=150)
        plt.close()

        # ==== 可视化3：CQT 特征 vs 时间 ====
        _, ax = plt.subplots(3,1,figsize=(8,6), sharex=True)
        ax[0].plot(df["mid_time"], df["cqt_energy"], 'o-');   ax[0].set_ylabel("CQT Energy")
        ax[1].plot(df["mid_time"], df["cqt_flat"],   'o-');   ax[1].set_ylabel("CQT Flatness")
        ax[2].plot(df["mid_time"], df["cqt_ent"],    'o-');   ax[2].set_ylabel("CQT Entropy")
        ax[2].set_xlabel("Time (s)")
        for a in ax:
            a.grid(True)
            a.set_xlim(df["mid_time"].min(), df["mid_time"].max())  # 统一横坐标范围
        ax[1].set_ylim(0, 1)  # 统一纵坐标范围（如平坦度、熵等），可根据实际调整
        ax[2].set_ylim(4, 7)
        plt.suptitle("Log-Freq (CQT) Features over Time")
        plt.tight_layout(rect=[0,0,1,0.95])
        img_path3 = os.path.join(save_dir, "cqt_features.png")
        plt.savefig(img_path3, dpi=150)
        plt.close()

        # 打印异响检测摘要
        print(f"\n[INFO] 异响检测摘要 - {audio_basename}:")
        print(f"  平均异响评分: {df['anomaly_score'].mean():.3f}")
        print(f"  最高异响评分: {df['anomaly_score'].max():.3f}")
        print(f"  异响频率点数: {(df['anomaly_score'] > 0.5).sum()}/{len(df)}")

        # 统计各类异响
        all_anomaly_types = []
        for types_str in df['anomaly_types']:
            if types_str and types_str != '':
                all_anomaly_types.extend(types_str.split(';'))

        if all_anomaly_types:
            from collections import Counter
            anomaly_counts = Counter(all_anomaly_types)
            print("  检测到的异响类型:")
            for anomaly_type, count in anomaly_counts.items():
                print(f"    {anomaly_type}: {count} 次")
        else:
            print("  未检测到明显异响")

        results[audio_path] = df

    return results

if __name__ == "__main__":
    # input_dir = r"test20250715\wavno"
    # input_dir = r"test20250714\153"
    input_dir = r"test20250714\156"
    audio_files = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.lower().endswith('.wav')]
    analyze_step_chirp_batch(audio_files, result_root="output_0716", min_duration=156, energy_threshold_db=-45, plot=True)
