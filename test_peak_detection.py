#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试峰值检测 - 只分析第一个频段
"""

import os
import sys
import numpy as np
import librosa

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from freq_split_optimized import split_freq_steps_optimized

def find_peaks(power_spectrum, min_height_ratio=0.001, min_distance=5):
    """
    找到功率谱中的明显峰值，包括边界峰值
    """
    # 计算峰值阈值
    max_power = np.max(power_spectrum)
    min_height = max_power * min_height_ratio

    print(f"    峰值检测参数: 最大功率={max_power:.2e}, 阈值={min_height:.2e} (比例={min_height_ratio})")

    # 简单的峰值检测，包括边界
    peaks = []

    # 检查所有点，包括边界附近
    for i in range(1, len(power_spectrum) - 1):
        # 检查是否是局部最大值且超过阈值
        if (power_spectrum[i] > min_height and
            power_spectrum[i] > power_spectrum[i-1] and
            power_spectrum[i] > power_spectrum[i+1]):

            # 检查与已有峰值的距离
            too_close = False
            for existing_peak in peaks:
                if abs(i - existing_peak) < min_distance:
                    # 如果当前峰值更高，替换已有峰值
                    if power_spectrum[i] > power_spectrum[existing_peak]:
                        peaks.remove(existing_peak)
                        break
                    else:
                        too_close = True
                        break

            if not too_close:
                peaks.append(i)
                print(f"    检测到峰值: 索引{i}, 功率={power_spectrum[i]:.2e}")

    # 检查边界点是否是峰值
    # 检查第一个点
    if (len(power_spectrum) > 1 and
        power_spectrum[0] > min_height and
        power_spectrum[0] > power_spectrum[1]):
        # 检查距离
        if not peaks or abs(0 - peaks[0]) >= min_distance:
            peaks.insert(0, 0)
            print(f"    检测到边界峰值: 索引0, 功率={power_spectrum[0]:.2e}")

    # 检查最后一个点
    last_idx = len(power_spectrum) - 1
    if (len(power_spectrum) > 1 and
        power_spectrum[last_idx] > min_height and
        power_spectrum[last_idx] > power_spectrum[last_idx-1]):
        # 检查距离
        if not peaks or abs(last_idx - peaks[-1]) >= min_distance:
            peaks.append(last_idx)
            print(f"    检测到边界峰值: 索引{last_idx}, 功率={power_spectrum[last_idx]:.2e}")

    peaks = sorted(peaks)
    print(f"    总共检测到{len(peaks)}个峰值")
    return np.array(peaks)

def main():
    """
    测试第一个频段的峰值检测
    """
    audio_path = "test20250722/鼓膜破裂（复测1.1).wav"
    
    print(f"测试峰值检测 - 第一个频段")
    print(f"音频文件: {audio_path}")
    print("="*50)
    
    try:
        # 获取频段分割
        print("进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        print("加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        
        # 分析第一个频段
        start_time, end_time = step_boundaries[0]
        expected_freq = freq_table[0]
        
        print(f"分析第一个频段: {expected_freq:.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频 - 取最中间的100ms
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        total_segment = y[start_sample:end_sample]
        
        # 计算最中间100ms的位置
        target_samples = 4800
        total_length = len(total_segment)
        center_idx = total_length // 2
        half_target = target_samples // 2
        start_idx = center_idx - half_target
        end_idx = center_idx + half_target
        segment_audio = total_segment[start_idx:end_idx]
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # FFT分析
        fft_size = len(segment_audio)
        window = np.hanning(fft_size)
        segment_audio = segment_audio * window
        
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围
        freq_mask = (positive_freqs >= 100) & (positive_freqs <= 20000)
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        print(f"频谱分析: {len(display_freqs)}个频率点, 范围{display_freqs[0]:.1f}-{display_freqs[-1]:.1f}Hz")
        print(f"功率范围: {np.min(display_power):.2e} - {np.max(display_power):.2e}")
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        print(f"检测到主频: {fundamental_freq:.1f}Hz (期望: {expected_freq:.1f}Hz)")
        
        # 峰值检测
        peak_idx = np.argmin(np.abs(display_freqs - fundamental_freq))
        search_range = 200  # Hz
        freq_resolution = display_freqs[1] - display_freqs[0]
        search_left = max(0, peak_idx - int(search_range / freq_resolution))
        search_right = min(len(display_freqs) - 1, peak_idx + int(search_range / freq_resolution))
        
        print(f"峰值搜索范围: 索引{search_left}-{search_right}, 频率{display_freqs[search_left]:.1f}-{display_freqs[search_right]:.1f}Hz")
        
        # 在搜索范围内找峰值
        search_power = display_power[search_left:search_right+1]
        peak_indices = find_peaks(search_power, min_height_ratio=0.001, min_distance=5)
        
        # 转换为全局索引并显示结果
        global_peak_indices = peak_indices + search_left
        
        print(f"检测到的峰值:")
        for i, global_idx in enumerate(global_peak_indices):
            freq = display_freqs[global_idx]
            power = display_power[global_idx]
            power_db = 10 * np.log10(power + 1e-12)
            print(f"  峰值{i+1}: {freq:.1f}Hz, 功率={power:.2e}, {power_db:.1f}dB")
        
        print(f"\n测试完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
