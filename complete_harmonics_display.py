#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整显示40-44段的所有谐波检测结果
使用优化的检测算法展示每个频段的完整谐波列表
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def complete_harmonics_display():
    """完整显示40-44段的所有谐波"""
    print("📊 完整显示40-44段的所有谐波检测结果")
    print("="*70)
    print("使用优化的检测算法:")
    print("1. 降低SNR阈值 (6-9dB)")
    print("2. 保守噪声估计")
    print("3. 放宽检测条件")
    print("4. 超高分辨率FFT分析")
    print("="*70)
    
    # 两个异常样本
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    target_segments = [40, 41, 42, 43, 44]  # 目标频段
    
    # 分析每个样本
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 完整分析 {sample_name}: {os.path.basename(audio_path)}")
        print("="*60)
        
        if os.path.exists(audio_path):
            # 完整分析所有目标频段
            all_segments_analysis = analyze_all_target_segments(audio_path, sample_name, target_segments)
            
            # 显示完整的谐波表格
            display_complete_harmonics_table(all_segments_analysis, sample_name)
            
            # 创建完整的谐波可视化
            create_complete_harmonics_visualization(all_segments_analysis, sample_name)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")

def analyze_all_target_segments(audio_path, sample_name, target_segments):
    """分析所有目标频段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        all_segments_analysis = []
        
        # 分析每个目标频段
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            print(f"\n📈 分析频段 {seg_idx} ({expected_freq:.1f}Hz)")
            print("-" * 40)
            
            # 完整的谐波分析
            segment_analysis = complete_harmonic_analysis(segment_audio, sr, expected_freq, seg_idx)
            
            if segment_analysis:
                all_segments_analysis.append(segment_analysis)
        
        return all_segments_analysis
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return []

def complete_harmonic_analysis(audio, sr, expected_freq, seg_idx):
    """完整的谐波分析"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(131072, len(audio))  # 128k点FFT
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 分析完整的正频率范围
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        nyquist_freq = sr / 2
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_complete(positive_freqs, positive_power, expected_freq)
        
        # 2. 保守噪声估计
        noise_analysis = conservative_noise_estimation(positive_freqs, positive_power, expected_freq)
        
        # 3. 完整谐波检测
        harmonic_analysis = complete_harmonic_detection(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_analysis, sr
        )
        
        print(f"   主频: {fundamental_analysis['freq']:.2f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"   噪声底噪: {noise_analysis['noise_floor_db']:.1f}dB")
        print(f"   检测谐波: {len(harmonic_analysis)}个")
        print(f"   频率分辨率: {freq_resolution:.4f}Hz")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'freq_resolution': freq_resolution,
            'nyquist_freq': nyquist_freq
        }
        
    except Exception as e:
        print(f"   ❌ 频段{seg_idx}分析失败: {e}")
        return None

def analyze_fundamental_complete(freqs, power, expected_freq):
    """完整分析主频"""
    
    bandwidth = 5.0
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'expected_freq': expected_freq,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def conservative_noise_estimation(freqs, power, fundamental_freq):
    """保守的噪声估计"""
    
    # 只排除主频和前5个谐波
    excluded_ranges = []
    for order in range(1, 6):
        signal_freq = fundamental_freq * order
        if signal_freq < freqs[-1]:
            excluded_ranges.append((signal_freq - 15, signal_freq + 15))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        noise_floor = np.percentile(noise_powers, 20)  # 20th percentile
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_sample_count': np.sum(noise_mask),
            'excluded_ranges': excluded_ranges
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0,
            'excluded_ranges': excluded_ranges
        }

def complete_harmonic_detection(freqs, power, fundamental_freq, 
                               fundamental_analysis, noise_analysis, sr):
    """完整的谐波检测"""
    
    if not fundamental_analysis or not noise_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 优化的SNR阈值
    base_snr_threshold = 6.0
    
    nyquist_freq = sr / 2
    
    print(f"   完整谐波检测 (基础SNR阈值: {base_snr_threshold:.1f}dB):")
    
    for order in range(2, 30):  # 检测到29次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            print(f"     {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率")
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 5000:
            search_bandwidth = 40.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 60.0
        else:
            search_bandwidth = 80.0
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        snr_db = harmonic_power_db - noise_floor_db
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 2000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 相对功率和频率误差
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 检测条件
        conditions = {
            'snr_sufficient': snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -65.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.9
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'search_bandwidth': search_bandwidth,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
            
            print(f"     ✅ {order:2d}次谐波: {actual_freq:8.1f}Hz, {harmonic_power_db:6.1f}dB, "
                  f"SNR={snr_db:5.1f}dB, 相对基频{relative_power_db:+6.1f}dB, 误差{freq_error:+5.1f}Hz")
    
    return detected_harmonics

def display_complete_harmonics_table(all_segments_analysis, sample_name):
    """显示完整的谐波表格"""
    
    print(f"\n📊 {sample_name} - 完整谐波检测结果表格")
    print("="*100)
    
    # 创建表格数据
    table_data = []
    
    for segment_analysis in all_segments_analysis:
        seg_idx = segment_analysis['segment_idx']
        expected_freq = segment_analysis['expected_freq']
        fundamental_analysis = segment_analysis['fundamental_analysis']
        harmonic_analysis = segment_analysis['harmonic_analysis']
        noise_analysis = segment_analysis['noise_analysis']
        
        # 基本信息
        row_data = {
            '频段': seg_idx,
            '期望频率': f"{expected_freq:.0f}Hz",
            '实际主频': f"{fundamental_analysis['freq']:.1f}Hz",
            '主频功率': f"{fundamental_analysis['power_db']:.1f}dB",
            '噪声底噪': f"{noise_analysis['noise_floor_db']:.1f}dB",
            '谐波数量': len(harmonic_analysis)
        }
        
        # 谐波详情
        harmonic_details = []
        for harmonic in harmonic_analysis:
            detail = f"{harmonic['order']}次({harmonic['freq']:.0f}Hz,{harmonic['power_db']:.1f}dB,SNR={harmonic['snr_db']:.1f}dB)"
            harmonic_details.append(detail)
        
        row_data['谐波详情'] = '; '.join(harmonic_details) if harmonic_details else '无'
        
        table_data.append(row_data)
    
    # 显示表格
    for i, row in enumerate(table_data):
        print(f"\n频段 {row['频段']} ({row['期望频率']}):")
        print(f"  实际主频: {row['实际主频']}")
        print(f"  主频功率: {row['主频功率']}")
        print(f"  噪声底噪: {row['噪声底噪']}")
        print(f"  谐波数量: {row['谐波数量']}个")
        print(f"  谐波详情:")
        
        # 分行显示谐波详情
        if row['谐波详情'] != '无':
            harmonics = row['谐波详情'].split('; ')
            for j, harmonic in enumerate(harmonics):
                print(f"    {j+1:2d}. {harmonic}")
        else:
            print("    无检测到的谐波")

def create_complete_harmonics_visualization(all_segments_analysis, sample_name):
    """创建完整的谐波可视化"""
    
    if not all_segments_analysis:
        print(f"   ❌ 无有效分析数据")
        return
    
    print(f"\n🎨 生成 {sample_name} 完整谐波可视化...")
    
    # 创建大图 (5行1列)
    fig, axes = plt.subplots(5, 1, figsize=(24, 30))
    
    for i, segment_analysis in enumerate(all_segments_analysis):
        ax = axes[i]
        
        # 提取数据
        freqs = segment_analysis['freqs']
        power_db = segment_analysis['power_db']
        fundamental_analysis = segment_analysis['fundamental_analysis']
        harmonic_analysis = segment_analysis['harmonic_analysis']
        noise_analysis = segment_analysis['noise_analysis']
        
        seg_idx = segment_analysis['segment_idx']
        expected_freq = segment_analysis['expected_freq']
        nyquist_freq = segment_analysis['nyquist_freq']
        
        # 设置显示范围
        if expected_freq <= 1000:
            freq_range = (0, min(20000, nyquist_freq))
        elif expected_freq <= 2000:
            freq_range = (0, min(22000, nyquist_freq))
        else:
            freq_range = (0, nyquist_freq)
        
        freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
        display_freqs = freqs[freq_mask]
        display_power_db = power_db[freq_mask]
        
        # 绘制基础频谱
        ax.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
        
        # 标记噪声底噪线
        noise_floor_db = noise_analysis['noise_floor_db']
        ax.axhline(y=noise_floor_db, color='gray', linestyle='--', alpha=0.8, 
                  label=f'噪声底噪 {noise_floor_db:.1f}dB')
        
        # 标记主频
        fund_freq = fundamental_analysis['freq']
        fund_power_db = fundamental_analysis['power_db']
        ax.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
               label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
        
        # 主频标注
        ax.annotate(f'主频\n{fund_freq:.1f}Hz\n{fund_power_db:.1f}dB', 
                   xy=(fund_freq, fund_power_db), 
                   xytext=(fund_freq, fund_power_db + 15),
                   ha='center', va='bottom', fontweight='bold', color='red',
                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记所有检测到的谐波
        harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta', 'olive', 'navy', 'maroon']
        
        for j, harmonic in enumerate(harmonic_analysis):
            if j < len(harmonic_colors):
                color = harmonic_colors[j % len(harmonic_colors)]
            else:
                color = 'gray'
                
            order = harmonic['order']
            freq = harmonic['freq']
            power_db = harmonic['power_db']
            snr_db = harmonic['snr_db']
            
            if freq_range[0] <= freq <= freq_range[1]:
                ax.plot(freq, power_db, 'o', color=color, markersize=8, alpha=0.8,
                       label=f'{order}次谐波 {freq:.0f}Hz (SNR={snr_db:.1f}dB)')
                
                # 标注前10个谐波
                if j < 10:
                    ax.annotate(f'{order}次\n{freq:.0f}Hz', 
                               xy=(freq, power_db), xytext=(freq, power_db + 6),
                               ha='center', va='bottom', fontsize=7, color=color,
                               arrowprops=dict(arrowstyle='->', color=color, lw=1))
        
        # 设置图表属性
        ax.set_title(f'{sample_name} - 频段{seg_idx} ({expected_freq:.1f}Hz) 完整谐波检测 '
                    f'(共{len(harmonic_analysis)}个谐波)', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(freq_range)
        ax.set_ylim(np.min(display_power_db) - 10, np.max(display_power_db) + 25)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=7)
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息文本框
        info_text = f"完整统计:\n"
        info_text += f"主频: {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)\n"
        info_text += f"噪声底噪: {noise_floor_db:.1f}dB\n"
        info_text += f"检测谐波: {len(harmonic_analysis)}个\n"
        info_text += f"最高次数: {harmonic_analysis[-1]['order']}次\n" if harmonic_analysis else "最高次数: 无\n"
        info_text += f"最高频率: {harmonic_analysis[-1]['freq']:.0f}Hz\n" if harmonic_analysis else "最高频率: 无\n"
        info_text += f"频率分辨率: {segment_analysis['freq_resolution']:.4f}Hz"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'complete_harmonics_display_{sample_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 完整谐波可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    complete_harmonics_display()
