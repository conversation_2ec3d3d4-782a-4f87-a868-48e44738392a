#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析test20250722/琴身内部异物1.1.wav，生成对数频谱
只画频谱和动态噪声阈值线，每段去除开头结尾8%
与harmonic_detection_system/harmonic_detector_api_fast.py保持一致
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'harmonic_detection_system'))

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")
    sys.exit(1)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_threshold(freqs, power, fundamental_freq):
    """
    估计动态噪声阈值 - 与harmonic_detection_system完全一致
    """
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None, None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 插值到完整频率范围
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    return noise_threshold_interp, window_centers, smoothed_noise

def analyze_single_segment_log_spectrum(seg_idx, start_time, end_time, expected_freq, audio_data, sr, output_dir):
    """
    分析单个频段并生成对数频谱图像 - 只画频谱和动态噪声阈值
    """
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = audio_data[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"    警告: 段 {seg_idx} 音频长度为0")
            return False
        
        # 去除开头和结尾8% - 与harmonic_detector_api_fast.py保持一致
        trim_length = int(len(segment_audio) * 0.08)
        if len(segment_audio) > 2 * trim_length:
            segment_audio = segment_audio[trim_length:-trim_length]
            actual_start_time = start_time + trim_length / sr
            actual_end_time = end_time - trim_length / sr
        else:
            actual_start_time = start_time
            actual_end_time = end_time
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与harmonic_detector_api_fast.py一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB - 对数频谱
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频 - 与harmonic_detector_api_fast.py一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 估计动态噪声阈值 - 与harmonic_detector_api_fast.py一致
        noise_threshold, window_centers, smoothed_noise = estimate_dynamic_noise_threshold(
            display_freqs, display_power, fundamental_freq)
        
        # 创建对数频谱可视化图像
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 绘制频谱 - 黑色线
        ax.semilogx(display_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='频谱')
        
        # 绘制动态噪声阈值 - 红色线
        if noise_threshold is not None:
            noise_threshold_db = 10 * np.log10(noise_threshold + 1e-12)
            ax.semilogx(display_freqs, noise_threshold_db, color='red', linewidth=1.5, alpha=0.9, 
                       linestyle='-', label='动态噪声阈值')
        
        # 标记主频 - 蓝色圆点
        fundamental_power_db = 10 * np.log10(display_power[np.argmin(np.abs(display_freqs - fundamental_freq))] + 1e-12)
        ax.plot(fundamental_freq, fundamental_power_db, 'o', color='blue', markersize=8, 
               markeredgecolor='darkblue', markeredgewidth=1.5, label=f'主频 {fundamental_freq:.1f}Hz')
        
        # 设置图形属性
        ax.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')
        ax.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
        ax.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz 对数频谱分析\n'
                    f'有效时间: {actual_start_time:.3f}s - {actual_end_time:.3f}s (去除首尾8%)', 
                    fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
        ax.legend(fontsize=12, loc='upper right', framealpha=0.9)
        
        # 设置对数频率范围
        min_freq = max(10, np.min(display_freqs[display_freqs > 0]))  # 避免0频率
        max_freq = min(20000, np.max(display_freqs))
        ax.set_xlim(min_freq, max_freq)
        
        # 保存图像
        output_filename = os.path.join(output_dir, f'log_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png')
        plt.tight_layout()
        plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"    ✅ 保存: {output_filename}")
        return True
        
    except Exception as e:
        print(f"    ❌ 错误: {str(e)}")
        return False

def main():
    """
    主函数：分析琴身内部异物1.1.wav音频文件 - 对数频谱版本
    """
    print("🎯 分析琴身内部异物1.1.wav - 93段对数频谱可视化")
    print("="*60)

    # 输入音频文件
    audio_path = "test20250722/琴身内部异物1.1.wav"

    # 检查文件是否存在
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return

    # 创建输出目录
    audio_name = os.path.splitext(os.path.basename(audio_path))[0]
    output_dir = f"{audio_name}_对数频谱分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")

    start_time = time.time()

    try:
        # 获取频段分割 - 与harmonic_detector_api_fast.py完全一致
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 频段分割完成，共{len(step_boundaries)}段")

        # 加载音频
        print("🎵 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000

        print(f"✅ 音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")

        # 分析每个频段
        print("📊 开始分析93个频段 (对数频谱)...")
        successful_count = 0
        failed_count = 0

        for seg_idx in range(len(step_boundaries)):
            start_time_seg, end_time_seg = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            success = analyze_single_segment_log_spectrum(
                seg_idx, start_time_seg, end_time_seg, expected_freq, y, sr, output_dir
            )

            if success:
                successful_count += 1
            else:
                failed_count += 1

        end_time = time.time()
        processing_time = end_time - start_time

        print("\n" + "="*60)
        print("✅ 对数频谱分析完成!")
        print(f"  成功分析: {successful_count}个频段")
        print(f"  失败: {failed_count}个频段")
        print(f"  总耗时: {processing_time:.1f}秒")
        print(f"  平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  输出目录: {output_dir}")

        # 创建汇总信息文件
        create_log_spectrum_summary(output_dir, audio_path, step_boundaries, freq_table,
                                   successful_count, failed_count, processing_time)

    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

def create_log_spectrum_summary(output_dir, audio_path, step_boundaries, freq_table,
                               successful_count, failed_count, processing_time):
    """
    创建对数频谱分析汇总信息文件
    """
    summary_file = os.path.join(output_dir, "对数频谱分析汇总.txt")

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("琴身内部异物1.1.wav - 93段对数频谱分析汇总\n")
        f.write("="*50 + "\n\n")

        f.write(f"音频文件: {audio_path}\n")
        f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析方法: 对数频谱 (semilogx)\n")
        f.write(f"数据处理: 去除每段首尾8%\n")
        f.write(f"参考系统: harmonic_detection_system/harmonic_detector_api_fast.py\n")
        f.write(f"总频段数: {len(step_boundaries)}\n")
        f.write(f"成功分析: {successful_count}个频段\n")
        f.write(f"失败: {failed_count}个频段\n")
        f.write(f"处理耗时: {processing_time:.1f}秒\n\n")

        f.write("可视化内容:\n")
        f.write("- 频谱曲线 (黑色线)\n")
        f.write("- 动态噪声阈值 (红色线)\n")
        f.write("- 主频标记 (蓝色圆点)\n\n")

        f.write("频段详情:\n")
        f.write("-" * 70 + "\n")
        f.write("段号  期望频率(Hz)  开始时间(s)  结束时间(s)  有效时长(s)  去除时长(s)\n")
        f.write("-" * 70 + "\n")

        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = freq_table[i]
            total_duration = end_time - start_time
            trim_duration = total_duration * 0.16  # 首尾各8%
            effective_duration = total_duration - trim_duration
            f.write(f"{i:2d}    {expected_freq:8.1f}    {start_time:8.3f}    {end_time:8.3f}    "
                   f"{effective_duration:8.3f}    {trim_duration:8.3f}\n")

    print(f"📄 汇总信息已保存: {summary_file}")

if __name__ == "__main__":
    main()
