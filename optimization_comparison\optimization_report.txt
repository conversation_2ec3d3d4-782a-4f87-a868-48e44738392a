特征优化效果报告
==================================================

1. 特征优化策略:
   - 删除低重要性特征: 17 个
   - 删除冗余特征: 30 个
   - 保留核心特征: 15 个

2. 性能对比结果:
   Random Forest:
     原始特征集 AUC: 0.739
     优化特征集 AUC: 0.722
     改进幅度: -0.017

   Logistic Regression:
     原始特征集 AUC: 0.654
     优化特征集 AUC: 0.657
     改进幅度: +0.003

   SVM:
     原始特征集 AUC: 0.648
     优化特征集 AUC: 0.597
     改进幅度: -0.051

3. 核心特征列表:
    1. vertical_line_count
    2. band_3_energy_ratio_std
    3. spectral_energy_kurtosis
    4. vertical_line_max_freq
    5. spectral_peak_kurtosis
    6. high_freq_noise_ratio_kurtosis
    7. thd
    8. spectral_energy_mean
    9. low_freq_energy_ratio
   10. low_freq_energy_variation
   11. noise_power_ratio
   12. thdn
   13. spectral_flatness_mean
   14. band_1_energy_ratio_std
   15. band_2_energy_ratio_std

4. 优化效果总结:
   - 平均AUC改进: -0.022
   - 特征数量减少: 47 个
   - 计算效率提升: 约 75.8%
