#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于个体负样本分析的精确检测系统
使用6个通用特征 + 竖线检测
"""

import os
import sys
import numpy as np
import pandas as pd
import librosa
from scipy.signal import stft, find_peaks
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

class PrecisionDetectionSystem:
    def __init__(self):
        self.fs = 48000
        
        # 基于个体分析的6个通用特征
        self.universal_features = [
            'thd',
            'harmonic_2_ratio',
            'harmonic_3_ratio', 
            'harmonic_total_ratio',
            'stat_p50'
        ]
        
        # 加载实际阈值数据
        self.load_feature_thresholds()
        
        print(f"🔧 精确检测系统初始化")
        print(f"📊 使用6个通用特征 + 竖线检测")
        print(f"📊 通用特征: {self.universal_features}")
    
    def load_feature_thresholds(self):
        """加载特征阈值数据"""
        try:
            # 尝试加载详细的阈值数据
            if os.path.exists('individual_negative_analysis_detailed.csv'):
                self.threshold_data = pd.read_csv('individual_negative_analysis_detailed.csv')
                print(f"✅ 加载阈值数据: {len(self.threshold_data)}条记录")
            else:
                print("⚠️ 未找到阈值数据，使用默认阈值")
                self.threshold_data = None
        except Exception as e:
            print(f"❌ 加载阈值数据失败: {e}")
            self.threshold_data = None
    
    def detect_audio_file(self, audio_path, debug=False):
        """检测单个音频文件"""
        try:
            if debug:
                print(f"🔍 检测文件: {os.path.basename(audio_path)}")
            
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            # 1. 竖线检测
            vl_result = self._detect_vertical_lines(audio_path, step_boundaries, freq_table)
            
            # 2. 通用特征检测
            universal_result = self._detect_universal_features(audio_path, step_boundaries, freq_table)
            
            # 3. 综合判定
            final_decision = self._make_precision_decision(vl_result, universal_result, debug)
            
            return {
                'status': 'success',
                'predicted_label': final_decision['label'],
                'confidence': final_decision['confidence'],
                'vertical_line_result': vl_result,
                'universal_feature_result': universal_result,
                'reasoning': final_decision['reasoning']
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _detect_vertical_lines(self, audio_path, step_boundaries, freq_table):
        """竖线检测（复用之前的算法）"""
        try:
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            vertical_line_segments = 0
            total_segments = len(step_boundaries)
            
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                start_sample = int(start_time * self.fs)
                end_sample = int(end_time * self.fs)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) == 0:
                    continue
                
                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))
                
                # STFT分析
                f, t, Zxx = stft(segment_audio, self.fs, nperseg=1024, noverlap=512)
                freq_mask = (f >= 100) & (f <= 20000)
                f_filtered = f[freq_mask]
                Zxx_filtered = Zxx[freq_mask, :]
                
                power_spectrum = np.abs(Zxx_filtered) ** 2
                power_db = 10 * np.log10(power_spectrum + 1e-12)
                
                # 总能量变化
                total_energy = np.sum(power_db, axis=0)
                energy_mean = np.mean(total_energy)
                energy_std = np.std(total_energy)
                
                # 峰值检测
                min_height = energy_mean + 0.5 * energy_std
                min_prominence = 0.3 * energy_std
                
                peaks, properties = find_peaks(total_energy, 
                                             height=min_height,
                                             distance=2,
                                             prominence=min_prominence)
                
                # 竖线检测
                for peak_idx in peaks:
                    if peak_idx < len(t):
                        peak_spectrum = power_db[:, peak_idx]
                        threshold = np.percentile(peak_spectrum, 70)
                        high_energy_mask = peak_spectrum > threshold
                        high_energy_indices = np.where(high_energy_mask)[0]
                        
                        if len(high_energy_indices) > 0:
                            freq_span = f_filtered[high_energy_indices[-1]] - f_filtered[high_energy_indices[0]]
                            freq_ratio = len(high_energy_indices) / len(f_filtered)
                            
                            high_energy_power = peak_spectrum[high_energy_indices]
                            background_power = np.delete(peak_spectrum, high_energy_indices)
                            if len(background_power) > 0:
                                line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                            else:
                                line_strength = 1.0
                            
                            # 竖线判定
                            if line_strength >= 2.0 and freq_span >= 1000 and freq_ratio >= 0.1:
                                vertical_line_segments += 1
                                break
            
            vl_ratio = vertical_line_segments / total_segments if total_segments > 0 else 0
            
            return {
                'vertical_line_segments': vertical_line_segments,
                'total_segments': total_segments,
                'vl_ratio': vl_ratio,
                'anomaly_detected': vl_ratio > 0.01  # 1%以上频段有竖线
            }
            
        except Exception as e:
            return {
                'vertical_line_segments': 0,
                'total_segments': 0,
                'vl_ratio': 0,
                'anomaly_detected': False,
                'error': str(e)
            }
    
    def _detect_universal_features(self, audio_path, step_boundaries, freq_table):
        """通用特征检测"""
        try:
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            anomalous_segments = 0
            total_segments = len(step_boundaries)
            feature_anomalies = {feature: 0 for feature in self.universal_features}
            
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                start_sample = int(start_time * self.fs)
                end_sample = int(end_time * self.fs)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) == 0:
                    continue
                
                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))
                
                # 提取特征
                features = self._extract_universal_features(segment_audio, expected_freq)
                
                # 检测异常
                segment_anomaly = False
                for feature in self.universal_features:
                    if feature in features:
                        if self._is_feature_anomalous(feature, features[feature], seg_idx):
                            feature_anomalies[feature] += 1
                            segment_anomaly = True
                
                if segment_anomaly:
                    anomalous_segments += 1
            
            anomaly_ratio = anomalous_segments / total_segments if total_segments > 0 else 0
            
            return {
                'anomalous_segments': anomalous_segments,
                'total_segments': total_segments,
                'anomaly_ratio': anomaly_ratio,
                'feature_anomalies': feature_anomalies,
                'anomaly_detected': anomaly_ratio > 0.02  # 2%以上频段异常
            }
            
        except Exception as e:
            return {
                'anomalous_segments': 0,
                'total_segments': 0,
                'anomaly_ratio': 0,
                'feature_anomalies': {},
                'anomaly_detected': False,
                'error': str(e)
            }
    
    def _extract_universal_features(self, audio, expected_freq):
        """提取通用特征"""
        features = {}
        
        try:
            # FFT分析
            fft = np.fft.fft(audio)
            freqs = np.fft.fftfreq(len(audio), 1/self.fs)
            magnitude = np.abs(fft)
            
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            # 基频能量
            fundamental_tolerance = 20
            fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
            if np.any(fundamental_mask):
                fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
            else:
                fundamental_energy = 0
            
            # 谐波能量
            harmonic_energies = []
            for harmonic in range(2, 6):
                harmonic_freq = expected_freq * harmonic
                if harmonic_freq < self.fs / 2:
                    harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                    if np.any(harmonic_mask):
                        harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                        harmonic_energies.append(harmonic_energy)
                    else:
                        harmonic_energies.append(0)
            
            # 计算谐波特征
            total_harmonic_energy = sum(harmonic_energies)
            total_energy = np.sum(positive_magnitude**2)
            
            if fundamental_energy > 0:
                features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
                if len(harmonic_energies) > 0:
                    features['harmonic_2_ratio'] = harmonic_energies[0] / fundamental_energy
                if len(harmonic_energies) > 1:
                    features['harmonic_3_ratio'] = harmonic_energies[1] / fundamental_energy
            else:
                features['thd'] = 0
                features['harmonic_2_ratio'] = 0
                features['harmonic_3_ratio'] = 0
            
            features['harmonic_total_ratio'] = total_harmonic_energy / total_energy if total_energy > 0 else 0
            
            # 统计特征
            features['stat_p50'] = np.percentile(audio, 50)
            
        except Exception as e:
            # 设置默认值
            for feature in self.universal_features:
                features[feature] = 0
        
        return features
    
    def _is_feature_anomalous(self, feature, value, segment_idx):
        """判断特征是否异常"""
        # 使用简化的阈值判定
        thresholds = {
            'thd': 0.05,
            'harmonic_2_ratio': 0.01,
            'harmonic_3_ratio': 0.005,
            'harmonic_total_ratio': 0.01,
            'stat_p50': 0.1
        }
        
        if feature in thresholds:
            if feature == 'stat_p50':
                return abs(value) > thresholds[feature]
            else:
                return value > thresholds[feature]
        
        return False
    
    def _make_precision_decision(self, vl_result, universal_result, debug=False):
        """精确判定"""
        # 检测到的异常类型
        anomalies = []
        confidence_scores = []
        
        # 竖线检测
        if vl_result['anomaly_detected']:
            anomalies.append('vertical_line')
            confidence_scores.append(min(vl_result['vl_ratio'] * 100, 1.0))
        
        # 通用特征检测
        if universal_result['anomaly_detected']:
            anomalies.append('universal_features')
            confidence_scores.append(min(universal_result['anomaly_ratio'] * 50, 1.0))
        
        # 综合判定
        if len(anomalies) > 0:
            predicted_label = 'neg'
            confidence = np.mean(confidence_scores)
            reasoning = f"检测到异常: {', '.join(anomalies)}"
        else:
            predicted_label = 'pos'
            confidence = 0.95
            reasoning = "未检测到异常"
        
        if debug:
            print(f"  竖线检测: {'异常' if vl_result['anomaly_detected'] else '正常'} "
                  f"({vl_result['vl_ratio']:.3f})")
            print(f"  通用特征: {'异常' if universal_result['anomaly_detected'] else '正常'} "
                  f"({universal_result['anomaly_ratio']:.3f})")
            print(f"  最终判定: {predicted_label} (置信度: {confidence:.3f})")
        
        return {
            'label': predicted_label,
            'confidence': confidence,
            'reasoning': reasoning,
            'detected_anomalies': anomalies
        }

def validate_precision_system():
    """验证精确检测系统"""
    print("🔍 验证基于个体分析的精确检测系统")
    print("="*70)

    detector = PrecisionDetectionSystem()

    # 定义测试文件夹
    test_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }

    validation_results = []

    # 测试所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue

            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)

            print(f"\n📁 测试文件夹: {folder_name} ({true_label}) - {len(wav_files)}个文件")
            print("-" * 60)

            for i, audio_path in enumerate(wav_files):
                filename = os.path.basename(audio_path)

                print(f"  🎵 [{i+1}/{len(wav_files)}] {filename}")

                # 检测
                result = detector.detect_audio_file(audio_path, debug=False)

                if result['status'] == 'success':
                    predicted_label = result['predicted_label']
                    confidence = result['confidence']
                    reasoning = result['reasoning']

                    # 判定正确性
                    is_correct = predicted_label == true_label
                    correctness_icon = "✅" if is_correct else "❌"

                    # 显示结果
                    print(f"     {correctness_icon} 预测: {predicted_label} (置信度: {confidence:.3f})")
                    print(f"        推理: {reasoning}")

                    # 保存结果
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'is_correct': is_correct,
                        'confidence': confidence,
                        'reasoning': reasoning,
                        'vl_ratio': result['vertical_line_result']['vl_ratio'],
                        'universal_ratio': result['universal_feature_result']['anomaly_ratio'],
                        'status': 'success'
                    })

                else:
                    print(f"     ❌ 检测失败: {result['error']}")
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': 'failed',
                        'is_correct': False,
                        'status': 'failed',
                        'error': result['error']
                    })

    # 生成验证报告
    generate_precision_validation_report(validation_results)

    return validation_results

def generate_precision_validation_report(results):
    """生成精确验证报告"""
    print(f"\n" + "="*70)
    print(f"📊 精确检测系统验证报告")
    print("="*70)

    df = pd.DataFrame(results)

    # 总体统计
    total_files = len(df)
    success_files = len(df[df['status'] == 'success'])

    print(f"\n📊 总体统计:")
    print(f"  总文件数: {total_files}")
    print(f"  成功检测: {success_files} ({success_files/total_files*100:.1f}%)")

    # 准确率统计
    success_data = df[df['status'] == 'success']

    if len(success_data) > 0:
        correct_predictions = success_data[success_data['is_correct'] == True]
        accuracy = len(correct_predictions) / len(success_data)

        print(f"\n📊 准确率统计:")
        print(f"  总体准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")

        # 按类别统计
        for label in ['pos', 'neg']:
            label_samples = success_data[success_data['true_label'] == label]
            if len(label_samples) > 0:
                label_correct = label_samples[label_samples['is_correct'] == True]
                label_accuracy = len(label_correct) / len(label_samples)
                avg_confidence = label_samples['confidence'].mean()
                print(f"  {label}样本准确率: {label_accuracy:.3f} ({len(label_correct)}/{len(label_samples)}) "
                      f"平均置信度: {avg_confidence:.3f}")

    # 检测方法效果分析
    if 'vl_ratio' in success_data.columns:
        print(f"\n📊 检测方法效果:")
        print("-" * 50)

        vl_detected = success_data[success_data['vl_ratio'] > 0.01]
        universal_detected = success_data[success_data['universal_ratio'] > 0.02]

        print(f"  竖线检测触发: {len(vl_detected)}个文件")
        print(f"  通用特征检测触发: {len(universal_detected)}个文件")

        # 按检测方法统计准确率
        if len(vl_detected) > 0:
            vl_accuracy = vl_detected['is_correct'].mean()
            print(f"  竖线检测准确率: {vl_accuracy:.3f}")

        if len(universal_detected) > 0:
            universal_accuracy = universal_detected['is_correct'].mean()
            print(f"  通用特征检测准确率: {universal_accuracy:.3f}")

    # 错误案例分析
    error_cases = success_data[success_data['is_correct'] == False]

    if len(error_cases) > 0:
        print(f"\n📊 错误案例分析:")
        print("-" * 50)

        for _, case in error_cases.iterrows():
            print(f"  ❌ {case['filename']}")
            print(f"     真实: {case['true_label']}, 预测: {case['predicted_label']} "
                  f"(置信度: {case['confidence']:.3f})")
            print(f"     推理: {case['reasoning']}")
            if 'vl_ratio' in case:
                print(f"     竖线比例: {case['vl_ratio']:.3f}, 通用特征比例: {case['universal_ratio']:.3f}")

    # 保存验证结果
    df.to_csv('precision_detection_validation.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 验证结果已保存: precision_detection_validation.csv")

if __name__ == "__main__":
    validate_precision_system()
