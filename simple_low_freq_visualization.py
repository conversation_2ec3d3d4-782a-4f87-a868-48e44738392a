#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版低音戳洞前5段可视化
确保图片能正确显示
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体和解决负号显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def simple_low_freq_visualization():
    """简化版低频段可视化"""
    print("🎯 简化版低音戳洞前5段可视化")
    print("="*50)
    
    # 两个样本文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    normal_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav'
    
    if not os.path.exists(hole_file) or not os.path.exists(normal_file):
        print("❌ 文件不存在")
        return
    
    # 分析两个样本
    hole_data = analyze_simple(hole_file, "低音戳洞")
    normal_data = analyze_simple(normal_file, "正常样本")
    
    if hole_data and normal_data:
        create_simple_visualization(hole_data, normal_data)

def analyze_simple(audio_path, sample_name):
    """简化分析"""
    try:
        print(f"\n分析 {sample_name}...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析前5段
        for seg_idx in range(min(5, len(step_boundaries))):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析
            fft_size = 32768  # 32k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power_db = 20 * np.log10(magnitude + 1e-12)
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power_db = power_db[:fft_size//2]
            
            # 找主频
            search_range = (expected_freq - 5, expected_freq + 5)
            search_mask = (positive_freqs >= search_range[0]) & (positive_freqs <= search_range[1])
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = positive_power_db[search_mask]
                max_idx = search_indices[np.argmax(search_powers)]
                main_freq = positive_freqs[max_idx]
                main_power = positive_power_db[max_idx]
            else:
                main_freq = expected_freq
                main_power = -60
            
            # 简单谐波检测
            harmonics = []
            for order in range(2, 11):  # 2-10次谐波
                harmonic_freq = main_freq * order
                if harmonic_freq >= sr/2:
                    break
                
                # 在谐波频率附近搜索
                harm_range = (harmonic_freq - 10, harmonic_freq + 10)
                harm_mask = (positive_freqs >= harm_range[0]) & (positive_freqs <= harm_range[1])
                
                if np.any(harm_mask):
                    harm_indices = np.where(harm_mask)[0]
                    harm_powers = positive_power_db[harm_mask]
                    harm_max_idx = harm_indices[np.argmax(harm_powers)]
                    harm_freq_actual = positive_freqs[harm_max_idx]
                    harm_power = positive_power_db[harm_max_idx]
                    
                    # 简单SNR检查
                    if harm_power > main_power - 50:  # 相对主频不超过50dB衰减
                        harmonics.append({
                            'order': order,
                            'freq': harm_freq_actual,
                            'power': harm_power
                        })
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'freqs': positive_freqs,
                'power_db': positive_power_db,
                'main_freq': main_freq,
                'main_power': main_power,
                'harmonics': harmonics
            })
            
            print(f"  段{seg_idx}: {expected_freq:.0f}Hz -> {main_freq:.1f}Hz ({main_power:.1f}dB), {len(harmonics)}个谐波")
        
        return {
            'sample_name': sample_name,
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def create_simple_visualization(hole_data, normal_data):
    """创建简化可视化"""
    print(f"\n🎨 生成简化可视化...")
    
    # 创建图表 (5行1列)
    fig, axes = plt.subplots(5, 1, figsize=(16, 20))
    
    hole_segments = hole_data['segments']
    normal_segments = normal_data['segments']
    
    min_segments = min(len(hole_segments), len(normal_segments))
    
    for i in range(min_segments):
        ax = axes[i]
        
        hole_seg = hole_segments[i]
        normal_seg = normal_segments[i]
        
        # 设置显示范围 (0-20kHz)
        freq_range = (0, 20000)
        
        # 处理低音戳洞数据
        hole_freqs = hole_seg['freqs']
        hole_power = hole_seg['power_db']
        hole_mask = (hole_freqs >= freq_range[0]) & (hole_freqs <= freq_range[1])
        hole_display_freqs = hole_freqs[hole_mask]
        hole_display_power = hole_power[hole_mask]
        
        # 处理正常样本数据
        normal_freqs = normal_seg['freqs']
        normal_power = normal_seg['power_db']
        normal_mask = (normal_freqs >= freq_range[0]) & (normal_freqs <= freq_range[1])
        normal_display_freqs = normal_freqs[normal_mask]
        normal_display_power = normal_power[normal_mask]
        
        # 绘制频谱
        ax.plot(hole_display_freqs, hole_display_power, 'r-', linewidth=0.8, alpha=0.7, 
               label=f'{hole_data["sample_name"]} 频谱')
        ax.plot(normal_display_freqs, normal_display_power, 'b-', linewidth=0.8, alpha=0.7, 
               label=f'{normal_data["sample_name"]} 频谱')
        
        # 标记主频
        ax.plot(hole_seg['main_freq'], hole_seg['main_power'], 'ro', markersize=8, 
               label=f'{hole_data["sample_name"]} 主频 {hole_seg["main_freq"]:.1f}Hz')
        ax.plot(normal_seg['main_freq'], normal_seg['main_power'], 'bo', markersize=8, 
               label=f'{normal_data["sample_name"]} 主频 {normal_seg["main_freq"]:.1f}Hz')
        
        # 标记谐波
        for j, harmonic in enumerate(hole_seg['harmonics'][:5]):  # 只显示前5个谐波
            ax.plot(harmonic['freq'], harmonic['power'], 's', color='red', 
                   markersize=5, alpha=0.8)
            if j < 3:  # 只标注前3个
                ax.annotate(f'{harmonic["order"]}次', 
                           xy=(harmonic['freq'], harmonic['power']), 
                           xytext=(harmonic['freq'], harmonic['power'] + 5),
                           ha='center', va='bottom', fontsize=8, color='red')
        
        for j, harmonic in enumerate(normal_seg['harmonics'][:5]):  # 只显示前5个谐波
            ax.plot(harmonic['freq'], harmonic['power'], '^', color='blue', 
                   markersize=5, alpha=0.8)
            if j < 3:  # 只标注前3个
                ax.annotate(f'{harmonic["order"]}次', 
                           xy=(harmonic['freq'], harmonic['power']), 
                           xytext=(harmonic['freq'], harmonic['power'] - 8),
                           ha='center', va='top', fontsize=8, color='blue')
        
        # 设置图表属性
        expected_freq = hole_seg['expected_freq']
        ax.set_title(f'频段{hole_seg["seg_idx"]} ({expected_freq:.0f}Hz) 对比 - 完整频谱 (0-20kHz)', 
                    fontweight='bold', fontsize=12)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(freq_range)
        
        # 计算y轴范围
        all_power = np.concatenate([hole_display_power, normal_display_power])
        y_min = np.percentile(all_power, 5) - 10
        y_max = np.percentile(all_power, 95) + 10
        ax.set_ylim(y_min, y_max)
        
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.set_xscale('log')  # 对数刻度更好显示宽频范围
        
        # 添加信息文本
        info_text = f"对比信息:\n"
        info_text += f"{hole_data['sample_name']}: {hole_seg['main_freq']:.1f}Hz, {len(hole_seg['harmonics'])}个谐波\n"
        info_text += f"{normal_data['sample_name']}: {normal_seg['main_freq']:.1f}Hz, {len(normal_seg['harmonics'])}个谐波\n"
        info_text += f"谐波差异: {len(hole_seg['harmonics']) - len(normal_seg['harmonics']):+d}个"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'simple_low_freq_comparison.png'
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    print(f"✅ 简化可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    simple_low_freq_visualization()
