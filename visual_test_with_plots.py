#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化测试频段分割效果（生成图表）
"""

import os
import sys
import glob
import matplotlib.pyplot as plt
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

# 设置中文字体和非阻塞模式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.ion()  # 开启交互模式

def visual_test_with_plots():
    """可视化测试频段分割效果（生成图表）"""
    print("🔍 可视化测试频段分割效果（生成图表）")
    print("="*60)
    
    # 定义测试文件夹和文件
    test_files = [
        r"../test20250717/pos/sd卡/sd1_1.wav",
        r"../test20250717/pos/完美/ok1.wav",
        r"../test20250717/neg/主板隔音eva取消.wav",
        r"../待定/主板边缘eva消除.wav"
    ]
    
    # 测试每个文件
    for i, audio_path in enumerate(test_files):
        if not os.path.exists(audio_path):
            print(f"❌ 文件不存在: {audio_path}")
            continue
            
        filename = os.path.basename(audio_path)
        folder_path = os.path.dirname(audio_path)
        folder_name = os.path.basename(folder_path)
        
        print(f"\n🎵 [{i+1}/{len(test_files)}] 测试文件: {folder_name}/{filename}")
        print("=" * 50)
        
        try:
            # 调用优化的频率分割算法，生成可视化图表
            step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path,
                min_duration=153,
                plot=True,  # 生成可视化图表
                debug=True,  # 显示详细信息
                search_window_start=0.1,
                search_window_end=1.5,
                correlation_length=1.0
            )
            
            # 提取关键信息
            start_offset = alignment_info.get('start_offset', 0)
            correlation_score = alignment_info.get('correlation_score', 0)
            alignment_quality = alignment_info.get('alignment_quality', {})
            
            # 显示结果摘要
            print(f"\n📊 分割结果摘要:")
            print(f"  文件: {folder_name}/{filename}")
            print(f"  开始时间: {start_offset:.3f}s")
            print(f"  相关性: {correlation_score:.3f}")
            print(f"  频段数量: {len(freq_table)}")
            print(f"  步进区间数: {len(step_bounds)}")
            
            if alignment_quality:
                overall_quality = alignment_quality.get('overall_quality', 'unknown')
                composite_score = alignment_quality.get('composite_score', 0)
                print(f"  整体质量: {overall_quality}")
                print(f"  综合评分: {composite_score:.3f}")
            
            # 显示频段范围信息
            if len(freq_table) > 0:
                min_freq = min(freq_table)
                max_freq = max(freq_table)
                freq_range = max_freq - min_freq
                print(f"  频率范围: {min_freq:.1f}Hz - {max_freq:.1f}Hz (跨度: {freq_range:.1f}Hz)")
            
            # 显示时间范围信息
            if len(step_bounds) > 0:
                total_duration = step_bounds[-1][1] - step_bounds[0][0]
                avg_step_duration = total_duration / len(step_bounds)
                print(f"  时间范围: {step_bounds[0][0]:.3f}s - {step_bounds[-1][1]:.3f}s")
                print(f"  总时长: {total_duration:.3f}s")
                print(f"  平均步长: {avg_step_duration:.3f}s")
            
            # 显示前5个和后5个频段信息
            print(f"\n🎼 前5个频段信息:")
            for j in range(min(5, len(freq_table))):
                freq = freq_table[j]
                if j < len(step_bounds):
                    start_time, end_time = step_bounds[j]
                    duration = end_time - start_time
                    print(f"    {j+1:2d}. {freq:6.1f}Hz  时间:{start_time:.3f}s-{end_time:.3f}s  时长:{duration:.3f}s")
            
            if len(freq_table) > 5:
                print(f"\n🎼 最后5个频段信息:")
                for j in range(max(0, len(freq_table)-5), len(freq_table)):
                    freq = freq_table[j]
                    if j < len(step_bounds):
                        start_time, end_time = step_bounds[j]
                        duration = end_time - start_time
                        print(f"    {j+1:2d}. {freq:6.1f}Hz  时间:{start_time:.3f}s-{end_time:.3f}s  时长:{duration:.3f}s")
            
            # 质量评估
            if correlation_score >= 0.8:
                quality_icon = "🟢"
                quality_text = "优秀"
            elif correlation_score >= 0.6:
                quality_icon = "🟡"
                quality_text = "良好"
            else:
                quality_icon = "🔴"
                quality_text = "需要改进"
            
            print(f"\n{quality_icon} 质量评估: {quality_text}")
            print(f"✅ 处理成功")
            
            # 保存图表
            plt.savefig(f'freq_split_{folder_name}_{filename.replace(".wav", "")}.png', 
                       dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存: freq_split_{folder_name}_{filename.replace('.wav', '')}.png")
            
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
        
        print("-" * 50)
        
        # 暂停一下让用户查看图表
        if i < len(test_files) - 1:
            input(f"\n按Enter继续下一个文件...")
    
    print(f"\n🎉 所有可视化测试完成！")
    print(f"📊 图表文件已保存在当前目录")

if __name__ == "__main__":
    visual_test_with_plots()
