#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断频谱问题：检查切割和频谱参数
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
import sys
sys.path.append('harmonic_detection_system')
from chirp import gen_freq_step

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_segment_spectrum(test_audio, ref_audio, start_time, end_time, expected_freq, sr=48000):
    """
    分析单个频段的频谱，诊断问题
    """
    print(f"\n🔍 分析频段: {expected_freq:.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
    
    # 提取段音频
    start_sample = int(start_time * sr)
    end_sample = int(end_time * sr)
    
    test_segment = test_audio[start_sample:end_sample]
    ref_segment = ref_audio[start_sample:end_sample]
    
    print(f"  原始段长度: 待测={len(test_segment)}, 参考={len(ref_segment)}")
    
    # 确保长度一致
    min_length = min(len(test_segment), len(ref_segment))
    test_segment = test_segment[:min_length]
    ref_segment = ref_segment[:min_length]
    
    # 取中间100ms
    target_samples = 4800
    if len(test_segment) > target_samples:
        center_idx = len(test_segment) // 2
        half_target = target_samples // 2
        start_idx = center_idx - half_target
        end_idx = center_idx + half_target
        test_segment = test_segment[start_idx:end_idx]
        ref_segment = ref_segment[start_idx:end_idx]
    
    print(f"  分析段长度: {len(test_segment)}样本 ({len(test_segment)/sr*1000:.1f}ms)")
    
    # 检查信号特征
    test_max = np.max(np.abs(test_segment))
    ref_max = np.max(np.abs(ref_segment))
    test_rms = np.sqrt(np.mean(test_segment**2))
    ref_rms = np.sqrt(np.mean(ref_segment**2))
    
    print(f"  待测信号: 最大值={test_max:.4f}, RMS={test_rms:.4f}")
    print(f"  参考信号: 最大值={ref_max:.4f}, RMS={ref_rms:.4f}")
    
    # FFT分析（不标准化）
    fft_size = len(test_segment)
    window = np.hanning(fft_size)
    
    test_windowed = test_segment * window
    ref_windowed = ref_segment * window
    
    test_fft = np.fft.fft(test_windowed)
    ref_fft = np.fft.fft(ref_windowed)
    
    freqs = np.fft.fftfreq(fft_size, 1/sr)
    positive_freqs = freqs[:fft_size//2]
    test_fft_pos = test_fft[:fft_size//2]
    ref_fft_pos = ref_fft[:fft_size//2]
    
    # 计算功率谱
    test_power = np.abs(test_fft_pos) ** 2
    ref_power = np.abs(ref_fft_pos) ** 2
    
    # 在期望频率附近找峰值
    search_mask = (positive_freqs >= expected_freq - 10) & (positive_freqs <= expected_freq + 10)
    
    if np.any(search_mask):
        test_peak_power = np.max(test_power[search_mask])
        ref_peak_power = np.max(ref_power[search_mask])
        test_peak_freq = positive_freqs[search_mask][np.argmax(test_power[search_mask])]
        ref_peak_freq = positive_freqs[search_mask][np.argmax(ref_power[search_mask])]
        
        print(f"  待测峰值: {test_peak_freq:.1f}Hz, 功率={test_peak_power:.2e}")
        print(f"  参考峰值: {ref_peak_freq:.1f}Hz, 功率={ref_peak_power:.2e}")
        
        if ref_peak_power > 0:
            power_ratio = test_peak_power / ref_peak_power
            print(f"  功率比值: {power_ratio:.6f}")
        else:
            print(f"  参考信号无峰值!")
    else:
        print(f"  在期望频率附近未找到峰值!")
    
    return test_segment, ref_segment, positive_freqs, test_power, ref_power

def main():
    """
    主函数：诊断频谱问题
    """
    print("🔍 诊断频谱问题")
    print("="*50)
    
    # 生成参考信号
    print("📊 生成参考信号...")
    sine_wave, t_list, freq_ssample_dict = gen_freq_step(
        start_freq=100, stop_freq=24000, octave=12,
        min_cycles=10, min_duration=153, fs=48000
    )
    
    # 加载待测音频
    print("🎵 加载待测音频...")
    test_audio, sr = librosa.load("test20250722/鼓膜破裂（复测1.1).wav", sr=48000)
    
    # 确保长度一致
    min_length = min(len(sine_wave), len(test_audio))
    ref_audio = sine_wave[:min_length]
    test_audio = test_audio[:min_length]
    
    print(f"✅ 信号长度: {min_length/sr:.2f}秒")
    
    # 提取频段信息
    step_boundaries = []
    frequency_points = []
    
    for freq in sorted(freq_ssample_dict.keys()):
        start_time, start_sample, duration_samples = freq_ssample_dict[freq]
        end_time = start_time + duration_samples / 48000
        step_boundaries.append((start_time, end_time))
        frequency_points.append(freq)
    
    # 分析前几个频段
    test_segments = [1, 10, 20, 50, 80]  # 选择几个代表性频段
    
    fig, axes = plt.subplots(len(test_segments), 2, figsize=(16, 4*len(test_segments)))
    
    for i, seg_idx in enumerate(test_segments):
        if seg_idx > len(step_boundaries):
            continue
            
        start_time, end_time = step_boundaries[seg_idx-1]
        expected_freq = frequency_points[seg_idx-1]
        
        # 分析频段
        test_seg, ref_seg, freqs, test_power, ref_power = analyze_segment_spectrum(
            test_audio, ref_audio, start_time, end_time, expected_freq
        )
        
        # 绘制时域信号
        t = np.arange(len(test_seg)) / sr * 1000  # ms
        axes[i, 0].plot(t, test_seg, 'b-', alpha=0.7, label='待测信号')
        axes[i, 0].plot(t, ref_seg, 'r-', alpha=0.7, label='参考信号')
        axes[i, 0].set_title(f'频段{seg_idx}: {expected_freq:.1f}Hz - 时域信号')
        axes[i, 0].set_xlabel('时间 (ms)')
        axes[i, 0].set_ylabel('幅值')
        axes[i, 0].legend()
        axes[i, 0].grid(True, alpha=0.3)
        
        # 绘制频域信号
        power_db_test = 10 * np.log10(test_power + 1e-12)
        power_db_ref = 10 * np.log10(ref_power + 1e-12)
        
        freq_range = (expected_freq/2, expected_freq*2)
        freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
        
        axes[i, 1].semilogx(freqs[freq_mask], power_db_test[freq_mask], 'b-', alpha=0.7, label='待测信号')
        axes[i, 1].semilogx(freqs[freq_mask], power_db_ref[freq_mask], 'r-', alpha=0.7, label='参考信号')
        axes[i, 1].axvline(expected_freq, color='k', linestyle='--', alpha=0.5, label=f'期望频率 {expected_freq:.1f}Hz')
        axes[i, 1].set_title(f'频段{seg_idx}: {expected_freq:.1f}Hz - 频域信号')
        axes[i, 1].set_xlabel('频率 (Hz)')
        axes[i, 1].set_ylabel('功率 (dB)')
        axes[i, 1].legend()
        axes[i, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('频谱诊断结果.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("\n📊 诊断完成!")
    print("💡 关键发现:")
    print("  1. 检查时域信号是否在正确的时间段包含期望频率")
    print("  2. 检查频域信号的主频是否匹配")
    print("  3. 检查两个信号的幅值关系")
    print("  4. 图像已保存: 频谱诊断结果.png")

if __name__ == "__main__":
    main()
