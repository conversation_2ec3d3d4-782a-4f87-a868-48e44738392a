import numpy as np
import soundfile as sf
import librosa
import librosa.display
import matplotlib.pyplot as plt
import os
from scipy.signal import find_peaks
from scipy.ndimage import uniform_filter1d
from scipy.fft import rfft
import scipy.signal

# ========================
# 参数配置
# ========================
REF_PATH = r"test20250703/pos1.wav"
MIC_PATH = r"test20250703/nag2.wav"
OUTPUT_DIR = "analysis_result"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 扫频参数
f_start = 50      # 起始频率 Hz
f_end = 10000     # 终止频率 Hz
fs = 44100        # 采样率 Hz

# ========================
# 加载音频并裁剪统一长度
# ========================
y_ref, sr = librosa.load(REF_PATH, sr=fs, mono=True)
y_mic, sr2 = librosa.load(MIC_PATH, sr=fs, mono=True)

min_len = min(len(y_ref), len(y_mic))
y_ref = y_ref[:min_len]
y_mic = y_mic[:min_len]

# ========================
# STFT 频谱图分析
# ========================
S_mic = librosa.stft(y_mic, n_fft=2048, hop_length=512)
S_db = librosa.amplitude_to_db(np.abs(S_mic), ref=np.max)

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

plt.figure(figsize=(12, 6))
librosa.display.specshow(S_db, sr=sr, hop_length=512, x_axis='time', y_axis='log', cmap='magma')
plt.colorbar(format="%+2.0f dB")
plt.title("频谱图 Spectrogram")
plt.tight_layout()
plt.savefig(os.path.join(OUTPUT_DIR, "spectrogram.png"))
plt.close()

# ========================
# ZCR 对比分析
# ========================
zcr_ref = librosa.feature.zero_crossing_rate(y_ref, frame_length=2048, hop_length=512)[0]
zcr_mic = librosa.feature.zero_crossing_rate(y_mic, frame_length=2048, hop_length=512)[0]
zcr_diff = zcr_mic - zcr_ref
zcr_diff_peaks, _ = find_peaks(np.abs(zcr_diff), height=np.mean(np.abs(zcr_diff)) * 2.5, distance=3, width=1)

plt.figure(figsize=(10, 5))
plt.plot(zcr_mic, label="Mic ZCR", color='orange')
plt.plot(zcr_ref, label="Ref ZCR", color='blue', alpha=0.6)
plt.plot(zcr_diff, label="ZCR差值", color='purple')
plt.plot(zcr_diff_peaks, zcr_diff[zcr_diff_peaks], "rx", label="异常点")
plt.title("ZCR 对比分析")
plt.xlabel("帧")
plt.ylabel("ZCR")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.savefig(os.path.join(OUTPUT_DIR, "zcr_compare.png"))
plt.close()

# ========================
# RMS 分析
# ========================
rms_vals = librosa.feature.rms(y=y_mic, frame_length=2048, hop_length=512)[0]
rms_peaks, _ = find_peaks(rms_vals, height=np.mean(rms_vals) * 2.5)

plt.figure(figsize=(10, 4))
plt.plot(rms_vals, label="RMS Energy", color='green')
plt.title("RMS（能量突变检测）")
plt.xlabel("帧")
plt.ylabel("RMS 值")
plt.grid(True)
plt.tight_layout()
plt.savefig(os.path.join(OUTPUT_DIR, "rms.png"))
plt.close()

# ========================
# 频率响应分析
# ========================
frame_energy_ref = np.sum(np.abs(librosa.stft(y_ref, n_fft=2048, hop_length=512)), axis=0)
frame_energy_mic = np.sum(np.abs(librosa.stft(y_mic, n_fft=2048, hop_length=512)), axis=0)
energy_corr = scipy.signal.correlate(frame_energy_mic, frame_energy_ref, mode='full')
delay_frames = np.argmax(energy_corr) - len(frame_energy_ref) + 1
delay_samples = delay_frames * 512

if delay_samples > 0:
    y_mic_aligned = y_mic[delay_samples:]
    y_ref_aligned = y_ref[:len(y_mic_aligned)]
elif delay_samples < 0:
    y_ref_aligned = y_ref[-delay_samples:]
    y_mic_aligned = y_mic[:len(y_ref_aligned)]
else:
    y_ref_aligned = y_ref
    y_mic_aligned = y_mic

S_ref = librosa.stft(y_ref_aligned, n_fft=2048, hop_length=512)
S_mic = librosa.stft(y_mic_aligned, n_fft=2048, hop_length=512)
dominant_bins_ref = np.argmax(np.abs(S_ref), axis=0)
dominant_amplitudes_ref = np.abs(S_ref)[dominant_bins_ref, range(S_ref.shape[1])]
dominant_amplitudes_mic = np.abs(S_mic)[dominant_bins_ref, range(S_mic.shape[1])]
freq_bins = librosa.fft_frequencies(sr=sr, n_fft=2048)
freqs_aligned = freq_bins[dominant_bins_ref]

response = dominant_amplitudes_mic / (dominant_amplitudes_ref + 1e-6)
response_db = 20 * np.log10(response + 1e-6)

# 限制在扫频范围内
valid_mask = (freqs_aligned >= f_start) & (freqs_aligned <= f_end)
freqs_aligned = freqs_aligned[valid_mask]
response_db = response_db[valid_mask]

smoothed = uniform_filter1d(response_db, size=15)
abnormal_indices = np.where(np.abs(response_db - smoothed) > 6)[0]
score_penalty = np.mean(np.abs(response_db - smoothed))
confidence_score = max(0, 100 - score_penalty * 4)

plt.figure(figsize=(10, 5))
plt.semilogx(freqs_aligned, response_db, color='blue', label='频率响应')
plt.semilogx(freqs_aligned, smoothed, color='black', linestyle='--', label='滑动平均')
plt.scatter(freqs_aligned[abnormal_indices], response_db[abnormal_indices], color='red', s=10, label='异常点')
plt.title("频率响应对比（自动时域对齐 + 主频轨迹）")
plt.xlabel("频率 (Hz)")
plt.ylabel("相对幅度 (dB)")
plt.legend()
plt.grid(True, which='both', linestyle='--')
plt.tight_layout()
plt.savefig(os.path.join(OUTPUT_DIR, "frequency_response.png"))
plt.close()

# ========================
# 失真分析（THD估计）
# ========================
N = 4096
window = np.hanning(N)
mid = len(y_mic) // 2
start = max(0, mid - N // 2)
end = start + N
ref_win = y_ref[start:end] * window
mic_win = y_mic[start:end] * window

ref_fft = np.abs(rfft(ref_win))
mic_fft = np.abs(rfft(mic_win))
freq_bins = np.fft.rfftfreq(N, d=1/sr)
valid_bins = (freq_bins >= f_start) & (freq_bins <= f_end)
mic_fft_valid = mic_fft.copy()
mic_fft_valid[~valid_bins] = 0

peak_idx = np.argmax(mic_fft_valid)
f0 = freq_bins[peak_idx]
harmonics = [i for i in range(2, 6)]
thd_numerator = 0
fundamental_power = mic_fft[peak_idx]**2

for h in harmonics:
    harmonic_freq = f0 * h
    if harmonic_freq > fs / 2:
        continue
    harmonic_bin = np.argmin(np.abs(freq_bins - harmonic_freq))
    thd_numerator += mic_fft[harmonic_bin]**2

thd_ratio = 100 * np.sqrt(thd_numerator / fundamental_power) if fundamental_power > 0 else 0

plt.figure(figsize=(10, 4))
plt.plot(freq_bins, 20 * np.log10(mic_fft + 1e-12), label="频谱 (Mic)", color='navy')

# 标注主频与谐波
plt.axvline(f0, color='red', linestyle='--', label=f'主频 {f0:.1f} Hz')
for h in harmonics:
    harmonic_freq = f0 * h
    if harmonic_freq > fs / 2:
        continue
    plt.axvline(harmonic_freq, color='orange', linestyle=':', label=f'{h} 次谐波 {harmonic_freq:.1f} Hz')

plt.title("THD 分析 - 频谱及谐波")
plt.xlabel("频率 (Hz)")
plt.ylabel("幅度 (dB)")
plt.xlim(0, f_end + 1000)
plt.ylim(top=0)
plt.grid(True, which='both', linestyle='--')
plt.legend(loc='upper right', fontsize=8)
plt.tight_layout()
plt.savefig(os.path.join(OUTPUT_DIR, "thd_spectrum.png"))
plt.close()

# ========================
# 异常分析报告输出
# ========================
with open(os.path.join(OUTPUT_DIR, "anomaly_report.txt"), 'w', encoding='utf-8') as f:
    f.write("=== 异常分析报告 ===\n")
    f.write(f"检测频率范围: {f_start} Hz ~ {f_end} Hz\n")
    f.write(f"ZCR异常峰数量: {len(zcr_diff_peaks)}\n")
    f.write(f"RMS异常峰数量: {len(rms_peaks)}\n")
    f.write(f"频率响应异常点数: {len(abnormal_indices)}\n")
    f.write(f"频响平均偏差: {score_penalty:.2f} dB\n")
    f.write(f"THD估算值: {thd_ratio:.2f} %\n")
    f.write(f"置信度评分（越高越正常）: {confidence_score:.2f}/100\n")
    if confidence_score < 80 or thd_ratio > 5:
        f.write("[警告] 检测到可能存在异常，请检查喇叭结构、失真或电气干扰。\n")
    else:
        f.write("[正常] 音频响应良好，未发现显著异常或失真。\n")
    f.write("\n图表输出：\n")
    f.write("- spectrogram.png（频谱图）\n")
    f.write("- zcr_compare.png（ZCR异常标记）\n")
    f.write("- rms.png（能量变化）\n")
    f.write("- frequency_response.png（频响曲线）\n")

print("分析完成，结果保存在 analysis_result 文件夹内。")
