#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化主频置零后的93段频谱
用于THD+N分析的第一步
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from freq_split_optimized import split_freq_steps_optimized

# 尝试导入matplotlib，如果失败则使用替代方案
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("matplotlib不可用，将保存数据而不生成图像")
    MATPLOTLIB_AVAILABLE = False

def zero_fundamental_frequency(freqs, power_spectrum, fundamental_freq, zero_bandwidth=10):
    """
    将主频及其附近频率置零
    
    Args:
        freqs: 频率数组
        power_spectrum: 功率谱
        fundamental_freq: 主频频率
        zero_bandwidth: 置零带宽 (Hz)
    
    Returns:
        zeroed_spectrum: 主频置零后的功率谱
        zero_mask: 置零区域的掩码
    """
    zeroed_spectrum = power_spectrum.copy()
    
    # 创建主频置零掩码
    zero_mask = np.abs(freqs - fundamental_freq) <= zero_bandwidth / 2
    
    # 将主频区域置零
    zeroed_spectrum[zero_mask] = 0
    
    return zeroed_spectrum, zero_mask

def analyze_segment_with_fundamental_zero(audio_data, sr, start_time, end_time, expected_freq, 
                                        seg_idx, output_dir):
    """
    分析单个频段，主频置零后的频谱
    """
    # 提取音频段
    start_sample = int(start_time * sr)
    end_sample = int(end_time * sr)
    segment = audio_data[start_sample:end_sample]
    
    # 去除首尾8%
    trim_samples = int(len(segment) * 0.08)
    if trim_samples > 0:
        segment = segment[trim_samples:-trim_samples]
    
    if len(segment) == 0:
        print(f"  警告: 频段 {seg_idx} 数据为空")
        return False
    
    # 计算FFT
    n_fft = 2048
    hop_length = n_fft // 4
    
    # 计算功率谱密度
    stft = librosa.stft(segment, n_fft=n_fft, hop_length=hop_length)
    power_spectrum = np.mean(np.abs(stft) ** 2, axis=1)
    
    # 频率轴
    freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
    
    # 转换为dB
    power_db = 10 * np.log10(power_spectrum + 1e-12)
    
    # 主频置零
    zeroed_spectrum, zero_mask = zero_fundamental_frequency(freqs, power_spectrum, expected_freq)
    zeroed_db = 10 * np.log10(zeroed_spectrum + 1e-12)
    
    # 创建可视化（如果matplotlib可用）
    if MATPLOTLIB_AVAILABLE:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

        # 上图：原始频谱
        ax1.semilogx(freqs[1:], power_db[1:], 'b-', linewidth=1.0, alpha=0.8, label='原始频谱')

        # 标记主频
        fundamental_idx = np.argmin(np.abs(freqs - expected_freq))
        ax1.plot(expected_freq, power_db[fundamental_idx], 'ro', markersize=8,
                 label=f'主频 {expected_freq:.1f}Hz')

        # 标记谐波
        for harmonic in range(2, 11):  # 2-10次谐波
            harmonic_freq = expected_freq * harmonic
            if harmonic_freq < sr/2:  # 在奈奎斯特频率内
                harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
                ax1.plot(harmonic_freq, power_db[harmonic_idx], 'go', markersize=6,
                         alpha=0.7, label=f'{harmonic}次谐波' if harmonic == 2 else "")

        ax1.set_xlim(50, sr/2)
        ax1.set_ylim(-80, 20)
        ax1.set_xlabel('频率 (Hz)', fontsize=12)
        ax1.set_ylabel('功率 (dB)', fontsize=12)
        ax1.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz - 原始频谱', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(fontsize=10)

        # 下图：主频置零后的频谱
        ax2.semilogx(freqs[1:], zeroed_db[1:], 'r-', linewidth=1.0, alpha=0.8, label='主频置零后频谱')

        # 显示置零区域
        zero_freqs = freqs[zero_mask]
        if len(zero_freqs) > 0:
            ax2.axvspan(np.min(zero_freqs), np.max(zero_freqs), alpha=0.3, color='gray',
                        label=f'主频置零区域 (±5Hz)')

        # 标记谐波（在置零后的频谱中）
        for harmonic in range(2, 11):
            harmonic_freq = expected_freq * harmonic
            if harmonic_freq < sr/2:
                harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
                ax2.plot(harmonic_freq, zeroed_db[harmonic_idx], 'go', markersize=6,
                         alpha=0.7, label=f'{harmonic}次谐波' if harmonic == 2 else "")

        ax2.set_xlim(50, sr/2)
        ax2.set_ylim(-80, 20)
        ax2.set_xlabel('频率 (Hz)', fontsize=12)
        ax2.set_ylabel('功率 (dB)', fontsize=12)
        ax2.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz - 主频置零后频谱 (用于THD+N分析)',
                      fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.legend(fontsize=10)

        plt.tight_layout()

        # 保存图像
        output_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_fundamental_zeroed.png')
        plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"  保存图像: {output_filename}")
    else:
        print(f"  数据已处理 (matplotlib不可用，跳过图像生成)")

    # 保存数据到文件
    data_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz_data.npz')
    np.savez(data_filename,
             freqs=freqs,
             original_spectrum=power_spectrum,
             zeroed_spectrum=zeroed_spectrum,
             zero_mask=zero_mask,
             expected_freq=expected_freq)
    print(f"  保存数据: {data_filename}")
    
    # 返回分析数据用于后续THD+N计算
    return {
        'segment_idx': seg_idx,
        'expected_freq': expected_freq,
        'freqs': freqs,
        'original_spectrum': power_spectrum,
        'zeroed_spectrum': zeroed_spectrum,
        'zero_mask': zero_mask,
        'start_time': start_time,
        'end_time': end_time
    }

def main():
    """
    主函数：分析指定音频文件的93段频谱，主频置零可视化
    """
    # 音频文件路径
    audio_path = "test20250722/鼓膜破裂（复测1.1).wav"
    
    print(f"🎯 主频置零频谱分析")
    print(f"📁 音频文件: {audio_path}")
    print("="*70)
    
    # 检查文件是否存在
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return
    
    # 创建输出目录
    audio_name = Path(audio_path).stem
    output_dir = f"{audio_name}_主频置零频谱分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        print(f"✅ 频段分割完成，共{len(step_boundaries)}段")
        
        # 加载音频
        print("🎵 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000
        
        print(f"✅ 音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")
        
        # 分析每个频段
        print(f"🚀 开始分析{len(step_boundaries)}个频段...")

        analysis_results = []
        successful_count = 0

        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = freq_table[i]  # 从freq_table获取对应的频率
            print(f"  分析频段 {i+1}: {expected_freq:.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")

            try:
                result = analyze_segment_with_fundamental_zero(
                    y, sr, start_time, end_time, expected_freq, i+1, output_dir
                )

                if result:
                    analysis_results.append(result)
                    successful_count += 1

            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        print(f"\n" + "="*70)
        print(f"✅ 主频置零频谱分析完成!")
        print(f"  成功分析: {successful_count}/{len(step_boundaries)} 个频段")
        print(f"  输出目录: {output_dir}")
        print(f"  下一步: 基于这些数据计算THD+N")
        
        # 保存分析结果摘要
        summary_file = os.path.join(output_dir, "主频置零分析摘要.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"主频置零频谱分析摘要\n")
            f.write(f"="*50 + "\n\n")
            f.write(f"音频文件: {audio_path}\n")
            f.write(f"总频段数: {len(step_boundaries)}\n")
            f.write(f"成功分析: {successful_count}\n")
            f.write(f"采样率: {sr} Hz\n")
            f.write(f"分析目的: THD+N计算的预处理步骤\n\n")
            
            f.write(f"频段详情:\n")
            f.write(f"{'序号':<4} {'频率(Hz)':<10} {'开始时间(s)':<12} {'结束时间(s)':<12}\n")
            f.write(f"-" * 50 + "\n")
            
            for result in analysis_results:
                f.write(f"{result['segment_idx']:<4} {result['expected_freq']:<10.1f} "
                       f"{result['start_time']:<12.3f} {result['end_time']:<12.3f}\n")
        
        print(f"📄 分析摘要已保存: {summary_file}")
        
    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
