{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["扬声器测试系统算法设计"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["'''导入库'''\n", "import numpy as np\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import IPython.display as ipd\n", "import math\n", "from scipy.signal import chirp, spectrogram, stft\n", "import librosa\n", "import librosa.display\n", "from scipy.signal import windows as W\n", "from matplotlib.pylab import mpl\n", "\n", "from pylab import mpl\n", "# 设置显示中文字体\n", "mpl.rcParams[\"font.sans-serif\"] = [\"SimHei\"]\n", "mpl.rcParams[\"axes.unicode_minus\"] = False"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["'''扫频生成函数'''\n", "\n", "GAIN = 1\n", "\n", "\n", "def get_duration(f, min_cycles=10, min_duration=10):\n", "    \"\"\"\n", "    计算给定频率的持续时间（毫秒）。\n", "\n", "    参数:\n", "    f (float): 频率(Hz)。\n", "    min_cycles (int): 每个频点扫描正弦周期的最小值。\n", "    min_duration (float): 每个频点扫描的最小时间（毫秒）。\n", "\n", "    返回:\n", "    float: 持续时间（毫秒）。\n", "    \"\"\"\n", "    \n", "    def _get_duration():\n", "        return 1 / f * math.ceil(min_duration / (1 / f * 1e3)) * 1e3\n", "    \n", "    # 计算持续时间\n", "    # unit_duration = int(1 * 1e3 / f)  # 单位周期持续时间（毫秒）\n", "    duration_of_min_cycles = 1 / f * min_cycles * 1e3  # 最小周期数对应的持续时间\n", "    # 如果持续时间不小于最小持续时间，则返回最小持续时间，否则，计算满足条件的时长\n", "    duration = duration_of_min_cycles if duration_of_min_cycles >= min_duration else _get_duration()\n", "    return duration\n", "\n", "\n", "def gen_sine_wave(f, t0, t1, A=1, fs=44100):\n", "    # 生成单频率正弦信号\n", "    assert t1 >= t0, \"t1 must be greater than or equal to t0\"\n", "    assert f < fs / 2, \"f must be less than half of the sampling rate\"\n", "    assert A >= 0, \"A must be non-negative\"\n", "    assert fs > 0, \"fs must be a positive number\"\n", "    assert f > 0, \"f must be a positive number\"\n", "    t = np.arange(t0 * 1e-3, t1 * 1e-3, 1/fs)\n", "    delta_t = 1 / fs * 1e3 - (t1 - t[-1] * 1e3)\n", "    delta_t = max(0, delta_t)\n", "    # if delta_t < 0:\n", "    #     print('wrong delta_t!')\n", "    y = A * np.sin(2 * np.pi * f * t)\n", "    return y, t, delta_t\n", "\n", "\n", "def gen_octave_freqpoints(octave, start_freq=100, stop_freq=20000):\n", "    \"\"\"\n", "    生成倍频程的频率点。\n", "\n", "    参数:\n", "    Octave (int): 倍频程参数(24、12、6、3、1)。\n", "    start_freq (float): 最小频率(Hz)。\n", "    stop_freq (float): 最大频率(Hz)。\n", "\n", "    返回:\n", "    list: 包含倍频程频率点的列表。\n", "    \"\"\"\n", "    # 计算1-20000Hz的全部倍频程频率数组\n", "    def calculate_octave_bands():\n", "        R_80 = [1., 1.03, 1.06, 1.09, 1.12, 1.15, 1.18, 1.22, 1.25, 1.28, 1.32, 1.36, 1.4, 1.45, 1.5, 1.55, 1.6, 1.65, 1.7, 1.75, 1.8, 1.85, 1.9, 1.95, 2., 2.06, 2.12, 2.18, 2.24, 2.3, 2.36, 2.43, 2.5, 2.58, 2.65, 2.72, 2.8, 2.9, 3., 3.07, 3.15, 3.25, 3.35, 3.45, 3.55, 3.65, 3.75, 3.87, 4., 4.12, 4.25, 4.37, 4.5, 4.62, 4.75, 4.87, 5., 5.15, 5.3, 5.45, 5.6, 5.8, 6., 6.15, 6.3, 6.5, 6.7, 6.9, 7.1, 7.3, 7.5, 7.75, 8., 8.25, 8.5, 8.75, 9., 9.25, 9.5, 9.75, 10.]\n", "        for i in range(81, 160):\n", "            R_80.append(round(R_80[i % 80] * 1e1, 2))\n", "        for i in range(160, 240):\n", "            R_80.append(round(R_80[i % 80] * 1e2, 2))\n", "        for i in range(240, 320):\n", "            R_80.append(round(R_80[i % 80] * 1e3, 2))\n", "        for i in range(320, 345):\n", "            R_80.append(round(R_80[i % 80] * 1e4, 2))\n", "        return R_80\n", "\n", "    # 根据Octave参数选择相应的倍频程频率数组\n", "    R_80 = calculate_octave_bands()\n", "    R_80_len = len(R_80)\n", "    if octave == 24:\n", "        R = R_80\n", "    elif octave == 12:\n", "        R = [R_80[i * 2] for i in range(round(R_80_len / 2) + 1)]\n", "    elif octave == 6:\n", "        R = [R_80[i * 4] for i in range(round(R_80_len / 4) + 1)]\n", "    elif octave == 3:\n", "        R = [R_80[i * 8] for i in range(round(R_80_len / 8) + 1)]\n", "    elif octave == 1:\n", "        R = [R_80[i * 24] for i in range(round(R_80_len / 24) + 1)]\n", "    else:\n", "        print(\"Please input the right Octave Parameter!\")\n", "        return []\n", "\n", "    # 筛选出在范围内的频率点\n", "    result = []\n", "    for freq in R:\n", "        if start_freq <= freq <= stop_freq:\n", "            result.append(freq)\n", "    return result\n", "\n", "\n", "def plot_wave(y, t, title, freq_ssample_dict, figsize=(16, 5), show_segtimes=False):\n", "    # 绘制正弦信号\n", "    plt.figure(figsize=figsize)\n", "    plt.plot(t, y)\n", "    if show_segtimes:\n", "        for f in freq_ssample_dict:\n", "            plt.vlines(x=freq_ssample_dict[f][0],ymin=y.min()*1.1, ymax=y.max()*1.1, color='r', linestyle='-')\n", "    plt.xlabel('时间 (秒)')\n", "    plt.ylabel('振幅')\n", "    plt.xlim([0, t[-1]])\n", "    plt.title(title)\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "\n", "def gen_freq_step(start_freq, stop_freq, min_cycles, min_duration, octave, fs, A=1, plot_freqs=True):\n", "    \"\"\"\n", "    生成扫频信号\n", "    \"\"\"\n", "    freq_ssample_dict = {}\n", "    if_reverse = False\n", "    if start_freq > stop_freq:\n", "        start_freq, stop_freq = stop_freq, start_freq\n", "        if_reverse = True\n", "\n", "    frequency_points = gen_octave_freqpoints(octave, start_freq, stop_freq)\n", "\n", "    if if_reverse:\n", "        frequency_points = frequency_points[::-1]\n", "        \n", "    print(f\"1/{octave}倍频程的频率点:\\n{frequency_points}\\n共有{len(frequency_points)}个频率点\")\n", "    if plot_freqs:\n", "        plt.plot(frequency_points, marker='.')\n", "        plt.grid(True)\n", "\n", "    t = 0\n", "    sine_wave = np.array([])\n", "\n", "    delta_t = 0\n", "    for f in frequency_points:\n", "        duration = get_duration(f, min_cycles, min_duration)\n", "        t0 = t\n", "        sine_wave_tmp, t_list_tmp, delta_t = gen_sine_wave(f, delta_t, duration, fs=fs, A=A)\n", "        t_list_tmp = t_list_tmp + t * 1e-3\n", "        sine_wave = np.concatenate((sine_wave, sine_wave_tmp))\n", "        freq_ssample_dict[f] = ((t + delta_t) * 1e-3, math.ceil((t + delta_t) * 1e-3 * fs), int((duration - delta_t) * 1e-3 * fs))\n", "        t += duration\n", "        print(f'{f}Hz, duration: {duration}ms, t0: {t0*1e-3}s, t: {t*1e-3}s')\n", "    t_list = np.arange(0, t * 1e-3, 1/fs)\n", "\n", "    print(f'ssample:{freq_ssample_dict}')\n", "    return sine_wave, t_list, freq_ssample_dict"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1/12倍频程的频率点:\n", "[100.0, 106.0, 112.0, 118.0, 125.0, 132.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 212.0, 224.0, 236.0, 250.0, 265.0, 280.0, 300.0, 315.0, 335.0, 355.0, 375.0, 400.0, 425.0, 450.0, 475.0, 500.0, 530.0, 560.0, 600.0, 630.0, 670.0, 710.0, 750.0, 800.0, 850.0, 900.0, 950.0, 1000.0, 1060.0, 1120.0, 1180.0, 1250.0, 1320.0, 1400.0, 1500.0, 1600.0, 1700.0, 1800.0, 1900.0, 2000.0, 2120.0, 2240.0, 2360.0, 2500.0, 2650.0, 2800.0, 3000.0, 3150.0, 3350.0, 3550.0, 3750.0, 4000.0, 4250.0, 4500.0, 4750.0, 5000.0, 5300.0, 5600.0, 6000.0, 6300.0, 6700.0, 7100.0, 7500.0, 8000.0, 8500.0, 9000.0, 9500.0, 10000.0, 10600.0, 11200.0, 11800.0, 12500.0, 13200.0, 14000.0, 15000.0, 16000.0, 17000.0, 18000.0, 19000.0, 20000.0]\n", "共有93个频率点\n", "100.0Hz, duration: 150.0ms, t0: 0.0s, t: 0.15s\n", "106.0Hz, duration: 150.94339622641508ms, t0: 0.15s, t: 0.3009433962264151s\n", "112.0Hz, duration: 151.78571428571428ms, t0: 0.3009433962264151s, t: 0.4527291105121294s\n", "118.0Hz, duration: 152.54237288135593ms, t0: 0.4527291105121294s, t: 0.6052714833934852s\n", "125.0Hz, duration: 152.0ms, t0: 0.6052714833934852s, t: 0.7572714833934853s\n", "132.0Hz, duration: 151.51515151515153ms, t0: 0.7572714833934853s, t: 0.9087866349086368s\n", "140.0Hz, duration: 150.0ms, t0: 0.9087866349086368s, t: 1.0587866349086366s\n", "150.0Hz, duration: 153.33333333333334ms, t0: 1.0587866349086366s, t: 1.2121199682419699s\n", "160.0Hz, duration: 150.00000000000003ms, t0: 1.2121199682419699s, t: 1.36211996824197s\n", "170.0Hz, duration: 152.94117647058826ms, t0: 1.36211996824197s, t: 1.5150611447125581s\n", "180.0Hz, duration: 150.0ms, t0: 1.5150611447125581s, t: 1.6650611447125583s\n", "190.0Hz, duration: 152.6315789473684ms, t0: 1.6650611447125583s, t: 1.8176927236599265s\n", "200.0Hz, duration: 150.0ms, t0: 1.8176927236599265s, t: 1.9676927236599266s\n", "212.0Hz, duration: 150.94339622641508ms, t0: 1.9676927236599266s, t: 2.1186361198863417s\n", "224.0Hz, duration: 151.78571428571428ms, t0: 2.1186361198863417s, t: 2.2704218341720557s\n", "236.0Hz, duration: 152.54237288135593ms, t0: 2.2704218341720557s, t: 2.4229642070534116s\n", "250.0Hz, duration: 152.0ms, t0: 2.4229642070534116s, t: 2.5749642070534113s\n", "265.0Hz, duration: 150.94339622641508ms, t0: 2.5749642070534113s, t: 2.725907603279827s\n", "280.0Hz, duration: 150.0ms, t0: 2.725907603279827s, t: 2.8759076032798268s\n", "300.0Hz, duration: 150.00000000000003ms, t0: 2.8759076032798268s, t: 3.0259076032798267s\n", "315.0Hz, duration: 152.3809523809524ms, t0: 3.0259076032798267s, t: 3.178288555660779s\n", "335.0Hz, duration: 152.23880597014923ms, t0: 3.178288555660779s, t: 3.3305273616309283s\n", "355.0Hz, duration: 152.11267605633805ms, t0: 3.3305273616309283s, t: 3.482640037687266s\n", "375.0Hz, duration: 152.0ms, t0: 3.482640037687266s, t: 3.6346400376872663s\n", "400.0Hz, duration: 150.0ms, t0: 3.6346400376872663s, t: 3.784640037687266s\n", "425.0Hz, duration: 150.58823529411762ms, t0: 3.784640037687266s, t: 3.9352282729813837s\n", "450.0Hz, duration: 151.11111111111111ms, t0: 3.9352282729813837s, t: 4.086339384092495s\n", "475.0Hz, duration: 151.57894736842104ms, t0: 4.086339384092495s, t: 4.237918331460916s\n", "500.0Hz, duration: 150.0ms, t0: 4.237918331460916s, t: 4.3879183314609165s\n", "530.0Hz, duration: 150.94339622641508ms, t0: 4.3879183314609165s, t: 4.5388617276873315s\n", "560.0Hz, duration: 150.0ms, t0: 4.5388617276873315s, t: 4.688861727687331s\n", "600.0Hz, duration: 150.00000000000003ms, t0: 4.688861727687331s, t: 4.838861727687331s\n", "630.0Hz, duration: 150.79365079365078ms, t0: 4.838861727687331s, t: 4.9896553784809825s\n", "670.0Hz, duration: 150.7462686567164ms, t0: 4.9896553784809825s, t: 5.140401647137698s\n", "710.0Hz, duration: 150.7042253521127ms, t0: 5.140401647137698s, t: 5.291105872489811s\n", "750.0Hz, duration: 150.66666666666666ms, t0: 5.291105872489811s, t: 5.441772539156478s\n", "800.0Hz, duration: 150.0ms, t0: 5.441772539156478s, t: 5.591772539156478s\n", "850.0Hz, duration: 150.58823529411762ms, t0: 5.591772539156478s, t: 5.742360774450597s\n", "900.0Hz, duration: 150.0ms, t0: 5.742360774450597s, t: 5.892360774450596s\n", "950.0Hz, duration: 150.52631578947367ms, t0: 5.892360774450596s, t: 6.04288709024007s\n", "1000.0Hz, duration: 150.0ms, t0: 6.04288709024007s, t: 6.1928870902400694s\n", "1060.0Hz, duration: 150.0ms, t0: 6.1928870902400694s, t: 6.34288709024007s\n", "1120.0Hz, duration: 150.0ms, t0: 6.34288709024007s, t: 6.492887090240069s\n", "1180.0Hz, duration: 150.0ms, t0: 6.492887090240069s, t: 6.64288709024007s\n", "1250.0Hz, duration: 150.4ms, t0: 6.64288709024007s, t: 6.793287090240069s\n", "1320.0Hz, duration: 150.0ms, t0: 6.793287090240069s, t: 6.943287090240069s\n", "1400.0Hz, duration: 150.0ms, t0: 6.943287090240069s, t: 7.093287090240069s\n", "1500.0Hz, duration: 150.0ms, t0: 7.093287090240069s, t: 7.243287090240069s\n", "1600.0Hz, duration: 150.0ms, t0: 7.243287090240069s, t: 7.39328709024007s\n", "1700.0Hz, duration: 150.58823529411762ms, t0: 7.39328709024007s, t: 7.543875325534187s\n", "1800.0Hz, duration: 150.0ms, t0: 7.543875325534187s, t: 7.693875325534187s\n", "1900.0Hz, duration: 150.0ms, t0: 7.693875325534187s, t: 7.843875325534187s\n", "2000.0Hz, duration: 150.0ms, t0: 7.843875325534187s, t: 7.993875325534187s\n", "2120.0Hz, duration: 150.0ms, t0: 7.993875325534187s, t: 8.143875325534188s\n", "2240.0Hz, duration: 150.0ms, t0: 8.143875325534188s, t: 8.293875325534188s\n", "2360.0Hz, duration: 150.0ms, t0: 8.293875325534188s, t: 8.443875325534187s\n", "2500.0Hz, duration: 150.0ms, t0: 8.443875325534187s, t: 8.593875325534187s\n", "2650.0Hz, duration: 150.188679245283ms, t0: 8.593875325534187s, t: 8.744064004779469s\n", "2800.0Hz, duration: 150.0ms, t0: 8.744064004779469s, t: 8.89406400477947s\n", "3000.0Hz, duration: 150.0ms, t0: 8.89406400477947s, t: 9.04406400477947s\n", "3150.0Hz, duration: 150.15873015873015ms, t0: 9.04406400477947s, t: 9.194222734938199s\n", "3350.0Hz, duration: 150.14925373134326ms, t0: 9.194222734938199s, t: 9.344371988669542s\n", "3550.0Hz, duration: 150.14084507042256ms, t0: 9.344371988669542s, t: 9.494512833739964s\n", "3750.0Hz, duration: 150.13333333333335ms, t0: 9.494512833739964s, t: 9.644646167073297s\n", "4000.0Hz, duration: 150.0ms, t0: 9.644646167073297s, t: 9.794646167073298s\n", "4250.0Hz, duration: 150.11764705882354ms, t0: 9.794646167073298s, t: 9.94476381413212s\n", "4500.0Hz, duration: 150.0ms, t0: 9.94476381413212s, t: 10.09476381413212s\n", "4750.0Hz, duration: 150.10526315789474ms, t0: 10.09476381413212s, t: 10.244869077290016s\n", "5000.0Hz, duration: 150.0ms, t0: 10.244869077290016s, t: 10.394869077290016s\n", "5300.0Hz, duration: 150.188679245283ms, t0: 10.394869077290016s, t: 10.545057756535298s\n", "5600.0Hz, duration: 150.0ms, t0: 10.545057756535298s, t: 10.695057756535299s\n", "6000.0Hz, duration: 150.0ms, t0: 10.695057756535299s, t: 10.845057756535297s\n", "6300.0Hz, duration: 150.0ms, t0: 10.845057756535297s, t: 10.995057756535298s\n", "6700.0Hz, duration: 150.0ms, t0: 10.995057756535298s, t: 11.145057756535298s\n", "7100.0Hz, duration: 150.0ms, t0: 11.145057756535298s, t: 11.295057756535298s\n", "7500.0Hz, duration: 150.0ms, t0: 11.295057756535298s, t: 11.445057756535299s\n", "8000.0Hz, duration: 150.0ms, t0: 11.445057756535299s, t: 11.595057756535297s\n", "8500.0Hz, duration: 150.0ms, t0: 11.595057756535297s, t: 11.745057756535298s\n", "9000.0Hz, duration: 150.0ms, t0: 11.745057756535298s, t: 11.895057756535298s\n", "9500.0Hz, duration: 150.0ms, t0: 11.895057756535298s, t: 12.045057756535298s\n", "10000.0Hz, duration: 150.0ms, t0: 12.045057756535298s, t: 12.195057756535299s\n", "10600.0Hz, duration: 150.0943396226415ms, t0: 12.195057756535299s, t: 12.345152096157939s\n", "11200.0Hz, duration: 150.0ms, t0: 12.345152096157939s, t: 12.49515209615794s\n", "11800.0Hz, duration: 150.00000000000003ms, t0: 12.49515209615794s, t: 12.64515209615794s\n", "12500.0Hz, duration: 150.00000000000003ms, t0: 12.64515209615794s, t: 12.79515209615794s\n", "13200.0Hz, duration: 150.0ms, t0: 12.79515209615794s, t: 12.945152096157939s\n", "14000.0Hz, duration: 150.00000000000003ms, t0: 12.945152096157939s, t: 13.095152096157939s\n", "15000.0Hz, duration: 150.0ms, t0: 13.095152096157939s, t: 13.24515209615794s\n", "16000.0Hz, duration: 150.0ms, t0: 13.24515209615794s, t: 13.39515209615794s\n", "17000.0Hz, duration: 150.0ms, t0: 13.39515209615794s, t: 13.54515209615794s\n", "18000.0Hz, duration: 150.0ms, t0: 13.54515209615794s, t: 13.695152096157939s\n", "19000.0Hz, duration: 150.0ms, t0: 13.695152096157939s, t: 13.845152096157939s\n", "20000.0Hz, duration: 150.0ms, t0: 13.845152096157939s, t: 13.99515209615794s\n", "ssample:{100.0: (0.0, 0, 7200), 106.0: (0.15001493710691827, 7201, 7244), 112.0: (0.3009434523809524, 14446, 7285), 118.0: (0.4527492937853107, 21732, 7321), 125.0: (0.6052916666666667, 29054, 7295), 132.0: (0.7572765151515152, 36350, 7272), 140.0: (0.9087916666666666, 43622, 7199), 150.0: (1.0587916666666664, 50822, 7359), 160.0: (1.2121249999999997, 58182, 7199), 170.0: (1.3621213235294112, 65382, 7341), 180.0: (1.5150624999999995, 72723, 7199), 190.0: (1.6650767543859646, 79924, 7325), 200.0: (1.8177083333333328, 87250, 7199), 212.0: (1.9677024371069178, 94450, 7244), 224.0: (2.1186517857142855, 101696, 7284), 236.0: (2.27043679378531, 108981, 7321), 250.0: (2.4229791666666656, 116303, 7295), 265.0: (2.574973270440251, 123599, 7244), 280.0: (2.725916666666666, 130844, 7199), 300.0: (2.8759166666666656, 138044, 7199), 315.0: (3.0259107142857133, 145244, 7314), 335.0: (3.1783028606965162, 152559, 7306), 355.0: (3.330533157276994, 159866, 7301), 375.0: (3.4826458333333323, 167167, 7295), 400.0: (3.634645833333332, 174463, 7199), 425.0: (3.784640931372548, 181663, 7228), 450.0: (3.935243055555554, 188892, 7252), 475.0: (4.0863585526315775, 196146, 7274), 500.0: (4.237937499999999, 203421, 7199), 530.0: (4.387931603773584, 210621, 7244), 560.0: (4.538874999999999, 217866, 7199), 600.0: (4.6888749999999995, 225066, 7199), 630.0: (4.838873015873015, 232266, 7237), 670.0: (4.98967039800995, 239505, 7235), 710.0: (5.140420774647886, 246741, 7232), 750.0: (5.291124999999999, 253974, 7231), 800.0: (5.441791666666666, 261206, 7199), 850.0: (5.591786764705882, 268406, 7227), 900.0: (5.742375, 275634, 7199), 950.0: (5.89236951754386, 282834, 7224), 1000.0: (6.042895833333334, 290059, 7199), 1060.0: (6.192895833333333, 297259, 7199), 1120.0: (6.342895833333333, 304459, 7199), 1180.0: (6.492895833333333, 311659, 7199), 1250.0: (6.642891666666666, 318859, 7218), 1320.0: (6.793291666666666, 326078, 7199), 1400.0: (6.943291666666666, 333278, 7199), 1500.0: (7.0932916666666666, 340478, 7199), 1600.0: (7.243291666666666, 347678, 7199), 1700.0: (7.3933075980392156, 354879, 7227), 1800.0: (7.543895833333333, 362107, 7199), 1900.0: (7.693895833333333, 369307, 7199), 2000.0: (7.843895833333333, 376507, 7199), 2120.0: (7.993895833333333, 383707, 7199), 2240.0: (8.143895833333334, 390907, 7199), 2360.0: (8.293895833333332, 398107, 7199), 2500.0: (8.443895833333332, 405307, 7199), 2650.0: (8.59389465408805, 412507, 7208), 2800.0: (8.744083333333332, 419716, 7199), 3000.0: (8.894083333333333, 426916, 7199), 3150.0: (9.044070436507935, 434116, 7207), 3350.0: (9.194225746268655, 441323, 7207), 3550.0: (9.344379988262908, 448531, 7206), 3750.0: (9.494533333333331, 455738, 7205), 4000.0: (9.644666666666664, 462944, 7199), 4250.0: (9.794653186274507, 470144, 7205), 4500.0: (9.944770833333331, 477349, 7199), 4750.0: (10.094769736842101, 484549, 7204), 5000.0: (10.244874999999997, 491754, 7199), 5300.0: (10.394873820754714, 498954, 7208), 5600.0: (10.545062499999997, 506163, 7199), 6000.0: (10.695062499999997, 513363, 7199), 6300.0: (10.845062499999997, 520563, 7199), 6700.0: (10.995062499999996, 527763, 7199), 7100.0: (11.145062499999996, 534963, 7199), 7500.0: (11.295062499999997, 542163, 7199), 8000.0: (11.445062499999997, 549363, 7199), 8500.0: (11.595062499999997, 556563, 7199), 9000.0: (11.745062499999996, 563763, 7199), 9500.0: (11.895062499999996, 570963, 7199), 10000.0: (12.045062499999997, 578163, 7199), 10600.0: (12.195072327044022, 585364, 7203), 11200.0: (12.345166666666662, 592568, 7199), 11800.0: (12.495166666666663, 599768, 7199), 12500.0: (12.645166666666663, 606968, 7199), 13200.0: (12.795166666666663, 614168, 7199), 14000.0: (12.945166666666662, 621368, 7199), 15000.0: (13.095166666666662, 628568, 7199), 16000.0: (13.245166666666663, 635768, 7199), 17000.0: (13.395166666666663, 642968, 7199), 18000.0: (13.545166666666663, 650168, 7199), 19000.0: (13.695166666666662, 657368, 7199), 20000.0: (13.845166666666662, 664568, 7199)}\n", "(671768,) (671768,)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["'''扫频生成'''\n", "octave_dict = {3:'R10', 6:'R20', 12:'R40', 24:'R80'}\n", "octave = 12            # R20(1/6倍频程)\n", "start_freq = 100\n", "stop_freq = 20000\n", "A = 1\n", "fs = 48000\n", "min_cycles = 10\n", "min_duration = 150\n", "\n", "sine_wave, t_list, freq_ssample_dict = gen_freq_step(start_freq, stop_freq, min_cycles, min_duration, octave, fs, A=A)\n", "print(sine_wave.shape, t_list.shape)\n", "plot_wave(sine_wave, t_list, '扫频信号', freq_ssample_dict)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''音频播放'''\n", "ipd.Audio(sine_wave,rate=fs)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["'''音频保存'''\n", "import soundfile as sf\n", "write_wav_path = f'sf{start_freq}-ef{stop_freq}-oct{octave_dict[octave]}-mc{min_cycles}-md{min_duration}-fs{fs}.wav'\n", "sf.write(write_wav_path, sine_wave, fs, 'PCM_16')\n"]}], "metadata": {"kernelspec": {"display_name": "mir1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 2}