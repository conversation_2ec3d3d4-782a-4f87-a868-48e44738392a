#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行化谐波异常检测器
使用多进程/多线程提升93段分析效率
"""

import os
import sys
import numpy as np
import librosa
from scipy.signal import savgol_filter
import multiprocessing as mp
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import time

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.dirname(current_dir))

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 导入原始算法函数
from harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    estimate_dynamic_noise_for_segment,
    detect_harmonics_for_segment
)

def analyze_single_segment(args):
    """
    分析单个频段的谐波 - 用于并行处理
    
    Args:
        args: tuple包含(seg_idx, segment_data, display_freqs, display_power, expected_freq)
    
    Returns:
        dict: 段分析结果
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr = args
    
    try:
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = audio_data[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            return {
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'harmonic_count': 0
            }
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 动态噪声分析
        noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
        
        # 谐波检测
        if noise_analysis:
            harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
            harmonic_count = len(harmonic_analysis)
        else:
            harmonic_count = 0
        
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'harmonic_count': harmonic_count
        }
        
    except Exception as e:
        print(f"段{seg_idx}分析失败: {e}")
        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'harmonic_count': 0
        }

class ParallelHarmonicDetector:
    """并行化谐波异常检测器"""
    
    def __init__(self, anomaly_threshold=2, parallel_method='process', num_workers=None):
        """
        初始化并行检测器
        
        Args:
            anomaly_threshold (int): 异常段个数阈值
            parallel_method (str): 并行方法 'process'(多进程) 或 'thread'(多线程)
            num_workers (int): 工作进程/线程数，None表示自动选择
        """
        self.anomaly_threshold = anomaly_threshold
        self.parallel_method = parallel_method
        self.num_workers = num_workers or min(mp.cpu_count(), 93)  # 最多93个worker
        self.segment_thresholds = self._load_pos_thresholds()
        
    def _load_pos_thresholds(self):
        """加载pos文件夹分析得到的阈值"""
        thresholds = [
            15, 12, 11, 11,  9, 11, 11, 10, 10,  8,  # 段0-9
            10,  9, 14, 13, 17, 12, 10,  8,  8,  6,  # 段10-19
             7,  7,  6,  5,  6,  7,  7,  5,  5,  6,  # 段20-29
             5,  4,  4,  3,  7,  9, 13,  7,  5,  5,  # 段30-39
             6,  8,  7,  4,  5,  5,  5,  4,  2,  2,  # 段40-49
             2,  2,  2,  2,  2,  2,  1,  2,  1,  2,  # 段50-59
             2,  2,  1,  2,  2,  2,  2,  2,  2,  2,  # 段60-69
             2,  2,  2,  1,  1,  1,  1,  1,  1,  1,  # 段70-79
             0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  # 段80-89
             0,  0,  0                               # 段90-92
        ]
        return thresholds
    
    def detect_parallel(self, audio_path):
        """
        并行检测音频文件
        
        Args:
            audio_path (str): 音频文件路径
            
        Returns:
            int: 1=正常, 0=异常
        """
        start_time = time.time()
        
        try:
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            # 加载音频
            y, sr = librosa.load(audio_path, sr=48000)
            if sr != 48000:
                y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            
            # 准备并行任务参数
            tasks = []
            for seg_idx in range(len(step_boundaries)):
                start_time_seg, end_time_seg = step_boundaries[seg_idx]
                expected_freq = freq_table[seg_idx]
                
                tasks.append((
                    seg_idx,
                    start_time_seg,
                    end_time_seg,
                    expected_freq,
                    y,  # 共享音频数据
                    sr
                ))
            
            # 并行处理
            if self.parallel_method == 'process':
                # 多进程
                with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
                    results = list(executor.map(analyze_single_segment, tasks))
            else:
                # 多线程
                with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                    results = list(executor.map(analyze_single_segment, tasks))
            
            # 按段索引排序
            results.sort(key=lambda x: x['seg_idx'])
            
            # 计算异常段个数
            anomaly_count = 0
            for i, result in enumerate(results):
                if i < len(self.segment_thresholds):
                    threshold = self.segment_thresholds[i]
                    harmonic_count = result['harmonic_count']
                    if harmonic_count > threshold:
                        anomaly_count += 1
            
            # 判断结果
            is_normal = anomaly_count < self.anomaly_threshold
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            return {
                'result': 1 if is_normal else 0,
                'anomaly_count': anomaly_count,
                'processing_time': processing_time,
                'parallel_method': self.parallel_method,
                'num_workers': self.num_workers
            }
            
        except Exception as e:
            raise Exception(f"并行检测失败: {str(e)}")
    
    def detect(self, audio_path):
        """简化接口，只返回检测结果"""
        result = self.detect_parallel(audio_path)
        return result['result']

def benchmark_parallel_methods():
    """性能基准测试"""
    print("🚀 并行方法性能基准测试")
    print("="*50)
    
    test_file = "../test20250717/neg/主板隔音eva取消.wav"
    if not os.path.exists(test_file):
        print("测试文件不存在")
        return
    
    # 测试配置
    configs = [
        {'method': 'process', 'workers': 1, 'name': '单进程'},
        {'method': 'process', 'workers': 4, 'name': '4进程'},
        {'method': 'process', 'workers': 8, 'name': '8进程'},
        {'method': 'thread', 'workers': 4, 'name': '4线程'},
        {'method': 'thread', 'workers': 8, 'name': '8线程'},
    ]
    
    results = []
    
    for config in configs:
        try:
            detector = ParallelHarmonicDetector(
                parallel_method=config['method'],
                num_workers=config['workers']
            )
            
            result = detector.detect_parallel(test_file)
            
            results.append({
                'name': config['name'],
                'time': result['processing_time'],
                'result': result['result'],
                'anomaly_count': result['anomaly_count']
            })
            
            print(f"{config['name']:8s}: {result['processing_time']:.2f}秒 "
                  f"(结果: {result['result']}, 异常段: {result['anomaly_count']})")
            
        except Exception as e:
            print(f"{config['name']:8s}: 失败 - {e}")
    
    # 计算加速比
    if results:
        baseline_time = results[0]['time']
        print(f"\n加速比分析 (基准: {baseline_time:.2f}秒):")
        for result in results:
            speedup = baseline_time / result['time']
            print(f"{result['name']:8s}: {speedup:.2f}x")

if __name__ == "__main__":
    benchmark_parallel_methods()
