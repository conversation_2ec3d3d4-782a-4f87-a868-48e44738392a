# 差值曲线优化显示说明

## 🎯 优化内容

我已经优化了差值曲线的显示方式，将其作为单独的子图绘制，提供更清晰的波动程度分析。

## 📊 新的布局设计

### 双子图布局
当启用 `--show-diff` 参数时，图像将分为两个子图：

1. **主频谱图** (上方，占3/4高度)
   - 原始频谱 (黑色线)
   - 掩码频谱 (绿色线，如果启用)
   - 动态噪声阈值 (红色线)
   - 全局底噪线 (橙色虚线)
   - 主频标记 (蓝色圆点)

2. **波动程度图** (下方，占1/4高度)
   - 差值曲线 (紫色线)
   - 显示最大差值和平均差值
   - 自适应纵坐标范围

### 单子图布局
当不启用 `--show-diff` 参数时，保持原有的单子图布局。

## 🎨 可视化改进

### 差值子图特点
- **独立坐标轴**: 差值曲线有自己的Y轴，不受主频谱影响
- **自适应范围**: Y轴范围根据实际差值自动调整
- **统计信息**: 标题显示最大差值和平均差值
- **清晰标签**: Y轴标签明确标注为 "|频谱-阈值| (dB)"

### 布局优化
- **高度比例**: 主图:差值图 = 3:1，确保主频谱有足够空间
- **图像尺寸**: 差值图时为16×12英寸，标准图为16×10英寸
- **间距调整**: 使用 `tight_layout()` 自动优化子图间距

## 🔧 使用方法

### 基本差值分析
```bash
python universal_spectrum_analyzer.py my_audio.wav --show-diff
```

### 梅尔刻度差值分析
```bash
python universal_spectrum_analyzer.py my_audio.wav --mel-scale --show-diff
```

### 组合功能
```bash
python universal_spectrum_analyzer.py my_audio.wav --show-diff --show-masked --freq-range 200 4000
```

## 📈 分析优势

### 1. 清晰度提升
- **独立显示**: 差值曲线不与其他曲线重叠
- **专用坐标**: 差值有自己的Y轴刻度
- **视觉分离**: 主频谱和波动分析分开显示

### 2. 信息丰富
- **统计数据**: 显示最大差值和平均差值
- **趋势分析**: 更容易观察波动的变化趋势
- **局部细节**: 可以清楚看到每个频率点的波动程度

### 3. 分析便利
- **对比分析**: 上下对照主频谱和波动程度
- **异常识别**: 快速定位高波动区域
- **质量评估**: 直观评估信号质量

## 📊 差值曲线解读

### 高差值区域 (>20dB)
- **主频位置**: 基频信号强度高
- **谐波位置**: 谐波信号明显
- **异常峰值**: 可能的干扰或失真

### 中等差值区域 (5-20dB)
- **信号边缘**: 信号成分的边界区域
- **弱谐波**: 较弱的谐波成分
- **过渡区域**: 信号到噪声的过渡

### 低差值区域 (<5dB)
- **纯噪声**: 主要是噪声成分
- **信号间隙**: 主频和谐波之间的区域
- **高频衰减**: 信号能量衰减的区域

## 📁 文件输出

### 文件命名
- **对数刻度差值**: `segment_XX_XXXXHz_log_diff.png`
- **梅尔刻度差值**: `segment_XX_XXXXHz_mel_diff.png`
- **掩码+差值**: `segment_XX_XXXXHz_log_masked_diff.png`

### 图像规格
- **尺寸**: 16×12英寸 (差值图), 16×10英寸 (标准图)
- **分辨率**: 200 DPI
- **格式**: PNG，白色背景

## 🎯 应用场景

### 1. 信号质量分析
- **信噪比评估**: 通过差值大小评估信号质量
- **频段对比**: 比较不同频段的信号强度
- **质量监控**: 监控录音质量的一致性

### 2. 异常检测
- **干扰识别**: 发现异常的高差值区域
- **设备问题**: 检测录音设备的异常响应
- **环境噪声**: 识别环境干扰的影响

### 3. 算法优化
- **阈值验证**: 验证动态噪声阈值的准确性
- **参数调优**: 根据差值分布优化算法参数
- **性能评估**: 评估信号检测算法的效果

## 💡 使用建议

### 分析流程
1. **整体观察**: 先看主频谱图了解信号分布
2. **波动分析**: 再看差值图了解波动程度
3. **异常定位**: 找出高差值的异常区域
4. **质量评估**: 根据差值统计评估整体质量

### 参数选择
- **频率范围**: 根据关注的频段选择合适范围
- **刻度选择**: 工程分析用对数，感知分析用梅尔
- **组合功能**: 可结合掩码功能进行深入分析

这种优化的双子图布局为频谱分析提供了更专业、更清晰的可视化效果！
