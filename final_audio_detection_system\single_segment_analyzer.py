#!/usr/bin/env python3
"""
单频段深度分析器
Single Segment Deep Analyzer
针对特定频段进行深度分析和可视化
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from scipy.signal import stft
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SingleSegmentAnalyzer:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,
            'noverlap': 768,
            'window': 'hann',
            'nfft': 1024
        }
        
        print(f"单频段深度分析器初始化完成")
        print(f"时间分辨率: {self.stft_params['nperseg']/sample_rate*1000:.1f}ms")
        print(f"频率分辨率: {sample_rate/self.stft_params['nfft']:.1f}Hz")
    
    def analyze_specific_segment(self, audio_path, start_time, end_time, expected_freq=None):
        """分析特定时间段的频段"""
        print(f"\n分析文件: {os.path.basename(audio_path)}")
        print(f"时间段: {start_time:.3f}s - {end_time:.3f}s")
        if expected_freq:
            print(f"期望频率: {expected_freq}Hz")
        print("="*60)
        
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                print("音频文件为空")
                return None
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            print(f"音频长度: {len(y)/sr:.2f}秒")
            
            # STFT分析
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 频率范围筛选 (100-20000Hz)
            freq_mask = (frequencies >= 100) & (frequencies <= 20000)
            frequencies = frequencies[freq_mask]
            Zxx = Zxx[freq_mask, :]
            
            # 提取指定时间段
            segment_mask = (times >= start_time) & (times <= end_time)
            segment_times = times[segment_mask]
            segment_Zxx = Zxx[:, segment_mask]
            
            if segment_Zxx.shape[1] == 0:
                print(f"指定时间段无数据")
                return None
            
            print(f"提取到的时间片数: {segment_Zxx.shape[1]}")
            print(f"频率点数: {len(frequencies)}")
            
            # 进行多种分析
            analysis_results = {
                'frequencies': frequencies,
                'times': segment_times,
                'spectrum': segment_Zxx,
                'original_analysis': self._analyze_original_spectrum(segment_Zxx, frequencies, segment_times),
                'suppressed_analysis': self._analyze_suppressed_spectrum(segment_Zxx, frequencies, segment_times),
                'threshold_analysis': self._analyze_with_different_thresholds(segment_Zxx, frequencies, segment_times),
                'time_evolution': self._analyze_time_evolution(segment_Zxx, frequencies, segment_times)
            }
            
            # 可视化结果
            self._visualize_analysis(analysis_results, audio_path, start_time, end_time, expected_freq)
            
            return analysis_results
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _analyze_original_spectrum(self, spectrum, frequencies, times):
        """分析原始频谱"""
        print("\n1. 原始频谱分析:")
        
        # 计算功率谱
        power_spectrum = np.abs(spectrum) ** 2
        
        # 转换为dB
        power_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 统计信息
        max_power = np.max(power_db)
        min_power = np.min(power_db)
        mean_power = np.mean(power_db)
        std_power = np.std(power_db)
        
        print(f"  功率范围: {min_power:.1f} - {max_power:.1f} dB")
        print(f"  平均功率: {mean_power:.1f} ± {std_power:.1f} dB")
        
        # 找到主频
        max_power_idx = np.unravel_index(np.argmax(power_spectrum), power_spectrum.shape)
        main_freq = frequencies[max_power_idx[0]]
        main_time = times[max_power_idx[1]]
        
        print(f"  主频: {main_freq:.1f}Hz (时刻: {main_time:.3f}s, 功率: {max_power:.1f}dB)")
        
        # 分析每个时间片的频率分布
        time_analysis = []
        for t_idx in range(power_spectrum.shape[1]):
            time_power = power_db[:, t_idx]
            max_idx = np.argmax(time_power)
            dominant_freq = frequencies[max_idx]
            max_power_t = time_power[max_idx]
            
            time_analysis.append({
                'time': times[t_idx],
                'dominant_freq': dominant_freq,
                'max_power': max_power_t,
                'power_std': np.std(time_power)
            })
        
        return {
            'power_db': power_db,
            'max_power': max_power,
            'min_power': min_power,
            'mean_power': mean_power,
            'main_freq': main_freq,
            'main_time': main_time,
            'time_analysis': time_analysis
        }
    
    def _analyze_suppressed_spectrum(self, spectrum, frequencies, times):
        """分析主频抑制后的频谱"""
        print("\n2. 主频抑制分析:")
        
        suppressed_results = []
        
        for t_idx in range(spectrum.shape[1]):
            time_spectrum = spectrum[:, t_idx]
            
            # 原始功率
            original_power = np.abs(time_spectrum) ** 2
            original_db = 10 * np.log10(original_power + 1e-12)
            
            # 主频抑制
            suppressed_spectrum = self._suppress_main_frequencies(time_spectrum)
            suppressed_power = np.abs(suppressed_spectrum) ** 2
            suppressed_db = 10 * np.log10(suppressed_power + 1e-12)
            
            # 计算残留能量
            non_zero_mask = np.abs(suppressed_spectrum) > 1e-12
            residual_points = np.sum(non_zero_mask)
            
            if residual_points > 0:
                max_residual = np.max(suppressed_db[non_zero_mask])
                mean_residual = np.mean(suppressed_db[non_zero_mask])
            else:
                max_residual = -np.inf
                mean_residual = -np.inf
            
            suppressed_results.append({
                'time': times[t_idx],
                'original_max': np.max(original_db),
                'suppressed_max': max_residual,
                'residual_points': residual_points,
                'mean_residual': mean_residual,
                'suppressed_spectrum': suppressed_db,
                'non_zero_mask': non_zero_mask
            })
        
        # 统计残留能量
        all_residual_max = [r['suppressed_max'] for r in suppressed_results if r['suppressed_max'] > -np.inf]
        total_residual_points = sum(r['residual_points'] for r in suppressed_results)
        
        print(f"  总残留异常点: {total_residual_points}")
        if all_residual_max:
            print(f"  最大残留能量: {np.max(all_residual_max):.1f}dB")
            print(f"  平均残留能量: {np.mean(all_residual_max):.1f}dB")
        else:
            print(f"  无残留能量")
        
        return {
            'suppressed_results': suppressed_results,
            'total_residual_points': total_residual_points,
            'max_residual_energy': np.max(all_residual_max) if all_residual_max else -np.inf
        }
    
    def _suppress_main_frequencies(self, spectrum):
        """抑制主频和高能量区域"""
        power_spectrum = np.abs(spectrum) ** 2
        
        # 找到高能量区域（90%分位数以上）
        high_energy_threshold = np.percentile(power_spectrum, 90)
        
        # 创建抑制后的频谱
        suppressed_spectrum = spectrum.copy()
        
        # 完全去除高能量区域
        high_energy_mask = power_spectrum > high_energy_threshold
        suppressed_spectrum[high_energy_mask] = 0
        
        return suppressed_spectrum
    
    def _analyze_with_different_thresholds(self, spectrum, frequencies, times):
        """使用不同阈值分析"""
        print("\n3. 不同阈值分析:")
        
        # 测试不同的阈值
        test_thresholds = [-60, -70, -80, -90, -100]
        threshold_results = {}
        
        for threshold in test_thresholds:
            anomaly_count = 0
            continuous_groups = 0
            
            for t_idx in range(spectrum.shape[1]):
                time_spectrum = spectrum[:, t_idx]
                
                # 主频抑制
                suppressed_spectrum = self._suppress_main_frequencies(time_spectrum)
                suppressed_db = 20 * np.log10(np.abs(suppressed_spectrum) + 1e-12)
                
                # 阈值检测
                non_zero_mask = np.abs(suppressed_spectrum) > 1e-12
                above_threshold = (suppressed_db > threshold) & non_zero_mask
                
                # 查找连续组
                groups = self._find_continuous_groups(above_threshold)
                valid_groups = [g for g in groups if len(g) >= 3]
                
                anomaly_count += sum(len(g) for g in valid_groups)
                continuous_groups += len(valid_groups)
            
            threshold_results[threshold] = {
                'anomaly_count': anomaly_count,
                'continuous_groups': continuous_groups
            }
            
            print(f"  阈值 {threshold}dB: {anomaly_count}个异常点, {continuous_groups}个连续组")
        
        return threshold_results
    
    def _find_continuous_groups(self, above_threshold_mask):
        """查找连续的频点组"""
        groups = []
        current_group = []
        
        for i, is_above in enumerate(above_threshold_mask):
            if is_above:
                current_group.append(i)
            else:
                if len(current_group) > 0:
                    groups.append(current_group)
                    current_group = []
        
        if len(current_group) > 0:
            groups.append(current_group)
        
        return groups
    
    def _analyze_time_evolution(self, spectrum, frequencies, times):
        """分析时间演化"""
        print("\n4. 时间演化分析:")
        
        # 计算每个时间片的特征
        time_features = []
        
        for t_idx in range(spectrum.shape[1]):
            time_spectrum = spectrum[:, t_idx]
            power_spectrum = np.abs(time_spectrum) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 主频抑制
            suppressed_spectrum = self._suppress_main_frequencies(time_spectrum)
            suppressed_db = 20 * np.log10(np.abs(suppressed_spectrum) + 1e-12)
            non_zero_mask = np.abs(suppressed_spectrum) > 1e-12
            
            features = {
                'time': times[t_idx],
                'max_power': np.max(power_db),
                'mean_power': np.mean(power_db),
                'power_std': np.std(power_db),
                'residual_points': np.sum(non_zero_mask),
                'max_residual': np.max(suppressed_db[non_zero_mask]) if np.any(non_zero_mask) else -np.inf
            }
            
            time_features.append(features)
        
        # 检测异常时刻
        residual_counts = [f['residual_points'] for f in time_features]
        mean_residual = np.mean(residual_counts)
        std_residual = np.std(residual_counts)
        threshold_residual = mean_residual + 2 * std_residual
        
        anomalous_times = [f['time'] for f in time_features if f['residual_points'] > threshold_residual]
        
        print(f"  平均残留点数: {mean_residual:.1f} ± {std_residual:.1f}")
        print(f"  异常时刻数: {len(anomalous_times)}")
        if anomalous_times:
            print(f"  异常时刻: {[f'{t:.3f}s' for t in anomalous_times[:5]]}")
        
        return {
            'time_features': time_features,
            'anomalous_times': anomalous_times,
            'mean_residual': mean_residual,
            'std_residual': std_residual
        }
    
    def _visualize_analysis(self, analysis_results, audio_path, start_time, end_time, expected_freq):
        """可视化分析结果"""
        print("\n5. 生成可视化图表...")
        
        frequencies = analysis_results['frequencies']
        times = analysis_results['times']
        spectrum = analysis_results['spectrum']
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'单频段深度分析: {os.path.basename(audio_path)}\n时间段: {start_time:.3f}s - {end_time:.3f}s', fontsize=14)
        
        # 1. 原始频谱图
        power_db = analysis_results['original_analysis']['power_db']
        im1 = axes[0, 0].imshow(power_db, aspect='auto', origin='lower', 
                               extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                               cmap='viridis')
        axes[0, 0].set_title('原始功率谱')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('频率 (Hz)')
        if expected_freq:
            axes[0, 0].axhline(y=expected_freq, color='red', linestyle='--', alpha=0.7, label=f'期望频率: {expected_freq}Hz')
            axes[0, 0].legend()
        plt.colorbar(im1, ax=axes[0, 0], label='功率 (dB)')
        
        # 2. 主频抑制后的频谱
        suppressed_results = analysis_results['suppressed_analysis']['suppressed_results']
        suppressed_matrix = np.array([r['suppressed_spectrum'] for r in suppressed_results]).T
        im2 = axes[0, 1].imshow(suppressed_matrix, aspect='auto', origin='lower',
                               extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                               cmap='viridis', vmin=-100, vmax=-40)
        axes[0, 1].set_title('主频抑制后频谱')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('频率 (Hz)')
        plt.colorbar(im2, ax=axes[0, 1], label='功率 (dB)')
        
        # 3. 残留异常点分布
        residual_matrix = np.array([r['non_zero_mask'].astype(int) for r in suppressed_results]).T
        im3 = axes[0, 2].imshow(residual_matrix, aspect='auto', origin='lower',
                               extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                               cmap='Reds')
        axes[0, 2].set_title('残留异常点分布')
        axes[0, 2].set_xlabel('时间 (s)')
        axes[0, 2].set_ylabel('频率 (Hz)')
        plt.colorbar(im3, ax=axes[0, 2], label='异常点')
        
        # 4. 时间演化 - 残留点数
        time_features = analysis_results['time_evolution']['time_features']
        plot_times = [f['time'] for f in time_features]
        residual_counts = [f['residual_points'] for f in time_features]
        
        axes[1, 0].plot(plot_times, residual_counts, 'b-', marker='o', markersize=3)
        axes[1, 0].set_title('残留异常点数时间演化')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('残留异常点数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 标记异常时刻
        anomalous_times = analysis_results['time_evolution']['anomalous_times']
        for t in anomalous_times:
            axes[1, 0].axvline(x=t, color='red', alpha=0.5, linestyle='--')
        
        # 5. 不同阈值效果
        threshold_results = analysis_results['threshold_analysis']
        thresholds = list(threshold_results.keys())
        anomaly_counts = [threshold_results[t]['anomaly_count'] for t in thresholds]
        
        axes[1, 1].plot(thresholds, anomaly_counts, 'g-', marker='s', markersize=6)
        axes[1, 1].set_title('不同阈值的异常检测效果')
        axes[1, 1].set_xlabel('阈值 (dB)')
        axes[1, 1].set_ylabel('检测到的异常点数')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 功率分布直方图
        all_power = power_db.flatten()
        all_suppressed = suppressed_matrix.flatten()
        all_suppressed = all_suppressed[all_suppressed > -np.inf]
        
        axes[1, 2].hist(all_power, bins=50, alpha=0.7, label='原始功率', density=True)
        if len(all_suppressed) > 0:
            axes[1, 2].hist(all_suppressed, bins=50, alpha=0.7, label='抑制后功率', density=True)
        axes[1, 2].set_title('功率分布直方图')
        axes[1, 2].set_xlabel('功率 (dB)')
        axes[1, 2].set_ylabel('密度')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f'single_segment_analysis_{start_time:.3f}s_{end_time:.3f}s.png'
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        print(f"  可视化结果已保存: {output_filename}")
        
        plt.show()

def main():
    """主函数"""
    # 初始化分析器
    analyzer = SingleSegmentAnalyzer()
    
    # 分析指定的频段
    audio_path = "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav"
    start_time = 11.07
    end_time = 11.225
    expected_freq = 1500  # 根据扫频规律估计的期望频率
    
    # 执行分析
    results = analyzer.analyze_specific_segment(audio_path, start_time, end_time, expected_freq)
    
    if results:
        print(f"\n分析完成！")
        print(f"建议基于此分析结果优化检测算法参数。")
    
    return analyzer, results

if __name__ == "__main__":
    analyzer, results = main()
