#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频谱区域可视化总结
展示主频、谐波、噪声区域的可视化特点
"""

import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def spectrum_visualization_summary():
    """频谱区域可视化总结"""
    print("🎨 频谱区域可视化总结")
    print("="*70)
    print("已生成的可视化图片:")
    print("1. spectrum_regions_1kHz信号.png - 1000Hz信号频谱分析")
    print("2. spectrum_regions_500Hz信号.png - 500Hz信号频谱分析") 
    print("3. spectrum_regions_5kHz信号.png - 5000Hz信号频谱分析")
    print("4. spectrum_regions_15kHz信号.png - 15000Hz信号频谱分析")
    print("="*70)
    
    # 分析结果总结
    analysis_summary = {
        '1kHz信号': {
            'fundamental': {'freq': '1000.00Hz', 'power': '87.61dB', 'bandwidth': '±5.0Hz'},
            'harmonics': {'count': 18, 'highest': '19次谐波', 'power_range': '79.6dB - 53.7dB'},
            'noise_regions': {'count': 20, 'total_bandwidth': '23123.0Hz'},
            'key_features': [
                '主频区域用红色标识，清晰可见',
                '18个谐波用不同颜色标识',
                '噪声区域用蓝色半透明显示',
                '频率范围: 0-5000Hz'
            ]
        },
        '500Hz信号': {
            'fundamental': {'freq': '500.00Hz', 'power': '87.60dB', 'bandwidth': '±3.0Hz'},
            'harmonics': {'count': 18, 'highest': '18次谐波', 'power_range': '79.7dB - 53.4dB'},
            'noise_regions': {'count': 20, 'total_bandwidth': '23409.0Hz'},
            'key_features': [
                '低频主频，带宽更窄(±3Hz)',
                '更多谐波可见(18个)',
                '谐波间隔更密集',
                '频率范围: 0-5000Hz'
            ]
        },
        '5kHz信号': {
            'fundamental': {'freq': '5000.00Hz', 'power': '87.61dB', 'bandwidth': '±12.0Hz'},
            'harmonics': {'count': 3, 'highest': '4次谐波', 'power_range': '79.6dB - 67.6dB'},
            'noise_regions': {'count': 5, 'total_bandwidth': '23807.0Hz'},
            'key_features': [
                '高频主频，带宽更宽(±12Hz)',
                '只有3个谐波(受奈奎斯特限制)',
                '谐波功率仍然很强',
                '频率范围: 0-20000Hz'
            ]
        },
        '15kHz信号': {
            'fundamental': {'freq': '15000.00Hz', 'power': '87.61dB', 'bandwidth': '±30.0Hz'},
            'harmonics': {'count': 0, 'highest': '无', 'power_range': '无'},
            'noise_regions': {'count': 2, 'total_bandwidth': '23937.0Hz'},
            'key_features': [
                '超高频主频，带宽很宽(±30Hz)',
                '无谐波(2次谐波=30kHz > 24kHz)',
                '大部分频谱都是噪声区域',
                '频率范围: 0-24000Hz'
            ]
        }
    }
    
    # 显示分析总结
    for signal_name, analysis in analysis_summary.items():
        print(f"\n📊 {signal_name} 分析总结:")
        print("-" * 40)
        
        fund = analysis['fundamental']
        print(f"   🔴 主频区域:")
        print(f"     频率: {fund['freq']}, 功率: {fund['power']}, 带宽: {fund['bandwidth']}")
        
        harm = analysis['harmonics']
        if harm['count'] > 0:
            print(f"   🟠 谐波区域:")
            print(f"     数量: {harm['count']}个, 最高: {harm['highest']}, 功率范围: {harm['power_range']}")
        else:
            print(f"   ❌ 无谐波 (受频率限制)")
        
        noise = analysis['noise_regions']
        print(f"   🔵 噪声区域:")
        print(f"     区域数: {noise['count']}个, 总带宽: {noise['total_bandwidth']}")
        
        print(f"   ✨ 可视化特点:")
        for feature in analysis['key_features']:
            print(f"     • {feature}")
    
    # 可视化方法说明
    print(f"\n🎨 可视化方法说明:")
    print("="*50)
    
    visualization_methods = {
        '颜色编码': {
            '红色区域': '主频(基频)区域 - 期望信号的核心频率',
            '彩色圆点': '谐波峰值 - 2次、3次、4次等谐波',
            '蓝色半透明': '噪声区域 - 除主频和谐波外的所有频率',
            '黑色线条': '完整频谱 - 原始FFT功率谱'
        },
        '标注系统': {
            '红色箭头': '主频峰值标注，显示频率和功率',
            '彩色箭头': '主要谐波标注(前4个)',
            '图例说明': '右侧图例显示所有检测到的分量',
            '频率轴': '双轴显示，下方Hz，上方对应频率点'
        },
        '自适应显示': {
            '频率范围': '根据主频自动调整显示范围',
            '功率范围': '自动调整Y轴范围适应信号动态',
            '带宽标识': '不同频率使用不同的搜索带宽',
            '阈值过滤': '只显示功率超过阈值的谐波'
        }
    }
    
    for category, methods in visualization_methods.items():
        print(f"\n   {category}:")
        for method, description in methods.items():
            print(f"     • {method}: {description}")
    
    # 技术特点说明
    print(f"\n🔧 技术特点:")
    print("="*50)
    
    technical_features = [
        "高分辨率FFT (16384点) 确保频率精度",
        "自适应带宽算法 (低频窄带宽，高频宽带宽)",
        "频率限制检查 (严格遵守奈奎斯特定理)",
        "功率阈值过滤 (排除噪声误判为谐波)",
        "连续噪声区域分组 (智能合并相邻噪声频点)",
        "多层次标注系统 (主频、谐波、噪声分层显示)",
        "实时频谱分析 (基于实际信号特征调整参数)"
    ]
    
    for i, feature in enumerate(technical_features, 1):
        print(f"   {i}. {feature}")
    
    # 应用价值说明
    print(f"\n💡 应用价值:")
    print("="*50)
    
    application_values = {
        '噪声检测': [
            "直观显示噪声分布区域",
            "量化噪声功率占比",
            "识别噪声频率特征"
        ],
        '信号质量评估': [
            "主频功率和稳定性分析",
            "谐波失真程度评估",
            "信噪比可视化展示"
        ],
        '故障诊断': [
            "异常谐波检测",
            "频率漂移识别",
            "干扰信号定位"
        ],
        '系统优化': [
            "滤波器设计指导",
            "频率规划参考",
            "性能基准建立"
        ]
    }
    
    for application, values in application_values.items():
        print(f"\n   {application}:")
        for value in values:
            print(f"     • {value}")
    
    # 创建对比图表
    create_comparison_chart(analysis_summary)

def create_comparison_chart(analysis_summary):
    """创建对比图表"""
    
    print(f"\n🎨 生成对比图表...")
    
    # 提取数据
    signals = list(analysis_summary.keys())
    fundamental_freqs = [float(analysis_summary[s]['fundamental']['freq'].replace('Hz', '')) for s in signals]
    harmonic_counts = [analysis_summary[s]['harmonics']['count'] for s in signals]
    bandwidths = [float(analysis_summary[s]['fundamental']['bandwidth'].replace('±', '').replace('Hz', '')) for s in signals]
    
    # 创建对比图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 主频 vs 谐波数量
    colors = ['red', 'orange', 'green', 'blue']
    ax1.bar(range(len(signals)), harmonic_counts, color=colors, alpha=0.7)
    ax1.set_title('主频 vs 检测到的谐波数量', fontweight='bold')
    ax1.set_xlabel('信号类型')
    ax1.set_ylabel('谐波数量')
    ax1.set_xticks(range(len(signals)))
    ax1.set_xticklabels(signals, rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, count in enumerate(harmonic_counts):
        ax1.text(i, count + 0.5, str(count), ha='center', va='bottom', fontweight='bold')
    
    # 2. 主频 vs 自适应带宽
    ax2.plot(fundamental_freqs, bandwidths, 'bo-', linewidth=2, markersize=8)
    ax2.set_title('主频 vs 自适应带宽', fontweight='bold')
    ax2.set_xlabel('主频 (Hz)')
    ax2.set_ylabel('带宽 (±Hz)')
    ax2.set_xscale('log')
    ax2.grid(True, alpha=0.3)
    
    # 添加数据点标签
    for freq, bw in zip(fundamental_freqs, bandwidths):
        ax2.annotate(f'{bw:.0f}Hz', (freq, bw), xytext=(5, 5), 
                    textcoords='offset points', fontsize=10)
    
    # 3. 频率限制效应
    theoretical_max_harmonics = [int(24000 / freq) for freq in fundamental_freqs]
    
    x = np.arange(len(signals))
    width = 0.35
    
    ax3.bar(x - width/2, theoretical_max_harmonics, width, label='理论最大谐波数', alpha=0.7, color='lightblue')
    ax3.bar(x + width/2, harmonic_counts, width, label='实际检测谐波数', alpha=0.7, color='darkblue')
    
    ax3.set_title('理论 vs 实际谐波数量', fontweight='bold')
    ax3.set_xlabel('信号类型')
    ax3.set_ylabel('谐波数量')
    ax3.set_xticks(x)
    ax3.set_xticklabels(signals, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 相对带宽分析
    relative_bandwidths = [2 * bw / freq * 100 for freq, bw in zip(fundamental_freqs, bandwidths)]
    
    ax4.semilogx(fundamental_freqs, relative_bandwidths, 'ro-', linewidth=2, markersize=8)
    ax4.set_title('相对带宽 vs 频率', fontweight='bold')
    ax4.set_xlabel('主频 (Hz)')
    ax4.set_ylabel('相对带宽 (%)')
    ax4.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(np.log10(fundamental_freqs), relative_bandwidths, 1)
    p = np.poly1d(z)
    ax4.plot(fundamental_freqs, p(np.log10(fundamental_freqs)), "r--", alpha=0.8, label='趋势线')
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('spectrum_regions_comparison.png', dpi=300, bbox_inches='tight')
    print(f"   ✅ 对比图表已保存: spectrum_regions_comparison.png")
    plt.close()
    
    print(f"\n📈 对比分析结论:")
    print("-" * 30)
    print(f"1. 低频信号有更多可检测谐波 (500Hz: 18个, 15kHz: 0个)")
    print(f"2. 自适应带宽随频率增加 (500Hz: ±3Hz, 15kHz: ±30Hz)")
    print(f"3. 相对带宽随频率降低 (保持检测精度)")
    print(f"4. 奈奎斯特限制显著影响高频谐波检测")

if __name__ == "__main__":
    spectrum_visualization_summary()
