#!/usr/bin/env python3
"""
鲁棒性增强的音频异常检测系统
Robust Audio Anomaly Detection System
解决单频点检测的鲁棒性不足问题
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import welch, find_peaks
from scipy import stats
import os
import json
import warnings
from accuracy_first_detector import AccuracyFirstDetector
warnings.filterwarnings('ignore')

class RobustAudioDetector(AccuracyFirstDetector):
    def __init__(self, sample_rate=48000):
        super().__init__(sample_rate)
        
        # 增强的检测策略
        self.detection_strategies = {
            'frequency_band_analysis': 0.3,    # 频段分析
            'global_pattern_analysis': 0.25,   # 全局模式分析
            'multi_scale_analysis': 0.2,       # 多尺度分析
            'statistical_analysis': 0.15,      # 统计分析
            'cross_band_analysis': 0.1         # 跨频段分析
        }
        
        print(f"鲁棒性增强检测器初始化完成")
        print(f"使用5种检测策略的融合方法")
    
    def detect_anomaly_robust(self, audio_path):
        """鲁棒性增强的异常检测"""
        # 提取特征
        features = self.extract_features(audio_path)
        
        if not features:
            return self._get_error_result(audio_path)
        
        # 多策略检测
        detection_scores = {}
        anomaly_details = []
        
        # 策略1: 频段分析 (改进的频段检测)
        band_score, band_details = self._frequency_band_analysis(features)
        detection_scores['frequency_band'] = band_score
        anomaly_details.extend(band_details)
        
        # 策略2: 全局模式分析
        global_score, global_details = self._global_pattern_analysis(features)
        detection_scores['global_pattern'] = global_score
        anomaly_details.extend(global_details)
        
        # 策略3: 多尺度分析
        multiscale_score, multiscale_details = self._multi_scale_analysis(features)
        detection_scores['multi_scale'] = multiscale_score
        anomaly_details.extend(multiscale_details)
        
        # 策略4: 统计分析
        statistical_score, statistical_details = self._statistical_analysis(features)
        detection_scores['statistical'] = statistical_score
        anomaly_details.extend(statistical_details)
        
        # 策略5: 跨频段分析
        crossband_score, crossband_details = self._cross_band_analysis(features)
        detection_scores['cross_band'] = crossband_score
        anomaly_details.extend(crossband_details)
        
        # 融合多策略结果
        final_score = self._fuse_detection_scores(detection_scores)
        
        # 鲁棒性判断
        anomaly_detected = self._robust_anomaly_decision(detection_scores, final_score)
        
        # 计算置信度
        confidence = self._calculate_robust_confidence(detection_scores, anomaly_detected)
        
        return {
            'file_path': audio_path,
            'anomaly_detected': anomaly_detected,
            'confidence': confidence,
            'final_score': final_score,
            'strategy_scores': detection_scores,
            'anomaly_details': anomaly_details[:5],
            'detection_method': 'robust_multi_strategy',
            'error': False
        }
    
    def _frequency_band_analysis(self, features):
        """改进的频段分析 - 使用频段组而非单频点"""
        score = 0.0
        details = []
        
        # 定义频段组
        frequency_groups = {
            'low_freq': {
                'bands': ['band_23_378Hz', 'band_24_400Hz', 'band_25_424Hz'],
                'weight': 0.3,
                'name': '低频段组'
            },
            'mid_freq': {
                'bands': ['band_31_599Hz', 'band_32_635Hz', 'band_33_673Hz'],
                'weight': 0.4,
                'name': '中频段组'
            },
            'high_freq': {
                'bands': ['band_34_713Hz', 'band_35_756Hz', 'band_36_801Hz'],
                'weight': 0.3,
                'name': '高频段组'
            }
        }
        
        for group_name, group_info in frequency_groups.items():
            group_anomalies = []
            
            for band_prefix in group_info['bands']:
                # 检查该频段的多个特征
                band_features = [f for f in features.keys() if f.startswith(band_prefix)]
                
                for feature_name in band_features:
                    if feature_name in self.detection_thresholds:
                        feature_value = features[feature_name]
                        threshold = self.detection_thresholds[feature_name]
                        
                        if self._is_feature_anomalous(feature_name, feature_value, threshold):
                            anomaly_degree = self._calculate_anomaly_degree(feature_value, threshold, feature_name)
                            group_anomalies.append(anomaly_degree)
            
            # 频段组异常判断：至少2个频段异常才认为组异常
            if len(group_anomalies) >= 2:
                group_score = np.mean(group_anomalies) * group_info['weight']
                score += group_score
                details.append(f"{group_info['name']}异常 (影响{len(group_anomalies)}个频段)")
        
        return score, details
    
    def _global_pattern_analysis(self, features):
        """全局模式分析"""
        score = 0.0
        details = []
        
        # 提取全局特征
        global_features = {k: v for k, v in features.items() if k.startswith('global_')}
        
        if not global_features:
            return 0.0, []
        
        # 能量分布异常检测
        if all(k in global_features for k in ['global_low_ratio', 'global_mid_ratio', 'global_high_ratio']):
            low_ratio = global_features['global_low_ratio']
            mid_ratio = global_features['global_mid_ratio']
            high_ratio = global_features['global_high_ratio']
            
            # 正常能量分布范围 (基于经验)
            normal_ranges = {
                'low': (0.1, 0.4),
                'mid': (0.4, 0.7),
                'high': (0.1, 0.3)
            }
            
            energy_anomalies = 0
            if not (normal_ranges['low'][0] <= low_ratio <= normal_ranges['low'][1]):
                energy_anomalies += 1
            if not (normal_ranges['mid'][0] <= mid_ratio <= normal_ranges['mid'][1]):
                energy_anomalies += 1
            if not (normal_ranges['high'][0] <= high_ratio <= normal_ranges['high'][1]):
                energy_anomalies += 1
            
            if energy_anomalies >= 2:
                score += 0.3
                details.append(f"全局能量分布异常 (异常频段数:{energy_anomalies})")
        
        # 动态范围异常检测
        if 'global_dynamic_range' in global_features:
            dynamic_range = global_features['global_dynamic_range']
            if dynamic_range < 20 or dynamic_range > 80:  # dB
                score += 0.2
                details.append(f"动态范围异常 ({dynamic_range:.1f}dB)")
        
        return score, details
    
    def _multi_scale_analysis(self, features):
        """多尺度分析 - 检查不同频率尺度的一致性"""
        score = 0.0
        details = []
        
        # 提取不同尺度的特征
        scales = {
            'fine': [f for f in features.keys() if 'spectral_rolloff' in f],
            'medium': [f for f in features.keys() if 'response_flatness' in f],
            'coarse': [f for f in features.keys() if 'energy' in f]
        }
        
        for scale_name, scale_features in scales.items():
            if len(scale_features) < 3:
                continue
            
            # 计算该尺度特征的一致性
            feature_values = [features[f] for f in scale_features if f in features]
            if len(feature_values) < 3:
                continue
            
            # 使用变异系数检测一致性
            cv = np.std(feature_values) / (np.mean(feature_values) + 1e-12)
            
            # 一致性阈值
            consistency_thresholds = {
                'fine': 0.3,
                'medium': 0.5,
                'coarse': 0.8
            }
            
            if cv > consistency_thresholds[scale_name]:
                scale_score = min(0.2, cv - consistency_thresholds[scale_name])
                score += scale_score
                details.append(f"{scale_name}尺度特征不一致 (CV:{cv:.3f})")
        
        return score, details
    
    def _statistical_analysis(self, features):
        """统计分析 - 基于特征分布的异常检测"""
        score = 0.0
        details = []
        
        # 收集所有数值特征
        numeric_features = {k: v for k, v in features.items() 
                          if isinstance(v, (int, float)) and not np.isnan(v)}
        
        if len(numeric_features) < 10:
            return 0.0, []
        
        feature_values = list(numeric_features.values())
        
        # Z-score异常检测
        z_scores = np.abs(stats.zscore(feature_values))
        outlier_count = np.sum(z_scores > 3)  # 3-sigma规则
        
        if outlier_count > len(feature_values) * 0.1:  # 超过10%的特征异常
            score += 0.3
            details.append(f"统计异常特征过多 ({outlier_count}/{len(feature_values)})")
        
        # 分布偏度检测
        skewness = stats.skew(feature_values)
        if abs(skewness) > 2:  # 严重偏斜
            score += 0.2
            details.append(f"特征分布严重偏斜 (偏度:{skewness:.2f})")
        
        return score, details
    
    def _cross_band_analysis(self, features):
        """跨频段分析 - 检查频段间的关系"""
        score = 0.0
        details = []
        
        # 提取频段特征
        band_features = {}
        for feature_name, value in features.items():
            if feature_name.startswith('band_') and 'spectral_rolloff' in feature_name:
                band_num = feature_name.split('_')[1]
                band_features[band_num] = value
        
        if len(band_features) < 5:
            return 0.0, []
        
        # 检查频谱滚降的单调性
        sorted_bands = sorted(band_features.items(), key=lambda x: int(x[0]))
        rolloff_values = [v for k, v in sorted_bands]
        
        # 计算单调性违反次数
        violations = 0
        for i in range(1, len(rolloff_values)):
            if rolloff_values[i] < rolloff_values[i-1] * 0.9:  # 允许10%的波动
                violations += 1
        
        if violations > len(rolloff_values) * 0.2:  # 超过20%违反单调性
            score += 0.3
            details.append(f"频谱滚降单调性异常 (违反次数:{violations})")
        
        return score, details
    
    def _fuse_detection_scores(self, detection_scores):
        """融合多策略检测分数"""
        final_score = 0.0
        
        for strategy, score in detection_scores.items():
            weight = self.detection_strategies.get(strategy, 0.1)
            final_score += score * weight
        
        return final_score
    
    def _robust_anomaly_decision(self, detection_scores, final_score):
        """鲁棒性异常判断"""
        # 策略1: 最终分数阈值
        if final_score > 0.4:
            return True
        
        # 策略2: 多策略一致性
        anomaly_strategies = sum(1 for score in detection_scores.values() if score > 0.2)
        if anomaly_strategies >= 3:  # 至少3个策略检测到异常
            return True
        
        # 策略3: 单策略强异常
        max_strategy_score = max(detection_scores.values())
        if max_strategy_score > 0.6:  # 单策略强异常
            return True
        
        return False
    
    def _calculate_robust_confidence(self, detection_scores, anomaly_detected):
        """计算鲁棒性置信度"""
        # 基于策略一致性计算置信度
        strategy_consistency = len([s for s in detection_scores.values() if s > 0.1])
        max_consistency = len(detection_scores)
        
        consistency_ratio = strategy_consistency / max_consistency
        
        if anomaly_detected:
            # 异常文件：策略越一致，置信度越高
            base_confidence = 0.6
            confidence = base_confidence + consistency_ratio * 0.35
        else:
            # 正常文件：策略越一致（都认为正常），置信度越高
            base_confidence = 0.95
            confidence = base_confidence - consistency_ratio * 0.25
        
        return np.clip(confidence, 0.01, 0.99)
    
    def _is_feature_anomalous(self, feature_name, feature_value, threshold):
        """判断特征是否异常"""
        if 'spectral_rolloff' in feature_name or 'snr' in feature_name and 'db' not in feature_name:
            return feature_value > threshold
        elif 'snr_db' in feature_name:
            return feature_value < threshold
        elif 'energy' in feature_name:
            return feature_value < threshold
        elif 'response_flatness' in feature_name or 'spectral_irregularity' in feature_name:
            return feature_value > threshold
        else:
            return False
    
    def _calculate_anomaly_degree(self, feature_value, threshold, feature_name):
        """计算异常程度"""
        if 'spectral_rolloff' in feature_name or 'snr' in feature_name and 'db' not in feature_name:
            if feature_value > threshold:
                return min(1.0, (feature_value - threshold) / threshold)
        elif 'snr_db' in feature_name:
            if feature_value < threshold:
                return min(1.0, (threshold - feature_value) / threshold)
        elif 'energy' in feature_name:
            if feature_value < threshold:
                return min(1.0, (threshold - feature_value) / (threshold + 1e-12))
        elif 'response_flatness' in feature_name or 'spectral_irregularity' in feature_name:
            if feature_value > threshold:
                return min(1.0, (feature_value - threshold) / threshold)
        
        return 0.0
    
    def _get_error_result(self, audio_path):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'final_score': 0.0,
            'strategy_scores': {},
            'anomaly_details': ['特征提取失败'],
            'detection_method': 'robust_multi_strategy',
            'error': True
        }
    
    def batch_detect_robust(self, directories):
        """鲁棒性批量检测"""
        results = []
        
        for directory in directories:
            if not os.path.exists(directory):
                print(f"目录不存在: {directory}")
                continue
            
            print(f"\n鲁棒性检测目录: {directory}")
            print("-" * 50)
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.wav'):
                        file_path = os.path.join(root, file)
                        print(f"鲁棒性检测: {file}")
                        
                        result = self.detect_anomaly_robust(file_path)
                        result['directory'] = directory
                        result['filename'] = file
                        results.append(result)
        
        return results
    
    def compare_detection_methods(self, directories):
        """比较原始方法和鲁棒性方法"""
        print("\n" + "="*80)
        print("检测方法对比分析")
        print("="*80)
        
        # 原始检测
        print("执行原始检测...")
        original_results = self.batch_detect(directories)
        
        # 鲁棒性检测
        print("执行鲁棒性检测...")
        robust_results = self.batch_detect_robust(directories)
        
        # 对比分析
        comparison = []
        for orig, robust in zip(original_results, robust_results):
            comparison.append({
                'filename': orig['filename'],
                'original_anomaly': orig['anomaly_detected'],
                'original_confidence': orig['confidence'],
                'robust_anomaly': robust['anomaly_detected'],
                'robust_confidence': robust['confidence'],
                'robust_final_score': robust['final_score'],
                'agreement': orig['anomaly_detected'] == robust['anomaly_detected']
            })
        
        # 统计对比结果
        total_files = len(comparison)
        agreement_count = sum(1 for c in comparison if c['agreement'])
        agreement_rate = agreement_count / total_files * 100
        
        print(f"\n对比结果:")
        print(f"  总文件数: {total_files}")
        print(f"  检测结果一致: {agreement_count} ({agreement_rate:.1f}%)")
        print(f"  检测结果不一致: {total_files - agreement_count}")
        
        # 显示不一致的文件
        disagreements = [c for c in comparison if not c['agreement']]
        if disagreements:
            print(f"\n检测结果不一致的文件:")
            for d in disagreements:
                print(f"  {d['filename']}:")
                print(f"    原始方法: {'异常' if d['original_anomaly'] else '正常'} ({d['original_confidence']:.1%})")
                print(f"    鲁棒方法: {'异常' if d['robust_anomaly'] else '正常'} ({d['robust_confidence']:.1%})")
        
        return comparison

def main():
    """主函数"""
    # 初始化鲁棒性检测器
    detector = RobustAudioDetector()
    
    # 定义要检测的目录
    directories = [
        "../test20250717",
        "../待定"
    ]
    
    # 执行对比检测
    comparison = detector.compare_detection_methods(directories)
    
    # 保存对比结果
    comparison_df = pd.DataFrame(comparison)
    comparison_df.to_csv('detection_method_comparison.csv', index=False)
    
    print(f"\n对比结果已保存: detection_method_comparison.csv")
    
    return detector, comparison

if __name__ == "__main__":
    detector, comparison = main()
