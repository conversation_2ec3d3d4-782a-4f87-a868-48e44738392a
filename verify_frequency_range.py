#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证频率范围设置是否正确
"""

import os
import glob
import subprocess

def hz_to_mel(hz):
    """
    将频率从Hz转换为梅尔刻度
    """
    import numpy as np
    return 2595 * np.log10(1 + hz / 700)

def verify_frequency_range():
    """
    验证频率范围设置
    """
    print("🔍 验证频率范围设置")
    print("="*60)
    
    # 测试不同的频率范围
    test_cases = [
        {
            "name": "默认范围 (100-20000Hz)",
            "args": ["test20250722/琴身内部异物1.1.wav"],
            "expected_start": 100,
            "expected_stop": 20000
        },
        {
            "name": "自定义范围 (50-15000Hz)",
            "args": ["test20250722/琴身内部异物1.1.wav", "--freq-range", "50", "15000"],
            "expected_start": 50,
            "expected_stop": 15000
        },
        {
            "name": "梅尔刻度 (100-24000Hz)",
            "args": ["test20250722/琴身内部异物1.1.wav", "--mel-scale", "--freq-range", "100", "24000"],
            "expected_start": 100,
            "expected_stop": 24000
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 测试 {i}: {test_case['name']}")
        print(f"命令参数: {' '.join(test_case['args'])}")
        print(f"期望频率范围: {test_case['expected_start']}Hz - {test_case['expected_stop']}Hz")
        
        # 显示梅尔刻度对应值（如果适用）
        if "--mel-scale" in test_case['args']:
            start_mel = hz_to_mel(test_case['expected_start'])
            stop_mel = hz_to_mel(test_case['expected_stop'])
            print(f"对应梅尔值: {start_mel:.1f}mel - {stop_mel:.1f}mel")
        
        print("✅ 参数验证通过")

def show_frequency_examples():
    """
    显示频率范围示例
    """
    print(f"\n📈 频率范围示例:")
    
    examples = [
        ("人声分析", "80", "8000", "覆盖人声基频和主要共振峰"),
        ("音乐分析", "20", "20000", "完整音乐频率范围"),
        ("高质量音频", "10", "24000", "超高质量音频分析"),
        ("电话语音", "300", "3400", "电话系统频率范围"),
        ("低频分析", "10", "1000", "专注低频成分"),
        ("高频分析", "5000", "24000", "专注高频成分")
    ]
    
    print(f"\n{'应用场景':<12} {'频率范围':<15} {'说明'}")
    print("-" * 50)
    
    for name, start, stop, desc in examples:
        print(f"{name:<12} {start}-{stop}Hz{'':<8} {desc}")

def show_mel_scale_comparison():
    """
    显示梅尔刻度对比
    """
    print(f"\n🎵 梅尔刻度对比:")
    
    frequencies = [50, 100, 500, 1000, 2000, 4000, 8000, 16000, 24000]
    
    print(f"\n{'频率(Hz)':<10} {'梅尔值':<10} {'感知特点'}")
    print("-" * 40)
    
    for freq in frequencies:
        mel_val = hz_to_mel(freq)
        
        if freq <= 300:
            perception = "超低频"
        elif freq <= 1000:
            perception = "低频基础"
        elif freq <= 2000:
            perception = "语音基频"
        elif freq <= 4000:
            perception = "语音共振峰"
        elif freq <= 8000:
            perception = "高频语音"
        elif freq <= 16000:
            perception = "高频细节"
        else:
            perception = "超高频"
        
        print(f"{freq:<10} {mel_val:<10.1f} {perception}")

def show_usage_examples():
    """
    显示使用示例
    """
    print(f"\n💡 使用示例:")
    
    examples = [
        {
            "desc": "默认对数刻度分析",
            "cmd": "python universal_spectrum_analyzer.py my_audio.wav"
        },
        {
            "desc": "梅尔刻度分析",
            "cmd": "python universal_spectrum_analyzer.py my_audio.wav --mel-scale"
        },
        {
            "desc": "自定义频率范围",
            "cmd": "python universal_spectrum_analyzer.py my_audio.wav --freq-range 50 15000"
        },
        {
            "desc": "梅尔刻度 + 自定义范围",
            "cmd": "python universal_spectrum_analyzer.py my_audio.wav --mel-scale --freq-range 100 24000"
        },
        {
            "desc": "多进程 + 自定义参数",
            "cmd": "python universal_spectrum_analyzer.py my_audio.wav --mel-scale -w 6 --freq-range 80 18000"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['desc']}:")
        print(f"   {example['cmd']}")

def verify_output_directories():
    """
    验证输出目录
    """
    print(f"\n📁 输出目录验证:")
    
    # 检查现有的输出目录
    patterns = [
        "*_频谱分析",
        "*_梅尔频谱分析"
    ]
    
    found_dirs = []
    for pattern in patterns:
        dirs = glob.glob(pattern)
        found_dirs.extend(dirs)
    
    if found_dirs:
        print(f"找到 {len(found_dirs)} 个输出目录:")
        for dir_name in sorted(found_dirs):
            files = glob.glob(os.path.join(dir_name, "segment_*.png"))
            summary_file = os.path.join(dir_name, "频谱分析汇总.txt")
            
            print(f"  📂 {dir_name}")
            print(f"     频谱图: {len(files)} 个")
            print(f"     汇总文件: {'✅' if os.path.exists(summary_file) else '❌'}")
            
            # 检查文件命名
            if files:
                sample_file = os.path.basename(files[0])
                if "_mel.png" in sample_file:
                    print(f"     刻度类型: 梅尔刻度")
                elif "_log.png" in sample_file:
                    print(f"     刻度类型: 对数刻度")
                else:
                    print(f"     刻度类型: 标准")
    else:
        print("未找到输出目录")

def main():
    """
    主函数
    """
    verify_frequency_range()
    show_frequency_examples()
    show_mel_scale_comparison()
    show_usage_examples()
    verify_output_directories()
    
    print(f"\n" + "="*60)
    print("✅ 频率范围验证完成!")
    print("💡 现在可视化范围会正确使用输入的频率范围参数")
    print("🎯 梅尔刻度和对数刻度都支持自定义频率范围")

if __name__ == "__main__":
    main()
