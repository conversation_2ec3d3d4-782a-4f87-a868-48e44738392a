#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐段对比：低音戳洞 vs test20250717所有文件的93段谐波数量
显示每一段的谐波数量和test文件夹的绝对范围
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import pandas as pd

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def segment_wise_comparison():
    """逐段谐波数量对比"""
    print("🎯 逐段对比：低音戳洞 vs test20250717所有文件的93段谐波数量")
    print("="*70)
    
    # 低音戳洞文件
    hole_file = r'test20250717\neg\录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    # test20250717目录
    test_dir = 'test20250717'
    
    if not os.path.exists(test_dir):
        print("❌ test20250717目录不存在")
        return
    
    # 分析低音戳洞
    print("\n📊 分析低音戳洞...")
    hole_segments = analyze_file_segments(hole_file, "低音戳洞")
    
    if not hole_segments:
        print("❌ 低音戳洞分析失败")
        return
    
    # 批量分析test20250717中的所有文件
    print(f"\n📊 批量分析{test_dir}中的所有文件...")
    all_test_segments = batch_analyze_all_segments(test_dir)
    
    if all_test_segments:
        # 创建逐段对比分析
        create_segment_wise_analysis(hole_segments, all_test_segments)

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def analyze_file_segments(audio_path, file_label):
    """分析单个文件的所有段"""
    try:
        print(f"  分析: {file_label}")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                segments_data.append({
                    'seg_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'harmonic_count': 0,
                    'noise_variation': 0
                })
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析 - 使用与dynamic_noise_harmonic_detection相同的高分辨率
            fft_size = 131072  # 128k点FFT (与dynamic_noise_harmonic_detection一致)
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 谐波检测
            if noise_analysis:
                harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
                harmonic_count = len(harmonic_analysis)
                noise_variation = noise_analysis['noise_variation_db']
            else:
                harmonic_count = 0
                noise_variation = 0
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'harmonic_count': harmonic_count,
                'noise_variation': noise_variation
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'label': file_label,
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def batch_analyze_all_segments(directory):
    """批量分析目录中所有文件的所有段"""
    
    # 查找所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"  找到{len(wav_files)}个wav文件")
    
    all_files_segments = []
    
    for i, wav_file in enumerate(wav_files):
        print(f"  处理进度: {i+1}/{len(wav_files)} - {os.path.basename(wav_file)}")
        
        # 确定文件标签
        if 'pos' in wav_file.lower() or 'good' in wav_file.lower() or 'ok' in wav_file.lower():
            label = 'Good'
        elif 'neg' in wav_file.lower() or 'bad' in wav_file.lower() or 'defect' in wav_file.lower():
            label = 'Defect'
        else:
            label = 'Unknown'
        
        result = analyze_file_segments(wav_file, label)
        if result:
            all_files_segments.append(result)
    
    print(f"✅ 成功分析{len(all_files_segments)}个文件")
    
    return all_files_segments

def create_segment_wise_analysis(hole_segments, all_test_segments):
    """创建逐段对比分析"""
    print(f"\n🎨 生成逐段对比分析...")
    
    # 确保有93段数据
    num_segments = len(hole_segments['segments'])
    print(f"  分析段数: {num_segments}")
    
    # 计算每段的统计数据
    segment_stats = []
    
    for seg_idx in range(num_segments):
        # 低音戳洞该段的谐波数量
        hole_harmonic_count = hole_segments['segments'][seg_idx]['harmonic_count']
        hole_noise_variation = hole_segments['segments'][seg_idx]['noise_variation']
        expected_freq = hole_segments['segments'][seg_idx]['expected_freq']
        
        # 收集所有test文件该段的谐波数量
        test_harmonic_counts = []
        test_noise_variations = []
        
        for file_data in all_test_segments:
            if seg_idx < len(file_data['segments']):
                test_harmonic_counts.append(file_data['segments'][seg_idx]['harmonic_count'])
                test_noise_variations.append(file_data['segments'][seg_idx]['noise_variation'])
        
        # 计算test文件的绝对范围
        if test_harmonic_counts:
            test_harmonic_min = min(test_harmonic_counts)
            test_harmonic_max = max(test_harmonic_counts)
            test_harmonic_range = test_harmonic_max - test_harmonic_min
            test_harmonic_mean = np.mean(test_harmonic_counts)
            
            test_noise_min = min(test_noise_variations)
            test_noise_max = max(test_noise_variations)
            test_noise_range = test_noise_max - test_noise_min
            test_noise_mean = np.mean(test_noise_variations)
        else:
            test_harmonic_min = test_harmonic_max = test_harmonic_range = test_harmonic_mean = 0
            test_noise_min = test_noise_max = test_noise_range = test_noise_mean = 0
        
        segment_stats.append({
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'hole_harmonic_count': hole_harmonic_count,
            'hole_noise_variation': hole_noise_variation,
            'test_harmonic_min': test_harmonic_min,
            'test_harmonic_max': test_harmonic_max,
            'test_harmonic_range': test_harmonic_range,
            'test_harmonic_mean': test_harmonic_mean,
            'test_noise_min': test_noise_min,
            'test_noise_max': test_noise_max,
            'test_noise_range': test_noise_range,
            'test_noise_mean': test_noise_mean,
            'hole_vs_test_min': hole_harmonic_count - test_harmonic_min,
            'hole_vs_test_max': hole_harmonic_count - test_harmonic_max,
            'hole_vs_test_mean': hole_harmonic_count - test_harmonic_mean
        })
    
    # 创建可视化
    create_segment_wise_visualization(segment_stats)
    
    # 保存详细数据到CSV
    save_segment_wise_data(segment_stats)
    
    # 打印逐段对比报告
    print_segment_wise_report(segment_stats)

def create_segment_wise_visualization(segment_stats):
    """创建逐段可视化"""
    
    # 提取数据
    segment_indices = [s['seg_idx'] for s in segment_stats]
    expected_freqs = [s['expected_freq'] for s in segment_stats]
    hole_harmonic_counts = [s['hole_harmonic_count'] for s in segment_stats]
    test_harmonic_mins = [s['test_harmonic_min'] for s in segment_stats]
    test_harmonic_maxs = [s['test_harmonic_max'] for s in segment_stats]
    test_harmonic_means = [s['test_harmonic_mean'] for s in segment_stats]
    
    # 创建图表 (2行1列)
    fig, axes = plt.subplots(2, 1, figsize=(24, 16))
    fig.suptitle('Segment-wise Harmonic Count Comparison: Hole vs Test20250717 All Files', 
                 fontsize=16, fontweight='bold')
    
    # 1. 逐段谐波数量对比
    ax1 = axes[0]
    
    # 绘制test文件的范围（阴影区域）
    ax1.fill_between(segment_indices, test_harmonic_mins, test_harmonic_maxs, 
                    alpha=0.3, color='lightblue', label='Test Files Range (Min-Max)')
    
    # 绘制test文件的平均值
    ax1.plot(segment_indices, test_harmonic_means, 'b-', linewidth=2, alpha=0.7, 
            label='Test Files Average')
    
    # 绘制低音戳洞的谐波数量
    ax1.plot(segment_indices, hole_harmonic_counts, 'ro-', linewidth=2, markersize=4, 
            label='Hole Sample')
    
    ax1.set_xlabel('Segment Index')
    ax1.set_ylabel('Harmonic Count')
    ax1.set_title('Segment-wise Harmonic Count Comparison (93 Segments)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 设置x轴刻度
    ax1.set_xticks(range(0, len(segment_indices), 10))
    ax1.set_xticklabels([f'S{i}' for i in range(0, len(segment_indices), 10)])
    
    # 2. 频率 vs 谐波数量对比
    ax2 = axes[1]
    
    # 绘制test文件的范围（阴影区域）
    ax2.fill_between(expected_freqs, test_harmonic_mins, test_harmonic_maxs, 
                    alpha=0.3, color='lightgreen', label='Test Files Range (Min-Max)')
    
    # 绘制test文件的平均值
    ax2.plot(expected_freqs, test_harmonic_means, 'g-', linewidth=2, alpha=0.7, 
            label='Test Files Average')
    
    # 绘制低音戳洞的谐波数量
    ax2.plot(expected_freqs, hole_harmonic_counts, 'ro-', linewidth=2, markersize=4, 
            label='Hole Sample')
    
    ax2.set_xlabel('Expected Frequency (Hz)')
    ax2.set_ylabel('Harmonic Count')
    ax2.set_title('Frequency vs Harmonic Count Comparison')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_xscale('log')
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'segment_wise_harmonic_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 逐段对比可视化已保存: {filename}")
    plt.close()

def save_segment_wise_data(segment_stats):
    """保存逐段数据到CSV"""
    
    # 创建DataFrame
    df = pd.DataFrame(segment_stats)
    
    # 重新排列列的顺序
    columns_order = [
        'seg_idx', 'expected_freq', 
        'hole_harmonic_count', 'hole_noise_variation',
        'test_harmonic_min', 'test_harmonic_max', 'test_harmonic_range', 'test_harmonic_mean',
        'test_noise_min', 'test_noise_max', 'test_noise_range', 'test_noise_mean',
        'hole_vs_test_min', 'hole_vs_test_max', 'hole_vs_test_mean'
    ]
    
    df = df[columns_order]
    
    # 保存到CSV
    filename = 'segment_wise_harmonic_comparison.csv'
    df.to_csv(filename, index=False, float_format='%.2f')
    print(f"✅ 逐段数据已保存: {filename}")

def print_segment_wise_report(segment_stats):
    """打印逐段对比报告"""
    print(f"\n📊 逐段谐波数量对比报告 (前20段示例):")
    print(f"{'='*120}")
    
    # 表头
    print(f"{'段号':<4} {'频率(Hz)':<8} {'低音戳洞':<8} {'Test最小':<8} {'Test最大':<8} {'Test范围':<8} {'Test平均':<8} {'洞vs最小':<8} {'洞vs最大':<8} {'洞vs平均':<8}")
    print(f"{'-'*120}")
    
    # 显示前20段
    for i, stats in enumerate(segment_stats[:20]):
        print(f"{stats['seg_idx']:<4} {stats['expected_freq']:<8.0f} {stats['hole_harmonic_count']:<8} "
              f"{stats['test_harmonic_min']:<8} {stats['test_harmonic_max']:<8} {stats['test_harmonic_range']:<8} "
              f"{stats['test_harmonic_mean']:<8.1f} {stats['hole_vs_test_min']:<8} {stats['hole_vs_test_max']:<8} "
              f"{stats['hole_vs_test_mean']:<8.1f}")
    
    print(f"\n... (显示前20段，完整数据请查看CSV文件)")
    
    # 统计摘要
    hole_total = sum([s['hole_harmonic_count'] for s in segment_stats])
    test_total_min = sum([s['test_harmonic_min'] for s in segment_stats])
    test_total_max = sum([s['test_harmonic_max'] for s in segment_stats])
    test_total_mean = sum([s['test_harmonic_mean'] for s in segment_stats])
    
    print(f"\n📈 总体统计:")
    print(f"  低音戳洞总谐波: {hole_total}个")
    print(f"  Test文件总谐波范围: {test_total_min}-{test_total_max}个")
    print(f"  Test文件总谐波平均: {test_total_mean:.1f}个")
    print(f"  低音戳洞 vs Test平均: {((hole_total - test_total_mean) / test_total_mean * 100):+.1f}%")
    
    # 频率段分析
    low_freq_segments = [s for s in segment_stats if s['expected_freq'] <= 1000]
    mid_freq_segments = [s for s in segment_stats if 1000 < s['expected_freq'] <= 5000]
    high_freq_segments = [s for s in segment_stats if s['expected_freq'] > 5000]
    
    print(f"\n🎵 频率段分析:")
    for segments, name in [
        (low_freq_segments, "低频段 (≤1kHz)"),
        (mid_freq_segments, "中频段 (1-5kHz)"),
        (high_freq_segments, "高频段 (>5kHz)")
    ]:
        if segments:
            hole_avg = np.mean([s['hole_harmonic_count'] for s in segments])
            test_avg = np.mean([s['test_harmonic_mean'] for s in segments])
            print(f"  {name}: 低音戳洞平均{hole_avg:.1f}个, Test平均{test_avg:.1f}个, 差异{((hole_avg - test_avg) / test_avg * 100):+.1f}%")

if __name__ == "__main__":
    segment_wise_comparison()
