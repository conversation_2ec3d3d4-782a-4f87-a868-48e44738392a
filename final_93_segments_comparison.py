#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本：两个样本93段主频、谐波、噪声的完整定位对比图
使用优化后的检测算法
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def final_93_segments_comparison():
    """最终版本：93段完整对比分析"""
    print("🎯 最终版本：两个样本93段主频、谐波、噪声完整对比分析")
    print("="*70)
    print("使用优化算法:")
    print("1. 超高分辨率FFT (131k点)")
    print("2. 优化SNR阈值 (6-9dB)")
    print("3. 保守噪声估计")
    print("4. 完整频率范围检测")
    print("="*70)
    
    # 两个异常样本
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    
    # 分析两个样本的所有93段
    all_samples_analysis = []
    
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 分析 {sample_name}: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        if os.path.exists(audio_path):
            # 分析所有93段
            sample_analysis = analyze_all_93_segments_optimized(audio_path, sample_name)
            if sample_analysis:
                all_samples_analysis.append(sample_analysis)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")
    
    if len(all_samples_analysis) == 2:
        # 创建完整的对比可视化
        create_final_comparison_visualization(all_samples_analysis)
        
        # 创建详细的统计对比
        create_detailed_statistics_comparison(all_samples_analysis)
    else:
        print("❌ 需要两个样本才能进行对比分析")

def analyze_all_93_segments_optimized(audio_path, sample_name):
    """使用优化算法分析所有93段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"   📊 总频段数: {len(step_boundaries)}")
        
        segment_analyses = []
        
        # 分析每个频段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 优化的单段分析
            segment_analysis = optimized_single_segment_analysis(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
            
            # 显示进度
            if seg_idx % 20 == 0 or seg_idx < 5:
                print(f"   处理频段 {seg_idx:2d}/{len(step_boundaries)} ({expected_freq:6.1f}Hz) - "
                      f"检测到{len(segment_analysis['harmonic_analysis']) if segment_analysis else 0}个谐波")
        
        return {
            'sample_name': sample_name,
            'audio_path': audio_path,
            'segment_analyses': segment_analyses,
            'total_segments': len(segment_analyses)
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def optimized_single_segment_analysis(audio, sr, expected_freq, seg_idx):
    """优化的单段分析"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(131072, len(audio))  # 128k点FFT
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只分析正频率
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_optimized(positive_freqs, positive_power, expected_freq)
        
        # 2. 保守噪声估计
        noise_analysis = conservative_noise_estimation_optimized(positive_freqs, positive_power, expected_freq)
        
        # 3. 优化谐波检测
        harmonic_analysis = optimized_harmonic_detection(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, noise_analysis, sr
        )
        
        # 4. 噪声区域分析
        noise_regions = analyze_noise_regions_optimized(
            positive_freqs, positive_power, fundamental_analysis, harmonic_analysis
        )
        
        # 5. 质量指标计算
        quality_metrics = calculate_quality_metrics_optimized(
            fundamental_analysis, harmonic_analysis, noise_regions, positive_power
        )
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'noise_analysis': noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'noise_regions': noise_regions,
            'quality_metrics': quality_metrics,
            'freq_resolution': positive_freqs[1] - positive_freqs[0]
        }
        
    except Exception as e:
        return None

def analyze_fundamental_optimized(freqs, power, expected_freq):
    """优化的主频分析"""
    
    # 自适应带宽
    if expected_freq <= 200:
        bandwidth = 2.0
    elif expected_freq <= 500:
        bandwidth = 3.0
    elif expected_freq <= 1000:
        bandwidth = 5.0
    elif expected_freq <= 2000:
        bandwidth = 8.0
    elif expected_freq <= 5000:
        bandwidth = 12.0
    elif expected_freq <= 10000:
        bandwidth = 20.0
    else:
        bandwidth = 30.0
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    # 估计局部噪声
    noise_estimation_mask = (freqs >= expected_freq - 50) & (freqs <= expected_freq + 50) & \
                           ~((freqs >= expected_freq - bandwidth*2) & (freqs <= expected_freq + bandwidth*2))
    
    if np.any(noise_estimation_mask):
        local_noise_powers = power[noise_estimation_mask]
        local_noise_floor = np.percentile(local_noise_powers, 25)
        local_noise_floor_db = 10 * np.log10(local_noise_floor + 1e-12)
        local_snr_db = fundamental_power_db - local_noise_floor_db
    else:
        local_noise_floor_db = -120
        local_snr_db = 120
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'bandwidth': bandwidth,
        'local_noise_floor_db': local_noise_floor_db,
        'local_snr_db': local_snr_db,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def conservative_noise_estimation_optimized(freqs, power, fundamental_freq):
    """优化的保守噪声估计"""
    
    # 只排除主频和前5个谐波
    excluded_ranges = []
    excluded_ranges.append((fundamental_freq - 15, fundamental_freq + 15))
    
    for order in range(2, 6):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < freqs[-1]:
            excluded_ranges.append((harmonic_freq - 15, harmonic_freq + 15))
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        noise_floor = np.percentile(noise_powers, 20)
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        
        return {
            'noise_floor': noise_floor,
            'noise_floor_db': noise_floor_db,
            'noise_sample_count': np.sum(noise_mask)
        }
    else:
        return {
            'noise_floor_db': -120,
            'noise_sample_count': 0
        }

def optimized_harmonic_detection(freqs, power, fundamental_freq, 
                                fundamental_analysis, noise_analysis, sr):
    """优化的谐波检测"""
    
    if not fundamental_analysis or not noise_analysis:
        return []
    
    detected_harmonics = []
    noise_floor_db = noise_analysis['noise_floor_db']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 优化的SNR阈值
    base_snr_threshold = 6.0
    
    nyquist_freq = sr / 2
    
    for order in range(2, 30):  # 检测到29次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 5000:
            search_bandwidth = 40.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 60.0
        else:
            search_bandwidth = 80.0
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        snr_db = harmonic_power_db - noise_floor_db
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 2000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 相对功率和频率误差
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 检测条件
        conditions = {
            'snr_sufficient': snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -65.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.9
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'snr_db': snr_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'index': actual_idx
            })
    
    return detected_harmonics

def analyze_noise_regions_optimized(freqs, power, fundamental_analysis, harmonic_analysis):
    """优化的噪声区域分析"""
    
    # 创建噪声掩码
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频
    if fundamental_analysis:
        fund_freq = fundamental_analysis['freq']
        fund_bandwidth = fundamental_analysis['bandwidth']
        fund_mask = (freqs >= fund_freq - fund_bandwidth) & (freqs <= fund_freq + fund_bandwidth)
        noise_mask &= ~fund_mask
    
    # 排除谐波
    for harmonic in harmonic_analysis:
        harm_freq = harmonic['freq']
        harm_bandwidth = 15.0
        harm_mask = (freqs >= harm_freq - harm_bandwidth) & (freqs <= harm_freq + harm_bandwidth)
        noise_mask &= ~harm_mask
    
    # 计算噪声统计
    if np.any(noise_mask):
        noise_powers = power[noise_mask]
        total_noise_power = np.sum(noise_powers)
        total_signal_power = np.sum(power)
        noise_ratio = total_noise_power / total_signal_power
        
        return {
            'total_noise_power': total_noise_power,
            'total_noise_power_db': 10 * np.log10(total_noise_power + 1e-12),
            'noise_ratio': noise_ratio,
            'noise_frequency_count': np.sum(noise_mask),
            'noise_frequency_coverage': np.sum(noise_mask) / len(freqs)
        }
    else:
        return {
            'total_noise_power_db': -120,
            'noise_ratio': 0,
            'noise_frequency_count': 0,
            'noise_frequency_coverage': 0
        }

def calculate_quality_metrics_optimized(fundamental_analysis, harmonic_analysis, 
                                       noise_regions, total_power):
    """优化的质量指标计算"""
    
    if not fundamental_analysis:
        return {
            'snr_db': -120,
            'thd_db': -120,
            'signal_to_noise_ratio': 0,
            'harmonic_count': 0
        }
    
    # SNR
    snr_db = fundamental_analysis['local_snr_db']
    
    # THD (总谐波失真)
    if harmonic_analysis:
        total_harmonic_power = sum(h['power'] for h in harmonic_analysis)
        fundamental_power = fundamental_analysis['power']
        thd = np.sqrt(total_harmonic_power) / fundamental_power
        thd_db = 20 * np.log10(thd + 1e-12)
    else:
        thd_db = -120
    
    # 信噪比
    signal_power = fundamental_analysis['power'] + sum(h['power'] for h in harmonic_analysis)
    noise_power = noise_regions['total_noise_power'] if noise_regions else 1e-12
    signal_to_noise_ratio = signal_power / noise_power
    
    return {
        'snr_db': snr_db,
        'thd_db': thd_db,
        'signal_to_noise_ratio': signal_to_noise_ratio,
        'harmonic_count': len(harmonic_analysis)
    }

def create_final_comparison_visualization(all_samples_analysis):
    """创建最终的对比可视化"""
    
    print(f"\n🎨 生成最终版本93段对比可视化...")
    
    # 创建超大图 (6行2列)
    fig = plt.figure(figsize=(24, 36))
    
    # 提取两个样本的数据
    sample1_data = all_samples_analysis[0]
    sample2_data = all_samples_analysis[1]
    
    sample1_segments = sample1_data['segment_analyses']
    sample2_segments = sample2_data['segment_analyses']
    
    # 确保两个样本的频段数量一致
    min_segments = min(len(sample1_segments), len(sample2_segments))
    
    # 提取对比数据
    segments = [s['segment_idx'] for s in sample1_segments[:min_segments]]
    freqs = [s['expected_freq'] for s in sample1_segments[:min_segments]]
    
    # 样本1数据
    sample1_snr = [s['quality_metrics']['snr_db'] for s in sample1_segments[:min_segments]]
    sample1_harmonics = [s['quality_metrics']['harmonic_count'] for s in sample1_segments[:min_segments]]
    sample1_noise_ratio = [s['noise_regions']['noise_ratio'] * 100 for s in sample1_segments[:min_segments]]
    sample1_thd = [s['quality_metrics']['thd_db'] for s in sample1_segments[:min_segments]]
    
    # 样本2数据
    sample2_snr = [s['quality_metrics']['snr_db'] for s in sample2_segments[:min_segments]]
    sample2_harmonics = [s['quality_metrics']['harmonic_count'] for s in sample2_segments[:min_segments]]
    sample2_noise_ratio = [s['noise_regions']['noise_ratio'] * 100 for s in sample2_segments[:min_segments]]
    sample2_thd = [s['quality_metrics']['thd_db'] for s in sample2_segments[:min_segments]]
    
    # 1. SNR对比 (左上)
    ax1 = plt.subplot(6, 2, 1)
    ax1.plot(segments, sample1_snr, 'b-o', markersize=3, alpha=0.7, label=sample1_data['sample_name'])
    ax1.plot(segments, sample2_snr, 'r-s', markersize=3, alpha=0.7, label=sample2_data['sample_name'])
    ax1.set_title('信噪比对比 (SNR)', fontweight='bold')
    ax1.set_xlabel('频段索引')
    ax1.set_ylabel('SNR (dB)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=15, color='orange', linestyle='--', alpha=0.5, label='良好阈值')
    
    # 2. SNR频率分布 (右上)
    ax2 = plt.subplot(6, 2, 2)
    scatter1 = ax2.scatter(freqs, sample1_snr, c='blue', s=20, alpha=0.6, label=sample1_data['sample_name'])
    scatter2 = ax2.scatter(freqs, sample2_snr, c='red', s=20, alpha=0.6, label=sample2_data['sample_name'])
    ax2.set_title('SNR vs 频率分布', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('SNR (dB)')
    ax2.set_xscale('log')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 谐波数量对比 (左中上)
    ax3 = plt.subplot(6, 2, 3)
    ax3.plot(segments, sample1_harmonics, 'b-o', markersize=3, alpha=0.7, label=sample1_data['sample_name'])
    ax3.plot(segments, sample2_harmonics, 'r-s', markersize=3, alpha=0.7, label=sample2_data['sample_name'])
    ax3.set_title('检测到的谐波数量对比', fontweight='bold')
    ax3.set_xlabel('频段索引')
    ax3.set_ylabel('谐波数量')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 谐波数量频率分布 (右中上)
    ax4 = plt.subplot(6, 2, 4)
    scatter3 = ax4.scatter(freqs, sample1_harmonics, c='blue', s=20, alpha=0.6, label=sample1_data['sample_name'])
    scatter4 = ax4.scatter(freqs, sample2_harmonics, c='red', s=20, alpha=0.6, label=sample2_data['sample_name'])
    ax4.set_title('谐波数量 vs 频率分布', fontweight='bold')
    ax4.set_xlabel('频率 (Hz)')
    ax4.set_ylabel('谐波数量')
    ax4.set_xscale('log')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 噪声比例对比 (左中下)
    ax5 = plt.subplot(6, 2, 5)
    ax5.plot(segments, sample1_noise_ratio, 'b-o', markersize=3, alpha=0.7, label=sample1_data['sample_name'])
    ax5.plot(segments, sample2_noise_ratio, 'r-s', markersize=3, alpha=0.7, label=sample2_data['sample_name'])
    ax5.set_title('噪声能量比例对比', fontweight='bold')
    ax5.set_xlabel('频段索引')
    ax5.set_ylabel('噪声比例 (%)')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    ax5.axhline(y=10, color='orange', linestyle='--', alpha=0.5, label='警告阈值')
    
    # 6. 噪声比例频率分布 (右中下)
    ax6 = plt.subplot(6, 2, 6)
    scatter5 = ax6.scatter(freqs, sample1_noise_ratio, c='blue', s=20, alpha=0.6, label=sample1_data['sample_name'])
    scatter6 = ax6.scatter(freqs, sample2_noise_ratio, c='red', s=20, alpha=0.6, label=sample2_data['sample_name'])
    ax6.set_title('噪声比例 vs 频率分布', fontweight='bold')
    ax6.set_xlabel('频率 (Hz)')
    ax6.set_ylabel('噪声比例 (%)')
    ax6.set_xscale('log')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 7. THD对比 (左下上)
    ax7 = plt.subplot(6, 2, 7)
    # 过滤有效的THD值
    valid_thd1_mask = np.array(sample1_thd) > -100
    valid_thd2_mask = np.array(sample2_thd) > -100
    
    if np.any(valid_thd1_mask):
        valid_segments1 = np.array(segments)[valid_thd1_mask]
        valid_thd1 = np.array(sample1_thd)[valid_thd1_mask]
        ax7.plot(valid_segments1, valid_thd1, 'b-o', markersize=3, alpha=0.7, label=sample1_data['sample_name'])
    
    if np.any(valid_thd2_mask):
        valid_segments2 = np.array(segments)[valid_thd2_mask]
        valid_thd2 = np.array(sample2_thd)[valid_thd2_mask]
        ax7.plot(valid_segments2, valid_thd2, 'r-s', markersize=3, alpha=0.7, label=sample2_data['sample_name'])
    
    ax7.set_title('总谐波失真对比 (THD)', fontweight='bold')
    ax7.set_xlabel('频段索引')
    ax7.set_ylabel('THD (dB)')
    ax7.legend()
    ax7.grid(True, alpha=0.3)
    
    # 8. 综合质量评分对比 (右下上)
    ax8 = plt.subplot(6, 2, 8)
    
    # 计算综合评分
    def calculate_composite_score(snr_values, harmonic_counts, noise_ratios, thd_values):
        snr_norm = np.clip(np.array(snr_values) / 50.0, 0, 1)
        noise_norm = np.clip(1 - np.array(noise_ratios) / 20.0, 0, 1)
        harmonic_norm = np.clip(np.array(harmonic_counts) / 10.0, 0, 1)
        thd_norm = np.clip(1 - (np.array(thd_values) + 50) / 50.0, 0, 1)
        
        return 0.4 * snr_norm + 0.3 * noise_norm + 0.2 * harmonic_norm + 0.1 * thd_norm
    
    sample1_scores = calculate_composite_score(sample1_snr, sample1_harmonics, sample1_noise_ratio, sample1_thd)
    sample2_scores = calculate_composite_score(sample2_snr, sample2_harmonics, sample2_noise_ratio, sample2_thd)
    
    ax8.plot(segments, sample1_scores, 'b-o', markersize=3, alpha=0.7, label=sample1_data['sample_name'])
    ax8.plot(segments, sample2_scores, 'r-s', markersize=3, alpha=0.7, label=sample2_data['sample_name'])
    ax8.set_title('综合质量评分对比', fontweight='bold')
    ax8.set_xlabel('频段索引')
    ax8.set_ylabel('综合评分 (0-1)')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    ax8.axhline(y=0.7, color='green', linestyle='--', alpha=0.5, label='优秀')
    ax8.axhline(y=0.5, color='orange', linestyle='--', alpha=0.5, label='良好')
    ax8.axhline(y=0.3, color='red', linestyle='--', alpha=0.5, label='较差')
    
    # 9. 差异分析热力图 (下方跨两列)
    ax9 = plt.subplot(6, 1, 5)
    
    # 计算差异矩阵
    diff_snr = np.array(sample2_snr) - np.array(sample1_snr)
    diff_harmonics = np.array(sample2_harmonics) - np.array(sample1_harmonics)
    diff_noise = np.array(sample2_noise_ratio) - np.array(sample1_noise_ratio)
    diff_scores = sample2_scores - sample1_scores
    
    diff_matrix = np.array([diff_snr, diff_harmonics, diff_noise, diff_scores])
    
    im = ax9.imshow(diff_matrix, cmap='RdBu_r', aspect='auto', interpolation='nearest')
    ax9.set_title(f'{sample2_data["sample_name"]} - {sample1_data["sample_name"]} 差异分析', fontweight='bold')
    ax9.set_xlabel('频段索引')
    ax9.set_ylabel('差异指标')
    ax9.set_yticks(range(4))
    ax9.set_yticklabels(['SNR差异', '谐波数差异', '噪声比例差异', '综合评分差异'])
    
    # 设置x轴标签
    x_ticks = range(0, len(segments), 10)
    ax9.set_xticks(x_ticks)
    ax9.set_xticklabels([f'{segments[i]}\n{freqs[i]:.0f}Hz' for i in x_ticks], rotation=45)
    
    plt.colorbar(im, ax=ax9, label='差异值')
    
    # 10. 统计摘要 (底部)
    ax10 = plt.subplot(6, 1, 6)
    ax10.axis('off')
    
    # 计算统计摘要
    summary_text = f"📊 最终版本93段对比统计摘要\n"
    summary_text += f"{'='*60}\n"
    summary_text += f"{sample1_data['sample_name']}:\n"
    summary_text += f"  平均SNR: {np.mean(sample1_snr):.1f}dB, 平均谐波数: {np.mean(sample1_harmonics):.1f}个\n"
    summary_text += f"  平均噪声比例: {np.mean(sample1_noise_ratio):.1f}%, 平均综合评分: {np.mean(sample1_scores):.3f}\n"
    summary_text += f"  总检测谐波: {np.sum(sample1_harmonics)}个\n\n"
    
    summary_text += f"{sample2_data['sample_name']}:\n"
    summary_text += f"  平均SNR: {np.mean(sample2_snr):.1f}dB, 平均谐波数: {np.mean(sample2_harmonics):.1f}个\n"
    summary_text += f"  平均噪声比例: {np.mean(sample2_noise_ratio):.1f}%, 平均综合评分: {np.mean(sample2_scores):.3f}\n"
    summary_text += f"  总检测谐波: {np.sum(sample2_harmonics)}个\n\n"
    
    summary_text += f"差异分析:\n"
    summary_text += f"  SNR差异: {np.mean(diff_snr):+.1f}dB, 谐波数差异: {np.mean(diff_harmonics):+.1f}个\n"
    summary_text += f"  噪声比例差异: {np.mean(diff_noise):+.1f}%, 综合评分差异: {np.mean(diff_scores):+.3f}\n"
    summary_text += f"  相似度: {1 - np.mean(np.abs(diff_scores)):.3f} (0-1, 越高越相似)"
    
    ax10.text(0.05, 0.95, summary_text, transform=ax10.transAxes, 
             verticalalignment='top', fontsize=11, fontfamily='monospace',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'final_93_segments_comparison_optimized.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 最终对比可视化已保存: {filename}")
    plt.close()

def create_detailed_statistics_comparison(all_samples_analysis):
    """创建详细的统计对比"""
    
    print(f"\n📊 详细统计对比分析:")
    print("="*60)
    
    sample1_data = all_samples_analysis[0]
    sample2_data = all_samples_analysis[1]
    
    sample1_segments = sample1_data['segment_analyses']
    sample2_segments = sample2_data['segment_analyses']
    
    print(f"{sample1_data['sample_name']}:")
    print(f"  总频段数: {len(sample1_segments)}")
    
    sample1_harmonics = [s['quality_metrics']['harmonic_count'] for s in sample1_segments]
    sample1_snr = [s['quality_metrics']['snr_db'] for s in sample1_segments]
    sample1_noise = [s['noise_regions']['noise_ratio'] * 100 for s in sample1_segments]
    
    print(f"  总检测谐波: {np.sum(sample1_harmonics)}个")
    print(f"  平均每段谐波: {np.mean(sample1_harmonics):.1f}个")
    print(f"  平均SNR: {np.mean(sample1_snr):.1f}dB")
    print(f"  平均噪声比例: {np.mean(sample1_noise):.1f}%")
    
    print(f"\n{sample2_data['sample_name']}:")
    print(f"  总频段数: {len(sample2_segments)}")
    
    sample2_harmonics = [s['quality_metrics']['harmonic_count'] for s in sample2_segments]
    sample2_snr = [s['quality_metrics']['snr_db'] for s in sample2_segments]
    sample2_noise = [s['noise_regions']['noise_ratio'] * 100 for s in sample2_segments]
    
    print(f"  总检测谐波: {np.sum(sample2_harmonics)}个")
    print(f"  平均每段谐波: {np.mean(sample2_harmonics):.1f}个")
    print(f"  平均SNR: {np.mean(sample2_snr):.1f}dB")
    print(f"  平均噪声比例: {np.mean(sample2_noise):.1f}%")
    
    print(f"\n对比分析:")
    print(f"  谐波数差异: {np.sum(sample2_harmonics) - np.sum(sample1_harmonics):+d}个")
    print(f"  平均SNR差异: {np.mean(sample2_snr) - np.mean(sample1_snr):+.1f}dB")
    print(f"  平均噪声差异: {np.mean(sample2_noise) - np.mean(sample1_noise):+.1f}%")
    
    # 相关性分析
    snr_corr = np.corrcoef(sample1_snr, sample2_snr)[0, 1]
    harmonic_corr = np.corrcoef(sample1_harmonics, sample2_harmonics)[0, 1]
    noise_corr = np.corrcoef(sample1_noise, sample2_noise)[0, 1]
    
    print(f"\n相关性分析:")
    print(f"  SNR相关性: {snr_corr:.3f}")
    print(f"  谐波数相关性: {harmonic_corr:.3f}")
    print(f"  噪声比例相关性: {noise_corr:.3f}")
    print(f"  平均相关性: {(snr_corr + harmonic_corr + noise_corr) / 3:.3f}")

if __name__ == "__main__":
    final_93_segments_comparison()
