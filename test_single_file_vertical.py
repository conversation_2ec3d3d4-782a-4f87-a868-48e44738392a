#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个文件的更新后竖线检测
"""

import os
import sys
sys.path.append('.')

from final_audio_detection_system.segment_vertical_line_detector import SegmentVerticalLineDetector
from final_audio_detection_system.integrated_vertical_line_detector import IntegratedVerticalLineDetector

def test_single_file():
    """测试单个文件"""
    print("🔍 测试单个文件的更新后竖线检测")
    print("="*60)
    
    # 测试文件
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    if not os.path.exists(audio_path):
        print(f"❌ 文件不存在: {audio_path}")
        return
    
    filename = os.path.basename(audio_path)
    
    print(f"🎵 测试文件: {filename}")
    print("="*60)
    
    try:
        # 1. 测试分段竖线检测器
        print(f"\n📊 1. 测试分段竖线检测器")
        print("-" * 50)
        
        segment_detector = SegmentVerticalLineDetector()
        segment_result = segment_detector.detect_vertical_lines_in_segments(audio_path)
        
        if segment_result:
            print(f"✅ 分段竖线检测成功")
            print(f"   检测到 {segment_result.get('total_vertical_lines', 0)} 条竖线")
            print(f"   异常频段: {segment_result.get('anomalous_segments', 0)}")
            print(f"   总频段数: {segment_result.get('total_segments', 0)}")
            print(f"   成功分析频段: {segment_result.get('successful_segments', 0)}")
        else:
            print(f"❌ 分段竖线检测失败")
        
        # 2. 测试集成竖线检测器
        print(f"\n📊 2. 测试集成竖线检测器")
        print("-" * 50)
        
        integrated_detector = IntegratedVerticalLineDetector()
        integrated_result = integrated_detector.detect_integrated_anomalies(audio_path)
        
        if integrated_result:
            print(f"✅ 集成竖线检测成功")
            print(f"   检测到 {integrated_result.get('total_vertical_lines', 0)} 条竖线")
            print(f"   异常频段: {integrated_result.get('anomalous_segments', 0)}")
            print(f"   总频段数: {integrated_result.get('total_segments', 0)}")
            print(f"   最大线强度: {integrated_result.get('max_line_strength', 0):.3f}")
            print(f"   总线强度: {integrated_result.get('total_line_strength', 0):.3f}")
            print(f"   频段异常率: {integrated_result.get('segment_anomaly_ratio', 0):.3f}")
        else:
            print(f"❌ 集成竖线检测失败")
        
        # 3. 对比优化效果
        print(f"\n📊 3. 优化效果验证")
        print("-" * 50)
        
        from freq_split_optimized import split_freq_steps_optimized
        
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path,
            start_freq=100,
            stop_freq=20000,
            octave=12,
            min_cycles=10,
            min_duration=153,
            fs=48000,
            search_window_start=0.1,
            search_window_end=1.5,
            correlation_length=1.0,
            plot=False,
            debug=False
        )
        
        print(f"✅ 优化频段分割结果:")
        print(f"   频段数量: {len(step_boundaries)}")
        print(f"   频率点数: {len(freq_table)}")
        print(f"   开始时间: {alignment_info.get('start_offset', 0):.3f}s")
        print(f"   相关性: {alignment_info.get('correlation_score', 0):.3f}")
        
        if alignment_info.get('alignment_quality'):
            quality = alignment_info['alignment_quality']
            print(f"   整体质量: {quality.get('overall_quality', 'unknown')}")
            print(f"   综合评分: {quality.get('composite_score', 0):.3f}")
            print(f"   时域相关性: {quality.get('time_correlation', 0):.3f}")
            print(f"   频域相似性: {quality.get('freq_similarity', 0):.3f}")
        
        print(f"\n🎯 测试总结:")
        print(f"   ✅ 优化的频段分割已成功应用到竖线检测中")
        print(f"   ✅ 峰值选择算法提供了更准确的对齐")
        print(f"   ✅ 竖线检测器能够正常工作")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_single_file()
