#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用音频频谱分析器
支持任意音频文件输入，生成对数频谱分析
使用方法: python universal_spectrum_analyzer.py <音频文件路径>
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from concurrent.futures import ProcessPoolExecutor
import time
from pathlib import Path
import argparse

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'harmonic_detection_system'))

try:
    from freq_split_optimized import split_freq_steps_optimized
    # 导入正确的动态噪声分析函数
    from harmonic_detector_api import estimate_dynamic_noise_for_segment
except ImportError as e:
    print(f"警告: 无法导入必要模块: {e}")
    print("请确保harmonic_detection_system目录在当前路径下")
    sys.exit(1)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_dynamic_noise_curve_exact(freqs, power, fundamental_freq):
    """
    计算动态噪声阈值曲线 - 完全基于harmonic_detector_api.estimate_dynamic_noise_for_segment
    但返回完整的动态噪声曲线用于绘制
    """
    from scipy.signal import savgol_filter
    
    # 滑动窗口参数 - 与harmonic_detector_api完全一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置 - 与原函数完全一致
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz (扩展到24kHz)
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= 24000:  # 扩展到24kHz
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计 - 与原函数完全一致
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None
    
    # 平滑噪声曲线 - 与原函数完全一致
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 插值到完整频率范围 - 用于绘制动态曲线
    dynamic_noise_curve = np.interp(freqs, window_centers, smoothed_noise)
    
    # 同时计算全局噪声底噪 - 与原函数一致
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    
    return dynamic_noise_curve, global_noise_floor_db

def hz_to_mel(hz):
    """
    将频率从Hz转换为梅尔刻度
    """
    return 2595 * np.log10(1 + hz / 700)

def mel_to_hz(mel):
    """
    将梅尔刻度转换为Hz
    """
    return 700 * (10**(mel / 2595) - 1)

def create_harmonic_mask(freqs, fundamental_freq):
    """
    创建主频和谐波的掩码
    """
    exclude_mask = np.zeros(len(freqs), dtype=bool)

    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude

    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= 24000:  # 扩展到24kHz
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude

    return exclude_mask

def calculate_spectrum_threshold_difference(power_db, dynamic_noise_curve):
    """
    计算频谱和动态阈值的差值绝对值，体现波动程度
    """
    if dynamic_noise_curve is None:
        return None

    # 将动态噪声阈值转换为dB（如果还不是）
    if np.max(dynamic_noise_curve) > 100:  # 判断是否已经是dB
        threshold_db = 10 * np.log10(dynamic_noise_curve + 1e-12)
    else:
        threshold_db = dynamic_noise_curve

    # 计算频谱和阈值的差值绝对值
    difference_curve = np.abs(power_db - threshold_db)

    # 平滑差值曲线
    if len(difference_curve) > 5:
        try:
            from scipy.signal import savgol_filter
            difference_curve = savgol_filter(difference_curve, min(len(difference_curve), 21), 3)
        except:
            pass

    return difference_curve

def analyze_single_segment_universal(args):
    """
    分析单个频段并生成频谱图像 - 通用版本，支持对数和梅尔刻度，支持掩码和差值可视化
    用于多进程处理
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, output_dir, use_mel_scale, start_freq, stop_freq, show_masked, show_diff = args
    
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = audio_data[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"    警告: 段 {seg_idx} 音频长度为0")
            return False
        
        # 去除开头和结尾8% - 与harmonic_detector_api_fast.py保持一致
        trim_length = int(len(segment_audio) * 0.08)
        if len(segment_audio) > 2 * trim_length:
            segment_audio = segment_audio[trim_length:-trim_length]
            actual_start_time = start_time + trim_length / sr
            actual_end_time = end_time - trim_length / sr
        else:
            actual_start_time = start_time
            actual_end_time = end_time
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与harmonic_detector_api_fast.py完全一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到24kHz
        freq_mask = positive_freqs <= 24000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB - 对数频谱
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频 - 与harmonic_detector_api_fast.py一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 计算动态噪声阈值曲线 - 完全基于harmonic_detector_api方法
        dynamic_noise_curve, global_noise_floor_db = calculate_dynamic_noise_curve_exact(display_freqs, display_power, fundamental_freq)

        # 同时调用原始函数进行验证
        noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)

        # 创建掩码频谱（如果需要）
        masked_power_db = None
        if show_masked:
            harmonic_mask = create_harmonic_mask(display_freqs, fundamental_freq)
            masked_power = display_power.copy()
            masked_power[harmonic_mask] = np.nan  # 将掩码区域设为NaN
            masked_power_db = 10 * np.log10(masked_power + 1e-12)

        # 计算频谱和阈值的差值曲线（如果需要）
        difference_curve = None
        if show_diff:
            difference_curve = calculate_spectrum_threshold_difference(power_db, dynamic_noise_curve)
        
        # 创建频谱可视化图像 - 支持对数和梅尔刻度，差值曲线单独子图
        if show_diff and difference_curve is not None:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), height_ratios=[3, 1])
            ax = ax1  # 主频谱图
        else:
            fig, ax = plt.subplots(1, 1, figsize=(16, 10))

        if use_mel_scale:
            # 梅尔刻度绘图
            mel_freqs = hz_to_mel(display_freqs)

            # 绘制频谱 - 黑色线
            ax.plot(mel_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='原始频谱')

            # 绘制掩码频谱（如果需要）
            if show_masked and masked_power_db is not None:
                ax.plot(mel_freqs, masked_power_db, color='green', linewidth=1.0, alpha=0.9,
                       label='掩码后频谱 (去除主频+谐波)')

            # 绘制动态噪声阈值曲线 - 红色线
            if dynamic_noise_curve is not None:
                ax.plot(mel_freqs, dynamic_noise_curve, color='red', linestyle='-', linewidth=1.5, alpha=0.9,
                       label=f'动态噪声阈值 (全局底噪: {global_noise_floor_db:.1f}dB)')

            # 差值曲线将在单独的子图中绘制

            # 验证一致性：绘制原始函数的全局噪声底噪线作为参考
            if noise_analysis:
                original_global_noise = noise_analysis['global_noise_floor_db']
                ax.axhline(y=original_global_noise, color='orange', linestyle='--', linewidth=1.0, alpha=0.7,
                          label=f'原始全局底噪: {original_global_noise:.1f}dB')

            # 标记主频 - 蓝色圆点
            fundamental_power_db = 10 * np.log10(display_power[np.argmin(np.abs(display_freqs - fundamental_freq))] + 1e-12)
            fundamental_mel = hz_to_mel(fundamental_freq)
            ax.plot(fundamental_mel, fundamental_power_db, 'o', color='blue', markersize=8,
                   markeredgecolor='darkblue', markeredgewidth=1.5, label=f'主频 {fundamental_freq:.1f}Hz')

            # 设置梅尔刻度范围 - 使用输入的频率范围
            min_freq_display = max(start_freq, np.min(display_freqs[display_freqs > 0]))
            max_freq_display = min(stop_freq, np.max(display_freqs))
            min_mel = hz_to_mel(min_freq_display)
            max_mel = hz_to_mel(max_freq_display)
            ax.set_xlim(min_mel, max_mel)

            # 设置梅尔刻度的刻度标签
            mel_ticks = np.linspace(min_mel, max_mel, 10)
            hz_ticks = mel_to_hz(mel_ticks)
            ax.set_xticks(mel_ticks)
            ax.set_xticklabels([f'{hz:.0f}' for hz in hz_ticks])

            # 设置图形属性
            ax.set_xlabel('频率 (Hz) - 梅尔刻度', fontsize=14, fontweight='bold')
            ax.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            title_suffix = ""
            if show_masked:
                title_suffix += " + 掩码"
            if show_diff:
                title_suffix += " + 波动差值"
            ax.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz 梅尔频谱分析{title_suffix}\n'
                        f'有效时间: {actual_start_time:.3f}s - {actual_end_time:.3f}s (去除首尾8%)',
                        fontsize=14, fontweight='bold')

        else:
            # 对数刻度绘图
            # 绘制频谱 - 黑色线
            ax.semilogx(display_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='原始频谱')

            # 绘制掩码频谱（如果需要）
            if show_masked and masked_power_db is not None:
                ax.semilogx(display_freqs, masked_power_db, color='green', linewidth=1.0, alpha=0.9,
                           label='掩码后频谱 (去除主频+谐波)')

            # 绘制动态噪声阈值曲线 - 红色线
            if dynamic_noise_curve is not None:
                ax.semilogx(display_freqs, dynamic_noise_curve, color='red', linestyle='-', linewidth=1.5, alpha=0.9,
                           label=f'动态噪声阈值 (全局底噪: {global_noise_floor_db:.1f}dB)')

            # 差值曲线将在单独的子图中绘制

            # 验证一致性：绘制原始函数的全局噪声底噪线作为参考
            if noise_analysis:
                original_global_noise = noise_analysis['global_noise_floor_db']
                ax.axhline(y=original_global_noise, color='orange', linestyle='--', linewidth=1.0, alpha=0.7,
                          label=f'原始全局底噪: {original_global_noise:.1f}dB')

            # 标记主频 - 蓝色圆点
            fundamental_power_db = 10 * np.log10(display_power[np.argmin(np.abs(display_freqs - fundamental_freq))] + 1e-12)
            ax.plot(fundamental_freq, fundamental_power_db, 'o', color='blue', markersize=8,
                   markeredgecolor='darkblue', markeredgewidth=1.5, label=f'主频 {fundamental_freq:.1f}Hz')

            # 设置对数频率范围 - 使用输入的频率范围
            min_freq_display = max(start_freq, np.min(display_freqs[display_freqs > 0]))  # 避免0频率
            max_freq_display = min(stop_freq, np.max(display_freqs))
            ax.set_xlim(min_freq_display, max_freq_display)

            # 设置图形属性
            ax.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')
            ax.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
            title_suffix = ""
            if show_masked:
                title_suffix += " + 掩码"
            if show_diff:
                title_suffix += " + 波动差值"
            ax.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz 对数频谱分析{title_suffix}\n'
                        f'有效时间: {actual_start_time:.3f}s - {actual_end_time:.3f}s (去除首尾8%)',
                        fontsize=14, fontweight='bold')

        ax.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
        ax.legend(fontsize=11, loc='upper right', framealpha=0.9)

        # 固定纵坐标范围 -80~40dB
        ax.set_ylim(-80, 40)

        # 绘制差值曲线子图（如果需要）
        if show_diff and difference_curve is not None:
            if use_mel_scale:
                mel_freqs = hz_to_mel(display_freqs)
                ax2.plot(mel_freqs, difference_curve, color='purple', linewidth=2.0, alpha=0.9, label='波动程度')
                ax2.set_xlabel('频率 (Hz) - 梅尔刻度', fontsize=12, fontweight='bold')

                # 设置梅尔刻度的刻度标签
                min_freq_display = max(start_freq, np.min(display_freqs[display_freqs > 0]))
                max_freq_display = min(stop_freq, np.max(display_freqs))
                min_mel = hz_to_mel(min_freq_display)
                max_mel = hz_to_mel(max_freq_display)
                ax2.set_xlim(min_mel, max_mel)

                mel_ticks = np.linspace(min_mel, max_mel, 8)
                hz_ticks = mel_to_hz(mel_ticks)
                ax2.set_xticks(mel_ticks)
                ax2.set_xticklabels([f'{hz:.0f}' for hz in hz_ticks])
            else:
                ax2.semilogx(display_freqs, difference_curve, color='purple', linewidth=2.0, alpha=0.9, label='波动程度')
                ax2.set_xlabel('频率 (Hz)', fontsize=12, fontweight='bold')

                # 设置对数频率范围
                min_freq_display = max(start_freq, np.min(display_freqs[display_freqs > 0]))
                max_freq_display = min(stop_freq, np.max(display_freqs))
                ax2.set_xlim(min_freq_display, max_freq_display)

            ax2.set_ylabel('|频谱-阈值| (dB)', fontsize=12, fontweight='bold')
            ax2.set_title(f'波动程度分析 (最大差值: {np.max(difference_curve):.1f}dB, 平均差值: {np.mean(difference_curve):.1f}dB)',
                         fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
            ax2.legend(fontsize=10, loc='upper right', framealpha=0.9)

            # 自适应纵坐标范围
            y_margin = np.max(difference_curve) * 0.1
            ax2.set_ylim(0, np.max(difference_curve) + y_margin)

            # 调整子图间距
            plt.tight_layout()

            # 返回差值统计信息
            return True, {
                'max_diff': np.max(difference_curve),
                'min_diff': np.min(difference_curve),
                'mean_diff': np.mean(difference_curve),
                'std_diff': np.std(difference_curve)
            }
        
        # 保存图像
        scale_suffix = "_mel" if use_mel_scale else "_log"
        mask_suffix = "_masked" if show_masked else ""
        diff_suffix = "_diff" if show_diff else ""
        output_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz{scale_suffix}{mask_suffix}{diff_suffix}.png')

        # 如果没有差值子图，需要调用tight_layout
        if not (show_diff and difference_curve is not None):
            plt.tight_layout()

        plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"    保存: {output_filename}")

        # 如果有差值统计，返回统计信息
        if show_diff and difference_curve is not None:
            return True, {
                'max_diff': np.max(difference_curve),
                'min_diff': np.min(difference_curve),
                'mean_diff': np.mean(difference_curve),
                'std_diff': np.std(difference_curve)
            }
        else:
            return True, None

    except Exception as e:
        print(f"    错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """
    主函数：通用音频频谱分析器
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='通用音频频谱分析器')
    parser.add_argument('audio_file', nargs='?',
                       default='test20250722/琴身内部异物1.1.wav',
                       help='音频文件路径')
    parser.add_argument('--workers', '-w', type=int, default=8,
                       help='并行进程数 (默认: 8)')
    parser.add_argument('--freq-range', nargs=2, type=float,
                       default=[100, 20000], metavar=('START', 'STOP'),
                       help='频率分析范围 (默认: 100 20000)')
    parser.add_argument('--mel-scale', action='store_true',
                       help='使用梅尔刻度而非对数刻度')
    parser.add_argument('--show-masked', action='store_true',
                       help='显示掩码去除主频和谐波后的频谱')
    parser.add_argument('--show-diff', action='store_true',
                       help='显示频谱和动态阈值的差值绝对值曲线（波动程度）')

    args = parser.parse_args()
    audio_path = args.audio_file
    num_workers = args.workers
    start_freq, stop_freq = args.freq_range
    use_mel_scale = args.mel_scale
    show_masked = args.show_masked
    show_diff = args.show_diff

    scale_type = "梅尔刻度" if use_mel_scale else "对数刻度"
    print(f"通用音频频谱分析器")
    print(f"音频文件: {audio_path}")
    print(f"分析参数: {start_freq}Hz - {stop_freq}Hz, {num_workers}进程, {scale_type}")
    print("="*70)

    # 检查文件是否存在
    if not os.path.exists(audio_path):
        print(f"音频文件不存在: {audio_path}")
        print(f"使用方法: python {os.path.basename(__file__)} <音频文件路径>")
        print(f"示例: python {os.path.basename(__file__)} my_audio.wav")
        print(f"梅尔刻度: python {os.path.basename(__file__)} my_audio.wav --mel-scale")
        return

    # 创建输出目录 - 基于输入文件名和刻度类型
    audio_name = os.path.splitext(os.path.basename(audio_path))[0]
    scale_suffix = "_梅尔频谱分析" if use_mel_scale else "_频谱分析"
    output_dir = f"{audio_name}{scale_suffix}"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    start_time = time.time()

    try:
        # 获取频段分割 - 与harmonic_detector_api_fast.py完全一致
        print("进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=start_freq, stop_freq=stop_freq, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"频段分割完成，共{len(step_boundaries)}段")

        # 加载音频
        print("加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000

        print(f"音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")

        # 准备多进程任务参数
        print(f"准备多进程分析...")
        tasks = []
        for seg_idx in range(len(step_boundaries)):
            start_time_seg, end_time_seg = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            tasks.append((
                seg_idx,
                start_time_seg,
                end_time_seg,
                expected_freq,
                y,  # 共享音频数据
                sr,
                output_dir,
                use_mel_scale,
                start_freq,
                stop_freq,
                show_masked,
                show_diff
            ))

        # 多进程并行处理
        print(f"使用{num_workers}个进程并行分析{len(step_boundaries)}个频段...")

        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            results = list(executor.map(analyze_single_segment_universal, tasks))

        # 统计结果和差值信息
        successful_count = 0
        failed_count = 0
        diff_stats_list = []

        for result in results:
            if isinstance(result, tuple):
                success, diff_stats = result
                if success:
                    successful_count += 1
                    if diff_stats:
                        diff_stats_list.append(diff_stats)
                else:
                    failed_count += 1
            else:
                # 兼容旧版本返回值
                if result:
                    successful_count += 1
                else:
                    failed_count += 1

        end_time = time.time()
        processing_time = end_time - start_time

        print("\n" + "="*70)
        print("频谱分析完成!")
        print(f"  音频文件: {os.path.basename(audio_path)}")
        print(f"  成功分析: {successful_count}个频段")
        print(f"  失败: {failed_count}个频段")
        print(f"  总耗时: {processing_time:.1f}秒")
        print(f"  平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  输出目录: {output_dir}")
        print(f"  使用进程数: {num_workers}")

        # 创建汇总信息文件
        create_summary(output_dir, audio_path, step_boundaries, freq_table,
                      successful_count, failed_count, processing_time, num_workers,
                      start_freq, stop_freq, use_mel_scale, show_diff, diff_stats_list)

    except Exception as e:
        print(f"分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

def create_summary(output_dir, audio_path, step_boundaries, freq_table,
                  successful_count, failed_count, processing_time, num_workers,
                  start_freq, stop_freq, use_mel_scale, show_diff=False, diff_stats_list=None):
    """
    创建分析汇总信息文件
    """
    summary_file = os.path.join(output_dir, "频谱分析汇总.txt")

    audio_filename = os.path.basename(audio_path)
    scale_method = "梅尔刻度" if use_mel_scale else "对数刻度 (semilogx)"

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"{audio_filename} - 频谱分析汇总\n")
        f.write("="*60 + "\n\n")

        f.write(f"音频文件: {audio_path}\n")
        f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析方法: {scale_method}\n")
        f.write(f"频率范围: {start_freq}Hz - {stop_freq}Hz\n")
        f.write(f"显示范围: 10Hz - 24000Hz\n")
        f.write(f"纵坐标范围: -80dB - 40dB\n")
        f.write(f"数据处理: 去除每段首尾8%\n")
        f.write(f"噪声阈值: 使用harmonic_detector_api.estimate_dynamic_noise_for_segment\n")
        f.write(f"多进程: {num_workers}个工作进程\n")
        f.write(f"参考系统: harmonic_detection_system/harmonic_detector_api_fast.py\n")
        f.write(f"总频段数: {len(step_boundaries)}\n")
        f.write(f"成功分析: {successful_count}个频段\n")
        f.write(f"失败: {failed_count}个频段\n")
        f.write(f"处理耗时: {processing_time:.1f}秒\n\n")

        f.write("可视化内容:\n")
        f.write("- 频谱曲线 (黑色线)\n")
        f.write("- 动态噪声阈值曲线 (红色线)\n")
        f.write("- 原始全局底噪线 (橙色虚线)\n")
        f.write("- 主频标记 (蓝色圆点)\n")
        if show_diff:
            f.write("- 差值曲线 (紫色线，单独子图)\n")
        f.write("\n")

        f.write("技术细节:\n")
        f.write("- FFT大小: 131072点 (128k)\n")
        f.write("- 窗函数: Hanning窗\n")
        f.write("- 频率轴: 对数刻度\n")
        f.write("- 图像尺寸: 16×10英寸, 200 DPI\n\n")

        f.write("频段详情:\n")
        f.write("-" * 70 + "\n")
        f.write("段号  期望频率(Hz)  开始时间(s)  结束时间(s)  有效时长(s)  去除时长(s)\n")
        f.write("-" * 70 + "\n")

        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = freq_table[i]
            total_duration = end_time - start_time
            trim_duration = total_duration * 0.16  # 首尾各8%
            effective_duration = total_duration - trim_duration
            f.write(f"{i:2d}    {expected_freq:8.1f}    {start_time:8.3f}    {end_time:8.3f}    "
                   f"{effective_duration:8.3f}    {trim_duration:8.3f}\n")

        # 添加差值统计信息
        if show_diff and diff_stats_list:
            f.write(f"\n差值统计信息:\n")
            f.write(f"----------------------------------------------------------------------\n")

            all_max_diffs = [stats['max_diff'] for stats in diff_stats_list]
            all_min_diffs = [stats['min_diff'] for stats in diff_stats_list]
            all_mean_diffs = [stats['mean_diff'] for stats in diff_stats_list]
            all_std_diffs = [stats['std_diff'] for stats in diff_stats_list]

            f.write(f"总体统计 (所有频段):\n")
            f.write(f"  平均差值最小值: {min(all_mean_diffs):.2f} dB\n")
            f.write(f"  平均差值最大值: {max(all_mean_diffs):.2f} dB\n")
            f.write(f"  平均差值均值: {np.mean(all_mean_diffs):.2f} dB\n")
            f.write(f"  平均差值标准差: {np.std(all_mean_diffs):.2f} dB\n")
            f.write(f"  最大差值范围: {min(all_max_diffs):.2f} - {max(all_max_diffs):.2f} dB\n")
            f.write(f"  最大差值均值: {np.mean(all_max_diffs):.2f} dB\n")

    print(f"汇总信息已保存: {summary_file}")

if __name__ == "__main__":
    main()
