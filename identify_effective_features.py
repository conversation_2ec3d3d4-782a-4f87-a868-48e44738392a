#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
识别有效特征：
只要在某个频段能够完全把至少一个负样本和所有正样本区分开，就是有效特征
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def identify_effective_features():
    """识别有效特征"""
    print("🔍 识别有效特征")
    print("="*70)
    print("标准：只要在某个频段能够完全把至少一个负样本和所有正样本区分开，就是有效特征")
    print("="*70)
    
    # 加载特征数据
    if not os.path.exists('comprehensive_features.csv'):
        print("❌ 请先运行 comprehensive_feature_analysis.py 生成特征数据")
        return
    
    df = pd.read_csv('comprehensive_features.csv')
    print(f"📊 加载特征数据: {len(df)}个频段样本")
    
    # 排除竖线检测特征和元信息
    exclude_features = [col for col in df.columns if col.startswith('vl_')] + \
                      ['label', 'folder', 'filename', 'segment_idx', 'start_time', 'end_time', 'duration', 'expected_freq']
    
    candidate_features = [col for col in df.columns if col not in exclude_features]
    
    print(f"📊 候选特征数: {len(candidate_features)}个")
    
    # 获取所有负样本文件
    neg_files = df[df['label'] == 'neg']['filename'].unique()
    print(f"📊 负样本文件: {len(neg_files)}个")
    for i, file in enumerate(neg_files, 1):
        print(f"  {i}. {file}")
    
    # 分析每个特征的有效性
    effective_features = {}
    feature_effectiveness = {}
    
    print(f"\n🔍 分析特征有效性...")
    print("="*70)
    
    for feature_idx, feature in enumerate(candidate_features):
        print(f"分析特征 {feature_idx+1}/{len(candidate_features)}: {feature}")
        
        feature_effective = False
        effective_segments = []
        effective_files = []
        
        # 检查每个频段
        for segment_idx in sorted(df['segment_idx'].unique()):
            segment_data = df[df['segment_idx'] == segment_idx]
            
            pos_data = segment_data[segment_data['label'] == 'pos']
            neg_data = segment_data[segment_data['label'] == 'neg']
            
            if len(pos_data) == 0 or len(neg_data) == 0:
                continue
            
            # 检查每个负样本文件
            for neg_file in neg_files:
                file_neg_data = neg_data[neg_data['filename'] == neg_file]
                
                if len(file_neg_data) == 0:
                    continue
                
                # 检查是否可以完全分离
                if can_completely_separate(pos_data, file_neg_data, feature):
                    feature_effective = True
                    effective_segments.append(segment_idx)
                    effective_files.append(neg_file)
        
        if feature_effective:
            effective_features[feature] = {
                'effective_segments': list(set(effective_segments)),
                'effective_files': list(set(effective_files)),
                'segment_count': len(set(effective_segments)),
                'file_count': len(set(effective_files))
            }
            
            print(f"  ✅ 有效特征")
            print(f"     有效频段: {len(set(effective_segments))}个")
            print(f"     可分离文件: {len(set(effective_files))}个")
        else:
            print(f"  ❌ 无效特征")
        
        feature_effectiveness[feature] = feature_effective
    
    # 生成分析报告
    generate_effectiveness_report(effective_features, neg_files, df)
    
    # 保存结果
    save_results(effective_features, feature_effectiveness)
    
    return effective_features

def can_completely_separate(pos_data, neg_data, feature):
    """检查是否可以完全分离"""
    try:
        pos_values = pos_data[feature].dropna()
        neg_values = neg_data[feature].dropna()
        
        if len(pos_values) == 0 or len(neg_values) == 0:
            return False
        
        # 计算统计量
        pos_mean, pos_std = np.mean(pos_values), np.std(pos_values)
        neg_mean, neg_std = np.mean(neg_values), np.std(neg_values)
        
        # 效应大小
        pooled_std = np.sqrt(((len(pos_values) - 1) * np.var(pos_values) + 
                             (len(neg_values) - 1) * np.var(neg_values)) / 
                            (len(pos_values) + len(neg_values) - 2))
        cohens_d = abs(pos_mean - neg_mean) / (pooled_std + 1e-12)
        
        # 重叠度计算
        pos_range = [pos_mean - 2*pos_std, pos_mean + 2*pos_std]
        neg_range = [neg_mean - 2*neg_std, neg_mean + 2*neg_std]
        
        # 检查是否有重叠
        overlap = max(0, min(pos_range[1], neg_range[1]) - max(pos_range[0], neg_range[0]))
        total_range = max(pos_range[1], neg_range[1]) - min(pos_range[0], neg_range[0])
        overlap_ratio = overlap / (total_range + 1e-12)
        
        # 完全分离的标准：Cohen's d > 2.0 且重叠率 < 0.1
        return cohens_d > 2.0 and overlap_ratio < 0.1
        
    except Exception as e:
        return False

def generate_effectiveness_report(effective_features, neg_files, df):
    """生成有效性报告"""
    print(f"\n📊 有效特征分析报告")
    print("="*70)
    
    total_features = len(effective_features)
    print(f"有效特征总数: {total_features}")
    
    if total_features == 0:
        print("❌ 没有找到有效特征")
        return
    
    # 按有效性排序
    sorted_features = sorted(effective_features.items(), 
                           key=lambda x: (x[1]['file_count'], x[1]['segment_count']), 
                           reverse=True)
    
    print(f"\n📊 有效特征详细列表:")
    print("-" * 70)
    
    for i, (feature, info) in enumerate(sorted_features, 1):
        print(f"{i:2d}. {feature}")
        print(f"    可分离文件数: {info['file_count']}/{len(neg_files)}")
        print(f"    有效频段数: {info['segment_count']}")
        print(f"    可分离文件: {', '.join(info['effective_files'])}")
        print(f"    有效频段: {info['effective_segments'][:5]}{'...' if len(info['effective_segments']) > 5 else ''}")
        print()
    
    # 按特征类型分组
    feature_groups = {
        'harmonic': [f for f in effective_features.keys() if 'harmonic' in f or 'thd' in f],
        'time_domain': [f for f in effective_features.keys() if f.startswith('td_')],
        'frequency_domain': [f for f in effective_features.keys() if f.startswith('fd_')],
        'spectral': [f for f in effective_features.keys() if f.startswith('spec_')],
        'statistical': [f for f in effective_features.keys() if f.startswith('stat_')],
        'energy': [f for f in effective_features.keys() if f.startswith('energy_')],
        'other': []
    }
    
    # 分类其他特征
    for feature in effective_features.keys():
        if not any(feature in group for group in feature_groups.values()):
            feature_groups['other'].append(feature)
    
    print(f"\n📊 按特征类型分组:")
    print("-" * 50)
    
    for group_name, features in feature_groups.items():
        if features:
            print(f"  {group_name.upper()}: {len(features)}个")
            for feature in features:
                info = effective_features[feature]
                print(f"    - {feature}: {info['file_count']}文件/{info['segment_count']}频段")
    
    # 文件覆盖分析
    print(f"\n📊 负样本文件覆盖分析:")
    print("-" * 50)
    
    file_coverage = {}
    for neg_file in neg_files:
        covering_features = [f for f, info in effective_features.items() if neg_file in info['effective_files']]
        file_coverage[neg_file] = len(covering_features)
    
    for neg_file in sorted(file_coverage.keys(), key=lambda x: file_coverage[x], reverse=True):
        print(f"  {neg_file}: {file_coverage[neg_file]}个有效特征")
    
    # 频段覆盖分析
    segment_coverage = defaultdict(int)
    for feature, info in effective_features.items():
        for segment in info['effective_segments']:
            segment_coverage[segment] += 1
    
    print(f"\n📊 频段覆盖分析:")
    print("-" * 50)
    
    if segment_coverage:
        max_coverage = max(segment_coverage.values())
        print(f"最高覆盖频段: {max_coverage}个特征")
        
        # 显示覆盖最多的前10个频段
        top_segments = sorted(segment_coverage.items(), key=lambda x: x[1], reverse=True)[:10]
        for segment, count in top_segments:
            freq = df[df['segment_idx'] == segment]['expected_freq'].iloc[0]
            print(f"  频段{segment:2d} ({freq:6.1f}Hz): {count}个特征")
    
    # 可视化
    visualize_effectiveness(effective_features, neg_files, df)

def visualize_effectiveness(effective_features, neg_files, df):
    """可视化有效性分析"""
    print(f"\n🔍 生成可视化分析")
    print("="*70)
    
    if not effective_features:
        print("❌ 没有有效特征，跳过可视化")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('有效特征分析结果', fontsize=16, fontweight='bold')
    
    # 1. 特征有效性分布
    ax1 = axes[0, 0]
    features = list(effective_features.keys())
    file_counts = [effective_features[f]['file_count'] for f in features]
    segment_counts = [effective_features[f]['segment_count'] for f in features]
    
    ax1.scatter(file_counts, segment_counts, alpha=0.7, s=60)
    ax1.set_xlabel('可分离文件数')
    ax1.set_ylabel('有效频段数')
    ax1.set_title('特征有效性分布')
    ax1.grid(True, alpha=0.3)
    
    # 2. 文件覆盖分析
    ax2 = axes[0, 1]
    file_coverage = {}
    for neg_file in neg_files:
        covering_features = [f for f, info in effective_features.items() if neg_file in info['effective_files']]
        file_coverage[neg_file] = len(covering_features)
    
    files = [f[:15] + '...' if len(f) > 15 else f for f in file_coverage.keys()]
    counts = list(file_coverage.values())
    
    ax2.bar(range(len(files)), counts, color='skyblue')
    ax2.set_xlabel('负样本文件')
    ax2.set_ylabel('有效特征数')
    ax2.set_title('文件覆盖分析')
    ax2.set_xticks(range(len(files)))
    ax2.set_xticklabels(files, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 3. 频段覆盖热力图
    ax3 = axes[1, 0]
    segment_coverage = defaultdict(int)
    for feature, info in effective_features.items():
        for segment in info['effective_segments']:
            segment_coverage[segment] += 1
    
    segments = sorted(segment_coverage.keys())
    coverage_values = [segment_coverage[s] for s in segments]
    
    if segments:
        ax3.plot(segments, coverage_values, 'o-', markersize=4)
        ax3.set_xlabel('频段索引')
        ax3.set_ylabel('有效特征数')
        ax3.set_title('频段覆盖分析')
        ax3.grid(True, alpha=0.3)
    
    # 4. 特征类型分布
    ax4 = axes[1, 1]
    feature_groups = {
        'harmonic': [f for f in effective_features.keys() if 'harmonic' in f or 'thd' in f],
        'time_domain': [f for f in effective_features.keys() if f.startswith('td_')],
        'frequency_domain': [f for f in effective_features.keys() if f.startswith('fd_')],
        'spectral': [f for f in effective_features.keys() if f.startswith('spec_')],
        'statistical': [f for f in effective_features.keys() if f.startswith('stat_')],
        'energy': [f for f in effective_features.keys() if f.startswith('energy_')],
        'other': []
    }
    
    # 分类其他特征
    for feature in effective_features.keys():
        if not any(feature in group for group in feature_groups.values()):
            feature_groups['other'].append(feature)
    
    group_names = []
    group_counts = []
    for group_name, features in feature_groups.items():
        if features:
            group_names.append(group_name)
            group_counts.append(len(features))
    
    if group_names:
        ax4.pie(group_counts, labels=group_names, autopct='%1.1f%%')
        ax4.set_title('特征类型分布')
    
    plt.tight_layout()
    plt.savefig('effective_features_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_results(effective_features, feature_effectiveness):
    """保存结果"""
    print(f"\n💾 保存结果")
    print("="*70)
    
    # 保存有效特征详细信息
    effective_df = []
    for feature, info in effective_features.items():
        effective_df.append({
            'feature': feature,
            'file_count': info['file_count'],
            'segment_count': info['segment_count'],
            'effective_files': ';'.join(info['effective_files']),
            'effective_segments': ';'.join(map(str, info['effective_segments']))
        })
    
    if effective_df:
        pd.DataFrame(effective_df).to_csv('effective_features_detailed.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 有效特征详细信息已保存: effective_features_detailed.csv")
    
    # 保存所有特征的有效性状态
    effectiveness_df = pd.DataFrame([
        {'feature': feature, 'is_effective': is_effective}
        for feature, is_effective in feature_effectiveness.items()
    ])
    effectiveness_df.to_csv('all_features_effectiveness.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 所有特征有效性状态已保存: all_features_effectiveness.csv")
    
    # 生成有效特征列表
    effective_feature_list = list(effective_features.keys())
    with open('effective_features_list.txt', 'w', encoding='utf-8') as f:
        f.write("# 有效特征列表\n")
        f.write(f"# 总数: {len(effective_feature_list)}\n")
        f.write("# 标准: 在某个频段能够完全把至少一个负样本和所有正样本区分开\n\n")
        for i, feature in enumerate(effective_feature_list, 1):
            f.write(f"{i:2d}. {feature}\n")
    
    print(f"✅ 有效特征列表已保存: effective_features_list.txt")
    print(f"📊 总共识别出 {len(effective_feature_list)} 个有效特征")

if __name__ == "__main__":
    effective_features = identify_effective_features()
