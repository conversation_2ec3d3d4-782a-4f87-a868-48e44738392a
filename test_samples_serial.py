#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串行处理test20250717和待定文件夹里的所有样本
"""

import os
import sys
import glob
import matplotlib.pyplot as plt
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def test_single_sample(audio_path, folder_name):
    """测试单个样本"""
    filename = os.path.basename(audio_path)
    
    print(f"\n🎵 测试文件: {folder_name}/{filename}")
    print("=" * 60)
    
    try:
        # 调用优化的频率分割算法，生成可视化图表
        step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path,
            min_duration=153,
            plot=True,  # 生成可视化图表
            debug=True,  # 显示详细信息
            search_window_start=0.1,
            search_window_end=1.5,
            correlation_length=1.0
        )
        
        # 提取关键信息
        start_offset = alignment_info.get('start_offset', 0)
        correlation_score = alignment_info.get('correlation_score', 0)
        alignment_quality = alignment_info.get('alignment_quality', {})
        
        # 显示结果摘要
        print(f"\n📊 分割结果摘要:")
        print(f"  文件: {folder_name}/{filename}")
        print(f"  开始时间: {start_offset:.3f}s")
        print(f"  相关性: {correlation_score:.3f}")
        print(f"  频段数量: {len(freq_table)}")
        print(f"  步进区间数: {len(step_bounds)}")
        
        if alignment_quality:
            overall_quality = alignment_quality.get('overall_quality', 'unknown')
            composite_score = alignment_quality.get('composite_score', 0)
            time_correlation = alignment_quality.get('time_correlation', 0)
            freq_similarity = alignment_quality.get('freq_similarity', 0)
            print(f"  整体质量: {overall_quality}")
            print(f"  综合评分: {composite_score:.3f}")
            print(f"  时域相关性: {time_correlation:.3f}")
            print(f"  频域相似性: {freq_similarity:.3f}")
        
        # 显示频段范围信息
        if len(freq_table) > 0:
            min_freq = min(freq_table)
            max_freq = max(freq_table)
            freq_range = max_freq - min_freq
            print(f"  频率范围: {min_freq:.1f}Hz - {max_freq:.1f}Hz (跨度: {freq_range:.1f}Hz)")
        
        # 显示时间范围信息
        if len(step_bounds) > 0:
            total_duration = step_bounds[-1][1] - step_bounds[0][0]
            avg_step_duration = total_duration / len(step_bounds)
            print(f"  时间范围: {step_bounds[0][0]:.3f}s - {step_bounds[-1][1]:.3f}s")
            print(f"  总时长: {total_duration:.3f}s")
            print(f"  平均步长: {avg_step_duration:.3f}s")
        
        # 显示前3个和后3个频段信息
        print(f"\n🎼 前3个频段:")
        for j in range(min(3, len(freq_table))):
            freq = freq_table[j]
            if j < len(step_bounds):
                start_time, end_time = step_bounds[j]
                duration = end_time - start_time
                print(f"    {j+1:2d}. {freq:6.1f}Hz  {start_time:.3f}s-{end_time:.3f}s  ({duration:.3f}s)")
        
        if len(freq_table) > 3:
            print(f"🎼 最后3个频段:")
            for j in range(max(0, len(freq_table)-3), len(freq_table)):
                freq = freq_table[j]
                if j < len(step_bounds):
                    start_time, end_time = step_bounds[j]
                    duration = end_time - start_time
                    print(f"    {j+1:2d}. {freq:6.1f}Hz  {start_time:.3f}s-{end_time:.3f}s  ({duration:.3f}s)")
        
        # 质量评估
        if correlation_score >= 0.8:
            quality_icon = "🟢"
            quality_text = "优秀"
        elif correlation_score >= 0.6:
            quality_icon = "🟡"
            quality_text = "良好"
        else:
            quality_icon = "🔴"
            quality_text = "需要改进"
        
        print(f"\n{quality_icon} 质量评估: {quality_text}")
        print(f"✅ 处理成功")
        
        # 保存图表
        safe_folder_name = folder_name.replace('/', '_').replace('\\', '_')
        safe_filename = filename.replace('.wav', '').replace(' ', '_')
        chart_name = f'freq_split_{safe_folder_name}_{safe_filename}.png'
        plt.savefig(chart_name, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_samples_serial():
    """串行测试所有样本"""
    print("🔍 串行测试test20250717和待定文件夹里的所有样本")
    print("="*70)
    
    # 定义测试文件夹
    folders = [
        r"../test20250717/pos/sd卡",
        r"../test20250717/pos/完美", 
        r"../test20250717/pos/转接板",
        r"../test20250717/pos/铁网",
        r"../test20250717/neg",
        r"../待定"
    ]
    
    total_files = 0
    success_count = 0
    
    # 收集所有文件
    all_files = []
    for folder in folders:
        if not os.path.exists(folder):
            print(f"❌ 文件夹不存在: {folder}")
            continue
        
        wav_files = glob.glob(os.path.join(folder, "*.wav"))
        folder_name = os.path.basename(folder)
        
        for wav_file in wav_files:
            all_files.append((wav_file, folder_name))
    
    total_files = len(all_files)
    print(f"📂 总共找到 {total_files} 个wav文件")
    
    # 串行处理每个文件
    for i, (audio_path, folder_name) in enumerate(all_files):
        print(f"\n{'='*70}")
        print(f"📊 进度: [{i+1}/{total_files}]")
        
        if test_single_sample(audio_path, folder_name):
            success_count += 1
        
        print("-" * 70)
    
    # 总结
    print(f"\n" + "="*70)
    print(f"🎉 所有样本测试完成！")
    print(f"📊 总计: {total_files} 个文件")
    print(f"✅ 成功: {success_count} 个文件")
    print(f"❌ 失败: {total_files - success_count} 个文件")
    print(f"📈 成功率: {success_count/total_files*100:.1f}%")
    print(f"📊 图表文件已保存在当前目录")

if __name__ == "__main__":
    test_samples_serial()
