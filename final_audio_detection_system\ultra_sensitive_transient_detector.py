#!/usr/bin/env python3
"""
超敏感瞬时多频段能量突增检测器
Ultra Sensitive Transient Multi-band Energy Burst Detector
专门检测微小的瞬时异常
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class UltraSensitiveTransientDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # 超敏感STFT参数
        self.stft_params = {
            'nperseg': 512,         # 约10ms @48kHz
            'noverlap': 384,        # 75%重叠
            'window': 'hann',
            'nfft': 512
        }
        
        # 超敏感检测参数
        self.detection_params = {
            'energy_threshold_sigma': 1.2,     # 进一步降低阈值
            'min_affected_bands': 0.02,        # 只需要2%频段受影响
            'max_burst_duration': 0.3,         # 增加最大持续时间
            'frequency_range': (100, 8000),    # 分析频率范围
            'baseline_percentile': 40,          # 进一步提高基线百分位
            'min_relative_increase': 1.5       # 降低最小相对增幅
        }
        
        print(f"超敏感瞬时突增检测器初始化完成")
        print(f"时间分辨率: {self.stft_params['nperseg']/sample_rate*1000:.1f}ms")
        print(f"检测参数: σ={self.detection_params['energy_threshold_sigma']}, 最少影响频段={self.detection_params['min_affected_bands']*100:.1f}%")
    
    def detect_transient_bursts(self, audio_path):
        """检测瞬时多频段能量突增"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # 短时傅里叶变换
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 计算功率谱
            power_spectrogram = np.abs(Zxx) ** 2
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            power_spectrogram = power_spectrogram[freq_mask, :]
            
            # 检测瞬时能量突增
            burst_analysis = self._analyze_energy_bursts(
                power_spectrogram, frequencies, times
            )
            
            # 计算异常分数
            anomaly_score = self._calculate_burst_anomaly_score(burst_analysis)
            
            # 判断是否异常 - 更敏感的阈值
            anomaly_detected = anomaly_score > 0.15  # 进一步降低判断阈值
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'burst_analysis': burst_analysis,
                'detection_method': 'ultra_sensitive_transient_burst',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_energy_bursts(self, power_spectrogram, frequencies, times):
        """分析能量突增模式"""
        analysis = {
            'total_bursts': 0,
            'max_burst_intensity': 0.0,
            'max_affected_bands': 0.0,
            'burst_frequency_span': 0.0,
            'avg_relative_increase': 0.0,
            'total_burst_time_ratio': 0.0,
            'burst_details': []
        }
        
        # 计算每个时间片的总能量
        time_energy = np.sum(power_spectrogram, axis=0)
        
        # 计算基线能量
        baseline_energy = np.percentile(time_energy, self.detection_params['baseline_percentile'])
        energy_std = np.std(time_energy)
        
        # 能量突增阈值
        burst_threshold = baseline_energy + self.detection_params['energy_threshold_sigma'] * energy_std
        
        # 检测能量突增时刻
        burst_indices = np.where(time_energy > burst_threshold)[0]
        
        if len(burst_indices) == 0:
            return analysis
        
        # 计算突增时间比例
        analysis['total_burst_time_ratio'] = len(burst_indices) / len(time_energy)
        
        # 分析每个突增事件
        burst_groups = self._group_consecutive_indices(burst_indices)
        
        all_relative_increases = []
        
        for group in burst_groups:
            burst_start_idx = group[0]
            burst_end_idx = group[-1]
            burst_duration = (burst_end_idx - burst_start_idx + 1) * (times[1] - times[0])
            
            # 跳过过长的事件
            if burst_duration > self.detection_params['max_burst_duration']:
                continue
            
            # 分析突增的频率特征
            burst_power = power_spectrogram[:, burst_start_idx:burst_end_idx+1]
            avg_burst_power = np.mean(burst_power, axis=1)
            
            # 计算基线功率
            baseline_mask = np.ones(power_spectrogram.shape[1], dtype=bool)
            baseline_mask[max(0, burst_start_idx-5):min(len(time_energy), burst_end_idx+6)] = False
            baseline_power = np.mean(power_spectrogram[:, baseline_mask], axis=1)
            
            # 计算相对能量增幅
            relative_increase = (avg_burst_power - baseline_power) / (baseline_power + 1e-12)
            
            # 检测受影响的频段 - 更敏感的阈值
            significant_increase_mask = relative_increase > self.detection_params['min_relative_increase']
            affected_bands_ratio = np.sum(significant_increase_mask) / len(frequencies)
            
            # 只考虑影响足够频段的突增 - 更宽松的条件
            if affected_bands_ratio >= self.detection_params['min_affected_bands']:
                burst_intensity = np.max(relative_increase)
                frequency_span = (np.max(frequencies[significant_increase_mask]) - 
                                np.min(frequencies[significant_increase_mask])) if np.any(significant_increase_mask) else 0
                
                analysis['total_bursts'] += 1
                analysis['max_burst_intensity'] = max(analysis['max_burst_intensity'], burst_intensity)
                analysis['max_affected_bands'] = max(analysis['max_affected_bands'], affected_bands_ratio)
                analysis['burst_frequency_span'] = max(analysis['burst_frequency_span'], frequency_span)
                
                all_relative_increases.extend(relative_increase[significant_increase_mask])
                
                analysis['burst_details'].append({
                    'time_start': times[burst_start_idx],
                    'time_end': times[burst_end_idx],
                    'duration': burst_duration,
                    'intensity': burst_intensity,
                    'affected_bands_ratio': affected_bands_ratio,
                    'frequency_span': frequency_span,
                    'peak_frequency': frequencies[np.argmax(relative_increase)],
                    'avg_relative_increase': np.mean(relative_increase[significant_increase_mask])
                })
        
        # 计算平均相对增幅
        if all_relative_increases:
            analysis['avg_relative_increase'] = np.mean(all_relative_increases)
        
        return analysis
    
    def _group_consecutive_indices(self, indices):
        """将连续的索引分组"""
        if len(indices) == 0:
            return []
        
        groups = []
        current_group = [indices[0]]
        
        for i in range(1, len(indices)):
            if indices[i] == indices[i-1] + 1:
                current_group.append(indices[i])
            else:
                groups.append(current_group)
                current_group = [indices[i]]
        
        groups.append(current_group)
        return groups
    
    def _calculate_burst_anomaly_score(self, burst_analysis):
        """计算突增异常分数"""
        if burst_analysis['total_bursts'] == 0:
            return 0.0
        
        # 基于多个因素计算异常分数
        score_components = {
            'burst_count': min(1.0, burst_analysis['total_bursts'] / 2.0),  # 进一步降低突增次数要求
            'intensity': min(1.0, burst_analysis['max_burst_intensity'] / 5.0),  # 降低强度要求
            'frequency_coverage': burst_analysis['max_affected_bands'] * 5,  # 大幅提高频段覆盖权重
            'avg_increase': min(1.0, burst_analysis['avg_relative_increase'] / 3.0),  # 降低平均增幅要求
            'time_ratio': min(1.0, burst_analysis['total_burst_time_ratio'] * 10),  # 突增时间比例
            'frequency_span': min(1.0, burst_analysis['burst_frequency_span'] / 1000.0)  # 降低频率跨度要求
        }
        
        # 加权计算最终分数
        weights = {
            'burst_count': 0.15,
            'intensity': 0.20,
            'frequency_coverage': 0.30,  # 提高频段覆盖权重
            'avg_increase': 0.20,
            'time_ratio': 0.10,
            'frequency_span': 0.05
        }
        
        anomaly_score = sum(score_components[key] * weights[key] for key in weights)
        
        return anomaly_score
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'burst_analysis': {},
            'detection_method': 'ultra_sensitive_transient_burst',
            'error': True,
            'error_message': error_msg
        }
    
    def test_specific_files(self, file_list):
        """测试特定文件"""
        print("\n" + "="*80)
        print("测试特定文件的瞬时突增检测")
        print("="*80)
        
        results = []
        for file_path in file_list:
            if os.path.exists(file_path):
                print(f"\n测试文件: {os.path.basename(file_path)}")
                print("-" * 50)
                
                result = self.detect_transient_bursts(file_path)
                result['filename'] = os.path.basename(file_path)
                results.append(result)
                
                # 显示详细结果
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    burst_count = result['burst_analysis'].get('total_bursts', 0)
                    max_intensity = result['burst_analysis'].get('max_burst_intensity', 0)
                    affected_bands = result['burst_analysis'].get('max_affected_bands', 0)
                    time_ratio = result['burst_analysis'].get('total_burst_time_ratio', 0)
                    
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    print(f"突增次数: {burst_count}")
                    print(f"最大强度: {max_intensity:.1f}")
                    print(f"最大受影响频段: {affected_bands:.1%}")
                    print(f"突增时间比例: {time_ratio:.1%}")
                    
                    # 显示突增详情
                    burst_details = result['burst_analysis'].get('burst_details', [])
                    if burst_details:
                        print(f"突增事件详情:")
                        for i, detail in enumerate(burst_details[:3]):  # 只显示前3个
                            print(f"  事件{i+1}: {detail['time_start']:.3f}s-{detail['time_end']:.3f}s, "
                                  f"强度:{detail['intensity']:.1f}, 频段:{detail['affected_bands_ratio']:.1%}")
                else:
                    print(f"处理失败: {result['error_message']}")
            else:
                print(f"文件不存在: {file_path}")
        
        return results

def main():
    """主函数"""
    # 初始化超敏感检测器
    detector = UltraSensitiveTransientDetector()
    
    # 测试特定的问题文件
    problem_files = [
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
        "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav",
        "../test20250717/neg/主板隔音eva取消.wav",
        "../test20250717/neg/主板隔音eva取消_1.wav",
        "../test20250717/neg/喇叭eva没贴.wav",
        "../test20250717/neg/喇叭eva没贴_1.wav"
    ]
    
    results = detector.test_specific_files(problem_files)
    
    # 保存结果
    results_data = []
    for result in results:
        row = {
            'filename': result['filename'],
            'anomaly_detected': result['anomaly_detected'],
            'confidence': result['confidence'],
            'anomaly_score': result['anomaly_score'],
            'total_bursts': result['burst_analysis'].get('total_bursts', 0),
            'max_burst_intensity': result['burst_analysis'].get('max_burst_intensity', 0),
            'max_affected_bands': result['burst_analysis'].get('max_affected_bands', 0),
            'avg_relative_increase': result['burst_analysis'].get('avg_relative_increase', 0),
            'total_burst_time_ratio': result['burst_analysis'].get('total_burst_time_ratio', 0),
            'burst_frequency_span': result['burst_analysis'].get('burst_frequency_span', 0),
            'error': result['error']
        }
        results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('ultra_sensitive_transient_test.csv', index=False)
    
    print(f"\n超敏感瞬时突增检测结果已保存: ultra_sensitive_transient_test.csv")
    
    # 统计结果
    total_files = len([r for r in results if not r['error']])
    detected_anomalies = len([r for r in results if r['anomaly_detected'] and not r['error']])
    
    print(f"\n检测统计:")
    print(f"  总文件数: {total_files}")
    print(f"  检测到异常: {detected_anomalies}")
    print(f"  检出率: {detected_anomalies/total_files*100:.1f}%" if total_files > 0 else "  检出率: 0%")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
