import numpy as np
import librosa
import matplotlib.pyplot as plt
import os
from align import align_signals_by_energy

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 配置 ==========
REF_PATH = r"test20250703/pos1.wav"    # 正样本路径
TEST_PATH = r"test20250703/pos3.wav"   # 待测样本路径
OUTPUT_DIR = "align_similarity_result"
os.makedirs(OUTPUT_DIR, exist_ok=True)

FS = 44100  # 采样率
N_FFT = 2048
HOP_LEN = 512

# ========== 加载音频 ==========
y_ref, sr1 = librosa.load(REF_PATH, sr=FS, mono=True)
y_test, sr2 = librosa.load(TEST_PATH, sr=FS, mono=True)

assert sr1 == sr2 == FS, "采样率不一致，请确保采样率相同"


# =========================
# 计算能量包络
# =========================
def compute_power_envelope(y, frame_length=2048, hop_length=512):
    rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
    return rms

def compute_energy_envelope(y, n_fft=N_FFT, hop_length=512):
    return np.sum(np.abs(librosa.stft(y, n_fft=n_fft, hop_length=hop_length)), axis=0)

# ========== 计算能量包络相似度 ==========
def compute_energy_envelope_similarity(y_ref, y_test, n_fft=N_FFT, hop_length=HOP_LEN):
    S_ref = compute_energy_envelope(y_ref, n_fft=n_fft, hop_length=hop_length)
    S_test = compute_energy_envelope(y_test, n_fft=n_fft, hop_length=hop_length)
    
    # S_ref = compute_power_envelope(y_ref, frame_length=n_fft, hop_length=hop_length)
    # S_test = compute_power_envelope(y_test, frame_length=n_fft, hop_length=hop_length)

    min_len = min(len(S_ref), len(S_test))
    S_ref = S_ref[:min_len]
    S_test = S_test[:min_len]

    # 归一化
    S_ref_norm = (S_ref - np.mean(S_ref)) / (np.std(S_ref) + 1e-8)
    S_test_norm = (S_test - np.mean(S_test)) / (np.std(S_test) + 1e-8)

    corr = np.corrcoef(S_ref_norm, S_test_norm)[0, 1]
    return corr, S_ref, S_test

# ========== 主流程 ==========
y_ref_aligned, y_test_aligned, delay_samples = align_signals_by_energy(y_ref, y_test)
similarity, energy_ref, energy_test = compute_energy_envelope_similarity(y_ref_aligned, y_test_aligned)

print(f"能量包络归一化相关系数: {similarity:.4f}")

if similarity < 0.998:
    print("[警告] 能量包络相似度较低，可能存在异常")
else:
    print("[正常] 能量包络相似度正常")

# ========== 画图对比 ==========
ref_name = os.path.splitext(os.path.basename(REF_PATH))[0]
test_name = os.path.splitext(os.path.basename(TEST_PATH))[0]
corr_str = f"{similarity:.4f}"
times = librosa.frames_to_time(np.arange(len(energy_ref)), sr=FS, hop_length=HOP_LEN)

plt.figure(figsize=(12, 4))
plt.plot(times, energy_ref, label="参考能量包络", alpha=0.7)
plt.plot(times, energy_test, label="待测能量包络", alpha=0.7)
plt.title(f"对齐后能量包络对比\n{ref_name} vs {test_name}  相关系数: {corr_str}")
plt.xlabel("时间 (秒)")
plt.ylabel("帧能量和")
plt.legend()
plt.tight_layout()

img_name = f"energy_envelope_{ref_name}_vs_{test_name}_corr{corr_str}.png"
plt.savefig(os.path.join(OUTPUT_DIR, img_name))
# plt.show()
