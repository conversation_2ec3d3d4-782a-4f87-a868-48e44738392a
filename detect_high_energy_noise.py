#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用多种方法检测低频戳洞文件的高能量噪声
每段生成检测结果可视化，使用多进程加速
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy import stats
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def method1_energy_threshold_detection(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法1: 能量阈值检测
    """
    # 排除信号区域
    noise_mask = ~exclude_mask
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 计算统计阈值
    mean_noise = np.mean(noise_powers)
    std_noise = np.std(noise_powers)
    threshold = mean_noise + 2 * std_noise
    
    # 检测超过阈值的点
    high_energy_mask = (power_db > threshold) & noise_mask
    high_energy_freqs = freqs[high_energy_mask]
    high_energy_powers = power_db[high_energy_mask]
    
    has_noise = len(high_energy_freqs) > 0
    
    return has_noise, list(zip(high_energy_freqs, high_energy_powers)), f"阈值: {threshold:.1f}dB, 检测到{len(high_energy_freqs)}个点"

def method2_relative_energy_analysis(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法2: 相对能量分析
    """
    # 信号区域能量
    signal_mask = exclude_mask
    signal_powers = power_db[signal_mask]
    signal_energy = np.mean(signal_powers) if len(signal_powers) > 0 else -80
    
    # 噪声区域能量
    noise_mask = ~exclude_mask
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    noise_energy = np.mean(noise_powers)
    
    # 相对阈值：噪声能量不应超过信号能量-20dB
    relative_threshold = signal_energy - 20
    
    # 检测异常高的噪声
    high_noise_mask = (power_db > relative_threshold) & noise_mask
    high_noise_freqs = freqs[high_noise_mask]
    high_noise_powers = power_db[high_noise_mask]
    
    has_noise = len(high_noise_freqs) > 0
    
    return has_noise, list(zip(high_noise_freqs, high_noise_powers)), f"信号能量: {signal_energy:.1f}dB, 相对阈值: {relative_threshold:.1f}dB"

def method3_frequency_domain_anomaly(freqs, power_db, fundamental_freq, exclude_mask, dynamic_threshold):
    """
    方法3: 频域异常检测
    """
    # 使用动态噪声阈值+安全裕度
    safety_margin = 12  # dB
    detection_threshold = dynamic_threshold + safety_margin
    
    # 在噪声区域检测
    noise_mask = ~exclude_mask
    anomaly_mask = (power_db > detection_threshold) & noise_mask
    
    anomaly_freqs = freqs[anomaly_mask]
    anomaly_powers = power_db[anomaly_mask]
    
    has_noise = len(anomaly_freqs) > 0
    
    return has_noise, list(zip(anomaly_freqs, anomaly_powers)), f"动态阈值+{safety_margin}dB: {detection_threshold:.1f}dB"

def method4_statistical_detection(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法4: 统计学方法（3σ准则）
    """
    # 噪声区域统计
    noise_mask = ~exclude_mask
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 3σ准则
    mean_noise = np.mean(noise_powers)
    std_noise = np.std(noise_powers)
    threshold_3sigma = mean_noise + 3 * std_noise
    
    # 检测异常点
    outlier_mask = (power_db > threshold_3sigma) & noise_mask
    outlier_freqs = freqs[outlier_mask]
    outlier_powers = power_db[outlier_mask]
    
    has_noise = len(outlier_freqs) > 0
    
    return has_noise, list(zip(outlier_freqs, outlier_powers)), f"3σ阈值: {threshold_3sigma:.1f}dB (μ={mean_noise:.1f}, σ={std_noise:.1f})"

def method5_sliding_window_detection(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法5: 滑动窗口检测
    """
    window_size = 100  # 频率bin
    step_size = 50
    detected_points = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 窗口内的噪声区域
        window_noise_mask = window_mask & ~exclude_mask
        
        if np.sum(window_noise_mask) < 10:
            continue
        
        window_powers = power_db[window_noise_mask]
        window_freqs = freqs[window_noise_mask]
        
        # 局部噪声底噪
        local_noise_floor = np.percentile(window_powers, 25)
        
        # 检测局部异常
        local_threshold = local_noise_floor + 15  # dB
        high_energy_mask = window_powers > local_threshold
        
        if np.any(high_energy_mask):
            high_freqs = window_freqs[high_energy_mask]
            high_powers = window_powers[high_energy_mask]
            detected_points.extend(zip(high_freqs, high_powers))
    
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"滑动窗口检测到{len(detected_points)}个异常点"

def method6_frequency_band_analysis(freqs, power_db, fundamental_freq, exclude_mask):
    """
    方法6: 频带能量比较
    """
    # 将频谱分成6个子带
    freq_bands = [
        (100, 500),
        (500, 1000), 
        (1000, 2000),
        (2000, 5000),
        (5000, 10000),
        (10000, 20000)
    ]
    
    band_energies = []
    detected_points = []
    
    for low_freq, high_freq in freq_bands:
        band_mask = (freqs >= low_freq) & (freqs <= high_freq)
        band_noise_mask = band_mask & ~exclude_mask
        
        if np.sum(band_noise_mask) == 0:
            band_energies.append(0)
            continue
        
        band_powers = power_db[band_noise_mask]
        band_energy = np.mean(band_powers)
        band_energies.append(band_energy)
    
    if len(band_energies) == 0:
        return False, [], "无有效频带"
    
    # 检测能量异常集中的频带
    mean_band_energy = np.mean(band_energies)
    std_band_energy = np.std(band_energies)
    
    for i, (low_freq, high_freq) in enumerate(freq_bands):
        if i < len(band_energies) and band_energies[i] > mean_band_energy + 1.5 * std_band_energy:
            # 在异常频带内寻找具体的高能量点
            band_mask = (freqs >= low_freq) & (freqs <= high_freq)
            band_noise_mask = band_mask & ~exclude_mask
            
            if np.sum(band_noise_mask) > 0:
                band_freqs = freqs[band_noise_mask]
                band_powers = power_db[band_noise_mask]
                
                # 在该频带内检测高能量点
                band_threshold = np.percentile(band_powers, 90)
                high_mask = band_powers > band_threshold
                
                if np.any(high_mask):
                    high_freqs = band_freqs[high_mask]
                    high_powers = band_powers[high_mask]
                    detected_points.extend(zip(high_freqs, high_powers))
    
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"频带分析检测到{len(detected_points)}个异常点"

def analyze_segment_for_noise(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的高能量噪声
    """

    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            return None

        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 高分辨率FFT
        fft_size = 131072
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]

        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window

        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2

        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]

        # 限制到20kHz
        freq_mask = positive_freqs <= 20000
        freqs = positive_freqs[freq_mask]
        power_linear = positive_power[freq_mask]
        power_db = 10 * np.log10(power_linear + 1e-12)

        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_linear[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
        else:
            fundamental_freq = expected_freq

        # 创建排除掩码
        exclude_mask = np.zeros(len(freqs), dtype=bool)

        # 排除主频±5Hz
        main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
        exclude_mask |= main_exclude

        # 排除前10个谐波±5Hz
        for order in range(2, 11):
            harmonic_freq = fundamental_freq * order
            if harmonic_freq <= freqs[-1]:
                harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
                exclude_mask |= harm_exclude

        # 计算动态噪声阈值（用于方法3）
        window_size = 200
        step_size = 50
        local_noise_levels = []

        for start_idx in range(0, len(freqs) - window_size, step_size):
            end_idx = start_idx + window_size
            window_mask = np.zeros(len(freqs), dtype=bool)
            window_mask[start_idx:end_idx] = True

            noise_mask = window_mask & ~exclude_mask

            if np.sum(noise_mask) > 10:
                window_noise_powers = power_db[noise_mask]
                local_noise = np.percentile(window_noise_powers, 25)
                local_noise_levels.append(local_noise)

        dynamic_threshold = np.mean(local_noise_levels) if local_noise_levels else -60

        # 应用6种检测方法
        methods = [
            ("能量阈值检测", method1_energy_threshold_detection),
            ("相对能量分析", method2_relative_energy_analysis),
            ("频域异常检测", method3_frequency_domain_anomaly),
            ("统计学方法", method4_statistical_detection),
            ("滑动窗口检测", method5_sliding_window_detection),
            ("频带能量比较", method6_frequency_band_analysis)
        ]

        results = {}
        for method_name, method_func in methods:
            if method_name == "频域异常检测":
                has_noise, detected_points, info = method_func(freqs, power_db, fundamental_freq, exclude_mask, dynamic_threshold)
            else:
                has_noise, detected_points, info = method_func(freqs, power_db, fundamental_freq, exclude_mask)

            results[method_name] = {
                'has_noise': has_noise,
                'detected_points': detected_points,
                'info': info
            }

        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'freqs': freqs,
            'power_db': power_db,
            'exclude_mask': exclude_mask,
            'dynamic_threshold': dynamic_threshold,
            'results': results
        }

    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_noise_detection_visualization(segment_data, output_dir, filename=""):
    """
    创建高能量噪声检测可视化
    """

    if not segment_data:
        return None

    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    freqs = segment_data['freqs']
    power_db = segment_data['power_db']
    exclude_mask = segment_data['exclude_mask']
    results = segment_data['results']

    # 创建图形 - 2x3布局显示6种方法
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n高能量噪声检测结果',
                 fontsize=16, fontweight='bold')

    method_names = list(results.keys())
    colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown']

    for i, (method_name, color) in enumerate(zip(method_names, colors)):
        ax = axes[i]
        result = results[method_name]

        # 绘制基础频谱
        ax.plot(freqs, power_db, 'lightgray', linewidth=1, alpha=0.7, label='频谱')

        # 标记主频
        ax.axvline(fundamental_freq, color='black', linestyle='-', linewidth=2,
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')

        # 标记排除区域
        excluded_freqs = freqs[exclude_mask]
        excluded_powers = power_db[exclude_mask]
        if len(excluded_freqs) > 0:
            ax.scatter(excluded_freqs, excluded_powers, c='lightblue', s=1,
                      alpha=0.5, label='排除区域')

        # 标记检测到的噪声点
        detected_points = result['detected_points']
        if detected_points:
            det_freqs, det_powers = zip(*detected_points)
            ax.scatter(det_freqs, det_powers, c=color, s=30, alpha=0.8,
                      marker='x', linewidth=2, label=f'检测到噪声({len(detected_points)}个)')

        # 设置标题和标签
        has_noise = result['has_noise']
        noise_status = "有噪声" if has_noise else "无噪声"
        ax.set_title(f'{method_name} - {noise_status}\n{result["info"]}', fontsize=10)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, 40)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

        # 设置边框颜色表示检测结果
        for spine in ax.spines.values():
            spine.set_color(color if has_noise else 'gray')
            spine.set_linewidth(3 if has_noise else 1)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"noise_detection_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment_noise_detection(args):
    """
    处理单个段的噪声检测（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 检测段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_for_noise(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_noise_detection_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 统计检测结果
                results = segment_data['results']
                noise_count = sum(1 for r in results.values() if r['has_noise'])

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'noise_methods': noise_count,
                    'total_methods': len(results),
                    'results': results
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 噪声检测: {noise_count}/{len(results)}种方法检测到噪声")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析低频戳洞文件的高能量噪声
    """

    print("🎯 低频戳洞文件高能量噪声检测")
    print("📝 使用6种方法检测每段的高能量噪声")
    print("="*80)

    # 查找低频戳洞文件
    target_file = None
    test_dirs = ["test20250717", "test20250722"]

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, _, files in os.walk(test_dir):
                for file in files:
                    if "低音戳洞" in file and file.lower().endswith('.wav'):
                        target_file = os.path.join(root, file)
                        break
                if target_file:
                    break
        if target_file:
            break

    if not target_file:
        print("❌ 未找到低频戳洞文件")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_noise_detection"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行噪声检测...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_noise_detection, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 统计分析
        method_stats = {}
        method_names = ["能量阈值检测", "相对能量分析", "频域异常检测", "统计学方法", "滑动窗口检测", "频带能量比较"]

        for method_name in method_names:
            method_stats[method_name] = {
                'detected_segments': 0,
                'total_segments': len(successful_results)
            }

        for result in successful_results:
            for method_name in method_names:
                if result['results'][method_name]['has_noise']:
                    method_stats[method_name]['detected_segments'] += 1

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件噪声检测完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        print(f"\n🔍 各方法检测结果统计:")
        for method_name, stats in method_stats.items():
            detected = stats['detected_segments']
            total = stats['total_segments']
            percentage = (detected / total * 100) if total > 0 else 0
            print(f"  📈 {method_name}: {detected}/{total} ({percentage:.1f}%)")

        # 综合分析
        multi_method_segments = []
        for result in successful_results:
            noise_count = result['noise_methods']
            if noise_count >= 3:  # 3种以上方法检测到噪声
                multi_method_segments.append((result['seg_idx'], result['expected_freq'], noise_count))

        print(f"\n🎯 重点关注段（≥3种方法检测到噪声）:")
        if multi_method_segments:
            for seg_idx, freq, count in multi_method_segments:
                print(f"  ⚠️  段{seg_idx} ({freq:.1f}Hz): {count}/6种方法检测到噪声")
        else:
            print("  ✅ 无段被多种方法同时检测为有噪声")

        print("="*80)
        print("🎯 高能量噪声检测分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
