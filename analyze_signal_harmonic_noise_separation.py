#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析两个异常样本的信号-谐波-噪声分离
对93个切片分别计算主频能量、谐波能量和噪声能量
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch, find_peaks
from scipy import signal

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_signal_harmonic_noise_separation():
    """分析两个异常样本的信号-谐波-噪声分离"""
    print("🔍 专门分析两个异常样本的信号-谐波-噪声分离")
    print("="*70)
    print("分析目标:")
    print("1. 对93个切片分别计算主频能量")
    print("2. 计算谐波能量 (2f, 3f, 4f, 5f)")
    print("3. 计算真正的噪声能量 (除主频和谐波外)")
    print("4. 可视化分析结果")
    print("="*70)
    
    # 两个异常样本 (使用完整路径)
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 分析结果存储
    analysis_results = {}
    
    for i, audio_path in enumerate(target_files):
        sample_name = f"异常样本{i+1}"
        print(f"\n🎯 分析 {sample_name}: {os.path.basename(audio_path)}")
        
        if os.path.exists(audio_path):
            result = analyze_single_sample_complete(audio_path, sample_name)
            if result:
                analysis_results[sample_name] = result
        else:
            print(f"   ❌ 文件不存在: {audio_path}")
    
    # 生成对比可视化
    if len(analysis_results) == 2:
        create_comprehensive_visualization(analysis_results)
        
        # 生成详细报告
        generate_detailed_report(analysis_results)
    
    return analysis_results

def analyze_single_sample_complete(audio_path, sample_name):
    """完整分析单个样本的93个切片"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        print(f"   📊 总切片数: {len(step_boundaries)}")
        
        segment_results = []
        
        # 分析所有93个切片
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 分析该切片的信号-谐波-噪声分离
            segment_analysis = analyze_segment_signal_harmonic_noise(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            segment_results.append(segment_analysis)
            
            # 显示进度
            if seg_idx % 20 == 0 or seg_idx < 10:
                print(f"   处理切片 {seg_idx:2d}/{len(step_boundaries)} ({expected_freq:6.1f}Hz)")
        
        return {
            'sample_name': sample_name,
            'audio_path': audio_path,
            'segment_results': segment_results,
            'total_segments': len(segment_results)
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def analyze_segment_signal_harmonic_noise(audio, sr, expected_freq, seg_idx):
    """分析单个切片的信号-谐波-噪声分离"""
    
    # 使用高分辨率FFT分析
    fft_size = max(8192, len(audio))  # 确保足够的频率分辨率
    
    # 零填充到指定长度
    if len(audio) < fft_size:
        audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
    else:
        audio_padded = audio[:fft_size]
    
    # FFT分析
    fft = np.fft.fft(audio_padded)
    freqs = np.fft.fftfreq(fft_size, 1/sr)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    # 只分析正频率
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    
    # 转换为dB
    power_db = 10 * np.log10(positive_power + 1e-12)
    
    # 1. 找到主频能量
    fundamental_analysis = find_fundamental_energy(positive_freqs, positive_power, expected_freq)
    
    # 2. 找到谐波能量
    harmonic_analysis = find_harmonic_energies(positive_freqs, positive_power, expected_freq, sr)
    
    # 3. 计算噪声能量
    noise_analysis = calculate_noise_energy(positive_freqs, positive_power, 
                                          fundamental_analysis, harmonic_analysis)
    
    # 4. 计算总能量和比例
    total_power = np.sum(positive_power)
    
    result = {
        'segment_idx': seg_idx,
        'expected_freq': expected_freq,
        'total_power_db': 10 * np.log10(total_power + 1e-12),
        
        # 主频信息
        'fundamental_freq': fundamental_analysis['freq'],
        'fundamental_power_db': fundamental_analysis['power_db'],
        'fundamental_ratio': fundamental_analysis['power'] / total_power,
        
        # 谐波信息
        'harmonic_count': len(harmonic_analysis['harmonics']),
        'harmonic_freqs': [h['freq'] for h in harmonic_analysis['harmonics']],
        'harmonic_powers_db': [h['power_db'] for h in harmonic_analysis['harmonics']],
        'total_harmonic_power_db': harmonic_analysis['total_power_db'],
        'harmonic_ratio': harmonic_analysis['total_power'] / total_power,
        
        # 噪声信息
        'noise_power_db': noise_analysis['power_db'],
        'noise_ratio': noise_analysis['power'] / total_power,
        'noise_floor_db': noise_analysis['floor_db'],
        'noise_peak_db': noise_analysis['peak_db'],
        
        # 质量指标
        'snr_db': fundamental_analysis['power_db'] - noise_analysis['power_db'],
        'thd_db': harmonic_analysis['total_power_db'] - fundamental_analysis['power_db'],
        'signal_to_total_ratio': (fundamental_analysis['power'] + harmonic_analysis['total_power']) / total_power,
        
        # 频谱信息
        'freq_resolution': positive_freqs[1] - positive_freqs[0],
        'analysis_bandwidth': positive_freqs[-1]
    }
    
    return result

def find_fundamental_energy(freqs, power, expected_freq):
    """找到主频能量"""
    
    # 在期望频率附近搜索主频峰值
    freq_tolerance = 5.0  # ±5Hz容差
    
    # 找到搜索范围
    search_mask = (freqs >= expected_freq - freq_tolerance) & (freqs <= expected_freq + freq_tolerance)
    
    if not np.any(search_mask):
        # 如果搜索范围无效，使用最接近的频率点
        closest_idx = np.argmin(np.abs(freqs - expected_freq))
        fundamental_freq = freqs[closest_idx]
        fundamental_power = power[closest_idx]
    else:
        # 在搜索范围内找到最大功率点
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        fundamental_freq = freqs[actual_idx]
        fundamental_power = power[actual_idx]
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': 10 * np.log10(fundamental_power + 1e-12),
        'freq_error': fundamental_freq - expected_freq
    }

def find_harmonic_energies(freqs, power, fundamental_freq, sr):
    """找到谐波能量"""
    
    harmonics = []
    harmonic_orders = [2, 3, 4, 5, 6]  # 2次到6次谐波
    
    for order in harmonic_orders:
        harmonic_freq = fundamental_freq * order
        
        # 检查是否在奈奎斯特频率内
        if harmonic_freq >= sr / 2:
            break
        
        # 在谐波频率附近搜索峰值
        freq_tolerance = 10.0  # ±10Hz容差
        search_mask = (freqs >= harmonic_freq - freq_tolerance) & (freqs <= harmonic_freq + freq_tolerance)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power[search_mask]
            
            # 找到局部最大值
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            
            actual_freq = freqs[actual_idx]
            harmonic_power = power[actual_idx]
            
            harmonics.append({
                'order': order,
                'expected_freq': harmonic_freq,
                'freq': actual_freq,
                'power': harmonic_power,
                'power_db': 10 * np.log10(harmonic_power + 1e-12),
                'freq_error': actual_freq - harmonic_freq
            })
    
    # 计算总谐波功率
    total_harmonic_power = sum(h['power'] for h in harmonics)
    
    return {
        'harmonics': harmonics,
        'total_power': total_harmonic_power,
        'total_power_db': 10 * np.log10(total_harmonic_power + 1e-12) if total_harmonic_power > 0 else -120
    }

def calculate_noise_energy(freqs, power, fundamental_analysis, harmonic_analysis):
    """计算噪声能量 (排除主频和谐波)"""
    
    # 创建掩码，排除主频和谐波频率
    noise_mask = np.ones(len(freqs), dtype=bool)
    
    # 排除主频 (±20Hz)
    fundamental_freq = fundamental_analysis['freq']
    fundamental_mask = (freqs >= fundamental_freq - 20) & (freqs <= fundamental_freq + 20)
    noise_mask &= ~fundamental_mask
    
    # 排除所有谐波 (±20Hz)
    for harmonic in harmonic_analysis['harmonics']:
        harmonic_freq = harmonic['freq']
        harmonic_mask = (freqs >= harmonic_freq - 20) & (freqs <= harmonic_freq + 20)
        noise_mask &= ~harmonic_mask
    
    # 计算噪声功率
    if np.any(noise_mask):
        noise_power_array = power[noise_mask]
        noise_power = np.sum(noise_power_array)
        noise_floor = np.percentile(noise_power_array, 10)  # 底噪水平
        noise_peak = np.max(noise_power_array)  # 噪声峰值
    else:
        noise_power = 1e-12
        noise_floor = 1e-12
        noise_peak = 1e-12
    
    return {
        'power': noise_power,
        'power_db': 10 * np.log10(noise_power + 1e-12),
        'floor_db': 10 * np.log10(noise_floor + 1e-12),
        'peak_db': 10 * np.log10(noise_peak + 1e-12),
        'frequency_bins': np.sum(noise_mask),
        'frequency_coverage': np.sum(noise_mask) / len(freqs)
    }

def create_comprehensive_visualization(analysis_results):
    """创建综合可视化"""
    print(f"\n🎨 生成综合可视化分析...")
    
    # 提取数据
    sample_names = list(analysis_results.keys())
    sample1_data = analysis_results[sample_names[0]]['segment_results']
    sample2_data = analysis_results[sample_names[1]]['segment_results']
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 主频功率对比 (左上)
    ax1 = plt.subplot(3, 3, 1)
    plot_power_comparison(ax1, sample1_data, sample2_data, 'fundamental_power_db', '主频功率 (dB)')
    
    # 2. 谐波功率对比 (中上)
    ax2 = plt.subplot(3, 3, 2)
    plot_power_comparison(ax2, sample1_data, sample2_data, 'total_harmonic_power_db', '总谐波功率 (dB)')
    
    # 3. 噪声功率对比 (右上)
    ax3 = plt.subplot(3, 3, 3)
    plot_power_comparison(ax3, sample1_data, sample2_data, 'noise_power_db', '噪声功率 (dB)')
    
    # 4. 能量比例堆叠图 - 样本1 (左中)
    ax4 = plt.subplot(3, 3, 4)
    plot_energy_ratio_stack(ax4, sample1_data, sample_names[0])
    
    # 5. 能量比例堆叠图 - 样本2 (中中)
    ax5 = plt.subplot(3, 3, 5)
    plot_energy_ratio_stack(ax5, sample2_data, sample_names[1])
    
    # 6. SNR对比 (右中)
    ax6 = plt.subplot(3, 3, 6)
    plot_power_comparison(ax6, sample1_data, sample2_data, 'snr_db', '信噪比 (dB)')
    
    # 7. 谐波失真对比 (左下)
    ax7 = plt.subplot(3, 3, 7)
    plot_power_comparison(ax7, sample1_data, sample2_data, 'thd_db', '总谐波失真 (dB)')
    
    # 8. 噪声底噪对比 (中下)
    ax8 = plt.subplot(3, 3, 8)
    plot_power_comparison(ax8, sample1_data, sample2_data, 'noise_floor_db', '噪声底噪 (dB)')
    
    # 9. 综合质量指标 (右下)
    ax9 = plt.subplot(3, 3, 9)
    plot_quality_metrics(ax9, sample1_data, sample2_data, sample_names)
    
    plt.suptitle('两个异常样本的信号-谐波-噪声分离分析 (93个切片)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('signal_harmonic_noise_analysis.png', dpi=300, bbox_inches='tight')
    print(f"   ✅ 综合分析图已保存: signal_harmonic_noise_analysis.png")
    plt.close()

def plot_power_comparison(ax, data1, data2, field, title):
    """绘制功率对比图"""
    
    segments = [d['segment_idx'] for d in data1]
    freqs = [d['expected_freq'] for d in data1]
    
    values1 = [d[field] for d in data1]
    values2 = [d[field] for d in data2]
    
    ax.plot(segments, values1, 'r-o', markersize=3, linewidth=2, label='异常样本1', alpha=0.8)
    ax.plot(segments, values2, 'b-s', markersize=3, linewidth=2, label='异常样本2', alpha=0.8)
    
    ax.set_title(title, fontweight='bold')
    ax.set_xlabel('切片索引')
    ax.set_ylabel('功率 (dB)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加频率标签
    freq_ticks = range(0, len(segments), 20)
    ax2 = ax.twiny()
    ax2.set_xlim(ax.get_xlim())
    ax2.set_xticks([segments[i] for i in freq_ticks if i < len(segments)])
    ax2.set_xticklabels([f'{freqs[i]:.0f}Hz' for i in freq_ticks if i < len(freqs)], rotation=45)

def plot_energy_ratio_stack(ax, data, sample_name):
    """绘制能量比例堆叠图"""
    
    segments = [d['segment_idx'] for d in data]
    
    fundamental_ratios = [d['fundamental_ratio'] for d in data]
    harmonic_ratios = [d['harmonic_ratio'] for d in data]
    noise_ratios = [d['noise_ratio'] for d in data]
    
    # 堆叠图
    ax.bar(segments, fundamental_ratios, label='主频能量', color='green', alpha=0.8)
    ax.bar(segments, harmonic_ratios, bottom=fundamental_ratios, label='谐波能量', color='orange', alpha=0.8)
    
    bottom_for_noise = [f + h for f, h in zip(fundamental_ratios, harmonic_ratios)]
    ax.bar(segments, noise_ratios, bottom=bottom_for_noise, label='噪声能量', color='red', alpha=0.8)
    
    ax.set_title(f'{sample_name} 能量分布', fontweight='bold')
    ax.set_xlabel('切片索引')
    ax.set_ylabel('能量比例')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 1)

def plot_quality_metrics(ax, data1, data2, sample_names):
    """绘制质量指标对比"""
    
    # 计算平均质量指标
    metrics = ['snr_db', 'thd_db', 'noise_ratio', 'signal_to_total_ratio']
    metric_names = ['平均SNR', '平均THD', '平均噪声比', '平均信号比']
    
    sample1_metrics = []
    sample2_metrics = []
    
    for metric in metrics:
        values1 = [d[metric] for d in data1 if metric in d]
        values2 = [d[metric] for d in data2 if metric in d]
        
        if values1 and values2:
            sample1_metrics.append(np.mean(values1))
            sample2_metrics.append(np.mean(values2))
        else:
            sample1_metrics.append(0)
            sample2_metrics.append(0)
    
    x = np.arange(len(metric_names))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, sample1_metrics, width, label=sample_names[0], color='red', alpha=0.7)
    bars2 = ax.bar(x + width/2, sample2_metrics, width, label=sample_names[1], color='blue', alpha=0.7)
    
    ax.set_title('平均质量指标对比', fontweight='bold')
    ax.set_xlabel('指标')
    ax.set_ylabel('数值')
    ax.set_xticks(x)
    ax.set_xticklabels(metric_names, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2f}', ha='center', va='bottom', fontsize=8)

def generate_detailed_report(analysis_results):
    """生成详细报告"""
    print(f"\n📊 详细分析报告")
    print("="*70)
    
    for sample_name, result in analysis_results.items():
        data = result['segment_results']
        
        print(f"\n🎯 {sample_name} ({os.path.basename(result['audio_path'])}):")
        print("-" * 50)
        
        # 计算统计量
        snr_values = [d['snr_db'] for d in data]
        noise_ratios = [d['noise_ratio'] for d in data]
        harmonic_ratios = [d['harmonic_ratio'] for d in data]
        fundamental_ratios = [d['fundamental_ratio'] for d in data]
        
        print(f"   📈 信噪比统计:")
        print(f"     平均SNR: {np.mean(snr_values):.2f} dB")
        print(f"     SNR范围: [{np.min(snr_values):.2f}, {np.max(snr_values):.2f}] dB")
        print(f"     SNR标准差: {np.std(snr_values):.2f} dB")
        
        print(f"   🔊 能量分布统计:")
        print(f"     平均主频能量比: {np.mean(fundamental_ratios):.3f} ({np.mean(fundamental_ratios)*100:.1f}%)")
        print(f"     平均谐波能量比: {np.mean(harmonic_ratios):.3f} ({np.mean(harmonic_ratios)*100:.1f}%)")
        print(f"     平均噪声能量比: {np.mean(noise_ratios):.3f} ({np.mean(noise_ratios)*100:.1f}%)")
        
        # 找出噪声最严重的频段
        worst_segments = sorted(enumerate(noise_ratios), key=lambda x: x[1], reverse=True)[:5]
        
        print(f"   ⚠️  噪声最严重的5个频段:")
        for i, (seg_idx, noise_ratio) in enumerate(worst_segments):
            freq = data[seg_idx]['expected_freq']
            snr = data[seg_idx]['snr_db']
            print(f"     {i+1}. 切片{seg_idx:2d} ({freq:6.1f}Hz): 噪声比{noise_ratio:.3f} ({noise_ratio*100:.1f}%), SNR={snr:.1f}dB")

if __name__ == "__main__":
    results = analyze_signal_harmonic_noise_separation()
    print(f"\n✅ 信号-谐波-噪声分离分析完成！")
