#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态噪声底噪分析
解决底噪动态变化导致的谐波漏检问题
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import librosa
from scipy.signal import find_peaks, savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def dynamic_noise_floor_analysis():
    """动态噪声底噪分析"""
    print("🔧 动态噪声底噪分析 - 解决底噪变化导致的谐波漏检")
    print("="*70)
    print("核心改进:")
    print("1. 局部动态噪声底噪估计")
    print("2. 频率相关的自适应SNR阈值")
    print("3. 滑动窗口噪声背景分析")
    print("4. 谐波候选的局部SNR验证")
    print("="*70)
    
    # 两个异常样本
    target_files = [
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    sample_names = ['异常样本1', '异常样本2']
    target_segments = [40, 41, 42, 43, 44]  # 重点分析这些频段
    
    # 分析每个样本
    for i, (audio_path, sample_name) in enumerate(zip(target_files, sample_names)):
        print(f"\n🎯 动态噪声分析 {sample_name}: {os.path.basename(audio_path)}")
        print("-" * 50)
        
        if os.path.exists(audio_path):
            # 动态噪声分析
            analysis_results = dynamic_noise_analyze_segments(audio_path, sample_name, target_segments)
            
            # 创建动态噪声可视化
            create_dynamic_noise_visualization(analysis_results, sample_name, target_segments)
        else:
            print(f"   ❌ 文件不存在: {audio_path}")

def dynamic_noise_analyze_segments(audio_path, sample_name, target_segments):
    """动态噪声分析频段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_analyses = []
        
        # 分析每个目标频段
        for seg_idx in target_segments:
            if seg_idx >= len(step_boundaries):
                continue
                
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            print(f"   动态噪声分析频段 {seg_idx} ({expected_freq:.1f}Hz)")
            
            # 动态噪声分析
            segment_analysis = dynamic_noise_analyze_single_segment(
                segment_audio, sr, expected_freq, seg_idx
            )
            
            if segment_analysis:
                segment_analyses.append(segment_analysis)
        
        return segment_analyses
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return []

def dynamic_noise_analyze_single_segment(audio, sr, expected_freq, seg_idx):
    """动态噪声分析单个频段"""
    
    try:
        # 超高分辨率FFT
        fft_size = max(65536, len(audio))
        if len(audio) < fft_size:
            audio_padded = np.pad(audio, (0, fft_size - len(audio)), 'constant')
        else:
            audio_padded = audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(audio_padded))
        audio_windowed = audio_padded * window
        
        fft = np.fft.fft(audio_windowed)
        freqs = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 分析完整的正频率范围
        positive_freqs = freqs[:fft_size//2]
        positive_power = power[:fft_size//2]
        power_db = 10 * np.log10(positive_power + 1e-12)
        
        freq_resolution = positive_freqs[1] - positive_freqs[0]
        nyquist_freq = sr / 2
        
        print(f"     频率分辨率: {freq_resolution:.3f}Hz")
        
        # 1. 主频分析
        fundamental_analysis = analyze_fundamental_precise(positive_freqs, positive_power, expected_freq)
        
        # 2. 动态噪声底噪分析
        dynamic_noise_analysis = analyze_dynamic_noise_floor(positive_freqs, positive_power, expected_freq)
        
        # 3. 局部噪声感知的谐波检测
        harmonic_analysis = detect_harmonics_with_local_noise(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, dynamic_noise_analysis, sr
        )
        
        # 4. 对比固定噪声底噪方法
        comparison_analysis = compare_noise_methods(
            positive_freqs, positive_power, expected_freq, 
            fundamental_analysis, dynamic_noise_analysis, sr
        )
        
        print(f"     主频: {fundamental_analysis['freq']:.2f}Hz ({fundamental_analysis['power_db']:.1f}dB)")
        print(f"     动态噪声谐波: {len(harmonic_analysis)}个")
        print(f"     全局噪声底噪: {dynamic_noise_analysis['global_noise_floor_db']:.1f}dB")
        print(f"     局部噪声范围: [{dynamic_noise_analysis['local_noise_min_db']:.1f}, {dynamic_noise_analysis['local_noise_max_db']:.1f}]dB")
        
        return {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'freqs': positive_freqs,
            'power': positive_power,
            'power_db': power_db,
            'fundamental_analysis': fundamental_analysis,
            'dynamic_noise_analysis': dynamic_noise_analysis,
            'harmonic_analysis': harmonic_analysis,
            'comparison_analysis': comparison_analysis,
            'freq_resolution': freq_resolution,
            'nyquist_freq': nyquist_freq
        }
        
    except Exception as e:
        print(f"     ❌ 频段{seg_idx}分析失败: {e}")
        return None

def analyze_fundamental_precise(freqs, power, expected_freq):
    """精确分析主频"""
    
    # 精确搜索带宽
    bandwidth = 5.0
    
    # 搜索主频
    search_mask = (freqs >= expected_freq - bandwidth) & (freqs <= expected_freq + bandwidth)
    
    if not np.any(search_mask):
        return None
    
    search_indices = np.where(search_mask)[0]
    search_powers = power[search_mask]
    
    # 找到最大功率点
    max_power_idx = np.argmax(search_powers)
    actual_idx = search_indices[max_power_idx]
    
    fundamental_freq = freqs[actual_idx]
    fundamental_power = power[actual_idx]
    fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'expected_freq': expected_freq,
        'freq_error': fundamental_freq - expected_freq,
        'index': actual_idx
    }

def analyze_dynamic_noise_floor(freqs, power, fundamental_freq):
    """分析动态噪声底噪"""
    
    power_db = 10 * np.log10(power + 1e-12)
    
    # 1. 全局噪声底噪估计 (排除明显信号)
    excluded_ranges = []
    
    # 排除主频和前8个谐波位置
    for order in range(1, 9):
        signal_freq = fundamental_freq * order
        if signal_freq < freqs[-1]:
            excluded_ranges.append((signal_freq - 20, signal_freq + 20))
    
    # 创建全局噪声掩码
    global_noise_mask = np.ones(len(freqs), dtype=bool)
    for start_freq, end_freq in excluded_ranges:
        exclude_mask = (freqs >= start_freq) & (freqs <= end_freq)
        global_noise_mask &= ~exclude_mask
    
    if np.any(global_noise_mask):
        global_noise_powers = power[global_noise_mask]
        global_noise_floor = np.percentile(global_noise_powers, 20)
        global_noise_floor_db = 10 * np.log10(global_noise_floor + 1e-12)
    else:
        global_noise_floor_db = -120
    
    # 2. 局部动态噪声分析 (滑动窗口)
    window_size = 200  # 200个频率bin的窗口
    step_size = 50     # 50个bin的步长
    
    local_noise_levels = []
    local_noise_freqs = []
    
    for i in range(0, len(freqs) - window_size, step_size):
        window_start = i
        window_end = i + window_size
        
        window_freqs = freqs[window_start:window_end]
        window_powers = power[window_start:window_end]
        window_center_freq = np.mean(window_freqs)
        
        # 检查窗口是否包含明显的信号
        contains_signal = False
        for start_freq, end_freq in excluded_ranges:
            if np.any((window_freqs >= start_freq) & (window_freqs <= end_freq)):
                contains_signal = True
                break
        
        if not contains_signal:
            # 纯噪声窗口，计算噪声水平
            local_noise_level = np.percentile(window_powers, 25)
            local_noise_levels.append(10 * np.log10(local_noise_level + 1e-12))
            local_noise_freqs.append(window_center_freq)
    
    # 3. 平滑局部噪声曲线
    if len(local_noise_levels) > 5:
        # 使用Savitzky-Golay滤波器平滑噪声曲线
        smoothed_noise_levels = savgol_filter(local_noise_levels, 
                                            min(len(local_noise_levels), 11), 3)
        
        # 插值到完整频率网格
        local_noise_interp = np.interp(freqs, local_noise_freqs, smoothed_noise_levels)
        
        local_noise_min_db = np.min(smoothed_noise_levels)
        local_noise_max_db = np.max(smoothed_noise_levels)
        local_noise_variation_db = local_noise_max_db - local_noise_min_db
    else:
        local_noise_interp = np.full(len(freqs), global_noise_floor_db)
        local_noise_min_db = global_noise_floor_db
        local_noise_max_db = global_noise_floor_db
        local_noise_variation_db = 0
    
    print(f"       全局噪声底噪: {global_noise_floor_db:.1f}dB")
    print(f"       局部噪声变化: {local_noise_min_db:.1f} - {local_noise_max_db:.1f}dB (变化{local_noise_variation_db:.1f}dB)")
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'local_noise_freqs': local_noise_freqs,
        'local_noise_levels': local_noise_levels if len(local_noise_levels) > 0 else [global_noise_floor_db],
        'local_noise_interp': local_noise_interp,
        'local_noise_min_db': local_noise_min_db,
        'local_noise_max_db': local_noise_max_db,
        'local_noise_variation_db': local_noise_variation_db,
        'excluded_ranges': excluded_ranges
    }

def detect_harmonics_with_local_noise(freqs, power, fundamental_freq, 
                                     fundamental_analysis, dynamic_noise_analysis, sr):
    """使用局部噪声感知的谐波检测"""
    
    if not fundamental_analysis or not dynamic_noise_analysis:
        return []
    
    detected_harmonics = []
    local_noise_interp = dynamic_noise_analysis['local_noise_interp']
    fundamental_power_db = fundamental_analysis['power_db']
    
    # 自适应SNR阈值 (考虑噪声变化)
    noise_variation = dynamic_noise_analysis['local_noise_variation_db']
    
    if noise_variation > 10:  # 噪声变化很大
        base_snr_threshold = 8.0
        print(f"       噪声变化大({noise_variation:.1f}dB)，使用低SNR阈值")
    elif noise_variation > 5:  # 噪声变化中等
        base_snr_threshold = 10.0
        print(f"       噪声变化中等({noise_variation:.1f}dB)，使用中等SNR阈值")
    else:  # 噪声变化小
        base_snr_threshold = 12.0
        print(f"       噪声变化小({noise_variation:.1f}dB)，使用标准SNR阈值")
    
    nyquist_freq = sr / 2
    
    print(f"       局部噪声感知谐波检测 (基础SNR阈值: {base_snr_threshold:.1f}dB):")
    
    for order in range(2, 25):
        expected_harmonic_freq = fundamental_freq * order
        
        # 检查频率限制
        if expected_harmonic_freq >= nyquist_freq:
            print(f"         {order}次谐波 ({expected_harmonic_freq:.0f}Hz): 超过奈奎斯特频率")
            break
        
        # 自适应搜索带宽
        if expected_harmonic_freq <= 2000:
            search_bandwidth = 12.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 20.0
        elif expected_harmonic_freq <= 10000:
            search_bandwidth = 30.0
        else:
            search_bandwidth = 50.0
        
        # 搜索谐波
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 获取该频率的局部噪声水平
        local_noise_db = local_noise_interp[actual_idx]
        
        # 计算局部SNR
        local_snr_db = harmonic_power_db - local_noise_db
        
        # 计算相对功率
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_snr_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_snr_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_snr_adjustment = 2.0
        else:
            freq_snr_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_snr_adjustment
        
        # 局部噪声感知的谐波判断条件
        conditions = {
            'local_snr_sufficient': local_snr_db >= adjusted_snr_threshold,
            'power_reasonable': relative_power_db >= -60.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        is_valid_harmonic = all(conditions.values())
        
        if is_valid_harmonic:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'expected_freq': expected_harmonic_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'local_snr_db': local_snr_db,
                'local_noise_db': local_noise_db,
                'relative_power_db': relative_power_db,
                'freq_error': freq_error,
                'search_bandwidth': search_bandwidth,
                'snr_threshold_used': adjusted_snr_threshold,
                'index': actual_idx
            })
            
            print(f"         ✅ {order}次谐波: {actual_freq:.1f}Hz, {harmonic_power_db:.1f}dB, "
                  f"局部SNR={local_snr_db:.1f}dB (阈值{adjusted_snr_threshold:.1f}dB), "
                  f"局部噪声={local_noise_db:.1f}dB")
        else:
            failed_conditions = [k for k, v in conditions.items() if not v]
            print(f"         ❌ {order}次谐波: {actual_freq:.1f}Hz - 未通过: {', '.join(failed_conditions)}")
            print(f"            局部SNR={local_snr_db:.1f}dB (需要{adjusted_snr_threshold:.1f}dB), "
                  f"局部噪声={local_noise_db:.1f}dB, 相对功率={relative_power_db:.1f}dB")
    
    return detected_harmonics

def compare_noise_methods(freqs, power, fundamental_freq, 
                         fundamental_analysis, dynamic_noise_analysis, sr):
    """对比固定噪声底噪和动态噪声底噪方法"""
    
    # 使用固定全局噪声底噪的方法
    global_noise_db = dynamic_noise_analysis['global_noise_floor_db']
    fixed_noise_harmonics = detect_harmonics_fixed_noise(
        freqs, power, fundamental_freq, fundamental_analysis, global_noise_db, sr
    )
    
    # 使用动态局部噪声的方法
    dynamic_noise_harmonics = detect_harmonics_with_local_noise(
        freqs, power, fundamental_freq, fundamental_analysis, dynamic_noise_analysis, sr
    )
    
    fixed_orders = {h['order'] for h in fixed_noise_harmonics}
    dynamic_orders = {h['order'] for h in dynamic_noise_harmonics}
    
    # 分析差异
    newly_detected = dynamic_orders - fixed_orders
    lost_harmonics = fixed_orders - dynamic_orders
    
    print(f"       噪声方法对比:")
    print(f"         固定噪声方法: {len(fixed_noise_harmonics)}个谐波")
    print(f"         动态噪声方法: {len(dynamic_noise_harmonics)}个谐波")
    if newly_detected:
        print(f"         动态方法新增: {sorted(newly_detected)}次谐波")
    if lost_harmonics:
        print(f"         动态方法丢失: {sorted(lost_harmonics)}次谐波")
    
    return {
        'fixed_count': len(fixed_noise_harmonics),
        'dynamic_count': len(dynamic_noise_harmonics),
        'newly_detected': list(newly_detected),
        'lost_harmonics': list(lost_harmonics),
        'improvement': len(dynamic_noise_harmonics) - len(fixed_noise_harmonics)
    }

def detect_harmonics_fixed_noise(freqs, power, fundamental_freq, 
                                fundamental_analysis, global_noise_db, sr):
    """使用固定全局噪声底噪的谐波检测 (对比方法)"""
    
    detected_harmonics = []
    fundamental_power_db = fundamental_analysis['power_db']
    min_snr_db = 12.0  # 固定SNR阈值
    nyquist_freq = sr / 2
    
    for order in range(2, 25):
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= nyquist_freq:
            break
        
        search_bandwidth = 15.0
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 使用固定全局噪声底噪
        global_snr_db = harmonic_power_db - global_noise_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 固定条件
        is_valid = (
            global_snr_db >= min_snr_db and
            relative_power_db >= -60.0 and
            abs(freq_error) <= search_bandwidth * 0.8
        )
        
        if is_valid:
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def create_dynamic_noise_visualization(segment_analyses, sample_name, target_segments):
    """创建动态噪声可视化"""
    
    if not segment_analyses:
        print(f"   ❌ 无有效分析数据")
        return
    
    print(f"   🎨 生成 {sample_name} 动态噪声可视化...")
    
    # 创建大图 (5行1列)
    fig, axes = plt.subplots(5, 1, figsize=(24, 30))
    
    for i, segment_analysis in enumerate(segment_analyses):
        ax = axes[i]
        
        # 提取数据
        freqs = segment_analysis['freqs']
        power_db = segment_analysis['power_db']
        fundamental_analysis = segment_analysis['fundamental_analysis']
        harmonic_analysis = segment_analysis['harmonic_analysis']
        dynamic_noise_analysis = segment_analysis['dynamic_noise_analysis']
        comparison_analysis = segment_analysis['comparison_analysis']
        
        seg_idx = segment_analysis['segment_idx']
        expected_freq = segment_analysis['expected_freq']
        nyquist_freq = segment_analysis['nyquist_freq']
        
        # 设置显示范围
        if expected_freq <= 1000:
            freq_range = (0, min(15000, nyquist_freq))
        elif expected_freq <= 2000:
            freq_range = (0, min(20000, nyquist_freq))
        else:
            freq_range = (0, nyquist_freq)
        
        freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
        display_freqs = freqs[freq_mask]
        display_power_db = power_db[freq_mask]
        
        # 绘制基础频谱
        ax.plot(display_freqs, display_power_db, 'k-', linewidth=0.8, alpha=0.7, label='频谱')
        
        # 绘制全局噪声底噪线
        global_noise_db = dynamic_noise_analysis['global_noise_floor_db']
        ax.axhline(y=global_noise_db, color='gray', linestyle='--', alpha=0.8, 
                  label=f'全局噪声底噪 {global_noise_db:.1f}dB')
        
        # 绘制动态局部噪声曲线
        local_noise_interp = dynamic_noise_analysis['local_noise_interp']
        display_local_noise = local_noise_interp[freq_mask]
        ax.plot(display_freqs, display_local_noise, 'purple', linewidth=2, alpha=0.8,
               label=f'动态局部噪声 (变化{dynamic_noise_analysis["local_noise_variation_db"]:.1f}dB)')
        
        # 填充噪声变化区域
        noise_min = dynamic_noise_analysis['local_noise_min_db']
        noise_max = dynamic_noise_analysis['local_noise_max_db']
        ax.fill_between(display_freqs, noise_min, noise_max, alpha=0.1, color='purple',
                       label='噪声变化范围')
        
        # 标记主频
        if fundamental_analysis:
            fund_freq = fundamental_analysis['freq']
            fund_power_db = fundamental_analysis['power_db']
            if freq_range[0] <= fund_freq <= freq_range[1]:
                ax.plot(fund_freq, fund_power_db, 'ro', markersize=12, 
                       label=f'主频 {fund_freq:.1f}Hz ({fund_power_db:.1f}dB)')
                
                # 主频标注
                ax.annotate(f'主频\n{fund_freq:.1f}Hz\n{fund_power_db:.1f}dB', 
                           xy=(fund_freq, fund_power_db), 
                           xytext=(fund_freq, fund_power_db + 15),
                           ha='center', va='bottom', fontweight='bold', color='red',
                           arrowprops=dict(arrowstyle='->', color='red', lw=2),
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        # 标记动态噪声检测的谐波
        harmonic_colors = ['orange', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta', 'olive', 'navy', 'maroon']
        
        for j, harmonic in enumerate(harmonic_analysis):
            if j >= len(harmonic_colors):
                color = 'gray'
            else:
                color = harmonic_colors[j]
                
            harm_freq = harmonic['freq']
            harm_power_db = harmonic['power_db']
            harm_order = harmonic['order']
            local_snr = harmonic['local_snr_db']
            local_noise = harmonic['local_noise_db']
            
            if freq_range[0] <= harm_freq <= freq_range[1]:
                # 标记新增检测的谐波
                if harm_order in comparison_analysis['newly_detected']:
                    marker_style = 's'  # 方形标记新增的
                    marker_size = 12
                    alpha = 1.0
                    label_suffix = ' (动态新增)'
                else:
                    marker_style = 'o'  # 圆形标记原有的
                    marker_size = 10
                    alpha = 0.8
                    label_suffix = ''
                
                ax.plot(harm_freq, harm_power_db, marker_style, color=color, 
                       markersize=marker_size, alpha=alpha,
                       label=f'{harm_order}次谐波 {harm_freq:.0f}Hz{label_suffix}')
                
                # 绘制局部噪声到谐波的连线
                ax.plot([harm_freq, harm_freq], [local_noise, harm_power_db], 
                       color=color, linestyle=':', alpha=0.6, linewidth=2)
                
                # 谐波标注 (只标注前8个和新增的)
                if j < 8 or harm_order in comparison_analysis['newly_detected']:
                    ax.annotate(f'{harm_order}次\n{harm_freq:.0f}Hz\n局部SNR={local_snr:.1f}dB', 
                               xy=(harm_freq, harm_power_db), 
                               xytext=(harm_freq, harm_power_db + 8),
                               ha='center', va='bottom', fontsize=8, color=color,
                               arrowprops=dict(arrowstyle='->', color=color, lw=1))
        
        # 设置图表属性
        improvement = comparison_analysis['improvement']
        improvement_text = f"(+{improvement})" if improvement > 0 else f"({improvement})" if improvement < 0 else ""
        
        ax.set_title(f'{sample_name} - 频段{seg_idx} ({expected_freq:.1f}Hz) 动态噪声分析 '
                    f'检测{len(harmonic_analysis)}个谐波{improvement_text}', 
                    fontweight='bold', fontsize=14)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(freq_range)
        ax.set_ylim(np.min(display_power_db) - 10, np.max(display_power_db) + 25)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.grid(True, alpha=0.3)
        
        # 添加动态噪声信息文本框
        info_text = f"动态噪声分析:\n"
        info_text += f"全局噪声: {global_noise_db:.1f}dB\n"
        info_text += f"局部噪声: [{noise_min:.1f}, {noise_max:.1f}]dB\n"
        info_text += f"噪声变化: {dynamic_noise_analysis['local_noise_variation_db']:.1f}dB\n"
        info_text += f"动态检测: {len(harmonic_analysis)}个谐波\n"
        info_text += f"固定检测: {comparison_analysis['fixed_count']}个谐波\n"
        if comparison_analysis['newly_detected']:
            info_text += f"新增谐波: {sorted(comparison_analysis['newly_detected'])}"
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightcyan', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'dynamic_noise_analysis_{sample_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ 动态噪声可视化已保存: {filename}")
    plt.close()

if __name__ == "__main__":
    dynamic_noise_floor_analysis()
