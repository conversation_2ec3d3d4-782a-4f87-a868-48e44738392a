import numpy as np
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
from scipy import signal
import warnings
import librosa

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning)

def detect_speaker_anomaly_freq(
    ref_path: str,
    rec_path: str,
    plot_result: bool = True,
    fs: int = 48000,
    smooth_win_len: int = 51,
    start_freq: int = 100,
    stop_freq: int = 10000,  # 限制到10kHz (人耳敏感区)
    baseline_win_len: int = 501  # 新增基线窗口
) -> tuple[float, str, dict]:
    """
    通过频域对比检测喇叭异常（免时域对齐）
    改进版本：聚焦谐振峰检测，避免误报正常频响差异
    
    参数:
        ref_path: 参考扫频信号文件路径
        rec_path: 录制信号文件路径
        plot_result: 是否绘制诊断图
        fs: 采样率
        smooth_win_len: 局部平滑窗口长度
        baseline_win_len: 基线提取窗口长度
        start_freq: 分析起始频率 (Hz)
        stop_freq: 分析截止频率 (Hz)
    
    返回:
        confidence: 喇叭正常置信度 (0.0-1.0)
        diagnosis: 诊断结果文本
        metrics: 关键指标字典
    """
    try:
        # 1. 加载音频文件
        ref, _ = librosa.load(ref_path, sr=fs, mono=True)
        rec, _ = librosa.load(rec_path, sr=fs, mono=True)
        
        # 2. 统一信号长度
        min_len = min(len(ref), len(rec))
        ref = ref[:min_len]
        rec = rec[:min_len]
        
        # 3. 加窗处理
        window = signal.windows.hann(min_len)
        ref_win = ref * window
        rec_win = rec * window
        
        # 4. 计算频响曲线
        fft_ref = np.fft.rfft(ref_win)
        fft_rec = np.fft.rfft(rec_win)
        freqs = np.fft.rfftfreq(min_len, 1/fs)
        
        # 计算功率谱比
        with np.errstate(divide='ignore', invalid='ignore'):
            magnitude_ratio = np.abs(fft_rec) / (np.abs(fft_ref) + 1e-10)
            response_db = 20 * np.log10(magnitude_ratio)
        
        # 处理无效值
        response_db = np.nan_to_num(response_db, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 5. 有效频带分析 (添加权重)
        mask = (freqs >= start_freq) & (freqs <= stop_freq)
        valid_freqs = freqs[mask]
        valid_response = response_db[mask]
        
        # 创建人耳敏感度权重 (中频权重高)
        freq_weights = np.ones_like(valid_freqs)
        mid_freq_mask = (valid_freqs >= 500) & (valid_freqs <= 5000)
        freq_weights[mid_freq_mask] = 2.0  # 中频区加倍权重
        
        # 6. 计算关键指标 (使用加权计算)
        weighted_response = valid_response * freq_weights
        
        # 频响标准差 (加权)
        if len(weighted_response) > 1:
            std_dev = np.sqrt(np.cov(weighted_response, aweights=freq_weights))
        else:
            std_dev = 0
        
        # 最大/平均偏差 (只考虑正向偏差)
        pos_deviation = np.maximum(valid_response, 0)
        max_deviation = np.max(pos_deviation) if len(pos_deviation) > 0 else 0
        mean_deviation = np.mean(pos_deviation) if len(pos_deviation) > 0 else 0
        
        # 7. 异常谐振峰检测 (核心改进)
        resonance_peaks = []
        resonance_count = 0
        
        try:
            # 基线提取 (大窗口平滑)
            baseline = signal.savgol_filter(
                response_db, 
                window_length=baseline_win_len, 
                polyorder=3
            )
            
            # 计算相对偏差 (消除整体频响形状影响)
            relative_response = response_db - baseline
            
            # 平滑相对偏差 (小窗口)
            smoothed_relative = signal.savgol_filter(
                relative_response, 
                window_length=smooth_win_len, 
                polyorder=3
            )
            
            # 动态峰值阈值 (基于局部波动)
            local_std = np.std(relative_response[mask])
            peak_threshold = max(6, 3 * local_std)  # 更严格的阈值
            
            # 检测显著峰值 (只检测正向峰值)
            peaks, properties = signal.find_peaks(
                smoothed_relative, 
                height=peak_threshold,
                prominence=4,  # 更高的突出度要求
                wlen=1000       # 限制检测范围
            )
            
            # 收集谐振峰信息 (添加窄带验证)
            for p in peaks:
                if start_freq <= freqs[p] <= stop_freq:
                    # 验证窄带特征
                    left = max(0, p-5)
                    right = min(len(smoothed_relative)-1, p+5)
                    bandwidth = freqs[right] - freqs[left]
                    
                    # 计算3dB带宽
                    half_power = smoothed_relative[p] - 3
                    left_idx = p
                    while left_idx > 0 and smoothed_relative[left_idx] > half_power:
                        left_idx -= 1
                    right_idx = p
                    while right_idx < len(smoothed_relative)-1 and smoothed_relative[right_idx] > half_power:
                        right_idx += 1
                    
                    actual_bandwidth = freqs[right_idx] - freqs[left_idx]
                    
                    # 窄带验证: 带宽小于中心频率的10%
                    if actual_bandwidth < 0.1 * freqs[p]:
                        resonance_peaks.append({
                            "frequency": freqs[p],
                            "magnitude": smoothed_relative[p],
                            "prominence": properties['prominences'][np.where(peaks == p)[0][0]],
                            "bandwidth": actual_bandwidth
                        })
            
            resonance_count = len(resonance_peaks)
        except Exception as e:
            print(f"谐振峰检测错误: {str(e)}")
            resonance_peaks = []
            resonance_count = 0
        
        # 8. 计算置信度 (聚焦谐振峰)
        # 基于谐振峰数量和强度
        peak_score = 1.0
        if resonance_peaks:
            peak_score = 1.0
            for peak in resonance_peaks:
                # 中频区谐振峰惩罚更大
                freq_weight = 2.0 if 500 <= peak["frequency"] <= 5000 else 1.0
                # 幅度越大惩罚越大
                mag_penalty = min(1.0, peak["magnitude"] / 12)
                peak_score -= 0.2 * mag_penalty * freq_weight
            peak_score = max(0, peak_score)
        
        # 基于平均正向偏差
        mean_dev_score = max(0, 1 - min(1, mean_deviation/8))
        
        # 综合置信度 (主要基于谐振峰)
        confidence = 0.7 * peak_score + 0.3 * mean_dev_score
        confidence = max(0, min(1, confidence))
        
        # 9. 诊断结果
        if confidence > 0.85:
            diagnosis = "喇叭状态正常"
        elif confidence > 0.7:
            diagnosis = "喇叭轻微异常"
        elif confidence > 0.5:
            diagnosis = "喇叭中度异常"
        else:
            diagnosis = "喇叭严重异常"
        
        # 10. 可视化结果
        if plot_result:
            plt.figure(figsize=(16, 10))
            
            # 只显示有效频带
            mask = (freqs >= start_freq) & (freqs <= stop_freq)
            plot_freqs = freqs[mask]
            plot_response = response_db[mask]
            plot_baseline = baseline[mask]
            plot_relative = relative_response[mask]
            plot_smoothed = smoothed_relative[mask]

            # 原始频响曲线
            plt.subplot(3, 1, 1)
            plt.semilogx(plot_freqs, plot_response, 'b-', alpha=0.6, label='原始频响')
            plt.semilogx(plot_freqs, plot_baseline, 'g--', linewidth=2, label='基线')
            plt.axvspan(start_freq, stop_freq, alpha=0.1, color='green', label='分析频带')
            plt.title('喇叭频响曲线与基线')
            plt.xlabel('频率 (Hz)')
            plt.ylabel('幅度 (dB)')
            plt.grid(True, which='both')
            plt.axhline(0, color='black', linestyle='--')
            plt.legend()
            plt.xlim(start_freq, stop_freq)
            plt.ylim(-20, 20)

            # 相对偏差曲线
            plt.subplot(3, 1, 2)
            plt.semilogx(plot_freqs, plot_relative, 'c-', alpha=0.7, label='相对偏差')
            plt.semilogx(plot_freqs, plot_smoothed, 'm-', linewidth=1.5, label='平滑相对偏差')
            # 标记谐振峰
            if resonance_peaks:
                for peak in resonance_peaks:
                    freq = peak["frequency"]
                    mag = peak["magnitude"]
                    if start_freq <= freq <= stop_freq:
                        plt.plot(freq, mag, 'ro')
                        plt.annotate(f"{freq:.0f}Hz", (freq, mag), 
                                    xytext=(0, 10), textcoords='offset points',
                                    fontsize=9)
            plt.axhline(peak_threshold, color='r', linestyle='--', label=f'峰值阈值 ({peak_threshold:.1f}dB)')
            plt.title('相对偏差分析 (聚焦谐振峰)')
            plt.xlabel('频率 (Hz)')
            plt.ylabel('相对幅度 (dB)')
            plt.grid(True, which='both')
            plt.legend()
            plt.xlim(start_freq, stop_freq)
            plt.ylim(-5, max(20, peak_threshold+5))

            # 诊断信息（保持不变）
            plt.subplot(3, 1, 3)
            plt.axis('off')
            
            # 谐振峰详情
            resonance_text = "未检测到显著谐振峰"
            if resonance_peaks:
                resonance_text = "检测到谐振峰:\n"
                resonance_text += "频率(Hz) | 幅度(dB) | 带宽(Hz) | 突出度(dB)\n"
                resonance_text += "----------------------------------------\n"
                for peak in resonance_peaks:
                    resonance_text += (f"{peak['frequency']:.0f} | "
                                     f"{peak['magnitude']:.1f} | "
                                     f"{peak['bandwidth']:.1f} | "
                                     f"{peak['prominence']:.1f}\n")
            
            # 诊断结果
            result_color = "green" if confidence > 0.7 else ("orange" if confidence > 0.5 else "red")
            
            info_text = (
                f"诊断结果: <span style='color:{result_color}; font-weight:bold'>{diagnosis}</span>\n"
                f"置信度: <span style='font-weight:bold'>{confidence:.1%}</span>\n\n"
                f"关键指标:\n"
                f"- 频响标准差: {std_dev:.2f} dB\n"
                f"- 最大正向偏差: {max_deviation:.2f} dB\n"
                f"- 平均正向偏差: {mean_deviation:.2f} dB\n"
                f"- 谐振峰数量: {resonance_count}个\n\n"
                f"{resonance_text}"
            )
            
            plt.text(0.01, 0.99, info_text, fontsize=12, va='top', ha='left',
                    bbox=dict(facecolor='lightyellow', alpha=0.7),
                    transform=plt.gca().transAxes)
            
            # plt.tight_layout()
            plt.savefig('speaker_diagnosis.png', dpi=120)
            plt.show()
        
        return confidence, diagnosis, {
            'std_dev': std_dev,
            'max_deviation': max_deviation,
            'mean_deviation': mean_deviation,
            'resonance_count': resonance_count,
            'resonance_peaks': resonance_peaks,
            'freq_response': list(zip(freqs, response_db)),
            'relative_response': list(zip(freqs, relative_response))
        }
    
    except Exception as e:
        print(f"检测过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, "检测失败", {}

# 使用示例
if __name__ == "__main__":
    # 替换为您的音频文件路径
    ref_file = r"录音样本\（1）吉他打开\步进扫频_50Hz至10000Hz_R80_185点_10cyc_212ms_39.6s.wav"    # 标准扫频信号
    rec_file = r"录音样本\（1）吉他打开\录音_步进扫频_50Hz至10000Hz_20250702_181324.wav"   # 麦克风录制信号
    
    print("="*50)
    print("喇叭谐振峰检测系统")
    print("="*50)
    
    confidence, result, metrics = detect_speaker_anomaly_freq(
        ref_file, 
        rec_file,
        start_freq=100,    # 忽略极低频
        stop_freq=20000,    # 聚焦主要频段
        baseline_win_len=1001  # 更平滑的基线
    )
    
    if confidence > 0:
        print(f"\n检测结果: {result}")
        print(f"置信度: {confidence:.1%}")
        print("关键指标:")
        print(f"- 频响标准差: {metrics['std_dev']:.2f} dB")
        print(f"- 最大正向偏差: {metrics['max_deviation']:.2f} dB")
        print(f"- 平均正向偏差: {metrics['mean_deviation']:.2f} dB")
        print(f"- 谐振峰数量: {metrics['resonance_count']}")
        
        if metrics['resonance_peaks']:
            print("\n谐振峰详情:")
            print("频率(Hz) | 幅度(dB) | 带宽(Hz) | 突出度(dB)")
            print("----------------------------------------")
            for peak in metrics['resonance_peaks']:
                print(f"{peak['frequency']:6.0f} | "
                     f"{peak['magnitude']:7.1f} | "
                     f"{peak['bandwidth']:8.1f} | "
                     f"{peak['prominence']:8.1f}")
        
        print("="*50)
    else:
        print("检测失败，请检查输入文件")