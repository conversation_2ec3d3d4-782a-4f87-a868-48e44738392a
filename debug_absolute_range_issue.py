#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试绝对范围问题
检查是正常样本范围计算错误还是特征计算错误
"""

import os
import pandas as pd
import numpy as np

def debug_absolute_range_issue():
    """调试绝对范围问题"""
    print("🔍 调试绝对范围问题")
    print("="*70)
    print("检查目标: 找出为什么明显有噪声的频段仍在正常样本范围内")
    print("="*70)
    
    # 检查数据文件
    if not os.path.exists('top4_features_93_segments.csv'):
        print("❌ 未找到数据文件")
        return
    
    df = pd.read_csv('top4_features_93_segments.csv')
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 筛选数据
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 数据概览:")
    print(f"   总记录数: {len(df)}")
    print(f"   噪声样本记录数: {len(target_data)} (应该是 2×93=186)")
    print(f"   正常样本记录数: {len(normal_data)} (应该是 54×93=5022)")
    print(f"   噪声样本文件数: {len(target_data['filename'].unique())}")
    print(f"   正常样本文件数: {len(normal_data['filename'].unique())}")
    
    # 检查数据完整性
    if len(target_data) != 186:
        print(f"⚠️  警告: 噪声样本记录数不对，应该是186，实际是{len(target_data)}")
    
    if len(normal_data) != 5022:
        print(f"⚠️  警告: 正常样本记录数不对，应该是5022，实际是{len(normal_data)}")
    
    # 详细检查几个关键频段
    key_segments = [0, 1, 2, 10, 20, 50, 80]  # 选择几个代表性频段
    
    for seg_idx in key_segments:
        print(f"\n" + "="*50)
        print(f"🔍 详细检查频段 {seg_idx}")
        check_segment_absolute_range(df, seg_idx, target_files)
    
    # 检查是否有正常样本被误标记
    print(f"\n" + "="*50)
    print(f"🔍 检查是否有正常样本被误标记为噪声样本")
    check_mislabeled_samples(df, target_files)
    
    # 检查特征计算的一致性
    print(f"\n" + "="*50)
    print(f"🔍 检查特征计算的一致性")
    check_feature_calculation_consistency(df)

def check_segment_absolute_range(df, segment_idx, target_files):
    """检查特定频段的绝对范围"""
    seg_data = df[df['segment_idx'] == segment_idx]
    
    if len(seg_data) == 0:
        print(f"   ❌ 频段{segment_idx}无数据")
        return
    
    freq = seg_data['expected_freq'].iloc[0]
    print(f"   频段{segment_idx} ({freq:.1f}Hz):")
    
    # 分离数据
    normal_seg = seg_data[seg_data['is_target'] == False]
    target_seg = seg_data[seg_data['is_target'] == True]
    
    print(f"     正常样本数: {len(normal_seg)}")
    print(f"     噪声样本数: {len(target_seg)}")
    
    # 检查每个特征
    features = ['true_noise_floor_median', 'true_noise_floor_mean', 
               'noise_floor_stability_mean', 'noise_floor_stability_std']
    
    for feature in features:
        print(f"\n     📊 {feature}:")
        
        normal_values = normal_seg[feature].dropna().values
        target_values = target_seg[feature].dropna().values
        
        if len(normal_values) == 0:
            print(f"       ❌ 正常样本无数据")
            continue
        
        if len(target_values) == 0:
            print(f"       ❌ 噪声样本无数据")
            continue
        
        # 正常样本统计
        normal_min = np.min(normal_values)
        normal_max = np.max(normal_values)
        normal_mean = np.mean(normal_values)
        normal_std = np.std(normal_values)
        
        print(f"       正常样本: 数量={len(normal_values)}")
        print(f"         范围: [{normal_min:.6f}, {normal_max:.6f}]")
        print(f"         均值±标准差: {normal_mean:.6f} ± {normal_std:.6f}")
        
        # 噪声样本统计
        target_min = np.min(target_values)
        target_max = np.max(target_values)
        target_mean = np.mean(target_values)
        
        print(f"       噪声样本: 数量={len(target_values)}")
        print(f"         范围: [{target_min:.6f}, {target_max:.6f}]")
        print(f"         均值: {target_mean:.6f}")
        
        # 检查分离情况
        if target_max < normal_min:
            gap = normal_min - target_max
            print(f"       ✅ 完全分离: 噪声样本在正常样本下方，间隙 {gap:.6f}")
        elif target_min > normal_max:
            gap = target_min - normal_max
            print(f"       ✅ 完全分离: 噪声样本在正常样本上方，间隙 {gap:.6f}")
        else:
            # 有重叠
            overlap_start = max(normal_min, target_min)
            overlap_end = min(normal_max, target_max)
            overlap_size = overlap_end - overlap_start
            
            print(f"       ❌ 有重叠: 重叠区间 [{overlap_start:.6f}, {overlap_end:.6f}]，大小 {overlap_size:.6f}")
            
            # 详细分析重叠原因
            print(f"       🔍 重叠分析:")
            print(f"         正常样本最小值: {normal_min:.6f}")
            print(f"         正常样本最大值: {normal_max:.6f}")
            print(f"         噪声样本最小值: {target_min:.6f}")
            print(f"         噪声样本最大值: {target_max:.6f}")
            
            # 检查是否有异常的正常样本
            if target_mean > normal_mean:
                # 噪声样本均值更高，检查正常样本中的异常高值
                high_normal_threshold = normal_mean + 2 * normal_std
                high_normal_count = np.sum(normal_values > high_normal_threshold)
                print(f"         正常样本中超过均值+2σ的数量: {high_normal_count}")
                
                if high_normal_count > 0:
                    high_normal_values = normal_values[normal_values > high_normal_threshold]
                    print(f"         异常高的正常样本值: {high_normal_values}")
            else:
                # 噪声样本均值更低，检查正常样本中的异常低值
                low_normal_threshold = normal_mean - 2 * normal_std
                low_normal_count = np.sum(normal_values < low_normal_threshold)
                print(f"         正常样本中低于均值-2σ的数量: {low_normal_count}")
                
                if low_normal_count > 0:
                    low_normal_values = normal_values[normal_values < low_normal_threshold]
                    print(f"         异常低的正常样本值: {low_normal_values}")

def check_mislabeled_samples(df, target_files):
    """检查是否有样本被误标记"""
    
    print(f"   检查样本标记的正确性:")
    
    # 检查目标文件是否正确标记
    for filename in target_files:
        file_data = df[df['filename'] == filename]
        if len(file_data) == 0:
            print(f"   ❌ 目标文件 {filename} 在数据中未找到")
        else:
            is_target_values = file_data['is_target'].unique()
            if len(is_target_values) != 1 or is_target_values[0] != True:
                print(f"   ❌ 目标文件 {filename} 标记错误: {is_target_values}")
            else:
                print(f"   ✅ 目标文件 {filename} 标记正确，记录数: {len(file_data)}")
    
    # 检查是否有其他文件被误标记为目标
    target_data = df[df['is_target'] == True]
    target_filenames = target_data['filename'].unique()
    
    print(f"   被标记为目标的文件:")
    for filename in target_filenames:
        count = len(target_data[target_data['filename'] == filename])
        if filename in target_files:
            print(f"     ✅ {filename} (正确，记录数: {count})")
        else:
            print(f"     ❌ {filename} (错误标记，记录数: {count})")
    
    # 检查正常样本中是否包含目标文件
    normal_data = df[df['is_target'] == False]
    normal_filenames = normal_data['filename'].unique()
    
    for filename in target_files:
        if filename in normal_filenames:
            count = len(normal_data[normal_data['filename'] == filename])
            print(f"   ❌ 目标文件 {filename} 也被标记为正常样本 (记录数: {count})")

def check_feature_calculation_consistency(df):
    """检查特征计算的一致性"""
    
    print(f"   检查特征计算的一致性:")
    
    features = ['true_noise_floor_median', 'true_noise_floor_mean', 
               'noise_floor_stability_mean', 'noise_floor_stability_std']
    
    # 检查特征值的合理性
    for feature in features:
        print(f"\n   📊 {feature}:")
        
        all_values = df[feature].dropna()
        
        print(f"     总数据点: {len(all_values)}")
        print(f"     数值范围: [{np.min(all_values):.6f}, {np.max(all_values):.6f}]")
        print(f"     均值: {np.mean(all_values):.6f}")
        print(f"     标准差: {np.std(all_values):.6f}")
        
        # 检查异常值
        if 'noise_floor' in feature:
            # 底噪特征应该是负值
            positive_count = np.sum(all_values > 0)
            if positive_count > 0:
                print(f"     ⚠️  有 {positive_count} 个正值 (底噪特征应该是负值)")
                positive_values = all_values[all_values > 0]
                print(f"       正值: {positive_values[:10]}...")  # 只显示前10个
        
        elif 'stability' in feature:
            # 稳定性特征应该是非负值
            negative_count = np.sum(all_values < 0)
            if negative_count > 0:
                print(f"     ⚠️  有 {negative_count} 个负值 (稳定性特征应该是非负值)")
                negative_values = all_values[all_values < 0]
                print(f"       负值: {negative_values[:10]}...")  # 只显示前10个
        
        # 检查是否有NaN或无穷大
        nan_count = np.sum(pd.isna(df[feature]))
        inf_count = np.sum(np.isinf(all_values))
        
        if nan_count > 0:
            print(f"     ⚠️  有 {nan_count} 个NaN值")
        
        if inf_count > 0:
            print(f"     ⚠️  有 {inf_count} 个无穷大值")
    
    # 检查同一文件不同频段的特征一致性
    print(f"\n   检查同一文件不同频段的特征变化:")
    
    # 选择一个正常文件和一个噪声文件进行检查
    normal_files = df[df['is_target'] == False]['filename'].unique()
    target_files = df[df['is_target'] == True]['filename'].unique()
    
    if len(normal_files) > 0 and len(target_files) > 0:
        normal_file = normal_files[0]
        target_file = target_files[0]
        
        print(f"     正常文件示例: {normal_file}")
        normal_file_data = df[df['filename'] == normal_file].sort_values('segment_idx')
        
        for feature in features[:2]:  # 只检查前两个特征
            values = normal_file_data[feature].values
            print(f"       {feature}: 范围 [{np.min(values):.3f}, {np.max(values):.3f}], 变化 {np.max(values) - np.min(values):.3f}")
        
        print(f"     噪声文件示例: {target_file}")
        target_file_data = df[df['filename'] == target_file].sort_values('segment_idx')
        
        for feature in features[:2]:  # 只检查前两个特征
            values = target_file_data[feature].values
            print(f"       {feature}: 范围 [{np.min(values):.3f}, {np.max(values):.3f}], 变化 {np.max(values) - np.min(values):.3f}")

if __name__ == "__main__":
    debug_absolute_range_issue()
