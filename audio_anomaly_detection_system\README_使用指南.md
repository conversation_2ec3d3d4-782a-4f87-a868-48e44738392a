# 音频异响检测系统使用指南

## 🎯 系统概述

这是一个基于机器学习的音频异响检测系统，专门用于检测步进扫频音频中的异响问题，包括：
- 竖线干扰（频谱图中的垂直线条）
- 低频漏气噪声
- 总谐波失真(THD)
- 其他音频异常

## 📁 文件结构

```
├── easy_detector.py              # 🌟 简易检测器（推荐使用）
├── usage_guide.py               # 完整使用指南
├── optimized_feature_extractor.py  # 优化的特征提取器
├── improved_vertical_detection.py  # 改进的竖线干扰检测
├── feature_analysis.py          # 特征有效性分析
├── step_chirp_test.py           # 原始分析脚本
├── freq_split.py               # 频段分割工具
└── dataset/                    # 数据集目录
    ├── pos/                   # 正样本（正常音频）
    └── neg/                   # 负样本（异常音频）
```

## 🚀 快速开始

### 方法1: 使用简易检测器（推荐）

```python
from easy_detector import AudioAnomalyDetector

# 创建检测器
detector = AudioAnomalyDetector()

# 如果有数据集，先训练模型
detector.train_from_dataset("dataset")

# 检测单个文件
result = detector.detect_anomaly("your_audio.wav")
print(f"检测结果: {result['overall_status']}")
print(f"正常概率: {result['normal_probability']:.3f}")

# 批量检测
results = detector.batch_detect("audio_folder", "results.csv")
```

### 方法2: 直接运行演示

```bash
python easy_detector.py
```

## 📋 详细使用步骤

### 1. 准备数据集

将您的音频文件按以下结构组织：

```
dataset/
├── pos/          # 正常音频文件
│   ├── normal1.wav
│   ├── normal2.wav
│   └── ...
└── neg/          # 异常音频文件
    ├── anomaly1.wav
    ├── anomaly2.wav
    └── ...
```

### 2. 训练检测器

```python
from easy_detector import AudioAnomalyDetector

detector = AudioAnomalyDetector()

# 从数据集训练
detector.train_from_dataset("dataset")

# 模型会自动保存为 audio_anomaly_detector.pkl
```

### 3. 使用检测器

#### 检测单个文件

```python
# 加载已训练的模型
detector.load_model("audio_anomaly_detector.pkl")

# 检测音频
result = detector.detect_anomaly("test_audio.wav")

print(f"文件: {result['file']}")
print(f"状态: {result['overall_status']}")
print(f"置信度: {result['confidence']}")
print(f"正常概率: {result['normal_probability']:.3f}")
print(f"异常频段: {result['anomaly_segments']}/{result['total_segments']}")
```

#### 批量检测

```python
# 检测整个文件夹
results = detector.batch_detect("test_folder", "detection_results.csv")

# 查看结果
for result in results:
    print(f"{result['file']}: {result['overall_status']}")
```

### 4. 分析特征（可选）

```python
# 提取单个文件的详细特征
features_df = detector.extract_audio_features("audio.wav")
print(features_df.describe())

# 分析特征重要性
from feature_analysis import analyze_feature_effectiveness
analyze_feature_effectiveness()
```

## 🔧 参数调整

### 特征提取参数

```python
# 自定义参数提取特征
from optimized_feature_extractor import extract_features_from_audio

features = extract_features_from_audio(
    "audio.wav",
    min_duration=156,        # 最小持续时间(ms)
    energy_threshold_db=-45, # 能量阈值(dB)
    start_freq=100,          # 起始频率(Hz)
    stop_freq=20000          # 结束频率(Hz)
)
```

### 竖线干扰检测参数

```python
from improved_vertical_detection import detect_vertical_line_interference_v2

# 调整检测敏感度
vertical_score, times, counts = detect_vertical_line_interference_v2(
    S, freqs, f0,
    threshold_ratio=3,      # 能量突增阈值倍数 (2-5)
    min_affected_freqs=10   # 最小受影响频率数 (5-15)
)
```

## 📊 结果解释

### 检测结果状态

- **正常**: 音频质量良好，无明显异响
- **可疑**: 存在轻微异常，建议进一步检查
- **异常**: 检测到明显异响问题

### 置信度等级

- **高**: 检测结果可靠性高
- **中**: 检测结果有一定可靠性
- **低**: 建议人工复核

### 关键特征说明

- `vertical_line_count`: 竖线干扰数量
- `vertical_interference_ratio`: 竖线干扰时间比例
- `low_freq_energy_ratio`: 低频能量比例（漏气噪声）
- `thd`/`thdn`: 总谐波失真
- `noise_power_ratio`: 噪声功率比例

## 🛠️ 高级用法

### 自定义特征选择

```python
# 使用自定义特征训练
detector = AudioAnomalyDetector()
detector.core_features = [
    'vertical_line_count',
    'low_freq_energy_ratio',
    'thd',
    'noise_power_ratio'
]
detector.train_from_dataset("dataset")
```

### 模型性能分析

```python
# 运行完整的特征分析
from feature_analysis import analyze_feature_effectiveness
analyze_feature_effectiveness()

# 对比优化前后性能
from optimized_classifier import main as run_comparison
run_comparison()
```

### 可视化分析

```python
# 运行原始分析脚本查看详细图表
from step_chirp_test import analyze_step_chirp_batch

results = analyze_step_chirp_batch(
    ["your_audio.wav"],
    result_root="analysis_output",
    plot=True  # 生成可视化图表
)
```

## ⚠️ 注意事项

1. **音频格式**: 目前只支持WAV格式
2. **采样率**: 建议使用48kHz或96kHz采样率
3. **文件命名**: 包含"156"的文件会使用156ms最小持续时间，其他使用153ms
4. **数据平衡**: 训练时确保正负样本数量相对平衡
5. **模型更新**: 随着数据增加，建议定期重新训练模型

## 🔍 故障排除

### 常见问题

1. **"特征提取失败"**
   - 检查音频文件是否损坏
   - 确认文件格式为WAV
   - 检查文件路径是否正确

2. **"模型性能差"**
   - 增加训练数据
   - 检查正负样本标签是否正确
   - 调整特征选择

3. **"检测结果不准确"**
   - 重新标注训练数据
   - 调整检测阈值
   - 使用更多特征

### 获取帮助

如果遇到问题，可以：
1. 查看控制台输出的详细错误信息
2. 检查生成的CSV文件中的特征值
3. 运行`usage_guide.py`查看完整示例

## 📈 性能指标

当前系统在测试数据集上的性能：
- 准确率: ~70%
- 特征数量: 从83个优化到15个核心特征
- 计算效率: 提升约75%
- 竖线干扰检测: 能有效区分正负样本

## 🎉 快速测试

运行以下命令进行快速测试：

```bash
# 完整工作流程演示
python usage_guide.py

# 简易检测器演示
python easy_detector.py

# 竖线干扰检测测试
python improved_vertical_detection.py
```
