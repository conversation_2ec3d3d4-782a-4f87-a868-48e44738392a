import os
import numpy as np
import matplotlib.pyplot as plt
import librosa
from step_chirp_test import detect_vertical_line_interference, analyze_step_chirp_batch
from freq_split import split_freq_steps

def test_vertical_line_detection():
    """
    测试修正后的竖线干扰检测
    """
    # 测试负样本（包含竖线干扰）
    neg_sample = "dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
    
    if not os.path.exists(neg_sample):
        print(f"[ERROR] 测试文件不存在: {neg_sample}")
        return
    
    print(f"[INFO] 测试竖线干扰检测: {os.path.basename(neg_sample)}")
    
    # 分割频段
    step_bounds, freq_table = split_freq_steps(
        neg_sample,
        min_duration=153,
        energy_threshold_db=-45,
        plot=False
    )
    
    # 加载音频
    y, sr = librosa.load(neg_sample, sr=None)
    
    # 测试几个频段的竖线干扰检测
    test_segments = [0, 10, 20, 30, 40]  # 测试前几个频段
    
    for i in test_segments:
        if i >= len(step_bounds) or i >= len(freq_table):
            continue
            
        t0, t1 = step_bounds[i]
        f0 = freq_table[i]
        
        # 提取音频段
        L = t1 - t0
        seg_start = t0 + 0.1 * L
        seg_end = t1 - 0.1 * L
        y_seg = y[int(seg_start*sr):int(seg_end*sr)]
        
        if len(y_seg) < 256:
            continue
        
        # STFT分析
        n_fft = min(2048, len(y_seg))
        S = np.abs(librosa.stft(y_seg, n_fft=n_fft, hop_length=512))**2
        freqs = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
        
        # 检测竖线干扰
        vertical_score, interference_times, affected_freq_counts = detect_vertical_line_interference(
            S, freqs, f0, threshold_ratio=3, min_affected_freqs=5
        )
        
        print(f"\n频段 {i}: {f0:.1f}Hz")
        print(f"  竖线干扰评分: {vertical_score:.3f}")
        print(f"  干扰时间帧数: {len(interference_times)}")
        print(f"  总时间帧数: {S.shape[1]}")
        if affected_freq_counts:
            print(f"  平均受影响频率数: {np.mean(affected_freq_counts):.1f}")
            print(f"  最大受影响频率数: {np.max(affected_freq_counts)}")
        
        # 可视化这个频段的频谱
        if i == 20:  # 选择一个频段进行详细可视化
            plt.figure(figsize=(12, 8))
            
            # 子图1：原始频谱
            plt.subplot(2, 2, 1)
            times = librosa.frames_to_time(np.arange(S.shape[1]), sr=sr, hop_length=512)
            S_db = librosa.amplitude_to_db(S, ref=np.max)
            plt.imshow(S_db, aspect='auto', origin='lower', 
                      extent=[times[0], times[-1], freqs[0], freqs[-1]])
            plt.colorbar(label='dB')
            plt.ylabel('Frequency (Hz)')
            plt.xlabel('Time (s)')
            plt.title(f'频谱图 - {f0:.1f}Hz段')
            plt.yscale('log')
            
            # 标记干扰时间点
            for t_idx in interference_times:
                t_time = times[t_idx] if t_idx < len(times) else times[-1]
                plt.axvline(t_time, color='red', linestyle='--', alpha=0.7)
            
            # 子图2：非主频非谐波频谱
            plt.subplot(2, 2, 2)
            # 识别主频和谐波
            harmonics = []
            for h in range(1, 6):
                harm_freq = h * f0
                if harm_freq < freqs[-1]:
                    harm_bandwidth = max(50, f0 * 0.05)
                    harm_mask = np.abs(freqs - harm_freq) <= harm_bandwidth
                    harmonics.extend(np.where(harm_mask)[0])
            
            harmonic_indices = sorted(list(set(harmonics)))
            non_harmonic_mask = np.ones(len(freqs), dtype=bool)
            if harmonic_indices:
                non_harmonic_mask[harmonic_indices] = False
            
            S_non_harmonic = S[non_harmonic_mask, :]
            freqs_non_harmonic = freqs[non_harmonic_mask]
            
            S_non_harmonic_db = librosa.amplitude_to_db(S_non_harmonic, ref=np.max)
            plt.imshow(S_non_harmonic_db, aspect='auto', origin='lower',
                      extent=[times[0], times[-1], freqs_non_harmonic[0], freqs_non_harmonic[-1]])
            plt.colorbar(label='dB')
            plt.ylabel('Frequency (Hz) - Non-harmonic')
            plt.xlabel('Time (s)')
            plt.title('非主频非谐波频谱')
            plt.yscale('log')
            
            # 标记干扰时间点
            for t_idx in interference_times:
                t_time = times[t_idx] if t_idx < len(times) else times[-1]
                plt.axvline(t_time, color='red', linestyle='--', alpha=0.7)
            
            # 子图3：每个时间帧的受影响频率数
            plt.subplot(2, 2, 3)
            all_affected_counts = []
            for t in range(S.shape[1]):
                frame_energy = S_non_harmonic[:, t]
                baseline = np.median(S_non_harmonic, axis=0)[t]
                threshold = baseline * 3
                affected = np.sum(frame_energy > threshold)
                all_affected_counts.append(affected)
            
            plt.plot(times, all_affected_counts, 'b-', alpha=0.7)
            plt.axhline(y=5, color='red', linestyle='--', label='阈值 (5)')
            plt.xlabel('Time (s)')
            plt.ylabel('受影响频率数')
            plt.title('时间帧受影响频率统计')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 子图4：基线能量变化
            plt.subplot(2, 2, 4)
            baseline_energy = np.median(S_non_harmonic, axis=0)
            plt.plot(times, baseline_energy, 'g-', label='基线能量')
            plt.xlabel('Time (s)')
            plt.ylabel('能量')
            plt.title('基线能量变化')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(f'vertical_detection_test_{f0:.0f}Hz.png', dpi=150)
            plt.show()
            
            break

def compare_samples():
    """
    对比正负样本的竖线干扰检测结果
    """
    pos_sample = "dataset/pos/录音_步进扫频_100Hz至20000Hz_20250714_152023_156.wav"
    neg_sample = "dataset/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav"
    
    samples = [
        (pos_sample, "正样本", 156),
        (neg_sample, "负样本", 153)
    ]
    
    for sample_path, label, min_dur in samples:
        if not os.path.exists(sample_path):
            print(f"[WARN] 文件不存在: {sample_path}")
            continue
            
        print(f"\n[INFO] 分析 {label}: {os.path.basename(sample_path)}")
        
        # 使用现有的分析函数
        results = analyze_step_chirp_batch(
            [sample_path], 
            result_root=f"vertical_test_{label}",
            min_duration=min_dur,
            energy_threshold_db=-45,
            plot=False
        )
        
        if sample_path in results:
            df = results[sample_path]
            print(f"  平均竖线干扰评分: {df['vertical_score'].mean():.3f}")
            print(f"  最大竖线干扰评分: {df['vertical_score'].max():.3f}")
            print(f"  高干扰频段数 (>0.3): {(df['vertical_score'] > 0.3).sum()}")
            print(f"  总频段数: {len(df)}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试修正后的竖线干扰检测")
    print("=" * 60)
    
    # 详细测试
    test_vertical_line_detection()
    
    print("\n" + "=" * 60)
    print("对比正负样本")
    print("=" * 60)
    
    # 对比测试
    compare_samples()
