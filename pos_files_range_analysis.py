#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合test20250717中pos文件夹下所有文件的93段谐波数量范围分析
生成各段最小最大值范围图片
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
import multiprocessing as mp
from multiprocessing import Pool

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib中文支持
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def analyze_pos_files_range():
    """分析pos文件夹下所有文件的93段范围"""
    print("🎯 综合分析test20250717/pos文件夹下所有文件的93段谐波范围")
    print("="*60)
    
    # pos文件夹路径
    pos_dir = os.path.join('test20250717', 'pos')
    
    if not os.path.exists(pos_dir):
        print("❌ pos文件夹不存在")
        return
    
    # 查找pos文件夹下的所有wav文件
    wav_files = []
    for root, dirs, files in os.walk(pos_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                wav_files.append(os.path.join(root, file))
    
    print(f"📊 找到{len(wav_files)}个pos文件")
    
    if len(wav_files) == 0:
        print("❌ pos文件夹中未找到wav文件")
        return
    
    # 使用多进程分析所有文件
    print("🚀 使用多进程分析所有pos文件...")
    
    num_processes = min(mp.cpu_count(), len(wav_files))
    with Pool(processes=num_processes) as pool:
        results = pool.map(analyze_single_pos_file, wav_files)
    
    # 过滤成功的结果
    successful_results = [r for r in results if r is not None]
    
    print(f"✅ 成功分析{len(successful_results)}个文件")
    
    if len(successful_results) == 0:
        print("❌ 没有成功分析的文件")
        return
    
    # 计算各段的范围统计
    range_stats = calculate_segment_ranges(successful_results)
    
    # 生成范围分析图片
    create_range_visualization(range_stats, successful_results)

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def analyze_single_pos_file(audio_path):
    """分析单个pos文件"""
    try:
        print(f"  分析: {os.path.basename(audio_path)}")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segments_data = []
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                segments_data.append({
                    'seg_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'harmonic_count': 0
                })
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 高分辨率FFT分析
            fft_size = 131072  # 128k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs_fft[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 谐波检测
            if noise_analysis:
                harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
                harmonic_count = len(harmonic_analysis)
            else:
                harmonic_count = 0
            
            segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'harmonic_count': harmonic_count
            })
        
        return {
            'filename': os.path.basename(audio_path),
            'filepath': audio_path,
            'segments': segments_data
        }
        
    except Exception as e:
        print(f"  ❌ 分析失败: {os.path.basename(audio_path)} - {e}")
        return None

def calculate_segment_ranges(successful_results):
    """计算各段的范围统计"""
    
    # 确定段数
    num_segments = len(successful_results[0]['segments'])
    
    range_stats = []
    
    for seg_idx in range(num_segments):
        # 收集该段所有文件的谐波数量
        harmonic_counts = []
        expected_freq = None
        
        for result in successful_results:
            if seg_idx < len(result['segments']):
                harmonic_counts.append(result['segments'][seg_idx]['harmonic_count'])
                if expected_freq is None:
                    expected_freq = result['segments'][seg_idx]['expected_freq']
        
        if harmonic_counts:
            range_stats.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'min_harmonics': min(harmonic_counts),
                'max_harmonics': max(harmonic_counts),
                'mean_harmonics': np.mean(harmonic_counts),
                'std_harmonics': np.std(harmonic_counts),
                'range_harmonics': max(harmonic_counts) - min(harmonic_counts),
                'all_counts': harmonic_counts
            })
    
    return range_stats

def create_range_visualization(range_stats, successful_results):
    """创建范围可视化"""
    
    print(f"\n🎨 生成pos文件夹93段范围分析图...")
    
    # 提取数据
    segment_indices = [s['seg_idx'] for s in range_stats]
    expected_freqs = [s['expected_freq'] for s in range_stats]
    min_harmonics = [s['min_harmonics'] for s in range_stats]
    max_harmonics = [s['max_harmonics'] for s in range_stats]
    mean_harmonics = [s['mean_harmonics'] for s in range_stats]
    std_harmonics = [s['std_harmonics'] for s in range_stats]
    
    # 创建图表 (2行1列)
    fig, axes = plt.subplots(2, 1, figsize=(20, 16))
    fig.suptitle(f'Pos文件夹93段谐波范围分析\n基于{len(successful_results)}个正常样本文件', 
                 fontsize=16, fontweight='bold')
    
    # 1. 段索引 vs 谐波数量范围
    ax1 = axes[0]
    
    # 绘制范围区域
    ax1.fill_between(segment_indices, min_harmonics, max_harmonics, 
                    alpha=0.3, color='lightgreen', label='正常样本范围 (最小-最大)')
    
    # 绘制平均值线
    ax1.plot(segment_indices, mean_harmonics, 'g-', linewidth=2, 
            label='平均值', marker='o', markersize=3)
    
    # 绘制标准差区间
    mean_plus_std = [m + s for m, s in zip(mean_harmonics, std_harmonics)]
    mean_minus_std = [max(0, m - s) for m, s in zip(mean_harmonics, std_harmonics)]
    ax1.fill_between(segment_indices, mean_minus_std, mean_plus_std, 
                    alpha=0.2, color='green', label='平均值±标准差')
    
    ax1.set_xlabel('段索引')
    ax1.set_ylabel('谐波数量')
    ax1.set_title('各段谐波数量范围分布')
    ax1.set_ylim(0, 30)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 设置x轴刻度
    ax1.set_xticks(range(0, len(segment_indices), 10))
    ax1.set_xticklabels([f'第{i}段' for i in range(0, len(segment_indices), 10)])
    
    # 2. 频率 vs 谐波数量范围
    ax2 = axes[1]
    
    # 绘制范围区域
    ax2.fill_between(expected_freqs, min_harmonics, max_harmonics, 
                    alpha=0.3, color='lightblue', label='正常样本范围 (最小-最大)')
    
    # 绘制平均值线
    ax2.plot(expected_freqs, mean_harmonics, 'b-', linewidth=2, 
            label='平均值', marker='o', markersize=3)
    
    # 绘制标准差区间
    ax2.fill_between(expected_freqs, mean_minus_std, mean_plus_std, 
                    alpha=0.2, color='blue', label='平均值±标准差')
    
    ax2.set_xlabel('期望频率 (Hz)')
    ax2.set_ylabel('谐波数量')
    ax2.set_title('谐波数量范围随频率变化')
    ax2.set_ylim(0, 30)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_xscale('log')
    
    # 添加统计信息
    total_files = len(successful_results)
    total_segments = len(range_stats)
    overall_min = min(min_harmonics)
    overall_max = max(max_harmonics)
    overall_mean = np.mean(mean_harmonics)
    
    info_text = f"统计信息:\n"
    info_text += f"分析文件数: {total_files}个\n"
    info_text += f"分析段数: {total_segments}段\n"
    info_text += f"全局范围: {overall_min}-{overall_max}个\n"
    info_text += f"全局平均: {overall_mean:.1f}个/段\n"
    
    # 频率段统计
    low_freq_stats = [s for s in range_stats if s['expected_freq'] <= 1000]
    mid_freq_stats = [s for s in range_stats if 1000 < s['expected_freq'] <= 5000]
    high_freq_stats = [s for s in range_stats if s['expected_freq'] > 5000]
    
    if low_freq_stats:
        low_avg = np.mean([s['mean_harmonics'] for s in low_freq_stats])
        info_text += f"低频段平均: {low_avg:.1f}个/段\n"
    
    if mid_freq_stats:
        mid_avg = np.mean([s['mean_harmonics'] for s in mid_freq_stats])
        info_text += f"中频段平均: {mid_avg:.1f}个/段\n"
    
    if high_freq_stats:
        high_avg = np.mean([s['mean_harmonics'] for s in high_freq_stats])
        info_text += f"高频段平均: {high_avg:.1f}个/段"
    
    ax2.text(0.02, 0.98, info_text, transform=ax2.transAxes, 
             verticalalignment='top', fontsize=12,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'pos_files_93segments_range_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ pos文件夹范围分析图已保存: {filename}")
    plt.close()
    
    # 打印详细统计
    print_range_statistics(range_stats, successful_results)

def print_range_statistics(range_stats, successful_results):
    """打印范围统计"""
    
    print(f"\n📊 Pos文件夹93段谐波范围统计:")
    print(f"{'='*80}")
    print(f"分析文件数: {len(successful_results)}个")
    print(f"分析段数: {len(range_stats)}段")
    
    print(f"\n所有93段详细统计:")
    print(f"{'段号':<4} {'频率(Hz)':<8} {'最小值':<6} {'最大值':<6} {'范围':<6} {'平均值':<8} {'标准差':<8}")
    print(f"{'-'*60}")

    for i, stats in enumerate(range_stats):
        print(f"{stats['seg_idx']:<4} {stats['expected_freq']:<8.0f} {stats['min_harmonics']:<6} "
              f"{stats['max_harmonics']:<6} {stats['range_harmonics']:<6} {stats['mean_harmonics']:<8.1f} "
              f"{stats['std_harmonics']:<8.2f}")

    print(f"\n📋 提取最大值阈值列表:")
    print("real_thresholds = [")
    for i in range(0, len(range_stats), 10):
        line = "    "
        for j in range(10):
            if i+j < len(range_stats):
                line += f"{range_stats[i+j]['max_harmonics']:2d}"
                if i+j < len(range_stats)-1:
                    line += ", "
        if i+10 < len(range_stats):
            line += f",  # 段{i}-{min(i+9, len(range_stats)-1)}"
        else:
            line += f"   # 段{i}-{len(range_stats)-1}"
        print(line)
    print("]")
    
    # 全局统计
    all_mins = [s['min_harmonics'] for s in range_stats]
    all_maxs = [s['max_harmonics'] for s in range_stats]
    all_means = [s['mean_harmonics'] for s in range_stats]
    
    print(f"\n📈 全局统计:")
    print(f"  最小谐波数: {min(all_mins)}个")
    print(f"  最大谐波数: {max(all_maxs)}个")
    print(f"  平均谐波数: {np.mean(all_means):.1f}个/段")
    print(f"  标准差: {np.std(all_means):.2f}")

if __name__ == "__main__":
    analyze_pos_files_range()
