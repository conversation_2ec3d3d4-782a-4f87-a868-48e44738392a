import numpy as np
import librosa
import matplotlib.pyplot as plt
import os
import librosa.display
from align import align_signals_by_energy

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def get_mel_spectrogram(y, sr, n_fft=2048, hop_length=512, n_mels=40):
    S = librosa.feature.melspectrogram(y=y, sr=sr, n_fft=n_fft,
                                       hop_length=hop_length, n_mels=n_mels)
    return librosa.power_to_db(S, ref=np.max)

def compute_mel_leakage(S_ref_db, S_test_db, threshold=6):
    diff = S_test_db - S_ref_db
    leak_mask = diff > threshold
    return np.mean(np.any(leak_mask, axis=0)), diff

def plot_and_save_mel(S_db, title, filename, sr, hop_length):
    plt.figure(figsize=(10, 4))
    librosa.display.specshow(S_db, sr=sr, hop_length=hop_length,
                             x_axis='time', y_axis='mel', cmap='viridis')
    plt.title(title)
    plt.colorbar(format="%+2.0f dB")
    plt.tight_layout()
    plt.savefig(filename, dpi=150)
    plt.close()

def plot_and_save_diff(S_diff, title, filename, sr, hop_length):
    plt.figure(figsize=(10, 4))
    librosa.display.specshow(S_diff, sr=sr, hop_length=hop_length,
                             x_axis='time', y_axis='mel', cmap='coolwarm', vmin=-10, vmax=10)
    plt.title(title)
    plt.colorbar(label='dB 差异')
    plt.tight_layout()
    plt.savefig(filename, dpi=150)
    plt.close()

def analyze_speaker_mel(ref_path, test_path, sr=44100, output_dir="mel_analysis_output"):
    os.makedirs(output_dir, exist_ok=True)
    name_ref = os.path.splitext(os.path.basename(ref_path))[0]
    name_test = os.path.splitext(os.path.basename(test_path))[0]

    y_ref, _  = librosa.load(ref_path, sr=sr, mono=True)
    y_test, _ = librosa.load(test_path, sr=sr, mono=True)

    y_ref, y_test = align_signals_by_energy(y_ref, y_test, fs=sr)
    y_ref = y_ref[int(1.09 * sr):int(11.729 * sr)]
    y_test = y_test[int(1.09 * sr):int(11.729 * sr)]
    min_len = min(len(y_ref), len(y_test))
    y_ref, y_test = y_ref[:min_len], y_test[:min_len]

    S_ref_db = get_mel_spectrogram(y_ref, sr)
    S_test_db = get_mel_spectrogram(y_test, sr)

    leak_ratio, diff_db = compute_mel_leakage(S_ref_db, S_test_db)

    print(f"\n--- [{name_ref}] vs [{name_test}] ---")
    print(f"Mel 泄漏帧占比: {leak_ratio*100:.2f}%")

    plot_and_save_mel(S_ref_db,
                      f"参考样本 Mel 频谱 [{name_ref}]",
                      os.path.join(output_dir, f"mel_spec_ref_{name_ref}.png"),
                      sr, hop_length=512)

    plot_and_save_mel(S_test_db,
                      f"测试样本 Mel 频谱 [{name_test}]",
                      os.path.join(output_dir, f"mel_spec_test_{name_test}.png"),
                      sr, hop_length=512)

    plot_and_save_diff(diff_db,
                       f"Mel 泄漏差异 [{name_ref} vs {name_test}]",
                       os.path.join(output_dir, f"mel_diff_{name_ref}_vs_{name_test}.png"),
                       sr, hop_length=512)

if __name__ == "__main__":
    REF_LIST = [
        r"20250707\sweep_test_0db.wav",
    ]
    TEST_LIST = [
        r"20250707\test0.wav",
        r"20250707\test1.wav",
        r"20250707\test2.wav",
        r"20250707\test3.wav",
    ]
    fs = 48000
    for ref_path in REF_LIST:
        for test_path in TEST_LIST:
            analyze_speaker_mel(ref_path, test_path, sr=fs)
