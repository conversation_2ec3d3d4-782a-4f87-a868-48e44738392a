#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能频率分割模块 - 简化版本
通过生成参考扫频信号并智能对齐来精确定位开始时间
"""

import numpy as np
import librosa
import librosa.display
import matplotlib.pyplot as plt
from scipy import signal
import sys
import os
sys.path.append('..')
from chirp import gen_freq_step

class SmartFreqSplitter:
    def __init__(self, start_freq=100, stop_freq=20000, octave=12, fs=48000, 
                 min_cycles=10, min_duration=156):
        """
        初始化智能频率分割器
        """
        self.start_freq = start_freq
        self.stop_freq = stop_freq
        self.octave = octave
        self.fs = fs
        self.min_cycles = min_cycles
        self.min_duration = min_duration
        
        # 生成参考扫频信号
        self.reference_signal, self.reference_times, self.freq_dict = self._generate_reference_signal()
        self.freq_table = list(self.freq_dict.keys())
        self.step_durations = [self.freq_dict[f][2] / fs for f in self.freq_table]
        
        print(f"参考信号生成完成: {len(self.freq_table)}个频点, 总时长{self.reference_times[-1]:.2f}秒")
    
    def _generate_reference_signal(self):
        """生成参考扫频信号"""
        sine_wave, t_list, freq_ssample_dict = gen_freq_step(
            self.start_freq, self.stop_freq, self.min_cycles, 
            self.min_duration, self.octave, self.fs, A=1
        )
        return sine_wave, t_list, freq_ssample_dict
    
    def split_freq_steps_smart(self, audio_path, 
                              search_window_start=0.1, search_window_end=2.0,
                              correlation_length=1.0, plot=True, debug=False):
        """
        智能频率步进分割
        """
        print(f"\n🔍 智能频率分割分析: {os.path.basename(audio_path)}")
        
        # 读取音频
        y, sr = librosa.load(audio_path, sr=self.fs)
        print(f"音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")
        
        # 如果采样率不匹配，重采样
        if sr != self.fs:
            y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            sr = self.fs
            print(f"重采样到{self.fs}Hz")
        
        # 智能寻找对齐位置
        alignment_result = self._smart_find_alignment(
            y, search_window_start, search_window_end, correlation_length, debug
        )
        
        if alignment_result is None:
            print("❌ 智能对齐失败，使用能量检测")
            return self._fallback_energy_detection(y, sr, plot)
        
        start_offset, correlation_score, search_info = alignment_result
        print(f"✅ 智能对齐成功: 开始时间={start_offset:.3f}秒, 相关性={correlation_score:.3f}")
        
        # 计算步进边界
        step_boundaries = self._calculate_step_boundaries(start_offset)
        
        # 验证对齐质量
        alignment_quality = self._validate_alignment(y, start_offset, correlation_length)
        
        # 可视化结果
        if plot:
            self._plot_smart_results(y, sr, step_boundaries, start_offset, 
                                   correlation_score, search_info, alignment_quality)
        
        alignment_info = {
            'start_offset': start_offset,
            'correlation_score': correlation_score,
            'alignment_quality': alignment_quality,
            'search_info': search_info
        }
        
        return step_boundaries, self.freq_table, alignment_info
    
    def _smart_find_alignment(self, y, search_start, search_end, corr_length, debug):
        """智能寻找对齐位置"""
        # 确定搜索范围
        search_start_sample = int(search_start * self.fs)
        search_end_sample = int(search_end * self.fs)
        corr_length_samples = int(corr_length * self.fs)
        
        # 确保搜索范围有效
        search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
        if search_start_sample >= search_end_sample:
            print(f"❌ 搜索范围无效: {search_start:.2f}s - {search_end:.2f}s")
            return None
        
        # 使用较短的参考段进行对齐
        ref_length_samples = min(corr_length_samples, int(0.3 * self.fs))  # 300ms
        reference_segment = self.reference_signal[:ref_length_samples]
        
        print(f"在{search_start:.2f}s-{search_end:.2f}s窗口内智能搜索...")
        print(f"使用{ref_length_samples/self.fs:.2f}秒参考段进行对齐...")
        
        # 滑动窗口计算
        search_length = search_end_sample - search_start_sample
        correlations = []
        positions = []
        frequency_scores = []
        
        step_size = max(1, self.fs // 500)  # 2ms步长
        for i in range(0, search_length - ref_length_samples, step_size):
            pos = search_start_sample + i
            test_segment = y[pos:pos + ref_length_samples]
            
            if len(test_segment) == len(reference_segment):
                # 计算相关性
                correlation = self._normalized_cross_correlation(reference_segment, test_segment)
                correlations.append(correlation)
                positions.append(pos / self.fs)
                
                # 计算频率特征评分
                freq_score = self._calculate_sweep_start_score(test_segment)
                frequency_scores.append(freq_score)
        
        if not correlations:
            print("❌ 未找到有效的搜索结果")
            return None
        
        # 转换为numpy数组
        correlations = np.array(correlations)
        positions = np.array(positions)
        frequency_scores = np.array(frequency_scores)
        
        # 智能选择最佳位置
        best_position, best_correlation = self._intelligent_selection(
            correlations, positions, frequency_scores, debug
        )
        
        search_info = {
            'search_range': (search_start, search_end),
            'correlations': correlations,
            'positions': positions,
            'frequency_scores': frequency_scores,
            'search_points': len(positions)
        }
        
        return best_position, best_correlation, search_info
    
    def _normalized_cross_correlation(self, ref_signal, test_signal):
        """计算归一化互相关"""
        min_len = min(len(ref_signal), len(test_signal))
        ref_signal = ref_signal[:min_len]
        test_signal = test_signal[:min_len]
        
        ref_norm = ref_signal - np.mean(ref_signal)
        test_norm = test_signal - np.mean(test_signal)
        
        correlation = np.corrcoef(ref_norm, test_norm)[0, 1]
        
        if np.isnan(correlation):
            correlation = 0.0
        
        return correlation
    
    def _calculate_sweep_start_score(self, signal_segment):
        """计算扫频开始特征评分"""
        if len(signal_segment) < 512:
            return 0.0
        
        # 计算频谱
        fft = np.fft.fft(signal_segment)
        freqs = np.fft.fftfreq(len(signal_segment), 1/self.fs)
        magnitude = np.abs(fft[:len(fft)//2])
        freqs = freqs[:len(freqs)//2]
        
        # 定义频率范围
        low_freq_mask = (freqs >= 80) & (freqs <= 300)   # 扫频开始范围
        mid_freq_mask = (freqs >= 300) & (freqs <= 1000)
        high_freq_mask = (freqs >= 1000) & (freqs <= 5000)
        
        if np.sum(low_freq_mask) == 0:
            return 0.0
        
        # 计算各频段能量
        low_energy = np.sum(magnitude[low_freq_mask])
        mid_energy = np.sum(magnitude[mid_freq_mask]) if np.sum(mid_freq_mask) > 0 else 0
        high_energy = np.sum(magnitude[high_freq_mask]) if np.sum(high_freq_mask) > 0 else 0
        
        total_energy = low_energy + mid_energy + high_energy
        
        if total_energy == 0:
            return 0.0
        
        # 扫频开始特征：低频能量占主导
        low_freq_ratio = low_energy / total_energy
        
        # 计算频谱尖锐度
        peak_magnitude = np.max(magnitude[low_freq_mask])
        avg_magnitude = np.mean(magnitude[low_freq_mask])
        sharpness = peak_magnitude / avg_magnitude if avg_magnitude > 0 else 0
        
        # 综合评分
        score = low_freq_ratio * 0.7 + min(sharpness / 10, 0.3)
        
        return min(score, 1.0)
    
    def _intelligent_selection(self, correlations, positions, frequency_scores, debug):
        """智能选择最佳对齐位置"""
        # 综合评分：相关性 + 频率特征
        combined_scores = correlations * 0.6 + frequency_scores * 0.4
        
        # 策略1: 寻找第一个超过阈值的点
        threshold = 0.4
        above_threshold = np.where(combined_scores >= threshold)[0]
        
        if len(above_threshold) > 0:
            # 选择第一个超过阈值的点
            best_idx = above_threshold[0]
            method = "first_above_threshold"
        else:
            # 策略2: 选择综合评分最高的点
            best_idx = np.argmax(combined_scores)
            method = "max_combined_score"
        
        best_position = positions[best_idx]
        best_correlation = correlations[best_idx]
        
        if debug:
            print(f"  智能选择结果:")
            print(f"    方法: {method}")
            print(f"    位置: {best_position:.3f}s")
            print(f"    相关性: {best_correlation:.3f}")
            print(f"    频率评分: {frequency_scores[best_idx]:.3f}")
            print(f"    综合评分: {combined_scores[best_idx]:.3f}")
        
        return best_position, best_correlation
    
    def _calculate_step_boundaries(self, start_offset):
        """计算步进边界"""
        step_boundaries = []
        t = start_offset
        
        for duration in self.step_durations:
            step_boundaries.append((t, t + duration))
            t += duration
        
        print(f"计算出{len(step_boundaries)}个步进区间")
        return step_boundaries
    
    def _validate_alignment(self, y, start_offset, validation_length):
        """验证对齐质量"""
        validation_samples = int(validation_length * self.fs)
        start_sample = int(start_offset * self.fs)
        
        if start_sample + validation_samples > len(y):
            validation_samples = len(y) - start_sample
        
        if validation_samples <= 0:
            return {'quality': 'poor', 'overall_score': 0.0}
        
        # 提取对应段
        audio_segment = y[start_sample:start_sample + validation_samples]
        ref_segment = self.reference_signal[:validation_samples]
        
        # 计算质量指标
        correlation = self._normalized_cross_correlation(ref_segment, audio_segment)
        
        # 综合评分
        if correlation > 0.7:
            quality = 'excellent'
        elif correlation > 0.5:
            quality = 'good'
        elif correlation > 0.3:
            quality = 'fair'
        else:
            quality = 'poor'
        
        return {
            'quality': quality,
            'overall_score': correlation,
            'time_correlation': correlation,
            'validation_length': validation_length
        }
    
    def _fallback_energy_detection(self, y, sr, plot):
        """回退到能量检测方法"""
        print("使用能量检测方法...")
        
        # RMS能量检测
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms_db = librosa.amplitude_to_db(rms, ref=np.max)
        frame_times = librosa.frames_to_time(np.arange(len(rms_db)), sr=sr, hop_length=512)
        
        # 寻找能量阈值以上的起点
        energy_threshold_db = -35
        above_threshold = np.where(rms_db > energy_threshold_db)[0]
        
        if len(above_threshold) > 0:
            start_offset = frame_times[above_threshold[0]]
        else:
            start_offset = 0.2
        
        step_boundaries = self._calculate_step_boundaries(start_offset)
        
        return step_boundaries, self.freq_table, {'method': 'energy_detection', 'start_offset': start_offset}

def split_freq_steps_smart(audio_path, start_freq=100, stop_freq=20000, octave=12, 
                          min_cycles=10, min_duration=156, fs=48000,
                          search_window_start=0.1, search_window_end=2.0,
                          correlation_length=1.0, plot=True, debug=False):
    """
    智能频率步进分割函数
    """
    splitter = SmartFreqSplitter(start_freq, stop_freq, octave, fs, min_cycles, min_duration)
    return splitter.split_freq_steps_smart(
        audio_path, search_window_start, search_window_end, correlation_length, plot, debug
    )

if __name__ == "__main__":
    # 测试智能频率分割
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    step_bounds, freq_table, alignment_info = split_freq_steps_smart(
        audio_path, 
        min_duration=153, 
        plot=False,  # 暂时关闭绘图
        debug=True,
        search_window_start=0.1,
        search_window_end=1.5,
        correlation_length=1.0
    )
    
    print(f"\n✅ 智能分割完成:")
    print(f"  步进区间数: {len(step_bounds)}")
    print(f"  频率点数: {len(freq_table)}")
    print(f"  对齐信息: {alignment_info}")
