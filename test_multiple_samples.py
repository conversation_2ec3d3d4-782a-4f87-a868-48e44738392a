#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个文件夹的样本
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from pathlib import Path

sys.path.append('.')
from final_audio_detection_system.freq_split_optimized import split_freq_steps_optimized

def test_folder_samples():
    """测试两个文件夹的样本"""
    print("🔍 测试两个文件夹的样本")
    print("="*60)
    
    # 定义测试文件夹
    folders = [
        r"../test20250717/pos/sd卡",
        r"../test20250717/pos/tf卡"
    ]
    
    results = []
    
    for folder in folders:
        print(f"\n📁 测试文件夹: {folder}")
        print("-" * 40)
        
        if not os.path.exists(folder):
            print(f"❌ 文件夹不存在: {folder}")
            continue
        
        # 获取所有wav文件
        wav_files = glob.glob(os.path.join(folder, "*.wav"))
        print(f"找到 {len(wav_files)} 个wav文件")
        
        for i, audio_path in enumerate(wav_files[:5]):  # 测试前5个文件
            filename = os.path.basename(audio_path)
            folder_name = os.path.basename(folder)
            
            print(f"\n🎵 [{i+1}/{min(5, len(wav_files))}] 测试文件: {filename}")
            
            try:
                # 调用优化的频率分割算法
                step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
                    audio_path,
                    min_duration=153,
                    plot=False,  # 不绘图，加快测试速度
                    debug=True,  # 显示详细信息
                    search_window_start=0.1,
                    search_window_end=1.5,
                    correlation_length=1.0
                )
                
                # 提取关键信息
                start_offset = alignment_info.get('start_offset', 0)
                correlation_score = alignment_info.get('correlation_score', 0)
                alignment_quality = alignment_info.get('alignment_quality', {})
                
                # 计算质量指标
                overall_quality = alignment_quality.get('overall_quality', 'unknown')
                composite_score = alignment_quality.get('composite_score', 0)
                time_correlation = alignment_quality.get('time_correlation', 0)
                freq_similarity = alignment_quality.get('freq_similarity', 0)
                
                result = {
                    'folder': folder_name,
                    'filename': filename,
                    'start_offset': start_offset,
                    'correlation_score': correlation_score,
                    'overall_quality': overall_quality,
                    'composite_score': composite_score,
                    'time_correlation': time_correlation,
                    'freq_similarity': freq_similarity,
                    'step_count': len(step_bounds),
                    'freq_count': len(freq_table),
                    'status': 'success'
                }
                
                print(f"  ✅ 成功: 开始时间={start_offset:.3f}s, 相关性={correlation_score:.3f}")
                print(f"     质量={overall_quality}, 评分={composite_score:.3f}")
                print(f"     步进数={len(step_bounds)}, 频点数={len(freq_table)}")
                
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
                result = {
                    'folder': folder_name,
                    'filename': filename,
                    'start_offset': 0,
                    'correlation_score': 0,
                    'overall_quality': 'failed',
                    'composite_score': 0,
                    'time_correlation': 0,
                    'freq_similarity': 0,
                    'step_count': 0,
                    'freq_count': 0,
                    'status': 'failed',
                    'error': str(e)
                }
            
            results.append(result)
    
    # 生成统计报告
    print("\n" + "="*60)
    print("📊 测试结果统计")
    print("="*60)
    
    if not results:
        print("❌ 没有测试结果")
        return
    
    df = pd.DataFrame(results)
    
    # 按文件夹分组统计
    for folder in df['folder'].unique():
        folder_data = df[df['folder'] == folder]
        success_count = len(folder_data[folder_data['status'] == 'success'])
        total_count = len(folder_data)
        
        print(f"\n📁 {folder} 文件夹:")
        print(f"  成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count > 0:
            success_data = folder_data[folder_data['status'] == 'success']
            
            print(f"  开始时间统计:")
            print(f"    平均值: {success_data['start_offset'].mean():.3f}s")
            print(f"    标准差: {success_data['start_offset'].std():.3f}s")
            print(f"    范围: {success_data['start_offset'].min():.3f}s - {success_data['start_offset'].max():.3f}s")
            
            print(f"  相关性统计:")
            print(f"    平均值: {success_data['correlation_score'].mean():.3f}")
            print(f"    标准差: {success_data['correlation_score'].std():.3f}")
            print(f"    范围: {success_data['correlation_score'].min():.3f} - {success_data['correlation_score'].max():.3f}")
            
            print(f"  质量分布:")
            quality_counts = success_data['overall_quality'].value_counts()
            for quality, count in quality_counts.items():
                print(f"    {quality}: {count}个 ({count/success_count*100:.1f}%)")
    
    # 详细结果表格
    print(f"\n📋 详细结果:")
    print("-" * 80)
    for _, row in df.iterrows():
        status_icon = "✅" if row['status'] == 'success' else "❌"
        print(f"{status_icon} {row['folder']}/{row['filename']}")
        if row['status'] == 'success':
            print(f"    开始时间: {row['start_offset']:.3f}s, 相关性: {row['correlation_score']:.3f}")
            print(f"    质量: {row['overall_quality']}, 评分: {row['composite_score']:.3f}")
        else:
            print(f"    错误: {row.get('error', 'Unknown error')}")
    
    # 保存结果到CSV
    output_file = "test_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    
    return df

if __name__ == "__main__":
    results_df = test_folder_samples()
