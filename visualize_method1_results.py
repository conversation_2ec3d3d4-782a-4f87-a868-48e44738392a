#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化方法1的93段THD+N分析结果
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def parse_results_file(file_path):
    """解析分析结果文件"""
    results = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到数据开始行
    data_start = False
    for i, line in enumerate(lines):
        if line.strip().startswith('序号'):
            data_start = True
            continue
        if line.strip().startswith('--'):
            continue
        if data_start and line.strip() and not line.strip().startswith('统计信息'):
            # 解析数据行
            parts = line.strip().split()
            if len(parts) >= 10:
                try:
                    result = {
                        'segment_idx': int(parts[0]),
                        'expected_freq': float(parts[1]),
                        'test_freq': float(parts[2]),
                        'ref_freq': float(parts[3]),
                        'freq_deviation': float(parts[4]),
                        'scaling_factor': float(parts[5]),
                        'test_power': float(parts[6]),
                        'ref_power': float(parts[7]),
                        'method1_thd_n': float(parts[8]),
                        'method2_thd_n': float(parts[9])
                    }
                    results.append(result)
                except ValueError:
                    continue
        elif line.strip().startswith('统计信息'):
            break
    
    return results

def create_comprehensive_visualization(results, output_dir):
    """创建综合可视化图表"""

    # 提取数据
    segments = [r['segment_idx'] for r in results]
    expected_freqs = [r['expected_freq'] for r in results]
    test_freqs = [r['test_freq'] for r in results]
    scaling_factors = [r['scaling_factor'] for r in results]
    test_powers = [r['test_power'] for r in results]
    ref_powers = [r['ref_power'] for r in results]
    method1_thd_n = [r['method1_thd_n'] for r in results]
    method2_thd_n = [r['method2_thd_n'] for r in results]
    freq_deviations = [r['freq_deviation'] for r in results]

    # 计算功率信息
    # 从缩放因子推算功率关系：
    # scaling_factor = test_peak_amp / ref_peak_amp
    # 如果假设参考信号每段功率为1，则：
    # 缩放后参考功率 = scaling_factor²
    # 但待测音频的实际功率需要考虑其完整的频谱，不只是峰值

    ref_power_original = [1.0] * len(results)  # 原始参考功率(归一化)
    ref_power_scaled = [sf**2 for sf in scaling_factors]  # 缩放后参考功率

    # 待测音频功率的估算：
    # 由于我们只有峰值幅值的缩放因子，需要做一些假设
    # 假设待测音频的功率分布与参考信号类似，但幅值按缩放因子调整
    # 这里我们使用一个修正因子来估算实际的待测功率
    # 实际上，待测音频可能包含更多噪声和谐波，所以功率会略高于缩放后的参考功率

    # 简化估算：假设待测功率 ≈ 缩放后参考功率 × (1 + THD+N/100)
    test_power_estimated = []
    for i, sf in enumerate(scaling_factors):
        base_power = sf**2  # 基础功率(对应主频)
        thd_factor = 1 + method1_thd_n[i]/100  # THD+N修正因子
        estimated_power = base_power * thd_factor
        test_power_estimated.append(estimated_power)

    total_test_power = sum(test_power_estimated)
    total_ref_power_original = sum(ref_power_original)
    total_ref_power_scaled = sum(ref_power_scaled)
    total_power_diff = total_test_power - total_ref_power_scaled

    # 创建大图
    fig = plt.figure(figsize=(20, 12))

    # 1. THD+N对比图（主图，占两列）
    ax1 = plt.subplot(2, 2, (1, 2))

    # 使用条形图，横轴为93段
    x_segments = range(1, len(segments) + 1)
    width = 0.25

    ax1.bar([x - width for x in x_segments], original_thd_n, width,
           label='原始THD+N', alpha=0.8, color='blue')
    ax1.bar(x_segments, method1_thd_n, width,
           label='方法1 THD+N (功率谱相减)', alpha=0.8, color='red')
    ax1.bar([x + width for x in x_segments], method2_thd_n, width,
           label='方法2 THD+N (双基投影)', alpha=0.8, color='green')

    ax1.set_xlabel('频段序号')
    ax1.set_ylabel('THD+N (%)')
    ax1.set_yscale('log')

    # 在标题中体现功率信息
    title = f'93段频率THD+N对比分析\n'
    title += f'待测音频总功率: {total_test_power:.3f} | 缩放后参考音频总功率: {total_ref_power_scaled:.3f} | 功率差: {total_power_diff:.3f}'
    ax1.set_title(title, fontsize=14, fontweight='bold')

    ax1.grid(True, alpha=0.3, axis='y')
    ax1.legend()
    ax1.set_xlim(0, len(segments) + 1)

    # 2. 频率偏差
    ax2 = plt.subplot(2, 2, 3)
    colors = ['red' if dev > 0 else 'blue' for dev in freq_deviations]
    ax2.bar(range(len(freq_deviations)), freq_deviations, color=colors, alpha=0.7)
    ax2.set_xlabel('频段序号')
    ax2.set_ylabel('频率偏差 (Hz)')
    ax2.set_title('频率偏差分布', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    # 3. 方法1 vs 方法2散点图
    ax3 = plt.subplot(2, 2, 4)
    ax3.loglog(method1_thd_n, method2_thd_n, 'go', markersize=5, alpha=0.7)

    # 添加对角线
    min_val = min(min(method1_thd_n), min(method2_thd_n))
    max_val = max(max(method1_thd_n), max(method2_thd_n))
    ax3.loglog([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='y=x')

    ax3.set_xlabel('方法1 THD+N (%)')
    ax3.set_ylabel('方法2 THD+N (%)')
    ax3.set_title('方法1 vs 方法2相关性', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    plt.tight_layout()

    # 保存图像
    image_path = os.path.join(output_dir, "THD+N_93段综合分析.png")
    plt.savefig(image_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ 综合分析图已保存: {image_path}")

    return image_path

def create_power_analysis_chart(results, output_dir):
    """创建功率分析图"""

    # 提取数据
    expected_freqs = [r['expected_freq'] for r in results]
    method1_thd_n = [r['method1_thd_n'] for r in results]
    scaling_factors = [r['scaling_factor'] for r in results]

    # 计算功率相关数据（基于缩放因子推算）
    # 原始参考信号功率为1，缩放后参考信号功率 = scaling_factor^2
    # 待测信号功率也是 scaling_factor^2（因为缩放是为了匹配待测信号）
    ref_power_original = [1.0] * len(results)  # 原始参考功率
    ref_power_scaled = [sf**2 for sf in scaling_factors]  # 缩放后参考功率
    test_power_normalized = [sf**2 for sf in scaling_factors]  # 待测功率
    power_diff = [tp - rp for tp, rp in zip(test_power_normalized, ref_power_scaled)]

    # 计算总功率统计
    total_test_power = sum(test_power_normalized)
    total_ref_power_original = sum(ref_power_original)
    total_ref_power_scaled = sum(ref_power_scaled)
    total_power_diff = total_test_power - total_ref_power_scaled

    fig, ax1 = plt.subplots(1, 1, figsize=(16, 10))

    # 方法1 THD+N条形图
    x_segments = range(1, len(expected_freqs) + 1)
    ax1.bar(x_segments, method1_thd_n, alpha=0.8, color='red',
           label='方法1 THD+N (功率谱相减法)')

    ax1.set_xlabel('频段序号', fontsize=12)
    ax1.set_ylabel('THD+N (%)', fontsize=12)
    ax1.set_yscale('log')

    # 在标题中体现功率信息
    title = f'方法1 THD+N频率响应特性\n'
    title += f'待测音频总功率: {total_test_power:.3f} | 缩放后参考音频总功率: {total_ref_power_scaled:.3f} | 功率差: {total_power_diff:.3f}'
    ax1.set_title(title, fontsize=14, fontweight='bold')

    ax1.grid(True, alpha=0.3, axis='y')
    ax1.legend(fontsize=12)
    ax1.set_xlim(0, len(expected_freqs) + 1)

    # 添加统计信息文本框
    stats_text = f'统计信息:\n'
    stats_text += f'频段数: {len(results)}\n'
    stats_text += f'THD+N范围: {min(method1_thd_n):.3f}% - {max(method1_thd_n):.3f}%\n'
    stats_text += f'平均THD+N: {np.mean(method1_thd_n):.3f}%\n'
    stats_text += f'功率比范围: {min(test_power_normalized):.3f} - {max(test_power_normalized):.3f}'

    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()

    # 保存图像
    image_path = os.path.join(output_dir, "方法1功率分析.png")
    plt.savefig(image_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ 功率分析图已保存: {image_path}")

    # 打印功率统计
    print("\n📊 功率统计分析:")
    print(f"  待测音频总功率: {total_test_power:.6f}")
    print(f"  原始参考音频总功率: {total_ref_power_original:.6f}")
    print(f"  缩放后参考音频总功率: {total_ref_power_scaled:.6f}")
    print(f"  功率差(待测-缩放后参考): {total_power_diff:.6f}")
    print(f"  功率比(待测/缩放后参考): {total_test_power/total_ref_power_scaled:.6f}")
    print(f"  平均缩放因子: {np.mean(scaling_factors):.6f}")

    return image_path

def main():
    """主函数"""
    # 文件路径
    results_file = "鼓膜破裂（复测1.1)_THD+N双方法分析/THD+N双方法分析汇总.txt"
    output_dir = "鼓膜破裂（复测1.1)_THD+N双方法分析"
    
    if not os.path.exists(results_file):
        print(f"❌ 结果文件不存在: {results_file}")
        return
    
    print("🎯 开始可视化方法1的93段结果...")
    
    # 解析结果
    results = parse_results_file(results_file)
    print(f"📊 成功解析 {len(results)} 个频段的数据")
    
    if len(results) == 0:
        print("❌ 没有找到有效数据")
        return
    
    # 创建可视化
    comprehensive_chart = create_comprehensive_visualization(results, output_dir)
    power_analysis_chart = create_power_analysis_chart(results, output_dir)

    print(f"\n✅ 方法1可视化完成!")
    print(f"📁 输出目录: {output_dir}")
    print(f"🖼️ 生成图像:")
    print(f"   - THD+N_93段综合分析.png")
    print(f"   - 方法1功率分析.png")

if __name__ == "__main__":
    main()
