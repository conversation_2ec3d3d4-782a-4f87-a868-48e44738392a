#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于动态噪声阈值曲线特征分离目标文件
识别低音戳洞、153632、155101等文件与其他文件的差异
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import json
from analyze_noise_curve_patterns import analyze_single_file_noise_pattern, classify_noise_pattern

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def analyze_all_files_patterns():
    """
    分析所有文件的噪声模式
    """
    
    print("🎯 分析所有文件的动态噪声阈值曲线模式")
    print("="*60)
    
    # 查找所有wav文件
    test_dir = "test20250717"
    if not os.path.exists(test_dir):
        print(f"❌ 目录不存在: {test_dir}")
        return None
    
    # 递归查找所有wav文件
    all_wav_files = []
    for root, dirs, files in os.walk(test_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                all_wav_files.append(os.path.join(root, file))
    
    print(f"🔍 找到{len(all_wav_files)}个wav文件")
    
    # 分析所有文件
    all_files_data = {}
    target_patterns = ['低音戳洞', '153632', '155101']
    
    for i, wav_file in enumerate(all_wav_files, 1):
        filename = os.path.basename(wav_file)
        print(f"[{i}/{len(all_wav_files)}] 分析: {filename[:50]}{'...' if len(filename) > 50 else ''}")
        
        try:
            result = analyze_single_file_noise_pattern(wav_file)
            
            if result:
                # 进行分类
                classification = classify_noise_pattern(result['noise_patterns'], result['filename'])
                result['classification'] = classification
                
                # 判断是否为目标文件
                is_target = any(pattern in filename for pattern in target_patterns)
                result['is_target'] = is_target
                
                all_files_data[filename] = result
                
                print(f"  ✅ 分类: {classification.get('classification', 'unknown')}")
                if is_target:
                    print(f"  🎯 目标文件!")
            else:
                print(f"  ❌ 分析失败")
        
        except Exception as e:
            print(f"  ❌ 错误: {e}")
    
    return all_files_data

def create_separation_analysis(all_files_data, output_dir):
    """
    创建分离分析可视化
    """
    
    if not all_files_data:
        print("❌ 无文件数据")
        return
    
    # 分离目标文件和其他文件
    target_files = {}
    other_files = {}
    
    for filename, data in all_files_data.items():
        if data.get('is_target', False):
            target_files[filename] = data
        else:
            other_files[filename] = data
    
    print(f"📊 目标文件: {len(target_files)}个")
    print(f"📊 其他文件: {len(other_files)}个")
    
    # 提取特征用于可视化
    def extract_features(files_data):
        features = {
            'std_curvature': [],
            'std_slope': [],
            'num_peaks': [],
            'num_valleys': [],
            'filenames': []
        }

        for filename, data in files_data.items():
            if 'classification' in data and 'features' in data['classification']:
                f = data['classification']['features']
                features['std_curvature'].append(f['std_curvature'])
                features['std_slope'].append(f['std_slope'])
                features['num_peaks'].append(f['num_peaks'])
                features['num_valleys'].append(f['num_valleys'])
                features['filenames'].append(filename)

        return features
    
    target_features = extract_features(target_files)
    other_features = extract_features(other_files)
    
    # 创建多维特征对比图
    fig, axes = plt.subplots(2, 3, figsize=(24, 16))
    fig.suptitle('目标文件与其他文件的噪声特征分离分析', fontsize=16, fontweight='bold')
    
    # 特征对比图
    feature_pairs = [
        ('std_curvature', 'std_slope', '曲率标准差 vs 斜率标准差'),
        ('std_curvature', 'num_peaks', '曲率标准差 vs 峰值数'),
        ('std_slope', 'num_valleys', '斜率标准差 vs 谷值数'),
        ('num_peaks', 'num_valleys', '峰值数 vs 谷值数'),
        ('std_curvature', 'num_valleys', '曲率标准差 vs 谷值数'),
        ('std_slope', 'num_peaks', '斜率标准差 vs 峰值数')
    ]
    
    for i, (feat1, feat2, title) in enumerate(feature_pairs):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        # 绘制其他文件
        if other_features[feat1] and other_features[feat2]:
            ax.scatter(other_features[feat1], other_features[feat2], 
                      c='lightblue', alpha=0.6, s=30, label='其他文件')
        
        # 绘制目标文件
        if target_features[feat1] and target_features[feat2]:
            colors = ['red', 'green', 'orange']
            target_patterns = ['低音戳洞', '153632', '155101']
            
            for j, (filename, data) in enumerate(target_files.items()):
                pattern_idx = -1
                for k, pattern in enumerate(target_patterns):
                    if pattern in filename:
                        pattern_idx = k
                        break
                
                if pattern_idx >= 0 and 'classification' in data and 'features' in data['classification']:
                    f = data['classification']['features']
                    ax.scatter(f[feat1], f[feat2], 
                              c=colors[pattern_idx], s=100, alpha=0.8,
                              label=target_patterns[pattern_idx] if j == 0 else "")
                    
                    # 标注文件名
                    short_name = filename[:15] + '...' if len(filename) > 15 else filename
                    ax.annotate(short_name, (f[feat1], f[feat2]), 
                               xytext=(5, 5), textcoords='offset points', 
                               fontsize=8, alpha=0.7)
        
        ax.set_xlabel(feat1.replace('_', ' ').title())
        ax.set_ylabel(feat2.replace('_', ' ').title())
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    plt.tight_layout()
    
    # 保存分离分析图
    output_path = os.path.join(output_dir, "noise_pattern_separation_analysis.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    return output_path

def create_classification_summary(all_files_data, output_dir):
    """
    创建分类汇总报告
    """
    
    # 统计分类结果
    classification_stats = {}
    target_files_details = {}
    
    for filename, data in all_files_data.items():
        if 'classification' in data:
            class_name = data['classification'].get('classification', 'unknown')
            
            if class_name not in classification_stats:
                classification_stats[class_name] = []
            classification_stats[class_name].append(filename)
            
            # 记录目标文件详情
            if data.get('is_target', False):
                target_files_details[filename] = {
                    'classification': class_name,
                    'features': data['classification'].get('features', {}),
                    'distance': data['classification'].get('closest_distance', 0)
                }
    
    # 生成汇总报告
    report = {
        'summary': {
            'total_files': len(all_files_data),
            'target_files': len(target_files_details),
            'other_files': len(all_files_data) - len(target_files_details)
        },
        'classification_stats': {k: len(v) for k, v in classification_stats.items()},
        'target_files_details': target_files_details,
        'classification_distribution': classification_stats
    }
    
    # 保存报告
    report_path = os.path.join(output_dir, "separation_analysis_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    return report_path, report

def main():
    """
    主函数 - 执行分离分析
    """
    
    # 创建输出目录
    output_dir = "noise_pattern_separation"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    # 分析所有文件
    all_files_data = analyze_all_files_patterns()
    
    if not all_files_data:
        print("❌ 无法获取文件数据")
        return
    
    # 创建分离分析
    print("\n📊 生成分离分析可视化...")
    viz_path = create_separation_analysis(all_files_data, output_dir)
    print(f"✅ 分离分析图已保存: {os.path.basename(viz_path)}")
    
    # 创建分类汇总报告
    print("\n📄 生成分类汇总报告...")
    report_path, report = create_classification_summary(all_files_data, output_dir)
    print(f"✅ 汇总报告已保存: {os.path.basename(report_path)}")
    
    # 打印关键结果
    print("\n" + "="*60)
    print("🎯 分离分析结果汇总:")
    print(f"📊 总文件数: {report['summary']['total_files']}")
    print(f"🎯 目标文件数: {report['summary']['target_files']}")
    print(f"📁 其他文件数: {report['summary']['other_files']}")
    
    print("\n📈 分类分布:")
    for class_name, count in report['classification_stats'].items():
        print(f"  {class_name}: {count}个文件")
    
    print("\n🎯 目标文件详情:")
    for filename, details in report['target_files_details'].items():
        short_name = filename[:40] + '...' if len(filename) > 40 else filename
        print(f"  {short_name}")
        print(f"    分类: {details['classification']}")
        print(f"    匹配距离: {details['distance']:.3f}")
    
    print("="*60)
    print("🎯 噪声模式分离分析完成！")

if __name__ == "__main__":
    main()
