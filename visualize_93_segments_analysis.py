#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化低音戳洞样本全部93段的谐波数量和噪声波动特征
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置matplotlib
import matplotlib
matplotlib.use('Agg')
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def visualize_93_segments():
    """可视化93段分析"""
    print("🎯 低音戳洞样本全部93段谐波数量和噪声波动特征分析")
    print("="*60)
    
    # 低音戳洞文件
    hole_file = 'test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    
    if not os.path.exists(hole_file):
        print("❌ 文件不存在")
        return
    
    # 分析全部93段
    all_segments_data = analyze_all_93_segments(hole_file)
    
    if all_segments_data:
        create_comprehensive_visualization(all_segments_data)

def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_trend': 0.0,
            'fluctuation_smoothness': 1.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 趋势 - 线性回归斜率
    if len(freq_centers) == len(noise_levels):
        try:
            # 计算频率与噪声的线性关系
            freq_array = np.array(freq_centers)
            coeffs = np.polyfit(freq_array, noise_array, 1)
            fluctuation_trend = coeffs[0]  # 斜率，正值表示噪声随频率增加
        except:
            fluctuation_trend = 0.0
    else:
        fluctuation_trend = 0.0
    
    # 5. 平滑度 - 相邻点差值的平均
    if len(noise_array) > 1:
        adjacent_diffs = np.abs(np.diff(noise_array))
        fluctuation_smoothness = 1.0 / (1.0 + np.mean(adjacent_diffs))  # 值越大越平滑
    else:
        fluctuation_smoothness = 1.0
    
    # 6. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)  # 标准差越小分数越高
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)  # 变异系数越小分数越高
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_trend': fluctuation_trend,
        'fluctuation_smoothness': fluctuation_smoothness,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

def analyze_all_93_segments(audio_path):
    """分析全部93段"""
    try:
        print(f"\n分析全部93段...")
        
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        all_segments_data = []
        
        print(f"开始分析{len(step_boundaries)}个频段...")
        
        # 分析所有段
        for seg_idx in range(len(step_boundaries)):
            if seg_idx % 10 == 0:
                print(f"  处理进度: {seg_idx}/{len(step_boundaries)}")
            
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # FFT分析
            fft_size = 32768  # 32k点FFT
            if len(segment_audio) < fft_size:
                segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
            else:
                segment_audio = segment_audio[:fft_size]
            
            # 应用窗函数
            window = np.hanning(len(segment_audio))
            segment_audio = segment_audio * window
            
            # FFT
            fft = np.fft.fft(segment_audio)
            freqs = np.fft.fftfreq(fft_size, 1/sr)
            magnitude = np.abs(fft)
            power = magnitude ** 2
            
            # 只取正频率
            positive_freqs = freqs[:fft_size//2]
            positive_power = power[:fft_size//2]
            
            # 限制显示范围到20kHz
            freq_mask = positive_freqs <= 20000
            display_freqs = positive_freqs[freq_mask]
            display_power = positive_power[freq_mask]
            
            # 找主频
            search_bandwidth = 2.0
            search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                         (display_freqs <= expected_freq + search_bandwidth)
            
            if np.any(search_mask):
                search_indices = np.where(search_mask)[0]
                search_powers = display_power[search_mask]
                max_power_idx = np.argmax(search_powers)
                actual_idx = search_indices[max_power_idx]
                fundamental_freq = display_freqs[actual_idx]
            else:
                fundamental_freq = expected_freq
            
            # 动态噪声分析
            noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)
            
            # 谐波检测
            if noise_analysis:
                harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
            else:
                harmonic_analysis = []
            
            all_segments_data.append({
                'seg_idx': seg_idx,
                'expected_freq': expected_freq,
                'fundamental_freq': fundamental_freq,
                'harmonic_count': len(harmonic_analysis),
                'noise_analysis': noise_analysis
            })
        
        print(f"✅ 完成全部{len(all_segments_data)}段分析")
        
        return {
            'filename': os.path.basename(audio_path),
            'segments': all_segments_data
        }

    except Exception as e:
        print(f"分析失败: {e}")
        return None

def create_comprehensive_visualization(all_data):
    """创建综合可视化"""
    print(f"\n🎨 生成93段综合可视化...")

    segments = all_data['segments']

    # 提取数据
    segment_indices = [seg['seg_idx'] for seg in segments]
    expected_freqs = [seg['expected_freq'] for seg in segments]
    fundamental_freqs = [seg['fundamental_freq'] for seg in segments]
    harmonic_counts = [seg['harmonic_count'] for seg in segments]

    # 噪声特征
    noise_variations = []
    stability_scores = []
    fluctuation_stds = []
    noise_floors = []

    for seg in segments:
        if seg['noise_analysis']:
            noise_variations.append(seg['noise_analysis']['noise_variation_db'])
            stability_scores.append(seg['noise_analysis']['noise_fluctuation_features']['fluctuation_stability_score'])
            fluctuation_stds.append(seg['noise_analysis']['noise_fluctuation_features']['fluctuation_std'])
            noise_floors.append(seg['noise_analysis']['global_noise_floor_db'])
        else:
            noise_variations.append(0)
            stability_scores.append(0)
            fluctuation_stds.append(0)
            noise_floors.append(-80)

    # 创建图表 (2行2列)
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle(f'Low Frequency Hole Sample - 93 Segments Analysis\nFile: {all_data["filename"]}',
                 fontsize=16, fontweight='bold')

    # 1. 谐波数量 vs 频率
    ax1 = axes[0, 0]
    scatter1 = ax1.scatter(expected_freqs, harmonic_counts, c=stability_scores,
                          cmap='RdYlGn', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
    ax1.set_xlabel('Expected Frequency (Hz)')
    ax1.set_ylabel('Number of Harmonics Detected')
    ax1.set_title('Harmonic Count vs Frequency (colored by Noise Stability)')
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')

    # 添加颜色条
    cbar1 = plt.colorbar(scatter1, ax=ax1)
    cbar1.set_label('Noise Stability Score')

    # 2. 噪声变化 vs 频率
    ax2 = axes[0, 1]
    scatter2 = ax2.scatter(expected_freqs, noise_variations, c=harmonic_counts,
                          cmap='viridis', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
    ax2.set_xlabel('Expected Frequency (Hz)')
    ax2.set_ylabel('Noise Variation (dB)')
    ax2.set_title('Noise Variation vs Frequency (colored by Harmonic Count)')
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')

    # 添加颜色条
    cbar2 = plt.colorbar(scatter2, ax=ax2)
    cbar2.set_label('Harmonic Count')

    # 3. 稳定性评分分布
    ax3 = axes[1, 0]
    ax3.hist(stability_scores, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax3.set_xlabel('Noise Stability Score')
    ax3.set_ylabel('Number of Segments')
    ax3.set_title('Distribution of Noise Stability Scores')
    ax3.grid(True, alpha=0.3)

    # 添加统计信息
    mean_stability = np.mean(stability_scores)
    ax3.axvline(mean_stability, color='red', linestyle='--', linewidth=2,
               label=f'Mean: {mean_stability:.3f}')
    ax3.legend()

    # 4. 谐波数量分布
    ax4 = axes[1, 1]
    ax4.hist(harmonic_counts, bins=range(0, max(harmonic_counts)+2),
            alpha=0.7, color='blue', edgecolor='black')
    ax4.set_xlabel('Number of Harmonics')
    ax4.set_ylabel('Number of Segments')
    ax4.set_title('Distribution of Harmonic Counts')
    ax4.grid(True, alpha=0.3)

    # 添加统计信息
    mean_harmonics = np.mean(harmonic_counts)
    ax4.axvline(mean_harmonics, color='red', linestyle='--', linewidth=2,
               label=f'Mean: {mean_harmonics:.1f}')
    ax4.legend()

    plt.tight_layout()

    # 保存图片
    filename = '93_segments_comprehensive_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 93段综合分析可视化已保存: {filename}")
    plt.close()

    # 创建第二个图表 - 详细趋势分析
    create_trend_analysis(all_data, expected_freqs, harmonic_counts, noise_variations, stability_scores, fundamental_freqs)

    # 打印统计摘要
    print_analysis_summary(all_data)

def create_trend_analysis(all_data, expected_freqs, harmonic_counts, noise_variations, stability_scores, fundamental_freqs):
    """创建趋势分析图"""

    fig2, axes2 = plt.subplots(3, 1, figsize=(20, 18))
    fig2.suptitle(f'Detailed Trend Analysis - 93 Segments\nFile: {all_data["filename"]}', fontsize=16, fontweight='bold')

    # 1. 谐波数量趋势
    ax1 = axes2[0]
    ax1.plot(expected_freqs, harmonic_counts, 'bo-', markersize=4, linewidth=1, alpha=0.7)
    ax1.set_xlabel('Expected Frequency (Hz)')
    ax1.set_ylabel('Number of Harmonics')
    ax1.set_title('Harmonic Count Trend Across All Segments')
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')

    # 添加移动平均
    if len(harmonic_counts) > 5:
        window_size = 5
        moving_avg = np.convolve(harmonic_counts, np.ones(window_size)/window_size, mode='valid')
        moving_avg_freqs = expected_freqs[window_size-1:]
        ax1.plot(moving_avg_freqs, moving_avg, 'r-', linewidth=2, alpha=0.8, label='Moving Average (5 points)')
        ax1.legend()

    # 2. 噪声特征趋势
    ax2 = axes2[1]
    ax2_twin = ax2.twinx()

    line1 = ax2.plot(expected_freqs, noise_variations, 'g-', linewidth=1, alpha=0.7, label='Noise Variation')
    line2 = ax2_twin.plot(expected_freqs, stability_scores, 'r-', linewidth=1, alpha=0.7, label='Stability Score')

    ax2.set_xlabel('Expected Frequency (Hz)')
    ax2.set_ylabel('Noise Variation (dB)', color='g')
    ax2_twin.set_ylabel('Stability Score', color='r')
    ax2.set_title('Noise Characteristics Trend')
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')

    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper right')

    # 3. 频率精度分析
    ax3 = axes2[2]
    freq_errors = [fund - exp for fund, exp in zip(fundamental_freqs, expected_freqs)]
    ax3.plot(expected_freqs, freq_errors, 'mo-', markersize=3, linewidth=1, alpha=0.7)
    ax3.set_xlabel('Expected Frequency (Hz)')
    ax3.set_ylabel('Frequency Error (Hz)')
    ax3.set_title('Fundamental Frequency Accuracy')
    ax3.grid(True, alpha=0.3)
    ax3.set_xscale('log')
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.tight_layout()

    # 保存第二个图片
    filename2 = '93_segments_trend_analysis.png'
    plt.savefig(filename2, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"✅ 93段趋势分析可视化已保存: {filename2}")
    plt.close()

def print_analysis_summary(all_data):
    """打印分析摘要"""
    segments = all_data['segments']

    harmonic_counts = [seg['harmonic_count'] for seg in segments]
    noise_variations = [seg['noise_analysis']['noise_variation_db'] if seg['noise_analysis'] else 0 for seg in segments]
    stability_scores = [seg['noise_analysis']['noise_fluctuation_features']['fluctuation_stability_score'] if seg['noise_analysis'] else 0 for seg in segments]

    print(f"\n📊 93段分析统计摘要:")
    print(f"{'='*60}")
    print(f"文件: {all_data['filename']}")
    print(f"总段数: {len(segments)}")
    print(f"\n谐波统计:")
    print(f"  总检测谐波: {sum(harmonic_counts)}个")
    print(f"  平均每段: {np.mean(harmonic_counts):.1f}个")
    print(f"  最多谐波: {max(harmonic_counts)}个")
    print(f"  最少谐波: {min(harmonic_counts)}个")
    print(f"  标准差: {np.std(harmonic_counts):.1f}")

    print(f"\n噪声变化统计:")
    print(f"  平均噪声变化: {np.mean(noise_variations):.1f}dB")
    print(f"  最大噪声变化: {max(noise_variations):.1f}dB")
    print(f"  最小噪声变化: {min(noise_variations):.1f}dB")
    print(f"  标准差: {np.std(noise_variations):.1f}dB")

    print(f"\n稳定性统计:")
    print(f"  平均稳定性评分: {np.mean(stability_scores):.3f}")
    print(f"  最高稳定性: {max(stability_scores):.3f}")
    print(f"  最低稳定性: {min(stability_scores):.3f}")
    print(f"  标准差: {np.std(stability_scores):.3f}")

    # 频率段分析
    low_freq_segments = [seg for seg in segments if seg['expected_freq'] <= 1000]
    mid_freq_segments = [seg for seg in segments if 1000 < seg['expected_freq'] <= 5000]
    high_freq_segments = [seg for seg in segments if seg['expected_freq'] > 5000]

    print(f"\n频率段分析:")
    print(f"  低频段 (≤1kHz): {len(low_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in low_freq_segments]):.1f}个谐波")
    print(f"  中频段 (1-5kHz): {len(mid_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in mid_freq_segments]):.1f}个谐波")
    print(f"  高频段 (>5kHz): {len(high_freq_segments)}段, 平均{np.mean([s['harmonic_count'] for s in high_freq_segments]):.1f}个谐波")

if __name__ == "__main__":
    visualize_93_segments()
