#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试THD+N分析 - 单个频段
"""

import os
import sys
import numpy as np
import librosa

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from freq_split_optimized import split_freq_steps_optimized

def main():
    """
    测试单个频段的THD+N分析
    """
    audio_path = "test20250722/鼓膜破裂（复测1.1).wav"
    
    print(f"测试THD+N分析 - 单个频段")
    print(f"音频文件: {audio_path}")
    print("="*50)
    
    if not os.path.exists(audio_path):
        print(f"音频文件不存在: {audio_path}")
        return
    
    try:
        # 获取频段分割
        print("进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        print(f"频段分割完成，共{len(step_boundaries)}段")
        
        # 加载音频
        print("加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000
        
        print(f"音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")
        
        # 分析第一个频段
        start_time, end_time = step_boundaries[0]
        expected_freq = freq_table[0]
        
        print(f"分析第一个频段: {expected_freq:.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        # 去除开头和结尾8%
        trim_length = int(len(segment_audio) * 0.08)
        if len(segment_audio) > 2 * trim_length:
            segment_audio = segment_audio[trim_length:-trim_length]
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到20kHz
        freq_mask = positive_freqs <= 20000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        print(f"检测到主频: {fundamental_freq:.2f}Hz (期望: {expected_freq:.1f}Hz)")
        
        # 主频功率
        fundamental_power = display_power[np.argmin(np.abs(display_freqs - fundamental_freq))]
        fundamental_db = 10 * np.log10(fundamental_power + 1e-12)
        
        print(f"主频功率: {fundamental_db:.1f}dB")
        
        # 检测谐波
        harmonic_count = 0
        harmonic_power_sum = 0
        
        for harmonic in range(2, 11):
            harmonic_freq = fundamental_freq * harmonic
            if harmonic_freq <= np.max(display_freqs):
                harmonic_idx = np.argmin(np.abs(display_freqs - harmonic_freq))
                harmonic_power = display_power[harmonic_idx]
                harmonic_db = 10 * np.log10(harmonic_power + 1e-12)
                harmonic_power_sum += harmonic_power
                harmonic_count += 1
                print(f"  {harmonic}次谐波 {harmonic_freq:.1f}Hz: {harmonic_db:.1f}dB")
        
        print(f"检测到 {harmonic_count} 个谐波")
        
        # 主频和谐波置零 - 使用频率自适应带宽
        def calculate_zero_bandwidth(freq):
            if freq < 200:
                return 10.0      # 低频用较大带宽
            elif freq < 1000:
                return 5.0       # 中频用中等带宽
            else:
                return 2.0       # 高频用较小带宽

        zeroed_spectrum = display_power.copy()

        # 主频置零
        main_bandwidth = calculate_zero_bandwidth(fundamental_freq)
        main_mask = np.abs(display_freqs - fundamental_freq) <= main_bandwidth / 2
        zeroed_spectrum[main_mask] = 0
        print(f"主频置零带宽: ±{main_bandwidth/2:.1f}Hz")

        # 谐波置零
        for harmonic in range(2, 11):
            harmonic_freq = fundamental_freq * harmonic
            if harmonic_freq <= np.max(display_freqs):
                harmonic_bandwidth = calculate_zero_bandwidth(harmonic_freq)
                harmonic_mask = np.abs(display_freqs - harmonic_freq) <= harmonic_bandwidth / 2
                zeroed_spectrum[harmonic_mask] = 0
                print(f"  {harmonic}次谐波置零带宽: ±{harmonic_bandwidth/2:.1f}Hz")
        
        # 噪声功率
        noise_power = np.sum(zeroed_spectrum)
        
        # THD+N计算
        thd_n_power = harmonic_power_sum + noise_power
        thd_n_ratio = thd_n_power / fundamental_power if fundamental_power > 0 else 0
        thd_n_db = 10 * np.log10(thd_n_ratio + 1e-12)
        thd_n_percent = np.sqrt(thd_n_ratio) * 100
        
        print(f"\nTHD+N结果:")
        print(f"  谐波功率总和: {10 * np.log10(harmonic_power_sum + 1e-12):.1f}dB")
        print(f"  噪声功率: {10 * np.log10(noise_power + 1e-12):.1f}dB")
        print(f"  THD+N: {thd_n_percent:.3f}% ({thd_n_db:.1f}dB)")
        
        print(f"\n测试完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
