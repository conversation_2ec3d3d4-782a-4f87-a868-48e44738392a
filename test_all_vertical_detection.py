#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
竖线检测test20250717和待定的所有文件
"""

import os
import sys
import glob
import pandas as pd
import numpy as np

sys.path.append('.')
from final_audio_detection_system.segment_vertical_line_detector import SegmentVerticalLineDetector
from final_audio_detection_system.integrated_vertical_line_detector import IntegratedVerticalLineDetector

def test_all_vertical_detection():
    """测试所有文件的竖线检测"""
    print("🔍 竖线检测test20250717和待定的所有文件")
    print("="*70)
    
    # 定义测试文件夹
    folders = [
        r"../test20250717/pos/sd卡",
        r"../test20250717/pos/完美", 
        r"../test20250717/pos/转接板",
        r"../test20250717/pos/铁网",
        r"../test20250717/neg",
        r"../待定"
    ]
    
    # 创建检测器
    segment_detector = SegmentVerticalLineDetector()
    integrated_detector = IntegratedVerticalLineDetector()
    
    results = []
    total_files = 0
    
    # 收集所有文件
    all_files = []
    for folder in folders:
        if not os.path.exists(folder):
            print(f"❌ 文件夹不存在: {folder}")
            continue
        
        wav_files = glob.glob(os.path.join(folder, "*.wav"))
        folder_name = os.path.basename(folder)
        
        for wav_file in wav_files:
            all_files.append((wav_file, folder_name))
    
    total_files = len(all_files)
    print(f"📂 总共找到 {total_files} 个wav文件")
    
    # 重点关注的文件（您提到的有很多竖线的文件）
    key_files = [
        "录音_步进扫频_100Hz至20000Hz_20250714_153632",
        "录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞",
        "录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换",
        "录音_步进扫频_100Hz至20000Hz_20250714_155101"
    ]
    
    # 处理每个文件
    for i, (audio_path, folder_name) in enumerate(all_files):
        filename = os.path.basename(audio_path)
        filename_base = filename.replace('.wav', '')
        
        print(f"\n{'='*70}")
        print(f"📊 进度: [{i+1}/{total_files}] 文件: {folder_name}/{filename}")
        
        # 标记是否为重点文件
        is_key_file = any(key_name in filename for key_name in key_files)
        if is_key_file:
            print(f"🎯 重点文件: 预期有很多竖线")
        
        try:
            # 1. 分段竖线检测
            segment_result = segment_detector.detect_vertical_lines_in_segments(audio_path)
            
            # 2. 集成竖线检测
            integrated_result = integrated_detector.detect_integrated_anomalies(audio_path)
            
            # 提取结果
            if segment_result:
                seg_total_lines = segment_result.get('total_vertical_lines', 0)
                seg_anomalous = segment_result.get('anomalous_segments', 0)
                seg_total_segments = segment_result.get('total_segments', 0)
                seg_success = segment_result.get('successful_segments', 0)
                seg_status = 'success'
            else:
                seg_total_lines = 0
                seg_anomalous = 0
                seg_total_segments = 0
                seg_success = 0
                seg_status = 'failed'
            
            if integrated_result:
                int_total_lines = integrated_result.get('total_vertical_lines', 0)
                int_anomalous = integrated_result.get('anomalous_segments', 0)
                int_total_segments = integrated_result.get('total_segments', 0)
                int_max_strength = integrated_result.get('max_line_strength', 0)
                int_total_strength = integrated_result.get('total_line_strength', 0)
                int_anomaly_ratio = integrated_result.get('segment_anomaly_ratio', 0)
                int_status = 'success'
            else:
                int_total_lines = 0
                int_anomalous = 0
                int_total_segments = 0
                int_max_strength = 0
                int_total_strength = 0
                int_anomaly_ratio = 0
                int_status = 'failed'
            
            # 记录结果
            result = {
                'folder': folder_name,
                'filename': filename,
                'filename_base': filename_base,
                'is_key_file': is_key_file,
                'seg_status': seg_status,
                'seg_total_lines': seg_total_lines,
                'seg_anomalous_segments': seg_anomalous,
                'seg_total_segments': seg_total_segments,
                'seg_success_segments': seg_success,
                'int_status': int_status,
                'int_total_lines': int_total_lines,
                'int_anomalous_segments': int_anomalous,
                'int_total_segments': int_total_segments,
                'int_max_strength': int_max_strength,
                'int_total_strength': int_total_strength,
                'int_anomaly_ratio': int_anomaly_ratio
            }
            
            # 显示结果
            print(f"📊 分段检测结果:")
            print(f"   状态: {'✅' if seg_status == 'success' else '❌'}")
            print(f"   总竖线数: {seg_total_lines}")
            print(f"   异常频段: {seg_anomalous}/{seg_total_segments}")
            print(f"   成功分析: {seg_success}/{seg_total_segments}")
            
            print(f"📊 集成检测结果:")
            print(f"   状态: {'✅' if int_status == 'success' else '❌'}")
            print(f"   总竖线数: {int_total_lines}")
            print(f"   异常频段: {int_anomalous}/{int_total_segments}")
            print(f"   最大线强度: {int_max_strength:.3f}")
            print(f"   总线强度: {int_total_strength:.3f}")
            print(f"   异常率: {int_anomaly_ratio:.3f}")
            
            # 判定异常程度
            if seg_total_lines > 50 or int_total_lines > 50:
                anomaly_level = "🔴 严重异常"
            elif seg_total_lines > 20 or int_total_lines > 20:
                anomaly_level = "🟡 中等异常"
            elif seg_total_lines > 5 or int_total_lines > 5:
                anomaly_level = "🟠 轻微异常"
            else:
                anomaly_level = "🟢 正常"
            
            print(f"📊 综合判定: {anomaly_level}")
            
            # 验证预期
            if is_key_file:
                if seg_total_lines > 20 or int_total_lines > 20:
                    print(f"✅ 符合预期: 重点文件确实检测到很多竖线")
                else:
                    print(f"⚠️ 与预期不符: 重点文件竖线数量较少")
            else:
                if seg_total_lines > 20 or int_total_lines > 20:
                    print(f"⚠️ 意外发现: 非重点文件检测到较多竖线")
                else:
                    print(f"✅ 符合预期: 非重点文件竖线数量正常")
            
        except Exception as e:
            print(f"❌ 检测失败: {str(e)}")
            result = {
                'folder': folder_name,
                'filename': filename,
                'filename_base': filename_base,
                'is_key_file': is_key_file,
                'seg_status': 'error',
                'seg_total_lines': 0,
                'seg_anomalous_segments': 0,
                'seg_total_segments': 0,
                'seg_success_segments': 0,
                'int_status': 'error',
                'int_total_lines': 0,
                'int_anomalous_segments': 0,
                'int_total_segments': 0,
                'int_max_strength': 0,
                'int_total_strength': 0,
                'int_anomaly_ratio': 0,
                'error': str(e)
            }
        
        results.append(result)
        print("-" * 70)
    
    # 生成统计报告
    print(f"\n" + "="*70)
    print(f"📊 竖线检测统计报告")
    print("="*70)
    
    df = pd.DataFrame(results)
    
    # 按竖线数量排序
    df_sorted = df.sort_values('seg_total_lines', ascending=False)
    
    print(f"\n📊 按分段检测竖线数量排序 (前20名):")
    print("-" * 70)
    for i, (_, row) in enumerate(df_sorted.head(20).iterrows()):
        key_mark = "🎯" if row['is_key_file'] else "  "
        print(f"{i+1:2d}. {key_mark} {row['folder']}/{row['filename']}")
        print(f"     分段检测: {row['seg_total_lines']}条竖线, {row['seg_anomalous_segments']}个异常频段")
        print(f"     集成检测: {row['int_total_lines']}条竖线, 最大强度{row['int_max_strength']:.3f}")
    
    # 重点文件分析
    print(f"\n📊 重点文件详细分析:")
    print("-" * 70)
    key_files_df = df[df['is_key_file'] == True]
    
    if len(key_files_df) > 0:
        for _, row in key_files_df.iterrows():
            print(f"🎯 {row['filename']}:")
            print(f"   分段检测: {row['seg_total_lines']}条竖线")
            print(f"   集成检测: {row['int_total_lines']}条竖线")
            print(f"   异常频段比例: {row['int_anomaly_ratio']:.3f}")
            print(f"   最大线强度: {row['int_max_strength']:.3f}")
            print()
    else:
        print("❌ 未找到重点文件")
    
    # 统计分析
    print(f"\n📊 整体统计:")
    print("-" * 70)
    
    success_files = df[df['seg_status'] == 'success']
    print(f"成功检测文件: {len(success_files)}/{len(df)}")
    
    if len(success_files) > 0:
        print(f"分段检测统计:")
        print(f"  平均竖线数: {success_files['seg_total_lines'].mean():.1f}")
        print(f"  最大竖线数: {success_files['seg_total_lines'].max()}")
        print(f"  有竖线的文件: {len(success_files[success_files['seg_total_lines'] > 0])}")
        print(f"  大量竖线文件(>20): {len(success_files[success_files['seg_total_lines'] > 20])}")
        
        print(f"集成检测统计:")
        print(f"  平均竖线数: {success_files['int_total_lines'].mean():.1f}")
        print(f"  最大竖线数: {success_files['int_total_lines'].max()}")
        print(f"  平均异常率: {success_files['int_anomaly_ratio'].mean():.3f}")
        print(f"  最大线强度: {success_files['int_max_strength'].max():.3f}")
    
    # 保存结果
    output_file = "vertical_line_detection_results.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    return df

if __name__ == "__main__":
    results_df = test_all_vertical_detection()
