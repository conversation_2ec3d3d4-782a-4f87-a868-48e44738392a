#!/usr/bin/env python3
"""
正确的多频段同步异常检测器
Correct Multi-band Synchronous Anomaly Detector
检测多个连续频段在某连续时间点的能量突然增加
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加freq_split路径
sys.path.append('..')
try:
    from freq_split import split_freq_steps
except ImportError:
    print("警告: 无法导入freq_split")
    def split_freq_steps(audio_path, **kwargs):
        return [], []

class CorrectMultiBandDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,        # 约21ms @48kHz
            'noverlap': 768,        # 75%重叠
            'window': 'hann',
            'nfft': 1024
        }
        
        # freq_split参数
        self.freq_split_params = {
            'start_freq': 100,
            'stop_freq': 20000,
            'octave': 12,
            'fs': sample_rate,
            'min_cycles': 10,
            'min_duration': 156,
            'energy_threshold_db': -45,
            'min_start_time': 0.2
        }
        
        # 正确的检测参数
        self.detection_params = {
            'frequency_range': (100, 20000),        # 修正：完整扫频范围
            'edge_exclude_ratio': 0.2,              # 排除频段边界20%
            'min_continuous_bands': 5,              # 最少连续频段数
            'min_continuous_time': 3,               # 最少连续时间点数
            'energy_increase_threshold': 2.0,       # 能量增加阈值(倍数)
            'sync_tolerance': 0.8                   # 同步容忍度
        }
        
        print(f"正确的多频段同步异常检测器初始化完成")
        print(f"频谱范围: {self.detection_params['frequency_range'][0]}-{self.detection_params['frequency_range'][1]}Hz")
        print(f"检测逻辑: 多个连续频段在连续时间点的同步能量增加")
    
    def detect_multi_band_anomalies(self, audio_path):
        """检测多频段同步异常"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # STFT时频分析 - 使用完整频率范围
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 计算功率谱
            power_spectrogram = np.abs(Zxx) ** 2
            
            # 频率范围筛选 - 修正为完整扫频范围
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            power_spectrogram = power_spectrogram[freq_mask, :]
            
            # 使用freq_split进行频段分割
            analysis = self._analyze_multi_band_sync(
                audio_path, power_spectrogram, frequencies, times
            )
            
            # 基于多频段同步异常判断
            anomaly_score, anomaly_detected = self._multi_band_decision(analysis)
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'analysis': analysis,
                'detection_method': 'correct_multi_band_sync',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _analyze_multi_band_sync(self, audio_path, power_spectrogram, frequencies, times):
        """分析多频段同步异常"""
        analysis = {
            'freq_split_success': False,
            'total_segments': 0,
            'segment_energy_matrix': None,
            'sync_anomaly_events': [],
            'max_sync_bands': 0,
            'max_sync_duration': 0,
            'total_sync_events': 0
        }
        
        try:
            # 使用freq_split进行频段分割
            step_boundaries, freq_table = split_freq_steps(
                audio_path,
                **self.freq_split_params,
                plot=False
            )
            
            if len(step_boundaries) == 0:
                return analysis
            
            analysis['freq_split_success'] = True
            analysis['total_segments'] = len(step_boundaries)
            
            # 构建频段能量矩阵
            segment_energy_matrix = self._build_segment_energy_matrix(
                power_spectrogram, frequencies, times, step_boundaries
            )
            
            analysis['segment_energy_matrix'] = segment_energy_matrix
            
            # 检测多频段同步异常
            sync_events = self._detect_sync_anomalies(segment_energy_matrix, step_boundaries)
            
            analysis['sync_anomaly_events'] = sync_events
            analysis['total_sync_events'] = len(sync_events)
            
            if sync_events:
                analysis['max_sync_bands'] = max(event['num_bands'] for event in sync_events)
                analysis['max_sync_duration'] = max(event['duration'] for event in sync_events)
            
            print(f"{os.path.basename(audio_path)}: 检测到{len(sync_events)}个多频段同步异常事件")
            
        except Exception as e:
            print(f"多频段同步分析失败: {e}")
        
        return analysis
    
    def _build_segment_energy_matrix(self, power_spectrogram, frequencies, times, step_boundaries):
        """构建频段能量矩阵"""
        num_segments = len(step_boundaries)
        num_time_frames = len(times)
        
        # 初始化频段能量矩阵 [segment, time]
        segment_energy_matrix = np.zeros((num_segments, num_time_frames))
        
        for seg_idx, (seg_start_time, seg_end_time) in enumerate(step_boundaries):
            # 找到频段对应的时间范围
            seg_start_idx = np.argmin(np.abs(times - seg_start_time))
            seg_end_idx = np.argmin(np.abs(times - seg_end_time))
            
            # 排除频段边界
            edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
            seg_core_start = seg_start_idx + edge_exclude
            seg_core_end = seg_end_idx - edge_exclude
            
            if seg_core_end <= seg_core_start:
                continue
            
            # 计算该频段在每个时间点的能量
            for t_idx in range(seg_core_start, seg_core_end):
                if t_idx < num_time_frames:
                    # 计算该时间点该频段的总能量
                    segment_energy_matrix[seg_idx, t_idx] = np.sum(power_spectrogram[:, t_idx])
        
        return segment_energy_matrix
    
    def _detect_sync_anomalies(self, segment_energy_matrix, step_boundaries):
        """检测多频段同步异常"""
        sync_events = []
        num_segments, num_times = segment_energy_matrix.shape
        
        # 对每个频段计算基线能量和异常阈值
        segment_baselines = []
        segment_thresholds = []
        
        for seg_idx in range(num_segments):
            seg_energy = segment_energy_matrix[seg_idx, :]
            valid_energy = seg_energy[seg_energy > 0]  # 排除零值
            
            if len(valid_energy) > 0:
                baseline = np.percentile(valid_energy, 25)
                threshold = baseline * self.detection_params['energy_increase_threshold']
                segment_baselines.append(baseline)
                segment_thresholds.append(threshold)
            else:
                segment_baselines.append(0)
                segment_thresholds.append(0)
        
        # 检测每个时间点的多频段同步异常
        for t_idx in range(num_times):
            # 检查该时间点有多少频段超过阈值
            anomalous_segments = []
            
            for seg_idx in range(num_segments):
                if (segment_energy_matrix[seg_idx, t_idx] > segment_thresholds[seg_idx] and
                    segment_thresholds[seg_idx] > 0):
                    anomalous_segments.append(seg_idx)
            
            # 检查是否有足够多的连续频段异常
            continuous_groups = self._find_continuous_groups(anomalous_segments)
            
            for group in continuous_groups:
                if len(group) >= self.detection_params['min_continuous_bands']:
                    # 检查时间连续性
                    sync_event = self._check_temporal_continuity(
                        segment_energy_matrix, segment_thresholds, group, t_idx
                    )
                    
                    if sync_event:
                        sync_events.append(sync_event)
        
        # 合并相近的事件
        merged_events = self._merge_nearby_events(sync_events)
        
        return merged_events
    
    def _find_continuous_groups(self, segment_indices):
        """找到连续的频段组"""
        if not segment_indices:
            return []
        
        groups = []
        current_group = [segment_indices[0]]
        
        for i in range(1, len(segment_indices)):
            if segment_indices[i] == segment_indices[i-1] + 1:
                current_group.append(segment_indices[i])
            else:
                if len(current_group) >= self.detection_params['min_continuous_bands']:
                    groups.append(current_group)
                current_group = [segment_indices[i]]
        
        if len(current_group) >= self.detection_params['min_continuous_bands']:
            groups.append(current_group)
        
        return groups
    
    def _check_temporal_continuity(self, segment_energy_matrix, segment_thresholds, segment_group, start_time_idx):
        """检查时间连续性"""
        num_times = segment_energy_matrix.shape[1]
        continuous_duration = 0
        
        # 向前和向后检查连续性
        for direction in [-1, 1]:
            t_idx = start_time_idx
            while 0 <= t_idx < num_times:
                # 检查该时间点的频段组是否仍然异常
                sync_ratio = 0
                for seg_idx in segment_group:
                    if (segment_energy_matrix[seg_idx, t_idx] > segment_thresholds[seg_idx] and
                        segment_thresholds[seg_idx] > 0):
                        sync_ratio += 1
                
                sync_ratio /= len(segment_group)
                
                if sync_ratio >= self.detection_params['sync_tolerance']:
                    continuous_duration += 1
                    t_idx += direction
                else:
                    break
        
        # 检查是否满足最小连续时间要求
        if continuous_duration >= self.detection_params['min_continuous_time']:
            return {
                'start_time_idx': start_time_idx,
                'duration': continuous_duration,
                'num_bands': len(segment_group),
                'segment_indices': segment_group,
                'intensity': np.mean([
                    np.max(segment_energy_matrix[seg_idx, start_time_idx:start_time_idx+continuous_duration])
                    for seg_idx in segment_group
                ])
            }
        
        return None
    
    def _merge_nearby_events(self, sync_events):
        """合并相近的同步事件"""
        if not sync_events:
            return []
        
        # 按时间排序
        sync_events.sort(key=lambda x: x['start_time_idx'])
        
        merged = [sync_events[0]]
        
        for event in sync_events[1:]:
            last_event = merged[-1]
            
            # 如果事件时间相近且频段重叠，则合并
            time_gap = event['start_time_idx'] - (last_event['start_time_idx'] + last_event['duration'])
            segment_overlap = set(event['segment_indices']) & set(last_event['segment_indices'])
            
            if time_gap <= 5 and len(segment_overlap) > 0:  # 时间间隔<=5帧且有频段重叠
                # 合并事件
                last_event['duration'] = max(
                    last_event['duration'],
                    event['start_time_idx'] + event['duration'] - last_event['start_time_idx']
                )
                last_event['num_bands'] = max(last_event['num_bands'], event['num_bands'])
                last_event['segment_indices'] = list(set(last_event['segment_indices'] + event['segment_indices']))
                last_event['intensity'] = max(last_event['intensity'], event['intensity'])
            else:
                merged.append(event)
        
        return merged
    
    def _multi_band_decision(self, analysis):
        """基于多频段同步异常判断"""
        if not analysis['freq_split_success'] or analysis['total_sync_events'] == 0:
            return 0.0, False
        
        # 基于同步异常事件的特征计算分数
        num_events = analysis['total_sync_events']
        max_bands = analysis['max_sync_bands']
        max_duration = analysis['max_sync_duration']
        
        # 计算异常分数
        event_score = min(1.0, num_events / 10.0)           # 事件数量分数
        band_score = min(1.0, max_bands / 20.0)             # 频段数量分数
        duration_score = min(1.0, max_duration / 10.0)      # 持续时间分数
        
        # 加权计算最终分数
        anomaly_score = (
            event_score * 0.4 +
            band_score * 0.4 +
            duration_score * 0.2
        )
        
        # 判断是否异常
        anomaly_detected = anomaly_score > 0.3
        
        return anomaly_score, anomaly_detected
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'analysis': {},
            'detection_method': 'correct_multi_band_sync',
            'error': True,
            'error_message': error_msg
        }
    
    def test_all_samples(self):
        """测试所有样本"""
        print("\n" + "="*80)
        print("正确的多频段同步异常检测")
        print("="*80)
        
        test_files = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav",
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        results = []
        for file_path in test_files:
            if os.path.exists(file_path):
                print(f"\n测试文件: {os.path.basename(file_path)}")
                print("-" * 50)
                
                result = self.detect_multi_band_anomalies(file_path)
                result['filename'] = os.path.basename(file_path)
                results.append(result)
                
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    
                    analysis = result['analysis']
                    print(f"同步异常事件: {analysis.get('total_sync_events', 0)}个")
                    print(f"最大同步频段: {analysis.get('max_sync_bands', 0)}个")
                    print(f"最大持续时间: {analysis.get('max_sync_duration', 0)}帧")
                else:
                    print(f"处理失败: {result['error_message']}")
        
        return results

def main():
    """主函数"""
    detector = CorrectMultiBandDetector()
    results = detector.test_all_samples()
    
    # 保存结果
    results_data = []
    for result in results:
        if not result['error']:
            analysis = result['analysis']
            row = {
                'filename': result['filename'],
                'anomaly_detected': result['anomaly_detected'],
                'confidence': result['confidence'],
                'anomaly_score': result['anomaly_score'],
                'freq_split_success': analysis.get('freq_split_success', False),
                'total_sync_events': analysis.get('total_sync_events', 0),
                'max_sync_bands': analysis.get('max_sync_bands', 0),
                'max_sync_duration': analysis.get('max_sync_duration', 0),
                'error': result['error']
            }
            results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('correct_multi_band_sync_results.csv', index=False)
    
    print(f"\n正确的多频段同步异常检测结果已保存: correct_multi_band_sync_results.csv")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
