#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析test20250722/琴身内部异物1.1.wav，生成对数频谱
使用正确的harmonic_detection_system动态噪声阈值计算
多进程加速，每段去除开头结尾8%
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import librosa
from concurrent.futures import ProcessPoolExecutor
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'harmonic_detection_system'))

try:
    from freq_split_optimized import split_freq_steps_optimized
    # 导入正确的动态噪声分析函数
    from harmonic_detector_api import estimate_dynamic_noise_for_segment
except ImportError as e:
    print(f"警告: 无法导入必要模块: {e}")
    sys.exit(1)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def calculate_dynamic_noise_curve_exact(freqs, power, fundamental_freq):
    """
    计算动态噪声阈值曲线 - 完全基于harmonic_detector_api.estimate_dynamic_noise_for_segment
    但返回完整的动态噪声曲线用于绘制
    """
    from scipy.signal import savgol_filter

    # 滑动窗口参数 - 与harmonic_detector_api完全一致
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长

    # 创建排除掩码 - 排除主频和谐波位置 - 与原函数完全一致
    exclude_mask = np.zeros(len(freqs), dtype=bool)

    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude

    # 排除前10个谐波位置±5Hz (扩展到24kHz)
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= 24000:  # 扩展到24kHz
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude

    # 滑动窗口噪声估计 - 与原函数完全一致
    local_noise_levels = []
    window_centers = []

    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True

        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask

        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile

            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])

    if len(local_noise_levels) == 0:
        return None, None

    # 平滑噪声曲线 - 与原函数完全一致
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels,
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels

    # 插值到完整频率范围 - 用于绘制动态曲线
    dynamic_noise_curve = np.interp(freqs, window_centers, smoothed_noise)

    # 同时计算全局噪声底噪 - 与原函数一致
    global_noise_floor_db = np.percentile(smoothed_noise, 20)

    return dynamic_noise_curve, global_noise_floor_db

def analyze_single_segment_log_spectrum_correct(args):
    """
    分析单个频段并生成对数频谱图像 - 使用正确的动态噪声阈值
    用于多进程处理
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, output_dir = args
    
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz ({start_time:.3f}s - {end_time:.3f}s)")
        
        # 提取段音频
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = audio_data[start_sample:end_sample]
        
        if len(segment_audio) == 0:
            print(f"    警告: 段 {seg_idx} 音频长度为0")
            return False
        
        # 去除开头和结尾8% - 与harmonic_detector_api_fast.py保持一致
        trim_length = int(len(segment_audio) * 0.08)
        if len(segment_audio) > 2 * trim_length:
            segment_audio = segment_audio[trim_length:-trim_length]
            actual_start_time = start_time + trim_length / sr
            actual_end_time = end_time - trim_length / sr
        else:
            actual_start_time = start_time
            actual_end_time = end_time
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 高分辨率FFT分析 - 与harmonic_detector_api_fast.py完全一致
        fft_size = 131072  # 128k点FFT
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]
        
        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window
        
        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]
        
        # 限制显示范围到24kHz
        freq_mask = positive_freqs <= 24000
        display_freqs = positive_freqs[freq_mask]
        display_power = positive_power[freq_mask]
        
        # 转换为dB - 对数频谱
        power_db = 10 * np.log10(display_power + 1e-12)
        
        # 找主频 - 与harmonic_detector_api_fast.py一致
        search_bandwidth = 2.0
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)
        
        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = display_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = display_freqs[actual_idx]
        else:
            fundamental_freq = expected_freq
        
        # 计算动态噪声阈值曲线 - 完全基于harmonic_detector_api方法
        dynamic_noise_curve, global_noise_floor_db = calculate_dynamic_noise_curve_exact(display_freqs, display_power, fundamental_freq)

        # 同时调用原始函数进行验证
        noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)

        # 创建对数频谱可视化图像
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))

        # 绘制频谱 - 黑色线
        ax.semilogx(display_freqs, power_db, color='black', linewidth=0.8, alpha=0.8, label='频谱')

        # 绘制动态噪声阈值曲线 - 红色线
        if dynamic_noise_curve is not None:
            ax.semilogx(display_freqs, dynamic_noise_curve, color='red', linestyle='-', linewidth=1.5, alpha=0.9,
                       label=f'动态噪声阈值 (全局底噪: {global_noise_floor_db:.1f}dB)')

        # 验证一致性：绘制原始函数的全局噪声底噪线作为参考
        if noise_analysis:
            original_global_noise = noise_analysis['global_noise_floor_db']
            ax.axhline(y=original_global_noise, color='orange', linestyle='--', linewidth=1.0, alpha=0.7,
                      label=f'原始全局底噪: {original_global_noise:.1f}dB')
        
        # 标记主频 - 蓝色圆点
        fundamental_power_db = 10 * np.log10(display_power[np.argmin(np.abs(display_freqs - fundamental_freq))] + 1e-12)
        ax.plot(fundamental_freq, fundamental_power_db, 'o', color='blue', markersize=8, 
               markeredgecolor='darkblue', markeredgewidth=1.5, label=f'主频 {fundamental_freq:.1f}Hz')
        
        # 设置图形属性
        ax.set_xlabel('频率 (Hz)', fontsize=14, fontweight='bold')
        ax.set_ylabel('功率 (dB)', fontsize=14, fontweight='bold')
        ax.set_title(f'频段 {seg_idx:02d}: {expected_freq:.1f}Hz 对数频谱分析\n'
                    f'有效时间: {actual_start_time:.3f}s - {actual_end_time:.3f}s (去除首尾8%)', 
                    fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
        ax.legend(fontsize=12, loc='upper right', framealpha=0.9)
        
        # 设置对数频率范围到24kHz
        min_freq = max(10, np.min(display_freqs[display_freqs > 0]))  # 避免0频率
        max_freq = min(24000, np.max(display_freqs))
        ax.set_xlim(min_freq, max_freq)

        # 固定纵坐标范围 -80~40dB
        ax.set_ylim(-80, 40)
        
        # 保存图像
        output_filename = os.path.join(output_dir, f'segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png')
        plt.tight_layout()
        plt.savefig(output_filename, dpi=200, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"    ✅ 保存: {output_filename}")
        return True
        
    except Exception as e:
        print(f"    ❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main(audio_path=None):
    """
    主函数：多进程分析音频文件 - 通用对数频谱分析

    Args:
        audio_path (str): 音频文件路径，如果为None则从命令行参数获取
    """
    # 获取音频文件路径
    if audio_path is None:
        if len(sys.argv) > 1:
            audio_path = sys.argv[1]
        else:
            # 默认文件路径
            audio_path = "test20250722/琴身内部异物1.1.wav"

    print(f"🎯 分析音频文件: {audio_path}")
    print("="*70)

    # 检查文件是否存在
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        print(f"💡 使用方法: python {os.path.basename(__file__)} <音频文件路径>")
        return

    # 创建输出目录 - 基于输入文件名
    audio_name = os.path.splitext(os.path.basename(audio_path))[0]
    output_dir = f"{audio_name}_对数频谱分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    start_time = time.time()
    
    try:
        # 获取频段分割 - 与harmonic_detector_api_fast.py完全一致
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        print(f"✅ 频段分割完成，共{len(step_boundaries)}段")
        
        # 加载音频
        print("🎵 加载音频文件...")
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
            sr = 48000
        
        print(f"✅ 音频加载完成，采样率: {sr}Hz，时长: {len(y)/sr:.2f}s")
        
        # 准备多进程任务参数
        print("🚀 准备多进程分析...")
        tasks = []
        for seg_idx in range(len(step_boundaries)):
            start_time_seg, end_time_seg = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            tasks.append((
                seg_idx,
                start_time_seg,
                end_time_seg,
                expected_freq,
                y,  # 共享音频数据
                sr,
                output_dir
            ))
        
        # 多进程并行处理93段
        num_workers = 8  # 与harmonic_detector_api_fast.py一致
        print(f"📊 使用{num_workers}个进程并行分析93个频段...")
        
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            results = list(executor.map(analyze_single_segment_log_spectrum_correct, tasks))
        
        # 统计结果
        successful_count = sum(1 for r in results if r)
        failed_count = len(results) - successful_count
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print("\n" + "="*70)
        print("✅ 对数频谱分析完成!")
        print(f"  音频文件: {os.path.basename(audio_path)}")
        print(f"  成功分析: {successful_count}个频段")
        print(f"  失败: {failed_count}个频段")
        print(f"  总耗时: {processing_time:.1f}秒")
        print(f"  平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  输出目录: {output_dir}")
        print(f"  使用进程数: {num_workers}")

        # 创建汇总信息文件
        create_correct_summary(output_dir, audio_path, step_boundaries, freq_table,
                              successful_count, failed_count, processing_time, num_workers)
        
    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

def create_correct_summary(output_dir, audio_path, step_boundaries, freq_table,
                          successful_count, failed_count, processing_time, num_workers):
    """
    创建对数频谱分析汇总信息文件
    """
    summary_file = os.path.join(output_dir, "对数频谱分析汇总.txt")

    audio_filename = os.path.basename(audio_path)
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"{audio_filename} - 对数频谱分析汇总\n")
        f.write("="*60 + "\n\n")

        f.write(f"音频文件: {audio_path}\n")
        f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析方法: 对数频谱 (semilogx)\n")
        f.write(f"数据处理: 去除每段首尾8%\n")
        f.write(f"噪声阈值: 使用harmonic_detector_api.estimate_dynamic_noise_for_segment\n")
        f.write(f"多进程: {num_workers}个工作进程\n")
        f.write(f"参考系统: harmonic_detection_system/harmonic_detector_api_fast.py\n")
        f.write(f"总频段数: {len(step_boundaries)}\n")
        f.write(f"成功分析: {successful_count}个频段\n")
        f.write(f"失败: {failed_count}个频段\n")
        f.write(f"处理耗时: {processing_time:.1f}秒\n\n")

        f.write("可视化内容:\n")
        f.write("- 频谱曲线 (黑色线)\n")
        f.write("- 全局噪声底噪线 (红色水平线)\n")
        f.write("- 主频标记 (蓝色圆点)\n\n")

        f.write("技术细节:\n")
        f.write("- FFT大小: 131072点 (128k)\n")
        f.write("- 窗函数: Hanning窗\n")
        f.write("- 频率范围: 10Hz - 20000Hz (对数轴)\n")
        f.write("- 图像尺寸: 16×10英寸, 200 DPI\n\n")

        f.write("频段详情:\n")
        f.write("-" * 70 + "\n")
        f.write("段号  期望频率(Hz)  开始时间(s)  结束时间(s)  有效时长(s)  去除时长(s)\n")
        f.write("-" * 70 + "\n")

        for i, (start_time, end_time) in enumerate(step_boundaries):
            expected_freq = freq_table[i]
            total_duration = end_time - start_time
            trim_duration = total_duration * 0.16  # 首尾各8%
            effective_duration = total_duration - trim_duration
            f.write(f"{i:2d}    {expected_freq:8.1f}    {start_time:8.3f}    {end_time:8.3f}    "
                   f"{effective_duration:8.3f}    {trim_duration:8.3f}\n")

    print(f"📄 汇总信息已保存: {summary_file}")

if __name__ == "__main__":
    main()
