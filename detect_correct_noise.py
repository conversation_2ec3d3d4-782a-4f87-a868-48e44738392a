#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的噪声检测算法
基于harmonic_detection_system，对比频谱与动态噪声阈值的变化波动
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    calculate_noise_fluctuation_features,
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_threshold(freqs, power, fundamental_freq):
    """
    估计动态噪声阈值 - 与harmonic_detection_system完全一致
    """
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None, None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    return smoothed_noise, window_centers, exclude_mask

def method1_spectrum_vs_noise_smoothness(freqs, power_db, smoothed_noise, window_centers, exclude_mask):
    """
    方法1: 频谱与动态噪声阈值的平滑度对比
    检测频谱相对于动态噪声阈值的异常波动
    """
    
    if smoothed_noise is None or len(smoothed_noise) == 0:
        return False, [], "无有效动态噪声阈值"
    
    # 将动态噪声阈值插值到频谱的频率网格
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    # 计算频谱与噪声阈值的差值（在非谐波区域）
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    noise_thresholds = noise_threshold_interp[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声区域点数太少"
    
    # 计算频谱相对于噪声阈值的偏差
    power_deviation = noise_powers - noise_thresholds
    
    # 计算频谱的平滑度（一阶差分）
    spectrum_diff1 = np.diff(noise_powers)
    spectrum_smoothness = np.std(spectrum_diff1)
    
    # 计算噪声阈值的平滑度
    threshold_diff1 = np.diff(noise_thresholds)
    threshold_smoothness = np.std(threshold_diff1)
    
    # 检测异常波动：频谱比噪声阈值更不平滑
    smoothness_ratio = spectrum_smoothness / (threshold_smoothness + 1e-6)
    
    # 找到显著超出噪声阈值且波动剧烈的点
    significant_deviation = power_deviation > 8  # 超出噪声阈值8dB
    
    detected_points = []
    if smoothness_ratio > 2.0:  # 频谱比噪声阈值不平滑2倍以上
        for i, (freq, power, deviation) in enumerate(zip(noise_freqs, noise_powers, power_deviation)):
            if significant_deviation[i]:
                detected_points.append((freq, power))
    
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"平滑度比: {smoothness_ratio:.2f}, 检测到{len(detected_points)}个异常波动点"

def method2_local_fluctuation_analysis(freqs, power_db, smoothed_noise, window_centers, exclude_mask):
    """
    方法2: 局部波动分析
    检测频谱在局部区域相对于噪声阈值的异常波动
    """
    
    if smoothed_noise is None:
        return False, [], "无有效动态噪声阈值"
    
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    noise_mask = ~exclude_mask
    
    # 局部窗口分析
    window_size = 50  # 局部窗口大小
    detected_points = []
    
    noise_indices = np.where(noise_mask)[0]
    
    for i in range(0, len(noise_indices) - window_size, 10):
        window_indices = noise_indices[i:i+window_size]
        
        window_freqs = freqs[window_indices]
        window_powers = power_db[window_indices]
        window_thresholds = noise_threshold_interp[window_indices]
        
        # 计算局部波动特征
        power_fluctuation = np.std(np.diff(window_powers))
        threshold_fluctuation = np.std(np.diff(window_thresholds))
        
        # 计算相对于阈值的偏差
        deviations = window_powers - window_thresholds
        significant_deviations = deviations > 6  # 超出6dB
        
        # 如果频谱波动明显大于阈值波动，且有显著偏差
        if power_fluctuation > threshold_fluctuation * 1.5 and np.any(significant_deviations):
            for j, (freq, power, deviation) in enumerate(zip(window_freqs, window_powers, deviations)):
                if significant_deviations[j]:
                    detected_points.append((freq, power))
    
    # 去重
    detected_points = list(set(detected_points))
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"检测到{len(detected_points)}个局部波动异常点"

def method3_harmonic_exclusion_residual(freqs, power_db, harmonic_analysis, exclude_mask):
    """
    方法3: 谐波排除后的残留分析
    使用harmonic_detection_system检测谐波，分析排除后的残留
    """
    
    # 创建更精确的谐波排除掩码
    precise_exclude_mask = exclude_mask.copy()
    
    if harmonic_analysis:
        for harmonic in harmonic_analysis:
            harmonic_freq = harmonic['freq']
            # 更精确的谐波排除（±3Hz）
            harmonic_exclude = (freqs >= harmonic_freq - 3) & (freqs <= harmonic_freq + 3)
            precise_exclude_mask |= harmonic_exclude
    
    # 分析残留频谱
    residual_mask = ~precise_exclude_mask
    residual_freqs = freqs[residual_mask]
    residual_powers = power_db[residual_mask]
    
    if len(residual_powers) < 10:
        return False, [], "残留频谱点数太少"
    
    # 计算残留频谱的统计特征
    residual_mean = np.mean(residual_powers)
    residual_std = np.std(residual_powers)
    
    # 检测残留中的异常高能量点
    anomaly_threshold = residual_mean + 2.5 * residual_std
    anomaly_mask = residual_powers > anomaly_threshold
    
    detected_points = [(freq, power) for freq, power in zip(residual_freqs[anomaly_mask], residual_powers[anomaly_mask])]
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"残留均值: {residual_mean:.1f}dB, 检测到{len(detected_points)}个残留异常点"

def method4_noise_threshold_deviation(freqs, power_db, smoothed_noise, window_centers, exclude_mask):
    """
    方法4: 噪声阈值偏差分析
    检测频谱显著超出动态噪声阈值的点
    """
    
    if smoothed_noise is None:
        return False, [], "无有效动态噪声阈值"
    
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    noise_mask = ~exclude_mask
    
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    noise_thresholds = noise_threshold_interp[noise_mask]
    
    # 计算偏差
    deviations = noise_powers - noise_thresholds
    
    # 动态阈值：基于偏差的统计分布
    deviation_mean = np.mean(deviations)
    deviation_std = np.std(deviations)
    
    # 检测显著偏差（超出均值+2σ且绝对值>10dB）
    significant_mask = (deviations > deviation_mean + 2 * deviation_std) & (deviations > 10)
    
    detected_points = [(freq, power) for freq, power in zip(noise_freqs[significant_mask], noise_powers[significant_mask])]
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"偏差阈值: {deviation_mean + 2 * deviation_std:.1f}dB, 检测到{len(detected_points)}个显著偏差点"

def method5_spectral_continuity_analysis(freqs, power_db, exclude_mask):
    """
    方法5: 频谱连续性分析
    检测频谱中的不连续跳跃和孤立峰值
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声区域点数太少"
    
    # 计算频谱的连续性
    power_diff = np.diff(noise_powers)
    
    # 检测不连续跳跃（大于3σ的变化）
    diff_std = np.std(power_diff)
    jump_threshold = 3 * diff_std
    
    jump_indices = np.where(np.abs(power_diff) > jump_threshold)[0]
    
    detected_points = []
    for idx in jump_indices:
        # 检查跳跃点及其邻近点
        for offset in [0, 1]:
            if idx + offset < len(noise_freqs):
                freq = noise_freqs[idx + offset]
                power = noise_powers[idx + offset]
                detected_points.append((freq, power))
    
    # 去重
    detected_points = list(set(detected_points))
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"跳跃阈值: {jump_threshold:.1f}dB, 检测到{len(detected_points)}个不连续点"

def method6_adaptive_noise_detection(freqs, power_db, smoothed_noise, window_centers, exclude_mask, fundamental_power_db):
    """
    方法6: 自适应噪声检测
    基于主频功率和动态噪声阈值的自适应检测
    """
    
    if smoothed_noise is None:
        return False, [], "无有效动态噪声阈值"
    
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    noise_mask = ~exclude_mask
    
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    noise_thresholds = noise_threshold_interp[noise_mask]
    
    # 自适应阈值：基于主频功率
    relative_threshold = fundamental_power_db - 25  # 比主频低25dB
    adaptive_threshold = np.maximum(noise_thresholds + 8, relative_threshold)  # 取较大值
    
    # 检测超出自适应阈值的点
    detection_mask = noise_powers > adaptive_threshold
    
    detected_points = [(freq, power) for freq, power in zip(noise_freqs[detection_mask], noise_powers[detection_mask])]
    has_noise = len(detected_points) > 0
    
    return has_noise, detected_points, f"自适应阈值范围: {np.min(adaptive_threshold):.1f}~{np.max(adaptive_threshold):.1f}dB, 检测到{len(detected_points)}个点"

def analyze_segment_for_correct_noise(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的正确噪声检测
    """

    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            return None

        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 高分辨率FFT
        fft_size = 131072
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]

        # 应用窗函数
        window = np.hanning(len(segment_audio))
        segment_audio = segment_audio * window

        # FFT
        fft = np.fft.fft(segment_audio)
        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2

        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        positive_power = power[:fft_size//2]

        # 限制到20kHz
        freq_mask = positive_freqs <= 20000
        freqs = positive_freqs[freq_mask]
        power_linear = positive_power[freq_mask]
        power_db = 10 * np.log10(power_linear + 1e-12)

        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_linear[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_power_db = power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = 10 * np.log10(np.max(power_linear) + 1e-12)

        # 估计动态噪声阈值
        smoothed_noise, window_centers, exclude_mask = estimate_dynamic_noise_threshold(freqs, power_linear, fundamental_freq)

        # 使用harmonic_detection_system检测谐波
        noise_analysis = {
            'global_noise_floor_db': np.mean(smoothed_noise) if smoothed_noise is not None else -60,
            'noise_variation_db': np.max(smoothed_noise) - np.min(smoothed_noise) if smoothed_noise is not None else 0,
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }

        harmonic_analysis = detect_harmonics_for_segment(freqs, power_linear, fundamental_freq, noise_analysis)

        # 应用6种正确的噪声检测方法
        methods = [
            ("频谱vs噪声平滑度", method1_spectrum_vs_noise_smoothness),
            ("局部波动分析", method2_local_fluctuation_analysis),
            ("谐波排除残留", method3_harmonic_exclusion_residual),
            ("噪声阈值偏差", method4_noise_threshold_deviation),
            ("频谱连续性分析", method5_spectral_continuity_analysis),
            ("自适应噪声检测", method6_adaptive_noise_detection)
        ]

        results = {}
        for method_name, method_func in methods:
            if method_name == "谐波排除残留":
                has_noise, detected_points, info = method_func(freqs, power_db, harmonic_analysis, exclude_mask)
            elif method_name == "频谱连续性分析":
                has_noise, detected_points, info = method_func(freqs, power_db, exclude_mask)
            elif method_name == "自适应噪声检测":
                has_noise, detected_points, info = method_func(freqs, power_db, smoothed_noise, window_centers, exclude_mask, fundamental_power_db)
            else:
                has_noise, detected_points, info = method_func(freqs, power_db, smoothed_noise, window_centers, exclude_mask)

            results[method_name] = {
                'has_noise': has_noise,
                'detected_points': detected_points,
                'info': info
            }

        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'power_db': power_db,
            'exclude_mask': exclude_mask,
            'smoothed_noise': smoothed_noise,
            'window_centers': window_centers,
            'harmonic_analysis': harmonic_analysis,
            'results': results
        }

    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_correct_noise_visualization(segment_data, output_dir, filename=""):
    """
    创建正确噪声检测可视化
    """

    if not segment_data:
        return None

    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    fundamental_power_db = segment_data['fundamental_power_db']
    freqs = segment_data['freqs']
    power_db = segment_data['power_db']
    exclude_mask = segment_data['exclude_mask']
    smoothed_noise = segment_data['smoothed_noise']
    window_centers = segment_data['window_centers']
    harmonic_analysis = segment_data['harmonic_analysis']
    results = segment_data['results']

    # 创建图形 - 2x3布局显示6种方法
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n正确噪声检测 (主频: {fundamental_power_db:.1f}dB)',
                 fontsize=16, fontweight='bold')

    method_names = list(results.keys())
    colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown']

    for i, (method_name, color) in enumerate(zip(method_names, colors)):
        ax = axes[i]
        result = results[method_name]

        # 绘制基础频谱
        ax.plot(freqs, power_db, 'lightgray', linewidth=1, alpha=0.7, label='频谱')

        # 绘制动态噪声阈值
        if smoothed_noise is not None and window_centers is not None:
            ax.plot(window_centers, smoothed_noise, 'cyan', linewidth=2, alpha=0.8, label='动态噪声阈值')

        # 标记主频
        ax.axvline(fundamental_freq, color='black', linestyle='-', linewidth=3,
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')

        # 标记检测到的谐波
        if harmonic_analysis:
            harmonic_freqs = [h['freq'] for h in harmonic_analysis]
            harmonic_powers = [h['power_db'] for h in harmonic_analysis]
            ax.scatter(harmonic_freqs, harmonic_powers, c='blue', s=30, alpha=0.6,
                      marker='^', label=f'谐波({len(harmonic_analysis)}个)')

        # 标记排除区域
        excluded_freqs = freqs[exclude_mask]
        excluded_powers = power_db[exclude_mask]
        if len(excluded_freqs) > 0:
            ax.scatter(excluded_freqs, excluded_powers, c='lightblue', s=1,
                      alpha=0.3, label='排除区域')

        # 标记检测到的噪声点
        detected_points = result['detected_points']
        if detected_points:
            det_freqs, det_powers = zip(*detected_points)
            ax.scatter(det_freqs, det_powers, c=color, s=50, alpha=0.8,
                      marker='x', linewidth=3, label=f'检测噪声({len(detected_points)}个)')

            # 标注一些检测点的功率值
            for j, (freq, power) in enumerate(detected_points[:3]):  # 只标注前3个
                ax.annotate(f'{power:.0f}', (freq, power), xytext=(5, 5),
                           textcoords='offset points', fontsize=8, color=color)

        # 设置标题和标签
        has_noise = result['has_noise']
        noise_status = "检测到噪声" if has_noise else "无噪声"
        ax.set_title(f'{method_name} - {noise_status}\n{result["info"]}', fontsize=10)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, 40)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

        # 设置边框颜色表示检测结果
        for spine in ax.spines.values():
            spine.set_color(color if has_noise else 'gray')
            spine.set_linewidth(3 if has_noise else 1)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"correct_noise_detection_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment_correct_noise(args):
    """
    处理单个段的正确噪声检测（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 检测段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_for_correct_noise(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_correct_noise_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 统计检测结果
                results = segment_data['results']
                noise_count = sum(1 for r in results.values() if r['has_noise'])

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_power_db': segment_data['fundamental_power_db'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'noise_methods': noise_count,
                    'total_methods': len(results),
                    'results': results
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 噪声检测: {noise_count}/{len(results)}种方法, 谐波: {result['harmonic_count']}个")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析低频戳洞文件的正确噪声检测
    """

    print("🎯 低频戳洞文件正确噪声检测")
    print("📝 基于harmonic_detection_system，对比频谱与动态噪声阈值的变化波动")
    print("📝 正确排除主频和谐波，检测真实噪声特征")
    print("="*80)

    # 查找低频戳洞文件
    target_file = None
    test_dirs = ["test20250717", "test20250722"]

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, _, files in os.walk(test_dir):
                for file in files:
                    if "低音戳洞" in file and file.lower().endswith('.wav'):
                        target_file = os.path.join(root, file)
                        break
                if target_file:
                    break
        if target_file:
            break

    if not target_file:
        print("❌ 未找到低频戳洞文件")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_correct_noise_detection"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行正确噪声检测...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_correct_noise, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 统计分析
        method_stats = {}
        method_names = ["频谱vs噪声平滑度", "局部波动分析", "谐波排除残留",
                       "噪声阈值偏差", "频谱连续性分析", "自适应噪声检测"]

        for method_name in method_names:
            method_stats[method_name] = {
                'detected_segments': 0,
                'total_segments': len(successful_results)
            }

        for result in successful_results:
            for method_name in method_names:
                if result['results'][method_name]['has_noise']:
                    method_stats[method_name]['detected_segments'] += 1

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件正确噪声检测完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        print(f"\n🔍 各方法检测结果统计:")
        for method_name, stats in method_stats.items():
            detected = stats['detected_segments']
            total = stats['total_segments']
            percentage = (detected / total * 100) if total > 0 else 0
            print(f"  📈 {method_name}: {detected}/{total} ({percentage:.1f}%)")

        # 综合分析
        multi_method_segments = []
        for result in successful_results:
            noise_count = result['noise_methods']
            if noise_count >= 3:  # 3种以上方法检测到噪声
                multi_method_segments.append((result['seg_idx'], result['expected_freq'],
                                            noise_count, result['fundamental_power_db'], result['harmonic_count']))

        print(f"\n🎯 重点关注段（≥3种方法检测到噪声）:")
        if multi_method_segments:
            for seg_idx, freq, count, main_power, harmonic_count in multi_method_segments:
                print(f"  ⚠️  段{seg_idx} ({freq:.1f}Hz, 主频{main_power:.1f}dB, 谐波{harmonic_count}个): {count}/6种方法检测到噪声")
        else:
            print("  ✅ 无段被多种方法同时检测为有噪声")

        # 主频功率和谐波统计
        main_powers = [r['fundamental_power_db'] for r in successful_results]
        harmonic_counts = [r['harmonic_count'] for r in successful_results]

        print(f"\n📊 频谱特征统计:")
        print(f"  平均主频功率: {np.mean(main_powers):.1f}dB")
        print(f"  主频功率范围: {np.min(main_powers):.1f} ~ {np.max(main_powers):.1f}dB")
        print(f"  平均谐波数: {np.mean(harmonic_counts):.1f}个")
        print(f"  谐波数范围: {np.min(harmonic_counts)} ~ {np.max(harmonic_counts)}个")

        print("="*80)
        print("🎯 正确噪声检测分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
