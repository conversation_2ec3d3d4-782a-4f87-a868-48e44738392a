#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频谱抖动幅度检测脚本
检测频谱中抖动幅度很大的区域，排除主频和谐波
使用多种方法：频谱与动态噪声阈值差值、连续大幅度检测等
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import librosa
from scipy.signal import savgol_filter
from multiprocessing import Pool, cpu_count
import time

# 添加路径
sys.path.append('.')
sys.path.append('harmonic_detection_system')

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从谐波检测系统导入
from harmonic_detection_system.harmonic_detector_api import (
    detect_harmonics_for_segment
)

# 设置matplotlib中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def estimate_dynamic_noise_threshold(freqs, power_linear, fundamental_freq):
    """
    估计动态噪声阈值
    """
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power_linear[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None, None, exclude_mask
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    return smoothed_noise, window_centers, exclude_mask

def method1_spectrum_threshold_difference(freqs, power_db, smoothed_noise, window_centers, exclude_mask):
    """
    方法1: 频谱与动态噪声阈值差值分析
    """
    
    if smoothed_noise is None:
        return False, [], "无有效动态噪声阈值"
    
    # 将动态噪声阈值插值到频谱的频率网格
    noise_threshold_interp = np.interp(freqs, window_centers, smoothed_noise)
    
    # 计算频谱与噪声阈值的差值（排除主频和谐波）
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    noise_thresholds = noise_threshold_interp[noise_mask]
    
    if len(noise_powers) == 0:
        return False, [], "无有效噪声区域"
    
    # 计算差值
    power_threshold_diff = noise_powers - noise_thresholds
    
    # 检测大幅度偏差（超出阈值15dB以上）
    large_deviation_mask = power_threshold_diff > 15
    
    detected_points = [(freq, power) for freq, power in zip(noise_freqs[large_deviation_mask], noise_powers[large_deviation_mask])]
    
    has_fluctuation = len(detected_points) > 0
    
    return has_fluctuation, detected_points, f"检测到{len(detected_points)}个大幅度偏差点"

def method2_continuous_large_amplitude_detection(freqs, power_db, exclude_mask):
    """
    方法2: 连续大幅度抖动检测
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声点太少"
    
    # 计算局部功率变化
    window_size = 50  # 局部窗口
    large_amplitude_points = []
    
    for i in range(len(noise_powers) - window_size):
        window_powers = noise_powers[i:i+window_size]
        window_freqs = noise_freqs[i:i+window_size]
        
        # 计算窗口内的抖动幅度
        power_range = np.max(window_powers) - np.min(window_powers)
        power_std = np.std(window_powers)
        
        # 检测大幅度抖动：范围>20dB且标准差>8dB
        if power_range > 20 and power_std > 8:
            # 找到窗口内的极值点
            for j, (freq, power) in enumerate(zip(window_freqs, window_powers)):
                if power > np.mean(window_powers) + power_std:
                    large_amplitude_points.append((freq, power))
    
    # 去重
    large_amplitude_points = list(set(large_amplitude_points))
    has_fluctuation = len(large_amplitude_points) > 0
    
    return has_fluctuation, large_amplitude_points, f"检测到{len(large_amplitude_points)}个连续大幅度抖动点"

def method3_spectral_roughness_analysis(freqs, power_db, exclude_mask):
    """
    方法3: 频谱粗糙度分析
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 10:
        return False, [], "噪声点太少"
    
    # 计算频谱的粗糙度（相邻点差分的平方和）
    diff1 = np.diff(noise_powers)
    diff2 = np.diff(diff1)  # 二阶差分
    
    # 计算局部粗糙度
    window_size = 30
    rough_points = []
    
    for i in range(len(diff2) - window_size):
        window_diff2 = diff2[i:i+window_size]
        local_roughness = np.sum(window_diff2**2)
        
        # 检测高粗糙度区域
        if local_roughness > 500:  # 粗糙度阈值
            center_idx = i + window_size // 2
            if center_idx < len(noise_freqs):
                freq = noise_freqs[center_idx]
                power = noise_powers[center_idx]
                rough_points.append((freq, power))
    
    # 去重
    rough_points = list(set(rough_points))
    has_fluctuation = len(rough_points) > 0
    
    return has_fluctuation, rough_points, f"检测到{len(rough_points)}个高粗糙度点"

def method4_local_variance_analysis(freqs, power_db, exclude_mask):
    """
    方法4: 局部方差分析
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声点太少"
    
    # 计算局部方差
    window_size = 40
    high_variance_points = []
    
    for i in range(len(noise_powers) - window_size):
        window_powers = noise_powers[i:i+window_size]
        window_freqs = noise_freqs[i:i+window_size]
        
        local_variance = np.var(window_powers)
        
        # 检测高方差区域
        if local_variance > 50:  # 方差阈值
            # 找到窗口内的异常点
            mean_power = np.mean(window_powers)
            std_power = np.std(window_powers)
            
            for freq, power in zip(window_freqs, window_powers):
                if abs(power - mean_power) > 1.5 * std_power:
                    high_variance_points.append((freq, power))
    
    # 去重
    high_variance_points = list(set(high_variance_points))
    has_fluctuation = len(high_variance_points) > 0
    
    return has_fluctuation, high_variance_points, f"检测到{len(high_variance_points)}个高方差点"

def method5_peak_density_analysis(freqs, power_db, exclude_mask):
    """
    方法5: 峰值密度分析
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 20:
        return False, [], "噪声点太少"
    
    # 简单峰值检测
    peaks = []
    for i in range(1, len(noise_powers) - 1):
        if (noise_powers[i] > noise_powers[i-1] and 
            noise_powers[i] > noise_powers[i+1] and
            noise_powers[i] > np.mean(noise_powers) + np.std(noise_powers)):
            peaks.append((noise_freqs[i], noise_powers[i]))
    
    # 分析峰值密度
    freq_span = noise_freqs[-1] - noise_freqs[0] if len(noise_freqs) > 1 else 1
    peak_density = len(peaks) / freq_span * 1000  # 每kHz的峰值数
    
    # 检测高峰值密度区域
    high_density_points = []
    if peak_density > 0.5:  # 峰值密度阈值
        high_density_points = peaks
    
    has_fluctuation = len(high_density_points) > 0
    
    return has_fluctuation, high_density_points, f"峰值密度: {peak_density:.2f}/kHz, 检测到{len(high_density_points)}个高密度峰值"

def method6_amplitude_jump_detection(freqs, power_db, exclude_mask):
    """
    方法6: 幅度跳跃检测
    """
    
    noise_mask = ~exclude_mask
    noise_freqs = freqs[noise_mask]
    noise_powers = power_db[noise_mask]
    
    if len(noise_powers) < 10:
        return False, [], "噪声点太少"
    
    # 计算相邻点的幅度跳跃
    power_jumps = np.abs(np.diff(noise_powers))
    
    # 检测大幅度跳跃
    jump_threshold = np.mean(power_jumps) + 3 * np.std(power_jumps)
    large_jump_indices = np.where(power_jumps > jump_threshold)[0]
    
    jump_points = []
    for idx in large_jump_indices:
        # 包括跳跃前后的点
        for offset in [0, 1]:
            if idx + offset < len(noise_freqs):
                freq = noise_freqs[idx + offset]
                power = noise_powers[idx + offset]
                jump_points.append((freq, power))
    
    # 去重
    jump_points = list(set(jump_points))
    has_fluctuation = len(jump_points) > 0
    
    return has_fluctuation, jump_points, f"跳跃阈值: {jump_threshold:.1f}dB, 检测到{len(jump_points)}个幅度跳跃点"

def analyze_segment_fluctuation_amplitude(seg_idx, start_time, end_time, expected_freq, y, sr, use_trim=True):
    """
    分析单个段的频谱抖动幅度
    """

    try:
        if use_trim:
            # 8%修剪
            trim_percentage = 0.08
            segment_duration = end_time - start_time
            trim_duration = segment_duration * trim_percentage

            trimmed_start_time = start_time + trim_duration
            trimmed_end_time = end_time - trim_duration

            if trimmed_end_time <= trimmed_start_time:
                print(f"  ⚠️  段{seg_idx}: 修剪后时长不足")
                return None

            start_sample = int(trimmed_start_time * sr)
            end_sample = int(trimmed_end_time * sr)
        else:
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)

        # 检查边界
        if start_sample >= len(y) or end_sample > len(y) or start_sample >= end_sample:
            print(f"  ⚠️  段{seg_idx}: 时间边界超出音频范围")
            return None

        segment_audio = y[start_sample:end_sample]

        if len(segment_audio) == 0:
            print(f"  ⚠️  段{seg_idx}: 音频段为空")
            return None

        if len(segment_audio) < 1024:
            print(f"  ⚠️  段{seg_idx}: 音频段太短")
            return None

        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))

        # 高分辨率FFT，不加窗
        fft_size = 131072
        if len(segment_audio) < fft_size:
            segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
        else:
            segment_audio = segment_audio[:fft_size]

        # 直接FFT
        fft_result = np.fft.fft(segment_audio)
        positive_fft = fft_result[:fft_size//2]
        freqs_full = np.fft.fftfreq(fft_size, 1/sr)[:fft_size//2]

        # 限制到20kHz
        freq_mask = freqs_full <= 20000
        freqs = freqs_full[freq_mask]
        positive_fft = positive_fft[freq_mask]

        # 计算功率谱
        power_linear = np.abs(positive_fft) ** 2
        power_db = 10 * np.log10(power_linear + 1e-12)

        # 找主频
        search_bandwidth = 2.0
        search_mask = (freqs >= expected_freq - search_bandwidth) & \
                     (freqs <= expected_freq + search_bandwidth)

        if np.any(search_mask):
            search_indices = np.where(search_mask)[0]
            search_powers = power_linear[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            fundamental_freq = freqs[actual_idx]
            fundamental_power_db = power_db[actual_idx]
        else:
            fundamental_freq = expected_freq
            fundamental_power_db = np.max(power_db)

        # 估计动态噪声阈值
        smoothed_noise, window_centers, exclude_mask = estimate_dynamic_noise_threshold(freqs, power_linear, fundamental_freq)

        # 使用harmonic_detection_system检测谐波
        noise_analysis = {
            'global_noise_floor_db': np.mean(smoothed_noise) if smoothed_noise is not None else -60,
            'noise_variation_db': np.max(smoothed_noise) - np.min(smoothed_noise) if smoothed_noise is not None else 0,
            'noise_fluctuation_features': {'fluctuation_stability_score': 0.5}
        }

        harmonic_analysis = detect_harmonics_for_segment(freqs, power_linear, fundamental_freq, noise_analysis)

        # 应用6种抖动幅度检测方法
        methods = [
            ("频谱阈值差值", method1_spectrum_threshold_difference),
            ("连续大幅度抖动", method2_continuous_large_amplitude_detection),
            ("频谱粗糙度", method3_spectral_roughness_analysis),
            ("局部方差分析", method4_local_variance_analysis),
            ("峰值密度分析", method5_peak_density_analysis),
            ("幅度跳跃检测", method6_amplitude_jump_detection)
        ]

        results = {}
        for method_name, method_func in methods:
            if method_name == "频谱阈值差值":
                has_fluctuation, detected_points, info = method_func(freqs, power_db, smoothed_noise, window_centers, exclude_mask)
            else:
                has_fluctuation, detected_points, info = method_func(freqs, power_db, exclude_mask)

            results[method_name] = {
                'has_fluctuation': has_fluctuation,
                'detected_points': detected_points,
                'info': info
            }

        return {
            'seg_idx': seg_idx,
            'expected_freq': expected_freq,
            'fundamental_freq': fundamental_freq,
            'fundamental_power_db': fundamental_power_db,
            'freqs': freqs,
            'power_db': power_db,
            'exclude_mask': exclude_mask,
            'smoothed_noise': smoothed_noise,
            'window_centers': window_centers,
            'harmonic_analysis': harmonic_analysis,
            'results': results
        }

    except Exception as e:
        print(f"❌ 段{seg_idx}分析失败: {e}")
        return None

def create_fluctuation_amplitude_visualization(segment_data, output_dir, filename=""):
    """
    创建抖动幅度检测可视化
    """

    if not segment_data:
        return None

    seg_idx = segment_data['seg_idx']
    expected_freq = segment_data['expected_freq']
    fundamental_freq = segment_data['fundamental_freq']
    fundamental_power_db = segment_data['fundamental_power_db']
    freqs = segment_data['freqs']
    power_db = segment_data['power_db']
    exclude_mask = segment_data['exclude_mask']
    smoothed_noise = segment_data['smoothed_noise']
    window_centers = segment_data['window_centers']
    harmonic_analysis = segment_data['harmonic_analysis']
    results = segment_data['results']

    # 创建图形 - 2x3布局显示6种方法
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    # 文件名
    display_filename = os.path.splitext(os.path.basename(filename))[0] if filename else "音频文件"
    fig.suptitle(f'{display_filename} - 段{seg_idx} ({expected_freq:.1f}Hz)\n频谱抖动幅度检测 (主频: {fundamental_power_db:.1f}dB)',
                 fontsize=16, fontweight='bold')

    method_names = list(results.keys())
    colors = ['red', 'orange', 'green', 'blue', 'purple', 'brown']

    for i, (method_name, color) in enumerate(zip(method_names, colors)):
        ax = axes[i]
        result = results[method_name]

        # 绘制基础频谱
        ax.plot(freqs, power_db, 'lightgray', linewidth=1, alpha=0.7, label='频谱')

        # 绘制动态噪声阈值（如果有）
        if smoothed_noise is not None and window_centers is not None:
            ax.plot(window_centers, smoothed_noise, 'cyan', linewidth=2, alpha=0.8, label='动态噪声阈值')

        # 标记主频
        ax.axvline(fundamental_freq, color='black', linestyle='-', linewidth=3,
                  alpha=0.8, label=f'主频: {fundamental_freq:.1f}Hz')

        # 标记检测到的谐波
        if harmonic_analysis:
            harmonic_freqs = [h['freq'] for h in harmonic_analysis]
            harmonic_powers_db = [h['power_db'] for h in harmonic_analysis]
            ax.scatter(harmonic_freqs, harmonic_powers_db, c='blue', s=30, alpha=0.6,
                      marker='^', label=f'谐波({len(harmonic_analysis)}个)')

        # 标记排除区域
        excluded_freqs = freqs[exclude_mask]
        excluded_powers = power_db[exclude_mask]
        if len(excluded_freqs) > 0:
            ax.scatter(excluded_freqs, excluded_powers, c='lightblue', s=1,
                      alpha=0.3, label='排除区域')

        # 标记检测到的抖动点
        detected_points = result['detected_points']
        if detected_points:
            det_freqs, det_powers = zip(*detected_points)
            ax.scatter(det_freqs, det_powers, c=color, s=50, alpha=0.8,
                      marker='x', linewidth=3, label=f'抖动点({len(detected_points)}个)')

            # 标注一些检测点的功率值
            for j, (freq, power) in enumerate(detected_points[:3]):  # 只标注前3个
                ax.annotate(f'{power:.0f}', (freq, power), xytext=(5, 5),
                           textcoords='offset points', fontsize=8, color=color)

        # 设置标题和标签
        has_fluctuation = result['has_fluctuation']
        fluctuation_status = "检测到抖动" if has_fluctuation else "无明显抖动"
        ax.set_title(f'{method_name} - {fluctuation_status}\n{result["info"]}', fontsize=10)
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率 (dB)')
        ax.set_xlim(100, 20000)
        ax.set_xscale('log')
        ax.set_ylim(-80, 40)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)

        # 设置边框颜色表示检测结果
        for spine in ax.spines.values():
            spine.set_color(color if has_fluctuation else 'gray')
            spine.set_linewidth(3 if has_fluctuation else 1)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, f"fluctuation_amplitude_segment_{seg_idx:02d}_{expected_freq:.0f}Hz.png")

    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()

    return output_path

def process_single_segment_fluctuation_amplitude(args):
    """
    处理单个段的抖动幅度检测（多进程包装函数）
    """
    seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim, output_dir, filename, total_segments = args

    try:
        print(f"[{seg_idx+1}/{total_segments}] 检测段{seg_idx}: {expected_freq:.1f}Hz")

        # 分析段
        segment_data = analyze_segment_fluctuation_amplitude(
            seg_idx, start_time, end_time, expected_freq, audio_data, sr, use_trim
        )

        if segment_data:
            # 生成可视化
            viz_path = create_fluctuation_amplitude_visualization(segment_data, output_dir, filename)

            if viz_path:
                # 统计检测结果
                results = segment_data['results']
                fluctuation_count = sum(1 for r in results.values() if r['has_fluctuation'])

                result = {
                    'success': True,
                    'seg_idx': seg_idx,
                    'viz_path': viz_path,
                    'expected_freq': expected_freq,
                    'fundamental_power_db': segment_data['fundamental_power_db'],
                    'harmonic_count': len(segment_data['harmonic_analysis']) if segment_data['harmonic_analysis'] else 0,
                    'fluctuation_methods': fluctuation_count,
                    'total_methods': len(results),
                    'results': results
                }

                print(f"  ✅ [{seg_idx+1}/{total_segments}] 成功: {os.path.basename(viz_path)}")
                print(f"  📊 抖动检测: {fluctuation_count}/{len(results)}种方法, 谐波: {result['harmonic_count']}个")

                return result
            else:
                print(f"  ❌ [{seg_idx+1}/{total_segments}] 可视化失败")
                return {'success': False, 'seg_idx': seg_idx, 'error': 'visualization_failed'}
        else:
            print(f"  ❌ [{seg_idx+1}/{total_segments}] 分析失败")
            return {'success': False, 'seg_idx': seg_idx, 'error': 'analysis_failed'}

    except Exception as e:
        print(f"  ❌ [{seg_idx+1}/{total_segments}] 处理异常: {e}")
        return {'success': False, 'seg_idx': seg_idx, 'error': str(e)}

def main():
    """
    主函数 - 分析琴身内部异物文件的频谱抖动幅度
    """

    print("🎯 琴身内部异物文件频谱抖动幅度检测")
    print("📝 使用6种方法检测频谱中抖动幅度很大的区域")
    print("📝 排除主频和谐波，去除开头结尾8%")
    print("="*80)

    # 查找琴身内部异物文件
    target_file = "test20250722/琴身内部异物1.1.wav"

    if not os.path.exists(target_file):
        print(f"❌ 未找到目标文件: {target_file}")
        return

    print(f"📁 找到目标文件: {target_file}")

    # 创建输出目录
    base_filename = os.path.splitext(os.path.basename(target_file))[0]
    safe_filename = ''.join(c for c in base_filename if c.isalnum() or c in '_-')
    output_dir = f"{safe_filename}_fluctuation_amplitude_detection"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    try:
        # 获取频段分割
        print("🔍 进行频段分割...")
        step_boundaries, freq_table, _ = split_freq_steps_optimized(
            target_file, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 分割完成，共{len(step_boundaries)}个频段")

        # 加载音频
        print("🔊 加载音频文件...")
        y, sr = librosa.load(target_file, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)

        print(f"✅ 音频加载完成: 采样率{sr}Hz, 时长{len(y)/sr:.2f}秒")

        # 确定进程数
        num_processes = min(cpu_count(), len(step_boundaries), 8)
        print(f"🚀 使用{num_processes}个进程并行处理{len(step_boundaries)}段")

        # 准备参数
        process_args = []
        for seg_idx in range(len(step_boundaries)):
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]

            args = (seg_idx, start_time, end_time, expected_freq, y, sr, True,
                   output_dir, target_file, len(step_boundaries))
            process_args.append(args)

        # 多进程处理
        start_time = time.time()
        print(f"\n⏱️  开始并行抖动幅度检测...")

        with Pool(processes=num_processes) as pool:
            results = pool.map(process_single_segment_fluctuation_amplitude, process_args)

        end_time = time.time()
        processing_time = end_time - start_time

        # 处理结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # 统计分析
        method_stats = {}
        method_names = ["频谱阈值差值", "连续大幅度抖动", "频谱粗糙度",
                       "局部方差分析", "峰值密度分析", "幅度跳跃检测"]

        for method_name in method_names:
            method_stats[method_name] = {
                'detected_segments': 0,
                'total_segments': len(successful_results)
            }

        for result in successful_results:
            for method_name in method_names:
                if result['results'][method_name]['has_fluctuation']:
                    method_stats[method_name]['detected_segments'] += 1

        # 生成汇总统计
        print("\n" + "="*80)
        print(f"📊 {base_filename}文件抖动幅度检测完成统计:")
        print(f"  ⏱️  总处理时间: {processing_time:.1f}秒")
        print(f"  🚀 平均每段: {processing_time/len(step_boundaries):.1f}秒")
        print(f"  ✅ 成功: {len(successful_results)}个段")
        print(f"  ❌ 失败: {len(failed_results)}个段")
        print(f"  📁 输出目录: {output_dir}")
        print(f"  📊 生成图片: {len(successful_results)}张")

        print(f"\n🔍 各方法检测结果统计:")
        for method_name, stats in method_stats.items():
            detected = stats['detected_segments']
            total = stats['total_segments']
            percentage = (detected / total * 100) if total > 0 else 0
            print(f"  📈 {method_name}: {detected}/{total} ({percentage:.1f}%)")

        # 综合分析
        multi_method_segments = []
        for result in successful_results:
            fluctuation_count = result['fluctuation_methods']
            if fluctuation_count >= 3:  # 3种以上方法检测到抖动
                multi_method_segments.append((result['seg_idx'], result['expected_freq'],
                                            fluctuation_count, result['fundamental_power_db']))

        print(f"\n🎯 重点关注段（≥3种方法检测到抖动）:")
        if multi_method_segments:
            for seg_idx, freq, count, main_power in multi_method_segments:
                print(f"  ⚠️  段{seg_idx} ({freq:.1f}Hz, 主频{main_power:.1f}dB): {count}/6种方法检测到抖动")
        else:
            print("  ✅ 无段被多种方法同时检测为有明显抖动")

        print("="*80)
        print("🎯 频谱抖动幅度检测分析完成！")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
