#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试阈值选择问题 - 可视化分析
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from final_audio_detection_system.freq_split_optimized import OptimizedFreqSplitter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def debug_threshold_selection_visual():
    """深度调试阈值选择问题"""
    print("🔍 深度调试阈值选择问题")
    print("="*60)
    
    # 创建分割器
    splitter = OptimizedFreqSplitter()
    
    # 测试音频路径
    audio_path = r"../test20250717/pos/sd卡/sd1_1.wav"
    
    print(f"测试文件: {audio_path}")
    
    # 读取测试音频
    y, sr = librosa.load(audio_path, sr=splitter.fs)
    if sr != splitter.fs:
        y = librosa.resample(y, orig_sr=sr, target_sr=splitter.fs)
    
    # 搜索参数
    search_start = 0.1
    search_end = 1.5
    corr_length = 1.0
    
    # 执行搜索
    search_start_sample = int(search_start * splitter.fs)
    search_end_sample = int(search_end * splitter.fs)
    corr_length_samples = int(corr_length * splitter.fs)
    
    search_end_sample = min(search_end_sample, len(y) - corr_length_samples)
    
    # 准备参考信号段
    initial_corr_length = min(corr_length_samples, int(0.5 * splitter.fs))
    reference_segment = splitter.reference_signal[:initial_corr_length]
    
    print(f"搜索参数:")
    print(f"  搜索窗口: {search_start:.2f}s - {search_end:.2f}s")
    print(f"  参考信号长度: {initial_corr_length} 样本 ({initial_corr_length/splitter.fs:.2f}秒)")
    
    # 计算相关性 - 使用与原算法相同的步长
    search_length = search_end_sample - search_start_sample
    correlations = []
    positions = []
    
    step_size = max(1, splitter.fs // 500)  # 2ms步长，与原始算法一致
    print(f"  步长: {step_size} 样本 ({step_size/splitter.fs*1000:.1f}ms)")
    
    for i in range(0, search_length - initial_corr_length, step_size):
        pos = search_start_sample + i
        test_segment = y[pos:pos + initial_corr_length]
        
        if len(test_segment) == len(reference_segment):
            correlation = splitter._normalized_cross_correlation(reference_segment, test_segment)
            correlations.append(correlation)
            positions.append(pos / splitter.fs)
    
    correlations = np.array(correlations)
    positions = np.array(positions)
    
    print(f"\n计算结果:")
    print(f"  计算了 {len(correlations)} 个相关性值")
    print(f"  时间范围: {positions[0]:.3f}s - {positions[-1]:.3f}s")
    print(f"  相关性范围: {np.min(correlations):.3f} - {np.max(correlations):.3f}")
    
    # 分析阈值选择逻辑
    max_correlation = np.max(correlations)
    max_idx = np.argmax(correlations)
    
    # 当前的阈值设置
    relative_threshold = max_correlation * 0.7  # 70%
    absolute_threshold = 0.4
    final_threshold = max(relative_threshold, absolute_threshold)
    
    print(f"\n阈值计算:")
    print(f"  最大相关性: {max_correlation:.3f} (位置: {positions[max_idx]:.3f}s)")
    print(f"  70%相对阈值: {relative_threshold:.3f}")
    print(f"  绝对阈值: {absolute_threshold:.3f}")
    print(f"  最终阈值: {final_threshold:.3f}")
    
    # 找到符合条件的点
    good_indices = np.where(correlations >= final_threshold)[0]
    
    print(f"\n符合条件的点分析:")
    print(f"  符合条件的点数: {len(good_indices)}")
    
    if len(good_indices) > 0:
        print(f"  所有符合条件的点:")
        for i, idx in enumerate(good_indices):
            print(f"    {i+1}. 索引={idx}, 时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.3f}")
        
        # 第一个符合条件的点
        first_good_idx = good_indices[0]
        first_good_pos = positions[first_good_idx]
        first_good_corr = correlations[first_good_idx]
        
        print(f"\n  ✅ 第一个符合条件的点:")
        print(f"    索引: {first_good_idx}")
        print(f"    时间: {first_good_pos:.3f}s")
        print(f"    相关性: {first_good_corr:.3f}")
        
        # 检查是否有更早的点接近阈值
        print(f"\n  检查更早的高相关性点:")
        early_high_corr = np.where((correlations >= final_threshold * 0.9) & 
                                  (positions < first_good_pos))[0]
        if len(early_high_corr) > 0:
            print(f"    发现 {len(early_high_corr)} 个更早的接近阈值的点:")
            for idx in early_high_corr[-5:]:  # 显示最后5个（最接近的）
                print(f"      时间={positions[idx]:.3f}s, 相关性={correlations[idx]:.3f} "
                      f"(阈值的{correlations[idx]/final_threshold*100:.1f}%)")
        else:
            print(f"    没有发现更早的接近阈值的点")
    else:
        print(f"  ❌ 没有符合条件的点！")
    
    # 创建详细的可视化分析
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle('阈值选择问题深度分析', fontsize=16, fontweight='bold')
    
    # 1. 完整相关性曲线
    ax1 = axes[0]
    ax1.plot(positions, correlations, 'b-', linewidth=1, alpha=0.8, label='相关性曲线')
    
    # 标记阈值线
    ax1.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, 
               label=f'最终阈值 ({final_threshold:.3f})')
    ax1.axhline(relative_threshold, color='purple', linestyle=':', alpha=0.8, 
               label=f'70%阈值 ({relative_threshold:.3f})')
    ax1.axhline(absolute_threshold, color='brown', linestyle=':', alpha=0.6, 
               label=f'绝对阈值 ({absolute_threshold:.3f})')
    
    # 标记最大相关性点
    ax1.plot(positions[max_idx], correlations[max_idx], 'go', markersize=10, 
            label=f'最大相关性 ({positions[max_idx]:.3f}s, {correlations[max_idx]:.3f})')
    
    # 标记符合条件的点
    if len(good_indices) > 0:
        ax1.plot(positions[good_indices], correlations[good_indices], 'ro', 
                markersize=6, alpha=0.7, label=f'符合条件的点 ({len(good_indices)}个)')
        
        # 特别标记第一个符合条件的点
        first_idx = good_indices[0]
        ax1.plot(positions[first_idx], correlations[first_idx], 'r*', 
                markersize=15, label=f'第一个符合条件 ({positions[first_idx]:.3f}s)')
        
        # 标记实际算法选择的点（如果不同）
        ax1.axvline(positions[first_idx], color='red', linestyle='-', linewidth=2, alpha=0.7,
                   label=f'算法选择位置')
    
    # 高亮显示符合条件的区域
    good_mask = correlations >= final_threshold
    if np.any(good_mask):
        ax1.fill_between(positions, 0, correlations, where=good_mask, 
                        alpha=0.2, color='green', label='符合条件区域')
    
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('相关性系数')
    ax1.set_title('完整相关性分析')
    ax1.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0.1, 0.6)  # 聚焦到关键区域
    
    # 2. 放大关键区域
    ax2 = axes[1]
    # 找到关键区域
    key_start = max(0.1, positions[0])
    key_end = min(0.5, positions[-1])
    key_mask = (positions >= key_start) & (positions <= key_end)
    
    ax2.plot(positions[key_mask], correlations[key_mask], 'b-', linewidth=2, label='相关性曲线')
    
    # 标记阈值线
    ax2.axhline(final_threshold, color='orange', linestyle='--', linewidth=2, 
               label=f'最终阈值 ({final_threshold:.3f})')
    
    # 标记所有峰值点
    from scipy.signal import find_peaks
    peaks, _ = find_peaks(correlations[key_mask], height=final_threshold*0.8, distance=5)
    if len(peaks) > 0:
        peak_positions = positions[key_mask][peaks]
        peak_correlations = correlations[key_mask][peaks]
        ax2.plot(peak_positions, peak_correlations, 'mo', markersize=8, 
                label=f'检测到的峰值 ({len(peaks)}个)')
        
        # 标注每个峰值
        for i, (pos, corr) in enumerate(zip(peak_positions, peak_correlations)):
            ax2.annotate(f'峰{i+1}\n{pos:.3f}s\n{corr:.3f}', 
                        xy=(pos, corr), xytext=(5, 10), 
                        textcoords='offset points', fontsize=8,
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 标记符合条件的点
    if len(good_indices) > 0:
        good_in_key = good_indices[np.isin(good_indices, np.where(key_mask)[0])]
        if len(good_in_key) > 0:
            ax2.plot(positions[good_in_key], correlations[good_in_key], 'ro', 
                    markersize=8, alpha=0.8, label=f'符合条件的点')
            
            # 标记第一个符合条件的点
            first_idx = good_indices[0]
            if first_idx in np.where(key_mask)[0]:
                ax2.plot(positions[first_idx], correlations[first_idx], 'r*', 
                        markersize=20, label=f'第一个符合条件')
    
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('相关性系数')
    ax2.set_title('关键区域放大分析 (0.1s - 0.5s)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 阈值选择逻辑验证
    ax3 = axes[2]
    
    # 绘制阈值选择过程
    threshold_steps = np.arange(0.3, 1.0, 0.01)
    selected_positions = []
    
    for thresh_ratio in threshold_steps:
        temp_threshold = max(max_correlation * thresh_ratio, absolute_threshold)
        temp_good = np.where(correlations >= temp_threshold)[0]
        if len(temp_good) > 0:
            selected_positions.append(positions[temp_good[0]])
        else:
            selected_positions.append(positions[max_idx])
    
    ax3.plot(threshold_steps * 100, selected_positions, 'g-', linewidth=2, 
            label='选择位置随阈值变化')
    ax3.axvline(70, color='orange', linestyle='--', linewidth=2, 
               label='当前70%阈值')
    ax3.axhline(positions[good_indices[0]] if len(good_indices) > 0 else positions[max_idx], 
               color='red', linestyle='-', linewidth=2, 
               label=f'当前选择位置')
    
    ax3.set_xlabel('阈值百分比 (%)')
    ax3.set_ylabel('选择的开始时间 (秒)')
    ax3.set_title('阈值敏感性分析')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('threshold_selection_debug.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 运行原始算法进行对比
    print(f"\n" + "="*60)
    print("🔄 运行原始算法进行对比")
    print("="*60)
    
    try:
        from freq_split_optimized import split_freq_steps_optimized
        step_bounds, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path,
            min_duration=153,
            plot=False,
            debug=True,
            search_window_start=search_start,
            search_window_end=search_end,
            correlation_length=corr_length
        )
        
        actual_start = alignment_info.get('start_offset', 0)
        actual_corr = alignment_info.get('correlation_score', 0)
        
        print(f"\n📊 对比结果:")
        print(f"  手动计算第一个符合条件的点: {first_good_pos:.3f}s (相关性: {first_good_corr:.3f})")
        print(f"  原始算法实际选择的点: {actual_start:.3f}s (相关性: {actual_corr:.3f})")
        print(f"  差异: {abs(actual_start - first_good_pos)*1000:.1f}ms")
        
        if abs(actual_start - first_good_pos) > 0.001:  # 1ms差异
            print(f"  ⚠️  存在差异！需要进一步调查原因")
        else:
            print(f"  ✅ 结果一致")
            
    except Exception as e:
        print(f"❌ 原始算法运行失败: {e}")
    
    return positions, correlations, final_threshold, good_indices

if __name__ == "__main__":
    positions, correlations, threshold, good_indices = debug_threshold_selection_visual()
