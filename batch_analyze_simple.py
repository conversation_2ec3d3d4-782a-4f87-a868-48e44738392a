#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版批量分析test20250717文件夹下的所有音频文件
避免Unicode编码问题
"""

import os
import glob
import subprocess
import time
from pathlib import Path

def find_all_wav_files(root_dir):
    """
    递归查找所有wav文件
    """
    wav_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.lower().endswith('.wav'):
                full_path = os.path.join(root, file)
                wav_files.append(full_path)
    return wav_files

def get_safe_filename(filename):
    """
    获取安全的文件名，避免特殊字符
    """
    # 移除或替换可能有问题的字符
    safe_name = filename.replace(' ', '_').replace('(', '').replace(')', '')
    return safe_name

def run_analysis_simple(wav_file):
    """
    运行单个音频文件的分析 - 简化版
    """
    # 使用相对路径
    rel_path = os.path.relpath(wav_file)
    
    cmd = [
        'python', 'universal_spectrum_analyzer.py',
        rel_path,
        '--mel-scale',
        '--show-diff', 
        '--workers', '8',
        '--freq-range', '100', '24000'
    ]
    
    print(f"Analyzing: {os.path.basename(wav_file)}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # 设置环境变量避免编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 运行分析
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='.', env=env)
        
        if result.returncode == 0:
            print(f"Success: {os.path.basename(wav_file)}")
            return True
        else:
            print(f"Failed: {result.stderr[:200]}...")  # 只显示前200字符
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def organize_results(output_root):
    """
    整理分析结果到统一目录结构
    """
    print("Organizing results...")
    
    # 查找所有生成的分析目录
    analysis_dirs = glob.glob("*_梅尔频谱分析")
    
    if not analysis_dirs:
        print("No analysis results found")
        return
    
    # 创建输出根目录
    os.makedirs(output_root, exist_ok=True)
    
    moved_count = 0
    for analysis_dir in analysis_dirs:
        try:
            # 获取原始音频文件名
            base_name = analysis_dir.replace('_梅尔频谱分析', '')
            
            # 查找对应的原始音频文件来确定目标路径
            original_files = glob.glob(f"test20250717/**/{base_name}.wav", recursive=True)
            
            if original_files:
                original_file = original_files[0]
                # 获取相对于test20250717的路径
                rel_path = os.path.relpath(original_file, 'test20250717')
                target_dir = os.path.join(output_root, os.path.dirname(rel_path))
                
                # 创建目标目录
                os.makedirs(target_dir, exist_ok=True)
                
                # 移动分析结果
                target_path = os.path.join(target_dir, analysis_dir)
                if os.path.exists(target_path):
                    import shutil
                    shutil.rmtree(target_path)
                
                import shutil
                shutil.move(analysis_dir, target_path)
                moved_count += 1
                print(f"Moved: {analysis_dir} -> {target_path}")
            else:
                print(f"Original file not found for: {analysis_dir}")
                
        except Exception as e:
            print(f"Error moving {analysis_dir}: {str(e)}")
    
    print(f"Organized {moved_count} analysis results")

def main():
    """
    主函数：简化版批量分析
    """
    print("Batch Analysis for test20250717")
    print("="*50)
    
    # 输入和输出目录
    input_root = "test20250717"
    output_root = "test20250717_梅尔频谱批量分析"
    
    # 检查输入目录
    if not os.path.exists(input_root):
        print(f"Input directory not found: {input_root}")
        return
    
    # 查找所有wav文件
    print("Searching for audio files...")
    wav_files = find_all_wav_files(input_root)
    
    if not wav_files:
        print("No wav files found")
        return
    
    print(f"Found {len(wav_files)} audio files")
    
    # 开始批量处理
    print("Starting batch analysis...")
    print("Parameters: --mel-scale --show-diff --workers 8 --freq-range 100 24000")
    
    start_time = time.time()
    successful_count = 0
    failed_count = 0
    
    for i, wav_file in enumerate(wav_files, 1):
        print(f"\n[{i}/{len(wav_files)}] " + "="*30)
        
        # 运行分析
        if run_analysis_simple(wav_file):
            successful_count += 1
        else:
            failed_count += 1
        
        # 显示进度
        elapsed = time.time() - start_time
        if i > 0:
            avg_time = elapsed / i
            remaining = (len(wav_files) - i) * avg_time
            print(f"Progress: {i}/{len(wav_files)}, Elapsed: {elapsed:.1f}s, Remaining: {remaining:.1f}s")
    
    # 整理结果
    print("\nOrganizing results...")
    organize_results(output_root)
    
    # 完成统计
    total_time = time.time() - start_time
    print(f"\n" + "="*50)
    print(f"Batch analysis completed!")
    print(f"Total files: {len(wav_files)}")
    print(f"Successful: {successful_count}")
    print(f"Failed: {failed_count}")
    print(f"Success rate: {successful_count/len(wav_files)*100:.1f}%")
    print(f"Total time: {total_time:.1f} seconds")
    print(f"Average per file: {total_time/len(wav_files):.1f} seconds")
    print(f"Results saved in: {output_root}")

if __name__ == "__main__":
    main()
