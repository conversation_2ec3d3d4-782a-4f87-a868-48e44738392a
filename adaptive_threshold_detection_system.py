#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应阈值检测系统
基于实际数据分布设置阈值
"""

import os
import sys
import numpy as np
import pandas as pd
import librosa
from scipy.signal import stft, find_peaks
from scipy import stats

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

class AdaptiveThresholdDetectionSystem:
    def __init__(self):
        self.fs = 48000
        
        # 基于实际数据分析的自适应阈值
        self.adaptive_thresholds = {
            # 谐波失真特征 - 基于正负样本分布设置
            'thd': 0.05,  # 5%
            'harmonic_3_ratio': 0.005,  # 0.5%
            
            # 时域特征 - 基于统计分布
            'td_skewness': 3.0,  # 3倍标准差
            'td_kurtosis': 8.0,  # 8倍标准差
            
            # 频域特征
            'fd_expected_freq_ratio': 0.3,  # 期望频率能量比例低于30%
            
            # 频谱特征
            'spec_bandwidth_mean': 1500,  # 1500Hz
            
            # 统计特征
            'stat_p50': 0.15,  # 中位数
            'stat_iqr': 0.3,   # 四分位距
            
            # 能量特征
            'energy_kurtosis': 5.0  # 能量峰度
        }
        
        # 文件级判定阈值（更敏感）
        self.file_level_thresholds = {
            'vertical_line_ratio': 0.01,    # 1%频段有竖线
            'anomaly_ratio': 0.05,          # 5%频段异常
            'feature_vote_ratio': 0.15      # 15%特征投票异常
        }
        
        print(f"🔧 自适应阈值检测系统初始化")
        print(f"📊 特征阈值: {len(self.adaptive_thresholds)}个")
        print(f"📊 文件级阈值: {self.file_level_thresholds}")
    
    def extract_segment_features(self, audio_path, segment_idx, start_time, end_time, expected_freq):
        """提取单个频段的特征（复用之前的代码）"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.fs)
            if sr != self.fs:
                y = librosa.resample(y, orig_sr=sr, target_sr=self.fs)
            
            # 提取频段音频
            start_sample = int(start_time * self.fs)
            end_sample = int(end_time * self.fs)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                return None
            
            # 标准化
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            features = {}
            
            # 1. 竖线检测特征
            features.update(self._extract_vertical_line_features(segment_audio))
            
            # 2. 其他特征
            features.update(self._extract_other_features(segment_audio, expected_freq))
            
            return features
            
        except Exception as e:
            print(f"提取频段{segment_idx}特征失败: {e}")
            return None
    
    def _extract_vertical_line_features(self, audio):
        """竖线检测特征"""
        features = {}
        
        try:
            # STFT分析
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            
            # 频率范围筛选
            freq_mask = (f >= 100) & (f <= 20000)
            f_filtered = f[freq_mask]
            Zxx_filtered = Zxx[freq_mask, :]
            
            power_spectrum = np.abs(Zxx_filtered) ** 2
            power_db = 10 * np.log10(power_spectrum + 1e-12)
            
            # 总能量变化
            total_energy = np.sum(power_db, axis=0)
            energy_mean = np.mean(total_energy)
            energy_std = np.std(total_energy)
            
            # 峰值检测
            min_height = energy_mean + 0.5 * energy_std
            min_prominence = 0.3 * energy_std
            
            peaks, properties = find_peaks(total_energy, 
                                         height=min_height,
                                         distance=2,
                                         prominence=min_prominence)
            
            # 竖线检测
            vertical_lines = 0
            max_line_strength = 0
            
            for peak_idx in peaks:
                if peak_idx < len(t):
                    peak_spectrum = power_db[:, peak_idx]
                    threshold = np.percentile(peak_spectrum, 70)
                    high_energy_mask = peak_spectrum > threshold
                    high_energy_indices = np.where(high_energy_mask)[0]
                    
                    if len(high_energy_indices) > 0:
                        # 频率跨度
                        freq_span = f_filtered[high_energy_indices[-1]] - f_filtered[high_energy_indices[0]]
                        
                        # 频率比例
                        freq_ratio = len(high_energy_indices) / len(f_filtered)
                        
                        # 线强度
                        high_energy_power = peak_spectrum[high_energy_indices]
                        background_power = np.delete(peak_spectrum, high_energy_indices)
                        if len(background_power) > 0:
                            line_strength = np.mean(high_energy_power) / (np.mean(background_power) + 1e-12)
                        else:
                            line_strength = 1.0
                        
                        max_line_strength = max(max_line_strength, line_strength)
                        
                        # 竖线判定
                        if line_strength >= 2.0 and freq_span >= 1000 and freq_ratio >= 0.1:
                            vertical_lines += 1
            
            features['vl_count'] = vertical_lines
            features['vl_max_strength'] = max_line_strength
            
        except Exception as e:
            features.update({
                'vl_count': 0,
                'vl_max_strength': 0
            })
        
        return features
    
    def _extract_other_features(self, audio, expected_freq):
        """提取其他特征"""
        features = {}
        
        try:
            # 谐波失真特征
            fft = np.fft.fft(audio)
            freqs = np.fft.fftfreq(len(audio), 1/self.fs)
            magnitude = np.abs(fft)
            
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            # 基频和谐波
            fundamental_tolerance = 20
            fundamental_mask = np.abs(positive_freqs - expected_freq) <= fundamental_tolerance
            if np.any(fundamental_mask):
                fundamental_energy = np.max(positive_magnitude[fundamental_mask])**2
            else:
                fundamental_energy = 0
            
            # 3次谐波
            harmonic_3_freq = expected_freq * 3
            if harmonic_3_freq < self.fs / 2:
                harmonic_3_mask = np.abs(positive_freqs - harmonic_3_freq) <= fundamental_tolerance
                if np.any(harmonic_3_mask):
                    harmonic_3_energy = np.max(positive_magnitude[harmonic_3_mask])**2
                else:
                    harmonic_3_energy = 0
            else:
                harmonic_3_energy = 0
            
            # 所有谐波
            harmonic_energies = []
            for harmonic in range(2, 6):
                harmonic_freq = expected_freq * harmonic
                if harmonic_freq < self.fs / 2:
                    harmonic_mask = np.abs(positive_freqs - harmonic_freq) <= fundamental_tolerance
                    if np.any(harmonic_mask):
                        harmonic_energy = np.max(positive_magnitude[harmonic_mask])**2
                        harmonic_energies.append(harmonic_energy)
                    else:
                        harmonic_energies.append(0)
            
            total_harmonic_energy = sum(harmonic_energies)
            if fundamental_energy > 0:
                features['thd'] = np.sqrt(total_harmonic_energy / fundamental_energy)
                features['harmonic_3_ratio'] = harmonic_3_energy / fundamental_energy
            else:
                features['thd'] = 0
                features['harmonic_3_ratio'] = 0
            
            # 时域特征
            features['td_skewness'] = stats.skew(audio)
            features['td_kurtosis'] = stats.kurtosis(audio)
            
            # 频域特征
            features['fd_total_energy'] = np.sum(positive_magnitude**2)
            freq_tolerance = 50
            freq_mask = np.abs(positive_freqs - expected_freq) <= freq_tolerance
            if np.any(freq_mask):
                expected_freq_energy = np.sum(positive_magnitude[freq_mask]**2)
                features['fd_expected_freq_ratio'] = expected_freq_energy / features['fd_total_energy']
            else:
                features['fd_expected_freq_ratio'] = 0
            
            # 频谱特征
            f, t, Zxx = stft(audio, self.fs, nperseg=1024, noverlap=512)
            magnitude_stft = np.abs(Zxx)
            power = magnitude_stft**2
            
            freq_weighted_sum = np.sum(f[:, np.newaxis] * power, axis=0)
            total_power = np.sum(power, axis=0)
            spectral_centroid = freq_weighted_sum / (total_power + 1e-12)
            
            freq_diff = f[:, np.newaxis] - spectral_centroid[np.newaxis, :]
            spectral_bandwidth = np.sqrt(np.sum((freq_diff**2) * power, axis=0) / (total_power + 1e-12))
            features['spec_bandwidth_mean'] = np.mean(spectral_bandwidth)
            
            # 统计特征
            features['stat_p50'] = np.percentile(audio, 50)
            features['stat_iqr'] = np.percentile(audio, 75) - np.percentile(audio, 25)
            
            # 能量特征
            frame_length = 1024
            hop_length = 512
            frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
            frame_energy = np.sum(frames**2, axis=0)
            features['energy_kurtosis'] = stats.kurtosis(frame_energy) if len(frame_energy) > 0 else 0
            
        except Exception as e:
            # 设置默认值
            default_features = {
                'thd': 0, 'harmonic_3_ratio': 0, 'td_skewness': 0, 'td_kurtosis': 0,
                'fd_total_energy': 0, 'fd_expected_freq_ratio': 0, 'spec_bandwidth_mean': 0,
                'stat_p50': 0, 'stat_iqr': 0, 'energy_kurtosis': 0
            }
            features.update(default_features)
        
        return features
    
    def detect_audio_file(self, audio_path, debug=False):
        """检测单个音频文件"""
        try:
            if debug:
                print(f"🔍 检测文件: {os.path.basename(audio_path)}")
            
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )
            
            segment_results = []
            anomaly_segments = 0
            vertical_line_segments = 0
            total_segments = len(step_boundaries)
            
            # 检测每个频段
            for seg_idx, ((start_time, end_time), expected_freq) in enumerate(zip(step_boundaries, freq_table)):
                features = self.extract_segment_features(audio_path, seg_idx, start_time, end_time, expected_freq)
                
                if features is None:
                    continue
                
                # 1. 竖线检测
                vl_anomaly = features['vl_count'] > 0 or features['vl_max_strength'] >= 2.0
                if vl_anomaly:
                    vertical_line_segments += 1
                
                # 2. 其他特征检测（使用自适应阈值）
                other_anomaly = self._detect_other_anomalies_adaptive(features)
                
                # 3. 综合判定
                is_segment_anomaly = vl_anomaly or other_anomaly
                
                if is_segment_anomaly:
                    anomaly_segments += 1
                
                segment_results.append({
                    'segment_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'vl_anomaly': vl_anomaly,
                    'other_anomaly': other_anomaly,
                    'is_anomaly': is_segment_anomaly,
                    'features': features
                })
            
            # 文件级判定（使用自适应阈值）
            if total_segments > 0:
                file_anomaly_ratio = anomaly_segments / total_segments
                vl_ratio = vertical_line_segments / total_segments
                
                # 自适应判定逻辑
                predicted_label = 'neg' if (
                    vl_ratio >= self.file_level_thresholds['vertical_line_ratio'] or 
                    file_anomaly_ratio >= self.file_level_thresholds['anomaly_ratio']
                ) else 'pos'
            else:
                file_anomaly_ratio = 0
                vl_ratio = 0
                predicted_label = 'unknown'
            
            return {
                'status': 'success',
                'predicted_label': predicted_label,
                'file_anomaly_ratio': file_anomaly_ratio,
                'vertical_line_ratio': vl_ratio,
                'anomaly_segments': anomaly_segments,
                'vertical_line_segments': vertical_line_segments,
                'total_segments': total_segments,
                'segment_results': segment_results
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _detect_other_anomalies_adaptive(self, features):
        """使用自适应阈值检测其他特征异常"""
        try:
            anomaly_votes = 0
            total_votes = 0
            
            # 谐波失真检测
            if features['thd'] > self.adaptive_thresholds['thd']:
                anomaly_votes += 1
            total_votes += 1
            
            if features['harmonic_3_ratio'] > self.adaptive_thresholds['harmonic_3_ratio']:
                anomaly_votes += 1
            total_votes += 1
            
            # 时域特征检测
            if abs(features['td_skewness']) > self.adaptive_thresholds['td_skewness']:
                anomaly_votes += 1
            total_votes += 1
            
            if abs(features['td_kurtosis']) > self.adaptive_thresholds['td_kurtosis']:
                anomaly_votes += 1
            total_votes += 1
            
            # 频域特征检测
            if features['fd_expected_freq_ratio'] < self.adaptive_thresholds['fd_expected_freq_ratio']:
                anomaly_votes += 1
            total_votes += 1
            
            # 频谱特征检测
            if features['spec_bandwidth_mean'] > self.adaptive_thresholds['spec_bandwidth_mean']:
                anomaly_votes += 1
            total_votes += 1
            
            # 统计特征检测
            if abs(features['stat_p50']) > self.adaptive_thresholds['stat_p50']:
                anomaly_votes += 1
            total_votes += 1
            
            if features['stat_iqr'] > self.adaptive_thresholds['stat_iqr']:
                anomaly_votes += 1
            total_votes += 1
            
            # 能量特征检测
            if abs(features['energy_kurtosis']) > self.adaptive_thresholds['energy_kurtosis']:
                anomaly_votes += 1
            total_votes += 1
            
            # 使用自适应投票阈值
            return anomaly_votes / total_votes >= self.file_level_thresholds['feature_vote_ratio']
            
        except Exception as e:
            return False

def validate_adaptive_system():
    """验证自适应阈值检测系统"""
    print("🔍 验证自适应阈值检测系统")
    print("="*70)

    detector = AdaptiveThresholdDetectionSystem()

    # 定义测试文件夹
    test_folders = {
        'pos': [
            r"../test20250717/pos/sd卡",
            r"../test20250717/pos/完美",
            r"../test20250717/pos/转接板",
            r"../test20250717/pos/铁网"
        ],
        'neg': [
            r"../test20250717/neg"
        ]
    }

    validation_results = []

    # 测试所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue

            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            folder_name = os.path.basename(folder)

            print(f"\n📁 测试文件夹: {folder_name} ({true_label}) - {len(wav_files)}个文件")
            print("-" * 60)

            for i, audio_path in enumerate(wav_files):
                filename = os.path.basename(audio_path)

                print(f"  🎵 [{i+1}/{len(wav_files)}] {filename}")

                # 检测
                result = detector.detect_audio_file(audio_path, debug=False)

                if result['status'] == 'success':
                    predicted_label = result['predicted_label']
                    file_anomaly_ratio = result['file_anomaly_ratio']
                    vl_ratio = result['vertical_line_ratio']
                    anomaly_segments = result['anomaly_segments']
                    vl_segments = result['vertical_line_segments']
                    total_segments = result['total_segments']

                    # 判定正确性
                    is_correct = predicted_label == true_label
                    correctness_icon = "✅" if is_correct else "❌"

                    # 显示结果
                    print(f"     {correctness_icon} 预测: {predicted_label}, 实际: {true_label}")
                    print(f"        异常频段: {anomaly_segments}/{total_segments} ({file_anomaly_ratio:.3f})")
                    print(f"        竖线频段: {vl_segments}/{total_segments} ({vl_ratio:.3f})")

                    # 保存结果
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'is_correct': is_correct,
                        'file_anomaly_ratio': file_anomaly_ratio,
                        'vertical_line_ratio': vl_ratio,
                        'anomaly_segments': anomaly_segments,
                        'vertical_line_segments': vl_segments,
                        'total_segments': total_segments,
                        'status': 'success'
                    })

                else:
                    print(f"     ❌ 检测失败: {result['error']}")
                    validation_results.append({
                        'folder': folder_name,
                        'filename': filename,
                        'true_label': true_label,
                        'predicted_label': 'failed',
                        'is_correct': False,
                        'status': 'failed',
                        'error': result['error']
                    })

    # 生成验证报告
    generate_adaptive_validation_report(validation_results)

    return validation_results

def generate_adaptive_validation_report(results):
    """生成自适应验证报告"""
    print(f"\n" + "="*70)
    print(f"📊 自适应阈值检测系统验证报告")
    print("="*70)

    df = pd.DataFrame(results)

    # 总体统计
    total_files = len(df)
    success_files = len(df[df['status'] == 'success'])

    print(f"\n📊 总体统计:")
    print(f"  总文件数: {total_files}")
    print(f"  成功检测: {success_files} ({success_files/total_files*100:.1f}%)")

    # 准确率统计
    success_data = df[df['status'] == 'success']

    if len(success_data) > 0:
        correct_predictions = success_data[success_data['is_correct'] == True]
        accuracy = len(correct_predictions) / len(success_data)

        print(f"\n📊 准确率统计:")
        print(f"  总体准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")

        # 按类别统计
        for label in ['pos', 'neg']:
            label_samples = success_data[success_data['true_label'] == label]
            if len(label_samples) > 0:
                label_correct = label_samples[label_samples['is_correct'] == True]
                label_accuracy = len(label_correct) / len(label_samples)
                print(f"  {label}样本准确率: {label_accuracy:.3f} ({len(label_correct)}/{len(label_samples)})")

    # 保存验证结果
    df.to_csv('adaptive_detection_validation.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 验证结果已保存: adaptive_detection_validation.csv")

if __name__ == "__main__":
    validate_adaptive_system()
