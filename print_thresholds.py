#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打印93个频段的谐波数量阈值
"""

from harmonic_anomaly_detector import HarmonicAnomalyDetector
import numpy as np

def print_thresholds():
    """打印阈值"""
    
    detector = HarmonicAnomalyDetector()
    
    print("🎯 93个频段谐波数量阈值")
    print("="*60)
    print("基于pos文件夹47个正常样本的各段最大值")
    print("="*60)
    print()
    
    # 计算频率（基于12平均律）
    freqs = []
    for i in range(93):
        freq = 100 * (2**(i/12))
        freqs.append(freq)
    
    print("段号  频率(Hz)   阈值  |  段号  频率(Hz)   阈值  |  段号  频率(Hz)   阈值")
    print("-"*75)
    
    # 分3列显示
    for i in range(0, 93, 3):
        line = ""
        for j in range(3):
            if i+j < 93:
                seg_idx = i+j
                freq = freqs[seg_idx]
                threshold = detector.segment_thresholds[seg_idx]
                line += f"{seg_idx:3d}  {freq:7.0f}Hz   {threshold:2d}个"
                if j < 2 and i+j+1 < 93:
                    line += "  |  "
        print(line)
    
    print()
    print("📊 阈值统计:")
    print(f"  最大阈值: {max(detector.segment_thresholds)}个 (段{detector.segment_thresholds.index(max(detector.segment_thresholds))})")
    print(f"  最小阈值: {min(detector.segment_thresholds)}个")
    print(f"  平均阈值: {np.mean(detector.segment_thresholds):.1f}个")
    print(f"  阈值>10的段数: {sum(1 for t in detector.segment_thresholds if t > 10)}个")
    print(f"  阈值=1的段数: {sum(1 for t in detector.segment_thresholds if t == 1)}个")
    
    print()
    print("🎵 频率段分析:")
    
    # 低频段 (≤1kHz)
    low_freq_indices = [i for i, f in enumerate(freqs) if f <= 1000]
    low_freq_thresholds = [detector.segment_thresholds[i] for i in low_freq_indices]
    print(f"  低频段 (≤1kHz): 段0-{max(low_freq_indices)}, 平均阈值{np.mean(low_freq_thresholds):.1f}个")
    
    # 中频段 (1-5kHz)
    mid_freq_indices = [i for i, f in enumerate(freqs) if 1000 < f <= 5000]
    mid_freq_thresholds = [detector.segment_thresholds[i] for i in mid_freq_indices]
    print(f"  中频段 (1-5kHz): 段{min(mid_freq_indices)}-{max(mid_freq_indices)}, 平均阈值{np.mean(mid_freq_thresholds):.1f}个")
    
    # 高频段 (>5kHz)
    high_freq_indices = [i for i, f in enumerate(freqs) if f > 5000]
    high_freq_thresholds = [detector.segment_thresholds[i] for i in high_freq_indices]
    print(f"  高频段 (>5kHz): 段{min(high_freq_indices)}-{max(high_freq_indices)}, 平均阈值{np.mean(high_freq_thresholds):.1f}个")
    
    print()
    print("🔍 特殊阈值段:")
    
    # 找出阈值最高的段
    max_threshold = max(detector.segment_thresholds)
    max_indices = [i for i, t in enumerate(detector.segment_thresholds) if t == max_threshold]
    for idx in max_indices:
        print(f"  段{idx} ({freqs[idx]:.0f}Hz): {max_threshold}个 (最高)")
    
    # 找出阈值>10的段
    high_threshold_indices = [i for i, t in enumerate(detector.segment_thresholds) if t > 10]
    print(f"  阈值>10的段: {high_threshold_indices}")
    
    print()
    print("📋 完整阈值列表:")
    print("段号: 阈值")
    for i, threshold in enumerate(detector.segment_thresholds):
        if i % 10 == 0:
            print()
        print(f"{i:2d}:{threshold:2d}", end="  ")
    print()

if __name__ == "__main__":
    print_thresholds()
