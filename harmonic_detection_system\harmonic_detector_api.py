#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
谐波异常检测器API
规范化的外部调用接口
"""

import os
import sys
import numpy as np
import librosa
from scipy.signal import savgol_filter

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.dirname(current_dir))

try:
    from freq_split_optimized import split_freq_steps_optimized
except ImportError:
    print("警告: 无法导入freq_split_optimized，请确保文件在正确路径")

# 从pos_files_range_analysis.py复制的完全相同的算法
def calculate_noise_fluctuation_features(noise_levels, freq_centers):
    """计算噪声波动特征"""
    
    if len(noise_levels) < 3:
        return {
            'fluctuation_std': 0.0,
            'fluctuation_range': 0.0,
            'fluctuation_coefficient': 0.0,
            'fluctuation_stability_score': 1.0
        }
    
    noise_array = np.array(noise_levels)
    
    # 1. 标准差 - 衡量整体波动程度
    fluctuation_std = np.std(noise_array)
    
    # 2. 范围 - 最大最小值差
    fluctuation_range = np.max(noise_array) - np.min(noise_array)
    
    # 3. 变异系数 - 标准化的波动程度
    mean_noise = np.mean(noise_array)
    fluctuation_coefficient = fluctuation_std / abs(mean_noise) if abs(mean_noise) > 1e-6 else 0.0
    
    # 4. 稳定性评分 - 综合评估 (0-1, 1表示最稳定)
    std_score = 1.0 / (1.0 + fluctuation_std / 10.0)
    coeff_score = 1.0 / (1.0 + fluctuation_coefficient * 100.0)
    fluctuation_stability_score = (std_score + coeff_score) / 2.0
    
    return {
        'fluctuation_std': fluctuation_std,
        'fluctuation_range': fluctuation_range,
        'fluctuation_coefficient': fluctuation_coefficient,
        'fluctuation_stability_score': fluctuation_stability_score
    }

def estimate_dynamic_noise_for_segment(freqs, power, fundamental_freq):
    """为单个频段估计动态噪声"""
    
    # 滑动窗口参数
    window_size = 200  # 200个频率bin
    step_size = 50     # 50个bin步长
    
    # 创建排除掩码 - 排除主频和谐波位置
    exclude_mask = np.zeros(len(freqs), dtype=bool)
    
    # 排除主频±5Hz
    main_exclude = (freqs >= fundamental_freq - 5) & (freqs <= fundamental_freq + 5)
    exclude_mask |= main_exclude
    
    # 排除前10个谐波位置±5Hz
    for order in range(2, 11):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq <= freqs[-1]:
            harm_exclude = (freqs >= harmonic_freq - 5) & (freqs <= harmonic_freq + 5)
            exclude_mask |= harm_exclude
    
    # 滑动窗口噪声估计
    local_noise_levels = []
    window_centers = []
    
    for start_idx in range(0, len(freqs) - window_size, step_size):
        end_idx = start_idx + window_size
        window_mask = np.zeros(len(freqs), dtype=bool)
        window_mask[start_idx:end_idx] = True
        
        # 在窗口内排除信号区域
        noise_mask = window_mask & ~exclude_mask
        
        if np.sum(noise_mask) > 10:  # 至少10个样本
            window_noise_powers = power[noise_mask]
            power_db_window = 10 * np.log10(window_noise_powers + 1e-12)
            local_noise = np.percentile(power_db_window, 25)  # 25th percentile
            
            local_noise_levels.append(local_noise)
            window_centers.append(freqs[start_idx + window_size // 2])
    
    if len(local_noise_levels) == 0:
        return None
    
    # 平滑噪声曲线
    if len(local_noise_levels) > 5:
        try:
            smoothed_noise = savgol_filter(local_noise_levels, 
                                         min(len(local_noise_levels), 11), 3)
        except:
            smoothed_noise = local_noise_levels
    else:
        smoothed_noise = local_noise_levels
    
    # 全局噪声统计
    global_noise_floor_db = np.percentile(smoothed_noise, 20)
    noise_variation_db = np.max(smoothed_noise) - np.min(smoothed_noise)
    
    # 计算噪声波动特征
    noise_fluctuation_features = calculate_noise_fluctuation_features(smoothed_noise, window_centers)
    
    return {
        'global_noise_floor_db': global_noise_floor_db,
        'noise_variation_db': noise_variation_db,
        'noise_fluctuation_features': noise_fluctuation_features
    }

def detect_harmonics_for_segment(freqs, power, fundamental_freq, noise_analysis):
    """为单个频段检测谐波"""
    
    if not noise_analysis:
        return []
    
    fundamental_power_db = 10 * np.log10(np.max(power) + 1e-12)
    global_noise_floor_db = noise_analysis['global_noise_floor_db']
    noise_variation_db = noise_analysis['noise_variation_db']
    stability_score = noise_analysis['noise_fluctuation_features']['fluctuation_stability_score']
    
    detected_harmonics = []
    
    # 根据噪声变化和稳定性调整SNR阈值
    if noise_variation_db > 10:
        base_snr_threshold = 8.0
    elif noise_variation_db > 5:
        base_snr_threshold = 10.0
    else:
        base_snr_threshold = 12.0
    
    # 根据稳定性调整
    if stability_score > 0.8:
        stability_adjustment = 1.0
    elif stability_score > 0.6:
        stability_adjustment = 0.0
    else:
        stability_adjustment = -1.0
    
    base_snr_threshold += stability_adjustment
    
    for order in range(2, 25):  # 检测2-24次谐波
        expected_harmonic_freq = fundamental_freq * order
        
        if expected_harmonic_freq >= freqs[-1]:
            break
        
        # 搜索带宽
        if expected_harmonic_freq <= 1000:
            search_bandwidth = 5.0
        elif expected_harmonic_freq <= 5000:
            search_bandwidth = 10.0
        else:
            search_bandwidth = 15.0
        
        search_mask = (freqs >= expected_harmonic_freq - search_bandwidth) & \
                     (freqs <= expected_harmonic_freq + search_bandwidth)
        
        if not np.any(search_mask):
            continue
        
        search_indices = np.where(search_mask)[0]
        search_powers = power[search_mask]
        
        # 找到最大功率点
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        actual_freq = freqs[actual_idx]
        harmonic_power = power[actual_idx]
        harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
        
        # 计算SNR
        global_snr_db = harmonic_power_db - global_noise_floor_db
        relative_power_db = harmonic_power_db - fundamental_power_db
        freq_error = actual_freq - expected_harmonic_freq
        
        # 频率相关的SNR阈值调整
        if expected_harmonic_freq <= 1000:
            freq_adjustment = 0
        elif expected_harmonic_freq <= 5000:
            freq_adjustment = 1.0
        elif expected_harmonic_freq <= 10000:
            freq_adjustment = 2.0
        else:
            freq_adjustment = 3.0
        
        adjusted_snr_threshold = base_snr_threshold + freq_adjustment
        
        # 谐波判断条件
        conditions = {
            'global_snr_sufficient': global_snr_db >= adjusted_snr_threshold,
            'relative_power_ok': relative_power_db >= -50.0,
            'frequency_accurate': abs(freq_error) <= search_bandwidth * 0.8
        }
        
        if all(conditions.values()):
            detected_harmonics.append({
                'order': order,
                'freq': actual_freq,
                'power_db': harmonic_power_db,
                'global_snr_db': global_snr_db
            })
    
    return detected_harmonics

class HarmonicDetectorAPI:
    """谐波异常检测器API类"""
    
    def __init__(self, anomaly_threshold=2):
        """
        初始化检测器
        
        Args:
            anomaly_threshold (int): 异常段个数阈值，默认为2
        """
        self.anomaly_threshold = anomaly_threshold
        self.segment_thresholds = self._load_pos_thresholds()
        
    def _load_pos_thresholds(self):
        """加载pos文件夹分析得到的阈值"""
        # 基于pos文件夹47个正常样本分析得到的真实最大值
        thresholds = [
            15, 12, 11, 11,  9, 11, 11, 10, 10,  8,  # 段0-9
            10,  9, 14, 13, 17, 12, 10,  8,  8,  6,  # 段10-19
             7,  7,  6,  5,  6,  7,  7,  5,  5,  6,  # 段20-29
             5,  4,  4,  3,  7,  9, 13,  7,  5,  5,  # 段30-39
             6,  8,  7,  4,  5,  5,  5,  4,  2,  2,  # 段40-49
             2,  2,  2,  2,  2,  2,  1,  2,  1,  2,  # 段50-59
             2,  2,  1,  2,  2,  2,  2,  2,  2,  2,  # 段60-69
             2,  2,  2,  1,  1,  1,  1,  1,  1,  1,  # 段70-79
             0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  # 段80-89
             0,  0,  0                               # 段90-92
        ]
        
        return thresholds

    def detect(self, audio_path):
        """
        检测单个音频文件的谐波异常

        Args:
            audio_path (str): 音频文件路径

        Returns:
            int: 检测结果，1表示正常，0表示异常

        Raises:
            FileNotFoundError: 音频文件不存在
            ValueError: 音频文件格式不支持或损坏
            Exception: 其他检测过程中的错误
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")

            # 分析音频文件
            analysis_result = self._analyze_audio_file(audio_path)
            if not analysis_result:
                raise ValueError(f"音频文件分析失败: {audio_path}")

            # 计算异常段个数
            anomaly_count = self._count_anomaly_segments(analysis_result['segments'])

            # 判断是否异常：异常段个数 >= 阈值 → 异常(0)，否则正常(1)
            if anomaly_count >= self.anomaly_threshold:
                return 0  # 异常
            else:
                return 1  # 正常

        except Exception as e:
            raise Exception(f"检测过程出错: {str(e)}")

    def detect_with_details(self, audio_path):
        """
        检测单个音频文件的谐波异常，返回详细信息

        Args:
            audio_path (str): 音频文件路径

        Returns:
            dict: 详细检测结果
            {
                'result': int,           # 1=正常, 0=异常
                'anomaly_count': int,    # 异常段个数
                'threshold': int,        # 异常段阈值
                'total_segments': int,   # 总段数
                'anomaly_ratio': float,  # 异常段比例
                'anomaly_segments': list # 异常段详情
            }
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")

            # 分析音频文件
            analysis_result = self._analyze_audio_file(audio_path)
            if not analysis_result:
                raise ValueError(f"音频文件分析失败: {audio_path}")

            # 计算异常段
            segments = analysis_result['segments']
            anomaly_segments = self._get_anomaly_segments(segments)
            anomaly_count = len(anomaly_segments)

            # 计算统计信息
            total_segments = len(segments)
            anomaly_ratio = anomaly_count / total_segments if total_segments > 0 else 0

            # 判断结果
            result = 1 if anomaly_count < self.anomaly_threshold else 0

            return {
                'result': result,
                'anomaly_count': anomaly_count,
                'threshold': self.anomaly_threshold,
                'total_segments': total_segments,
                'anomaly_ratio': anomaly_ratio,
                'anomaly_segments': anomaly_segments
            }

        except Exception as e:
            raise Exception(f"检测过程出错: {str(e)}")

    def _analyze_audio_file(self, audio_path):
        """分析音频文件的93段谐波"""
        try:
            # 获取频段分割
            step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
                audio_path, start_freq=100, stop_freq=20000, octave=12,
                min_cycles=10, min_duration=153, fs=48000,
                search_window_start=0.1, search_window_end=1.5,
                correlation_length=1.0, plot=False, debug=False
            )

            # 加载音频
            y, sr = librosa.load(audio_path, sr=48000)
            if sr != 48000:
                y = librosa.resample(y, orig_sr=sr, target_sr=48000)

            segments_data = []

            # 分析所有段
            for seg_idx in range(len(step_boundaries)):
                start_time, end_time = step_boundaries[seg_idx]
                expected_freq = freq_table[seg_idx]

                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)
                segment_audio = y[start_sample:end_sample]

                if len(segment_audio) == 0:
                    segments_data.append({
                        'seg_idx': seg_idx,
                        'expected_freq': expected_freq,
                        'harmonic_count': 0
                    })
                    continue

                # 标准化
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio))

                # 高分辨率FFT分析
                fft_size = 131072  # 128k点FFT
                if len(segment_audio) < fft_size:
                    segment_audio = np.pad(segment_audio, (0, fft_size - len(segment_audio)), 'constant')
                else:
                    segment_audio = segment_audio[:fft_size]

                # 应用窗函数
                window = np.hanning(len(segment_audio))
                segment_audio = segment_audio * window

                # FFT
                fft = np.fft.fft(segment_audio)
                freqs_fft = np.fft.fftfreq(fft_size, 1/sr)
                magnitude = np.abs(fft)
                power = magnitude ** 2

                # 只取正频率
                positive_freqs = freqs_fft[:fft_size//2]
                positive_power = power[:fft_size//2]

                # 限制显示范围到20kHz
                freq_mask = positive_freqs <= 20000
                display_freqs = positive_freqs[freq_mask]
                display_power = positive_power[freq_mask]

                # 找主频
                search_bandwidth = 2.0
                search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                             (display_freqs <= expected_freq + search_bandwidth)

                if np.any(search_mask):
                    search_indices = np.where(search_mask)[0]
                    search_powers = display_power[search_mask]
                    max_power_idx = np.argmax(search_powers)
                    actual_idx = search_indices[max_power_idx]
                    fundamental_freq = display_freqs[actual_idx]
                else:
                    fundamental_freq = expected_freq

                # 动态噪声分析
                noise_analysis = estimate_dynamic_noise_for_segment(display_freqs, display_power, fundamental_freq)

                # 谐波检测
                if noise_analysis:
                    harmonic_analysis = detect_harmonics_for_segment(display_freqs, display_power, fundamental_freq, noise_analysis)
                    harmonic_count = len(harmonic_analysis)
                else:
                    harmonic_count = 0

                segments_data.append({
                    'seg_idx': seg_idx,
                    'expected_freq': expected_freq,
                    'harmonic_count': harmonic_count
                })

            return {
                'filename': os.path.basename(audio_path),
                'filepath': audio_path,
                'segments': segments_data
            }

        except Exception as e:
            return None

    def _count_anomaly_segments(self, segments):
        """计算异常段个数"""
        anomaly_count = 0
        for i, segment in enumerate(segments):
            if i < len(self.segment_thresholds):
                threshold = self.segment_thresholds[i]
                harmonic_count = segment['harmonic_count']
                if harmonic_count > threshold:
                    anomaly_count += 1
        return anomaly_count

    def _get_anomaly_segments(self, segments):
        """获取异常段详情"""
        anomaly_segments = []
        for i, segment in enumerate(segments):
            if i < len(self.segment_thresholds):
                threshold = self.segment_thresholds[i]
                harmonic_count = segment['harmonic_count']
                if harmonic_count > threshold:
                    anomaly_segments.append({
                        'seg_idx': i,
                        'expected_freq': segment['expected_freq'],
                        'harmonic_count': harmonic_count,
                        'threshold': threshold,
                        'excess': harmonic_count - threshold
                    })
        return anomaly_segments

# 便捷函数
def detect_audio_harmonic_anomaly(audio_path, anomaly_threshold=2):
    """
    便捷函数：检测音频文件的谐波异常

    Args:
        audio_path (str): 音频文件路径
        anomaly_threshold (int): 异常段个数阈值，默认为2

    Returns:
        int: 检测结果，1表示正常，0表示异常
    """
    detector = HarmonicDetectorAPI(anomaly_threshold=anomaly_threshold)
    return detector.detect(audio_path)
