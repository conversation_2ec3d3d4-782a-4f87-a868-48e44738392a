#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析噪声强度特征的合理性
探讨是否需要结合信噪比、去除主频和谐波等方法
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch, find_peaks
from scipy import signal

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_noise_feature_rationality():
    """分析噪声强度特征的合理性"""
    print("🔍 分析噪声强度特征的合理性")
    print("="*70)
    print("当前问题分析:")
    print("1. 当前特征: 真实底噪水平 (频谱最低10%功率)")
    print("2. 问题: 没有考虑信号本身的影响")
    print("3. 改进方向: 信噪比、去除主频谐波、频谱纯净度等")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 选择一个正常样本和两个噪声样本进行对比分析
    normal_sample = "test20250717/pos/完美/录音_步进扫频_100Hz至20000Hz_20250714_154028.wav"
    noise_sample1 = target_files[0]
    noise_sample2 = target_files[1]
    
    print(f"📊 样本对比分析:")
    print(f"   正常样本: {os.path.basename(normal_sample)}")
    print(f"   噪声样本1: {os.path.basename(noise_sample1)}")
    print(f"   噪声样本2: {os.path.basename(noise_sample2)}")
    
    # 分析100Hz频段 (关键频段)
    segment_idx = 0
    expected_freq = 100.0
    
    print(f"\n🎯 重点分析100Hz频段:")
    print("-" * 50)
    
    # 提取并分析每个样本的100Hz频段
    samples = [
        (normal_sample, "正常样本"),
        (noise_sample1, "噪声样本1"), 
        (noise_sample2, "噪声样本2")
    ]
    
    analysis_results = {}
    
    for sample_path, sample_name in samples:
        print(f"\n📈 {sample_name}: {os.path.basename(sample_path)}")
        
        if os.path.exists(sample_path):
            result = analyze_single_sample_segment(sample_path, segment_idx, expected_freq)
            analysis_results[sample_name] = result
            
            # 显示分析结果
            display_analysis_result(result, sample_name)
        else:
            print(f"   ❌ 文件不存在")
    
    # 对比分析
    print(f"\n🔄 对比分析:")
    print("="*70)
    compare_analysis_results(analysis_results)
    
    # 提出改进的特征
    print(f"\n💡 改进的噪声特征建议:")
    print("="*70)
    propose_improved_features(analysis_results)

def analyze_single_sample_segment(audio_path, segment_idx, expected_freq):
    """分析单个样本的特定频段"""
    
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        # 提取目标频段
        start_time, end_time = step_boundaries[segment_idx]
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        segment_audio = y[start_sample:end_sample]
        
        # 标准化
        if np.max(np.abs(segment_audio)) > 0:
            segment_audio = segment_audio / np.max(np.abs(segment_audio))
        
        # 多种分析方法
        result = {}
        
        # 1. 当前方法: STFT + 底噪分析
        result['current_method'] = analyze_current_method(segment_audio, sr, expected_freq)
        
        # 2. 改进方法1: 信噪比分析
        result['snr_analysis'] = analyze_snr(segment_audio, sr, expected_freq)
        
        # 3. 改进方法2: 去除主频后的噪声分析
        result['signal_removed_noise'] = analyze_noise_after_signal_removal(segment_audio, sr, expected_freq)
        
        # 4. 改进方法3: 频谱纯净度分析
        result['spectral_purity'] = analyze_spectral_purity(segment_audio, sr, expected_freq)
        
        # 5. 改进方法4: 谐波失真分析
        result['harmonic_distortion'] = analyze_harmonic_distortion(segment_audio, sr, expected_freq)
        
        return result
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def analyze_current_method(audio, sr, expected_freq):
    """当前方法分析"""
    
    # STFT分析
    f, t, Zxx = stft(audio, sr, nperseg=1024, noverlap=512)
    power_spectrum = np.abs(Zxx) ** 2
    power_db = 10 * np.log10(power_spectrum + 1e-12)
    
    # 当前的底噪计算
    all_noise_floors = []
    
    for frame_idx in range(power_spectrum.shape[1]):
        frame_power_db = power_db[:, frame_idx]
        noise_floor_10 = np.percentile(frame_power_db, 10)
        all_noise_floors.append(noise_floor_10)
    
    return {
        'noise_floor_median': np.median(all_noise_floors),
        'noise_floor_mean': np.mean(all_noise_floors),
        'method_description': '频谱最低10%功率的统计值'
    }

def analyze_snr(audio, sr, expected_freq):
    """信噪比分析"""
    
    # 使用Welch方法计算功率谱
    freqs, psd = welch(audio, sr, nperseg=1024)
    psd_db = 10 * np.log10(psd + 1e-12)
    
    # 找到期望频率附近的信号功率
    freq_idx = np.argmin(np.abs(freqs - expected_freq))
    signal_band_width = 10  # ±10个频率bin
    
    signal_start = max(0, freq_idx - signal_band_width)
    signal_end = min(len(freqs), freq_idx + signal_band_width)
    
    # 信号功率 (期望频率附近的最大功率)
    signal_power_db = np.max(psd_db[signal_start:signal_end])
    
    # 噪声功率 (排除信号频带后的平均功率)
    noise_mask = np.ones(len(freqs), dtype=bool)
    noise_mask[signal_start:signal_end] = False
    
    if np.any(noise_mask):
        noise_power_db = np.mean(psd_db[noise_mask])
        snr_db = signal_power_db - noise_power_db
    else:
        noise_power_db = -120
        snr_db = 120
    
    return {
        'signal_power_db': signal_power_db,
        'noise_power_db': noise_power_db,
        'snr_db': snr_db,
        'method_description': '信号功率 vs 背景噪声功率'
    }

def analyze_noise_after_signal_removal(audio, sr, expected_freq):
    """去除主频后的噪声分析"""
    
    # 设计陷波滤波器去除主频
    nyquist = sr / 2
    
    # 去除主频 ±5Hz
    low_freq = max(1, expected_freq - 5) / nyquist
    high_freq = min(nyquist - 1, expected_freq + 5) / nyquist
    
    # 带阻滤波器
    sos = signal.butter(4, [low_freq, high_freq], btype='bandstop', output='sos')
    filtered_audio = signal.sosfilt(sos, audio)
    
    # 分析滤波后的信号功率
    freqs, psd_filtered = welch(filtered_audio, sr, nperseg=1024)
    psd_filtered_db = 10 * np.log10(psd_filtered + 1e-12)
    
    # 计算去除主频后的噪声水平
    residual_noise_mean = np.mean(psd_filtered_db)
    residual_noise_std = np.std(psd_filtered_db)
    residual_noise_max = np.max(psd_filtered_db)
    
    # 计算原始信号的总功率
    original_power = np.mean(audio ** 2)
    filtered_power = np.mean(filtered_audio ** 2)
    
    # 信号去除比例
    signal_removal_ratio = 1 - (filtered_power / (original_power + 1e-12))
    
    return {
        'residual_noise_mean_db': residual_noise_mean,
        'residual_noise_std_db': residual_noise_std,
        'residual_noise_max_db': residual_noise_max,
        'signal_removal_ratio': signal_removal_ratio,
        'method_description': '去除主频±5Hz后的残余噪声'
    }

def analyze_spectral_purity(audio, sr, expected_freq):
    """频谱纯净度分析"""
    
    # FFT分析
    fft = np.fft.fft(audio)
    freqs = np.fft.fftfreq(len(audio), 1/sr)
    magnitude = np.abs(fft)
    
    # 只分析正频率
    positive_freqs = freqs[:len(freqs)//2]
    positive_magnitude = magnitude[:len(magnitude)//2]
    
    # 找到主频峰值
    freq_idx = np.argmin(np.abs(positive_freqs - expected_freq))
    main_peak_power = positive_magnitude[freq_idx]
    
    # 计算频谱集中度 (主频功率 / 总功率)
    total_power = np.sum(positive_magnitude)
    spectral_concentration = main_peak_power / (total_power + 1e-12)
    
    # 计算频谱平坦度 (几何平均 / 算术平均)
    geometric_mean = np.exp(np.mean(np.log(positive_magnitude + 1e-12)))
    arithmetic_mean = np.mean(positive_magnitude)
    spectral_flatness = geometric_mean / (arithmetic_mean + 1e-12)
    
    # 计算频谱熵 (衡量频谱分布的均匀性)
    magnitude_normalized = positive_magnitude / (total_power + 1e-12)
    magnitude_normalized = magnitude_normalized + 1e-12
    spectral_entropy = -np.sum(magnitude_normalized * np.log2(magnitude_normalized))
    
    return {
        'spectral_concentration': spectral_concentration,
        'spectral_flatness': spectral_flatness,
        'spectral_entropy': spectral_entropy,
        'main_peak_power': main_peak_power,
        'method_description': '频谱能量分布的纯净度指标'
    }

def analyze_harmonic_distortion(audio, sr, expected_freq):
    """谐波失真分析"""
    
    # FFT分析
    fft = np.fft.fft(audio)
    freqs = np.fft.fftfreq(len(audio), 1/sr)
    magnitude = np.abs(fft)
    
    # 只分析正频率
    positive_freqs = freqs[:len(freqs)//2]
    positive_magnitude = magnitude[:len(magnitude)//2]
    
    # 找到基频和谐波
    fundamental_idx = np.argmin(np.abs(positive_freqs - expected_freq))
    fundamental_power = positive_magnitude[fundamental_idx]
    
    # 寻找谐波 (2f, 3f, 4f, 5f)
    harmonic_powers = []
    harmonic_freqs = []
    
    for harmonic_order in range(2, 6):  # 2次到5次谐波
        harmonic_freq = expected_freq * harmonic_order
        
        if harmonic_freq < sr/2:  # 在奈奎斯特频率内
            harmonic_idx = np.argmin(np.abs(positive_freqs - harmonic_freq))
            
            # 在谐波频率附近±2Hz范围内找峰值
            search_range = int(2 * len(positive_freqs) / (sr/2))  # ±2Hz对应的索引范围
            search_start = max(0, harmonic_idx - search_range)
            search_end = min(len(positive_magnitude), harmonic_idx + search_range)
            
            harmonic_power = np.max(positive_magnitude[search_start:search_end])
            harmonic_powers.append(harmonic_power)
            harmonic_freqs.append(harmonic_freq)
    
    # 计算总谐波失真 (THD)
    if len(harmonic_powers) > 0:
        total_harmonic_power = np.sum(np.array(harmonic_powers) ** 2)
        fundamental_power_squared = fundamental_power ** 2
        
        thd = np.sqrt(total_harmonic_power) / (fundamental_power + 1e-12)
        thd_db = 20 * np.log10(thd + 1e-12)
    else:
        thd = 0
        thd_db = -120
    
    # 计算信号与谐波+噪声的比值 (SINAD)
    total_power = np.sum(positive_magnitude ** 2)
    signal_power = fundamental_power ** 2
    noise_and_distortion_power = total_power - signal_power
    
    if noise_and_distortion_power > 0:
        sinad = signal_power / noise_and_distortion_power
        sinad_db = 10 * np.log10(sinad)
    else:
        sinad_db = 120
    
    return {
        'thd': thd,
        'thd_db': thd_db,
        'sinad_db': sinad_db,
        'harmonic_powers': harmonic_powers,
        'harmonic_freqs': harmonic_freqs,
        'fundamental_power': fundamental_power,
        'method_description': '谐波失真和信号纯净度分析'
    }

def display_analysis_result(result, sample_name):
    """显示分析结果"""
    
    if result is None:
        return
    
    print(f"   📊 {sample_name} 分析结果:")
    
    # 当前方法
    current = result['current_method']
    print(f"     当前方法 (底噪): median={current['noise_floor_median']:.3f}dB, mean={current['noise_floor_mean']:.3f}dB")
    
    # 信噪比
    snr = result['snr_analysis']
    print(f"     信噪比分析: SNR={snr['snr_db']:.2f}dB, 信号={snr['signal_power_db']:.2f}dB, 噪声={snr['noise_power_db']:.2f}dB")
    
    # 去除主频后噪声
    removed = result['signal_removed_noise']
    print(f"     去除主频后: 残余噪声={removed['residual_noise_mean_db']:.2f}dB, 信号去除率={removed['signal_removal_ratio']:.3f}")
    
    # 频谱纯净度
    purity = result['spectral_purity']
    print(f"     频谱纯净度: 集中度={purity['spectral_concentration']:.4f}, 平坦度={purity['spectral_flatness']:.4f}")
    
    # 谐波失真
    harmonic = result['harmonic_distortion']
    print(f"     谐波失真: THD={harmonic['thd_db']:.2f}dB, SINAD={harmonic['sinad_db']:.2f}dB")

def compare_analysis_results(analysis_results):
    """对比分析结果"""
    
    if len(analysis_results) < 2:
        return
    
    print(f"📊 特征对比分析:")
    print("-" * 50)
    
    # 提取所有样本的关键指标
    metrics = {}
    
    for sample_name, result in analysis_results.items():
        if result is None:
            continue
            
        metrics[sample_name] = {
            'current_noise_floor': result['current_method']['noise_floor_median'],
            'snr_db': result['snr_analysis']['snr_db'],
            'residual_noise_db': result['signal_removed_noise']['residual_noise_mean_db'],
            'spectral_concentration': result['spectral_purity']['spectral_concentration'],
            'thd_db': result['harmonic_distortion']['thd_db'],
            'sinad_db': result['harmonic_distortion']['sinad_db']
        }
    
    # 显示对比表格
    print(f"{'指标':>15} {'正常样本':>12} {'噪声样本1':>12} {'噪声样本2':>12} {'差异1':>8} {'差异2':>8}")
    print("-" * 75)
    
    if '正常样本' in metrics and '噪声样本1' in metrics:
        normal = metrics['正常样本']
        noise1 = metrics['噪声样本1']
        noise2 = metrics.get('噪声样本2', {})
        
        for metric_name, normal_val in normal.items():
            noise1_val = noise1.get(metric_name, 0)
            noise2_val = noise2.get(metric_name, 0) if noise2 else 0
            
            diff1 = noise1_val - normal_val
            diff2 = noise2_val - normal_val if noise2 else 0
            
            print(f"{metric_name:>15} {normal_val:>12.3f} {noise1_val:>12.3f} {noise2_val:>12.3f} {diff1:>+8.3f} {diff2:>+8.3f}")

def propose_improved_features(analysis_results):
    """提出改进的特征"""
    
    print(f"💡 基于分析结果的改进特征建议:")
    print("-" * 50)
    
    print(f"1. 🎯 信噪比特征 (SNR-based features):")
    print(f"   - 优势: 直接反映信号质量，物理意义明确")
    print(f"   - 计算: 主频功率 vs 背景噪声功率")
    print(f"   - 适用: 所有频段，特别是低频段")
    
    print(f"\n2. 🔧 去除主频后的残余噪声 (Signal-removed noise):")
    print(f"   - 优势: 排除信号本身影响，专注噪声成分")
    print(f"   - 计算: 陷波滤波器去除主频后的功率")
    print(f"   - 适用: 检测环境噪声、电路噪声")
    
    print(f"\n3. 📊 频谱纯净度特征 (Spectral purity):")
    print(f"   - 优势: 衡量频谱分布的集中程度")
    print(f"   - 计算: 主频集中度、频谱平坦度、频谱熵")
    print(f"   - 适用: 检测频谱污染、多音干扰")
    
    print(f"\n4. 🎵 谐波失真特征 (Harmonic distortion):")
    print(f"   - 优势: 检测非线性失真和设备问题")
    print(f"   - 计算: THD、SINAD等标准音频指标")
    print(f"   - 适用: 检测设备故障、非线性失真")
    
    print(f"\n🏆 推荐的综合检测策略:")
    print(f"   主要特征: SNR (信噪比)")
    print(f"   辅助特征: 去除主频后残余噪声")
    print(f"   验证特征: 频谱纯净度")
    print(f"   故障检测: 谐波失真")
    
    print(f"\n⚠️  当前方法的问题:")
    print(f"   1. 底噪特征受信号功率影响")
    print(f"   2. 没有考虑信号与噪声的相对关系")
    print(f"   3. 容易被强信号掩盖真实噪声水平")
    print(f"   4. 缺乏物理意义的解释")

if __name__ == "__main__":
    analyze_noise_feature_rationality()
