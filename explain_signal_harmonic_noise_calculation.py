#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细说明主频、谐波、噪声的计算方法
包含具体的算法步骤和示例
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import welch
import librosa

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def explain_signal_harmonic_noise_calculation():
    """详细说明主频、谐波、噪声的计算方法"""
    print("📚 主频、谐波、噪声计算方法详解")
    print("="*70)
    
    # 生成示例信号进行演示
    demo_signal = generate_demo_signal()
    
    # 1. 详细说明主频计算
    print("\n1️⃣ 主频(基频)计算方法:")
    print("-" * 50)
    explain_fundamental_calculation(demo_signal)
    
    # 2. 详细说明谐波计算
    print("\n2️⃣ 谐波计算方法:")
    print("-" * 50)
    explain_harmonic_calculation(demo_signal)
    
    # 3. 详细说明噪声计算
    print("\n3️⃣ 噪声计算方法:")
    print("-" * 50)
    explain_noise_calculation(demo_signal)
    
    # 4. 完整示例演示
    print("\n4️⃣ 完整计算示例:")
    print("-" * 50)
    demonstrate_complete_calculation(demo_signal)
    
    # 5. 可视化演示
    print("\n5️⃣ 可视化演示:")
    print("-" * 50)
    create_calculation_visualization(demo_signal)

def generate_demo_signal():
    """生成演示信号"""
    print("🎵 生成演示信号 (1000Hz主频 + 谐波 + 噪声)")
    
    # 信号参数
    fs = 48000  # 采样率
    duration = 1.0  # 1秒
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    # 基频 1000Hz
    fundamental_freq = 1000.0
    fundamental_amplitude = 1.0
    
    # 构建信号
    signal = fundamental_amplitude * np.sin(2 * np.pi * fundamental_freq * t)
    
    # 添加谐波 (2次、3次、4次谐波)
    harmonic_amplitudes = [0.3, 0.1, 0.05]  # 谐波幅度递减
    for i, amp in enumerate(harmonic_amplitudes):
        harmonic_order = i + 2  # 2次、3次、4次谐波
        harmonic_freq = fundamental_freq * harmonic_order
        signal += amp * np.sin(2 * np.pi * harmonic_freq * t)
    
    # 添加白噪声
    noise_level = 0.1
    noise = noise_level * np.random.randn(len(t))
    signal += noise
    
    # 添加一些有色噪声 (模拟实际情况)
    colored_noise_freq = 1500.0  # 1500Hz的干扰
    colored_noise_amp = 0.05
    colored_noise = colored_noise_amp * np.sin(2 * np.pi * colored_noise_freq * t + np.random.randn(len(t)) * 0.1)
    signal += colored_noise
    
    demo_info = {
        'signal': signal,
        'time': t,
        'fs': fs,
        'fundamental_freq': fundamental_freq,
        'fundamental_amplitude': fundamental_amplitude,
        'harmonic_freqs': [fundamental_freq * (i+2) for i in range(len(harmonic_amplitudes))],
        'harmonic_amplitudes': harmonic_amplitudes,
        'noise_level': noise_level,
        'colored_noise_freq': colored_noise_freq,
        'colored_noise_amp': colored_noise_amp
    }
    
    print(f"   ✅ 信号生成完成:")
    print(f"     基频: {fundamental_freq}Hz, 幅度: {fundamental_amplitude}")
    print(f"     谐波: {demo_info['harmonic_freqs']}Hz, 幅度: {harmonic_amplitudes}")
    print(f"     白噪声水平: {noise_level}")
    print(f"     有色噪声: {colored_noise_freq}Hz, 幅度: {colored_noise_amp}")
    
    return demo_info

def explain_fundamental_calculation(demo_signal):
    """详细说明主频计算方法"""
    
    signal = demo_signal['signal']
    fs = demo_signal['fs']
    expected_freq = demo_signal['fundamental_freq']
    
    print("📊 主频(基频)计算步骤:")
    print("   目标: 找到期望频率附近的最强信号分量")
    
    # 步骤1: FFT变换
    print("\n   步骤1: FFT变换")
    fft_size = max(8192, len(signal))  # 确保足够的频率分辨率
    
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    # 只分析正频率
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    
    freq_resolution = positive_freqs[1] - positive_freqs[0]
    print(f"     FFT长度: {fft_size}")
    print(f"     频率分辨率: {freq_resolution:.2f} Hz")
    print(f"     分析频率范围: 0 - {positive_freqs[-1]:.0f} Hz")
    
    # 步骤2: 确定搜索范围
    print("\n   步骤2: 确定主频搜索范围")
    freq_tolerance = 5.0  # ±5Hz容差
    search_start = expected_freq - freq_tolerance
    search_end = expected_freq + freq_tolerance
    
    search_mask = (positive_freqs >= search_start) & (positive_freqs <= search_end)
    search_indices = np.where(search_mask)[0]
    
    print(f"     期望频率: {expected_freq} Hz")
    print(f"     搜索范围: [{search_start}, {search_end}] Hz")
    print(f"     搜索频点数: {len(search_indices)}")
    
    # 步骤3: 找到峰值
    print("\n   步骤3: 在搜索范围内找到最大功率点")
    if len(search_indices) > 0:
        search_powers = positive_power[search_mask]
        max_power_idx = np.argmax(search_powers)
        actual_idx = search_indices[max_power_idx]
        
        fundamental_freq = positive_freqs[actual_idx]
        fundamental_power = positive_power[actual_idx]
        fundamental_power_db = 10 * np.log10(fundamental_power + 1e-12)
        
        print(f"     找到的主频: {fundamental_freq:.2f} Hz")
        print(f"     主频功率: {fundamental_power:.2e}")
        print(f"     主频功率(dB): {fundamental_power_db:.2f} dB")
        print(f"     频率误差: {fundamental_freq - expected_freq:.2f} Hz")
    else:
        print("     ❌ 在搜索范围内未找到有效频点")
        return None
    
    # 步骤4: 计算主频能量占比
    print("\n   步骤4: 计算主频能量占比")
    total_power = np.sum(positive_power)
    fundamental_ratio = fundamental_power / total_power
    
    print(f"     总功率: {total_power:.2e}")
    print(f"     主频功率占比: {fundamental_ratio:.3f} ({fundamental_ratio*100:.1f}%)")
    
    return {
        'freq': fundamental_freq,
        'power': fundamental_power,
        'power_db': fundamental_power_db,
        'ratio': fundamental_ratio,
        'freq_error': fundamental_freq - expected_freq
    }

def explain_harmonic_calculation(demo_signal):
    """详细说明谐波计算方法"""
    
    signal = demo_signal['signal']
    fs = demo_signal['fs']
    fundamental_freq = demo_signal['fundamental_freq']
    
    print("🎵 谐波计算步骤:")
    print("   目标: 找到基频的整数倍频率分量 (2f, 3f, 4f, 5f...)")
    
    # FFT分析 (与主频计算相同)
    fft_size = max(8192, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    
    print(f"\n   基频: {fundamental_freq} Hz")
    print(f"   奈奎斯特频率: {fs/2} Hz")
    
    # 步骤1: 确定要搜索的谐波次数
    print("\n   步骤1: 确定谐波搜索范围")
    harmonic_orders = []
    for order in range(2, 10):  # 2次到9次谐波
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < fs / 2:  # 必须在奈奎斯特频率内
            harmonic_orders.append(order)
        else:
            break
    
    print(f"     可搜索的谐波次数: {harmonic_orders}")
    
    # 步骤2: 逐个搜索谐波
    print("\n   步骤2: 逐个搜索谐波峰值")
    harmonics = []
    
    for order in harmonic_orders:
        expected_harmonic_freq = fundamental_freq * order
        
        # 谐波搜索范围 (±10Hz)
        harmonic_tolerance = 10.0
        search_start = expected_harmonic_freq - harmonic_tolerance
        search_end = expected_harmonic_freq + harmonic_tolerance
        
        search_mask = (positive_freqs >= search_start) & (positive_freqs <= search_end)
        search_indices = np.where(search_mask)[0]
        
        if len(search_indices) > 0:
            search_powers = positive_power[search_mask]
            max_power_idx = np.argmax(search_powers)
            actual_idx = search_indices[max_power_idx]
            
            actual_freq = positive_freqs[actual_idx]
            harmonic_power = positive_power[actual_idx]
            harmonic_power_db = 10 * np.log10(harmonic_power + 1e-12)
            
            harmonics.append({
                'order': order,
                'expected_freq': expected_harmonic_freq,
                'actual_freq': actual_freq,
                'power': harmonic_power,
                'power_db': harmonic_power_db,
                'freq_error': actual_freq - expected_harmonic_freq
            })
            
            print(f"     {order}次谐波: 期望{expected_harmonic_freq:.0f}Hz, "
                  f"实际{actual_freq:.1f}Hz, 功率{harmonic_power_db:.1f}dB")
        else:
            print(f"     {order}次谐波: 未找到明显峰值")
    
    # 步骤3: 计算总谐波功率
    print("\n   步骤3: 计算总谐波功率和失真")
    if harmonics:
        total_harmonic_power = sum(h['power'] for h in harmonics)
        total_harmonic_power_db = 10 * np.log10(total_harmonic_power + 1e-12)
        
        # 计算总谐波失真 (THD)
        # 需要基频功率来计算THD
        fundamental_power = demo_signal.get('fundamental_power', 1.0)  # 这里简化处理
        
        print(f"     找到谐波数: {len(harmonics)}")
        print(f"     总谐波功率: {total_harmonic_power:.2e}")
        print(f"     总谐波功率(dB): {total_harmonic_power_db:.2f} dB")
        
        # 计算能量占比
        total_power = np.sum(positive_power)
        harmonic_ratio = total_harmonic_power / total_power
        print(f"     谐波功率占比: {harmonic_ratio:.4f} ({harmonic_ratio*100:.2f}%)")
    else:
        print("     未找到明显谐波")
        total_harmonic_power = 0
        total_harmonic_power_db = -120
        harmonic_ratio = 0
    
    return {
        'harmonics': harmonics,
        'total_power': total_harmonic_power,
        'total_power_db': total_harmonic_power_db,
        'ratio': harmonic_ratio,
        'count': len(harmonics)
    }

def explain_noise_calculation(demo_signal):
    """详细说明噪声计算方法"""
    
    signal = demo_signal['signal']
    fs = demo_signal['fs']
    fundamental_freq = demo_signal['fundamental_freq']
    
    print("🔊 噪声计算步骤:")
    print("   目标: 计算除主频和谐波外的所有频率分量")
    
    # FFT分析
    fft_size = max(8192, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    
    # 步骤1: 创建噪声掩码 (排除主频和谐波)
    print("\n   步骤1: 创建噪声频率掩码")
    noise_mask = np.ones(len(positive_freqs), dtype=bool)  # 初始全为True
    
    # 排除主频
    fundamental_bandwidth = 20.0  # ±20Hz
    fundamental_mask = (positive_freqs >= fundamental_freq - fundamental_bandwidth) & \
                      (positive_freqs <= fundamental_freq + fundamental_bandwidth)
    noise_mask &= ~fundamental_mask  # 从噪声掩码中排除主频
    
    excluded_count = np.sum(fundamental_mask)
    print(f"     排除主频 {fundamental_freq}Hz ±{fundamental_bandwidth}Hz: {excluded_count} 个频点")
    
    # 排除谐波
    harmonic_bandwidth = 20.0  # ±20Hz
    harmonic_orders = [2, 3, 4, 5, 6]
    
    for order in harmonic_orders:
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < fs / 2:
            harmonic_mask = (positive_freqs >= harmonic_freq - harmonic_bandwidth) & \
                           (positive_freqs <= harmonic_freq + harmonic_bandwidth)
            noise_mask &= ~harmonic_mask
            
            excluded_count = np.sum(harmonic_mask)
            print(f"     排除{order}次谐波 {harmonic_freq:.0f}Hz ±{harmonic_bandwidth}Hz: {excluded_count} 个频点")
    
    # 步骤2: 计算噪声统计量
    print("\n   步骤2: 计算噪声功率统计量")
    
    if np.any(noise_mask):
        noise_power_array = positive_power[noise_mask]
        noise_freq_array = positive_freqs[noise_mask]
        
        # 各种噪声统计量
        total_noise_power = np.sum(noise_power_array)
        mean_noise_power = np.mean(noise_power_array)
        noise_floor = np.percentile(noise_power_array, 10)  # 底噪水平 (最低10%)
        noise_peak = np.max(noise_power_array)  # 噪声峰值
        noise_std = np.std(noise_power_array)  # 噪声功率标准差
        
        # 转换为dB
        total_noise_power_db = 10 * np.log10(total_noise_power + 1e-12)
        mean_noise_power_db = 10 * np.log10(mean_noise_power + 1e-12)
        noise_floor_db = 10 * np.log10(noise_floor + 1e-12)
        noise_peak_db = 10 * np.log10(noise_peak + 1e-12)
        
        print(f"     噪声频点数: {len(noise_power_array)}")
        print(f"     噪声频率覆盖: {len(noise_power_array)/len(positive_freqs)*100:.1f}%")
        print(f"     总噪声功率: {total_noise_power:.2e} ({total_noise_power_db:.2f} dB)")
        print(f"     平均噪声功率: {mean_noise_power:.2e} ({mean_noise_power_db:.2f} dB)")
        print(f"     噪声底噪: {noise_floor:.2e} ({noise_floor_db:.2f} dB)")
        print(f"     噪声峰值: {noise_peak:.2e} ({noise_peak_db:.2f} dB)")
        print(f"     噪声动态范围: {noise_peak_db - noise_floor_db:.2f} dB")
        
        # 步骤3: 计算噪声能量占比
        print("\n   步骤3: 计算噪声能量占比")
        total_signal_power = np.sum(positive_power)
        noise_ratio = total_noise_power / total_signal_power
        
        print(f"     总信号功率: {total_signal_power:.2e}")
        print(f"     噪声功率占比: {noise_ratio:.4f} ({noise_ratio*100:.2f}%)")
        
        # 步骤4: 分析噪声分布
        print("\n   步骤4: 分析噪声频率分布")
        
        # 找到噪声最强的频率
        max_noise_idx = np.argmax(noise_power_array)
        max_noise_freq = noise_freq_array[max_noise_idx]
        max_noise_power_db = 10 * np.log10(noise_power_array[max_noise_idx] + 1e-12)
        
        print(f"     最强噪声频率: {max_noise_freq:.1f} Hz")
        print(f"     最强噪声功率: {max_noise_power_db:.2f} dB")
        
        # 噪声频段分析
        freq_bands = {
            '低频噪声 (0-500Hz)': (0, 500),
            '中频噪声 (500-2000Hz)': (500, 2000),
            '高频噪声 (2000-10000Hz)': (2000, 10000),
            '超高频噪声 (>10000Hz)': (10000, fs/2)
        }
        
        for band_name, (f_low, f_high) in freq_bands.items():
            band_mask = (noise_freq_array >= f_low) & (noise_freq_array < f_high)
            if np.any(band_mask):
                band_power = np.sum(noise_power_array[band_mask])
                band_ratio = band_power / total_noise_power
                print(f"     {band_name}: {band_ratio:.3f} ({band_ratio*100:.1f}%)")
    
    else:
        print("     ❌ 没有剩余频点用于噪声分析")
        total_noise_power = 1e-12
        total_noise_power_db = -120
        noise_floor_db = -120
        noise_peak_db = -120
        noise_ratio = 0
    
    return {
        'total_power': total_noise_power,
        'power_db': total_noise_power_db,
        'floor_db': noise_floor_db,
        'peak_db': noise_peak_db,
        'ratio': noise_ratio,
        'frequency_bins': np.sum(noise_mask) if np.any(noise_mask) else 0,
        'frequency_coverage': np.sum(noise_mask) / len(positive_freqs) if len(positive_freqs) > 0 else 0
    }

def demonstrate_complete_calculation(demo_signal):
    """演示完整的计算过程"""
    
    print("🔬 完整计算演示:")
    
    # 1. 计算主频
    fundamental_result = explain_fundamental_calculation(demo_signal)
    
    # 2. 计算谐波
    harmonic_result = explain_harmonic_calculation(demo_signal)
    
    # 3. 计算噪声
    noise_result = explain_noise_calculation(demo_signal)
    
    # 4. 综合分析
    print("\n📊 综合分析结果:")
    print("-" * 30)
    
    if fundamental_result and harmonic_result and noise_result:
        print(f"主频功率占比: {fundamental_result['ratio']:.3f} ({fundamental_result['ratio']*100:.1f}%)")
        print(f"谐波功率占比: {harmonic_result['ratio']:.4f} ({harmonic_result['ratio']*100:.2f}%)")
        print(f"噪声功率占比: {noise_result['ratio']:.4f} ({noise_result['ratio']*100:.2f}%)")
        
        total_accounted = fundamental_result['ratio'] + harmonic_result['ratio'] + noise_result['ratio']
        print(f"总计占比: {total_accounted:.3f} ({total_accounted*100:.1f}%)")
        
        # 计算信噪比
        snr_db = fundamental_result['power_db'] - noise_result['power_db']
        print(f"\n信噪比 (SNR): {snr_db:.2f} dB")
        
        # 计算总谐波失真
        thd_db = harmonic_result['total_power_db'] - fundamental_result['power_db']
        print(f"总谐波失真 (THD): {thd_db:.2f} dB")

def create_calculation_visualization(demo_signal):
    """创建计算过程可视化"""
    
    print("🎨 生成计算过程可视化...")
    
    signal = demo_signal['signal']
    fs = demo_signal['fs']
    
    # FFT分析
    fft_size = max(8192, len(signal))
    if len(signal) < fft_size:
        signal_padded = np.pad(signal, (0, fft_size - len(signal)), 'constant')
    else:
        signal_padded = signal[:fft_size]
    
    fft = np.fft.fft(signal_padded)
    freqs = np.fft.fftfreq(fft_size, 1/fs)
    magnitude = np.abs(fft)
    power = magnitude ** 2
    
    positive_freqs = freqs[:fft_size//2]
    positive_power = power[:fft_size//2]
    power_db = 10 * np.log10(positive_power + 1e-12)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 上图: 时域信号
    t = demo_signal['time']
    ax1.plot(t[:1000], signal[:1000], 'b-', linewidth=1)
    ax1.set_title('时域信号 (前1000个采样点)', fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('幅度')
    ax1.grid(True, alpha=0.3)
    
    # 下图: 频域分析
    ax2.plot(positive_freqs, power_db, 'b-', linewidth=1, alpha=0.7, label='完整频谱')
    
    # 标记主频
    fundamental_freq = demo_signal['fundamental_freq']
    fund_idx = np.argmin(np.abs(positive_freqs - fundamental_freq))
    ax2.plot(positive_freqs[fund_idx], power_db[fund_idx], 'ro', markersize=8, label=f'主频 ({fundamental_freq}Hz)')
    
    # 标记谐波
    harmonic_colors = ['orange', 'green', 'purple', 'brown']
    for i, order in enumerate([2, 3, 4, 5]):
        harmonic_freq = fundamental_freq * order
        if harmonic_freq < fs/2:
            harm_idx = np.argmin(np.abs(positive_freqs - harmonic_freq))
            color = harmonic_colors[i] if i < len(harmonic_colors) else 'gray'
            ax2.plot(positive_freqs[harm_idx], power_db[harm_idx], 'o', 
                    color=color, markersize=6, label=f'{order}次谐波 ({harmonic_freq:.0f}Hz)')
    
    # 标记噪声区域
    noise_mask = np.ones(len(positive_freqs), dtype=bool)
    
    # 排除主频和谐波
    for order in [1, 2, 3, 4, 5]:
        freq = fundamental_freq * order
        if freq < fs/2:
            exclude_mask = (positive_freqs >= freq - 20) & (positive_freqs <= freq + 20)
            noise_mask &= ~exclude_mask
    
    if np.any(noise_mask):
        ax2.fill_between(positive_freqs, -150, power_db, where=noise_mask, 
                        alpha=0.2, color='red', label='噪声区域')
    
    ax2.set_title('频域分析 - 主频/谐波/噪声分离', fontweight='bold')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('功率 (dB)')
    ax2.set_xlim(0, 8000)  # 只显示0-8kHz
    ax2.set_ylim(-100, np.max(power_db) + 10)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('signal_harmonic_noise_calculation_demo.png', dpi=300, bbox_inches='tight')
    print("   ✅ 可视化已保存: signal_harmonic_noise_calculation_demo.png")
    plt.close()

if __name__ == "__main__":
    explain_signal_harmonic_noise_calculation()
