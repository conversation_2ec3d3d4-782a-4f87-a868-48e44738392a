#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实现改进的噪声特征
基于信噪比、去除主频、频谱纯净度等科学方法
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import librosa
from scipy.signal import stft, welch, butter, sosfilt
from scipy import signal

sys.path.append('.')
from freq_split_optimized import split_freq_steps_optimized

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def implement_improved_noise_features():
    """实现改进的噪声特征"""
    print("🔧 实现改进的噪声特征")
    print("="*70)
    print("新特征设计:")
    print("1. SNR (信噪比) - 主频功率 vs 背景噪声功率")
    print("2. 去除主频后残余噪声 - 陷波滤波后的噪声水平")
    print("3. 频谱纯净度 - 主频集中度和频谱平坦度")
    print("4. 谐波失真 - THD和SINAD")
    print("="*70)
    
    # 目标样本
    target_files = [
        '录音_步进扫频_100Hz至20000Hz_20250714_153632.wav',
        '录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav'
    ]
    
    # 测试文件夹
    test_folders = {
        'pos': [
            "test20250717/pos/sd卡",
            "test20250717/pos/完美", 
            "test20250717/pos/转接板",
            "test20250717/pos/铁网"
        ],
        'neg': [
            "test20250717/neg"
        ]
    }
    
    all_results = []
    
    # 处理所有文件
    for true_label, folders in test_folders.items():
        for folder in folders:
            if not os.path.exists(folder):
                print(f"❌ 文件夹不存在: {folder}")
                continue
            
            import glob
            wav_files = glob.glob(os.path.join(folder, "*.wav"))
            
            for audio_path in wav_files:
                filename = os.path.basename(audio_path)
                print(f"🎵 处理文件: {filename}")
                
                # 提取改进的特征
                improved_features = extract_improved_features(audio_path, filename, target_files)
                
                if improved_features:
                    all_results.extend(improved_features)
    
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 保存结果
    df.to_csv('improved_noise_features.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 改进特征已保存: improved_noise_features.csv")
    
    # 分析改进特征的分离效果
    analyze_improved_features_separation(df, target_files)
    
    return df

def extract_improved_features(audio_path, filename, target_files):
    """提取改进的噪声特征"""
    try:
        # 获取频段分割
        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            audio_path, start_freq=100, stop_freq=20000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )
        
        # 加载音频
        y, sr = librosa.load(audio_path, sr=48000)
        if sr != 48000:
            y = librosa.resample(y, orig_sr=sr, target_sr=48000)
        
        segment_results = []
        
        # 分析前10个频段 (重点关注低频段)
        target_segments = list(range(min(10, len(step_boundaries))))
        
        for seg_idx in target_segments:
            start_time, end_time = step_boundaries[seg_idx]
            expected_freq = freq_table[seg_idx]
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            segment_audio = y[start_sample:end_sample]
            
            if len(segment_audio) == 0:
                continue
            
            # 标准化音频
            if np.max(np.abs(segment_audio)) > 0:
                segment_audio = segment_audio / np.max(np.abs(segment_audio))
            
            # 提取改进的特征
            improved_features = extract_segment_improved_features(segment_audio, sr, expected_freq)
            
            # 添加元数据
            segment_info = {
                'filename': filename,
                'segment_idx': seg_idx,
                'expected_freq': expected_freq,
                'start_time': start_time,
                'end_time': end_time,
                'is_target': filename in target_files,
                'sample_type': 'target' if filename in target_files else 'normal'
            }
            
            # 合并特征和元数据
            segment_info.update(improved_features)
            segment_results.append(segment_info)
        
        return segment_results
        
    except Exception as e:
        print(f"❌ 处理文件失败 {filename}: {e}")
        return None

def extract_segment_improved_features(audio, sr, expected_freq):
    """提取单个频段的改进特征"""
    features = {}
    
    try:
        # 1. 信噪比特征 (SNR-based features)
        snr_features = calculate_snr_features(audio, sr, expected_freq)
        features.update(snr_features)
        
        # 2. 去除主频后残余噪声特征
        residual_noise_features = calculate_residual_noise_features(audio, sr, expected_freq)
        features.update(residual_noise_features)
        
        # 3. 频谱纯净度特征
        purity_features = calculate_spectral_purity_features(audio, sr, expected_freq)
        features.update(purity_features)
        
        # 4. 谐波失真特征
        distortion_features = calculate_harmonic_distortion_features(audio, sr, expected_freq)
        features.update(distortion_features)
        
    except Exception as e:
        # 设置默认值
        default_features = {
            'snr_db': 0, 'signal_power_db': -120, 'noise_power_db': -120,
            'residual_noise_mean_db': -120, 'residual_noise_std_db': 0, 'signal_removal_ratio': 0,
            'spectral_concentration': 0, 'spectral_flatness': 0, 'spectral_entropy': 0,
            'thd_db': -120, 'sinad_db': 0, 'fundamental_power_db': -120
        }
        features.update(default_features)
    
    return features

def calculate_snr_features(audio, sr, expected_freq):
    """计算信噪比特征"""
    
    # 使用Welch方法计算功率谱
    freqs, psd = welch(audio, sr, nperseg=1024, noverlap=512)
    psd_db = 10 * np.log10(psd + 1e-12)
    
    # 找到期望频率附近的信号功率
    freq_idx = np.argmin(np.abs(freqs - expected_freq))
    
    # 信号频带: 期望频率 ±10Hz
    freq_resolution = freqs[1] - freqs[0]
    signal_bandwidth_bins = max(1, int(10 / freq_resolution))  # ±10Hz对应的bin数
    
    signal_start = max(0, freq_idx - signal_bandwidth_bins)
    signal_end = min(len(freqs), freq_idx + signal_bandwidth_bins)
    
    # 信号功率 (信号频带内的最大功率)
    signal_power_db = np.max(psd_db[signal_start:signal_end])
    
    # 噪声功率计算方法1: 排除信号频带后的平均功率
    noise_mask = np.ones(len(freqs), dtype=bool)
    noise_mask[signal_start:signal_end] = False
    
    if np.any(noise_mask):
        noise_power_db_method1 = np.mean(psd_db[noise_mask])
    else:
        noise_power_db_method1 = -120
    
    # 噪声功率计算方法2: 频谱最低10%的平均功率
    noise_power_db_method2 = np.percentile(psd_db, 10)
    
    # 选择更保守的噪声估计 (更高的噪声水平)
    noise_power_db = max(noise_power_db_method1, noise_power_db_method2)
    
    # 计算信噪比
    snr_db = signal_power_db - noise_power_db
    
    return {
        'snr_db': snr_db,
        'signal_power_db': signal_power_db,
        'noise_power_db': noise_power_db,
        'noise_power_method1_db': noise_power_db_method1,
        'noise_power_method2_db': noise_power_db_method2
    }

def calculate_residual_noise_features(audio, sr, expected_freq):
    """计算去除主频后的残余噪声特征"""
    
    # 设计陷波滤波器去除主频
    nyquist = sr / 2
    
    # 去除主频 ±15Hz (更宽的陷波)
    notch_bandwidth = 15
    low_freq = max(1, expected_freq - notch_bandwidth) / nyquist
    high_freq = min(nyquist - 1, expected_freq + notch_bandwidth) / nyquist
    
    # 确保频率范围有效
    if low_freq >= high_freq or low_freq <= 0 or high_freq >= 1:
        # 如果频率范围无效，使用高通滤波器
        if expected_freq > 50:
            sos = butter(4, (expected_freq - notch_bandwidth) / nyquist, btype='highpass', output='sos')
        else:
            # 对于很低的频率，不进行滤波
            filtered_audio = audio
            sos = None
    else:
        # 带阻滤波器
        sos = butter(4, [low_freq, high_freq], btype='bandstop', output='sos')
    
    if sos is not None:
        filtered_audio = sosfilt(sos, audio)
    else:
        filtered_audio = audio
    
    # 分析滤波后的信号
    freqs, psd_filtered = welch(filtered_audio, sr, nperseg=1024, noverlap=512)
    psd_filtered_db = 10 * np.log10(psd_filtered + 1e-12)
    
    # 计算残余噪声统计量
    residual_noise_mean_db = np.mean(psd_filtered_db)
    residual_noise_std_db = np.std(psd_filtered_db)
    residual_noise_max_db = np.max(psd_filtered_db)
    residual_noise_min_db = np.min(psd_filtered_db)
    
    # 计算信号去除效果
    original_power = np.mean(audio ** 2)
    filtered_power = np.mean(filtered_audio ** 2)
    signal_removal_ratio = 1 - (filtered_power / (original_power + 1e-12))
    
    return {
        'residual_noise_mean_db': residual_noise_mean_db,
        'residual_noise_std_db': residual_noise_std_db,
        'residual_noise_max_db': residual_noise_max_db,
        'residual_noise_min_db': residual_noise_min_db,
        'signal_removal_ratio': signal_removal_ratio
    }

def calculate_spectral_purity_features(audio, sr, expected_freq):
    """计算频谱纯净度特征"""
    
    # FFT分析
    fft = np.fft.fft(audio)
    freqs = np.fft.fftfreq(len(audio), 1/sr)
    magnitude = np.abs(fft)
    
    # 只分析正频率
    positive_freqs = freqs[:len(freqs)//2]
    positive_magnitude = magnitude[:len(magnitude)//2]
    
    # 找到主频峰值
    freq_idx = np.argmin(np.abs(positive_freqs - expected_freq))
    main_peak_power = positive_magnitude[freq_idx]
    
    # 计算频谱集中度 (主频功率 / 总功率)
    total_power = np.sum(positive_magnitude)
    spectral_concentration = main_peak_power / (total_power + 1e-12)
    
    # 计算频谱平坦度 (几何平均 / 算术平均)
    # 避免log(0)的问题
    magnitude_safe = positive_magnitude + 1e-12
    geometric_mean = np.exp(np.mean(np.log(magnitude_safe)))
    arithmetic_mean = np.mean(positive_magnitude)
    spectral_flatness = geometric_mean / (arithmetic_mean + 1e-12)
    
    # 计算频谱熵
    magnitude_normalized = positive_magnitude / (total_power + 1e-12)
    magnitude_normalized = magnitude_normalized + 1e-12
    spectral_entropy = -np.sum(magnitude_normalized * np.log2(magnitude_normalized))
    
    # 计算主频附近的功率集中度
    bandwidth_bins = max(1, int(20 * len(positive_freqs) / (sr/2)))  # ±20Hz
    peak_start = max(0, freq_idx - bandwidth_bins)
    peak_end = min(len(positive_magnitude), freq_idx + bandwidth_bins)
    
    peak_region_power = np.sum(positive_magnitude[peak_start:peak_end])
    peak_concentration = peak_region_power / (total_power + 1e-12)
    
    return {
        'spectral_concentration': spectral_concentration,
        'spectral_flatness': spectral_flatness,
        'spectral_entropy': spectral_entropy,
        'peak_concentration': peak_concentration,
        'main_peak_power_db': 20 * np.log10(main_peak_power + 1e-12)
    }

def calculate_harmonic_distortion_features(audio, sr, expected_freq):
    """计算谐波失真特征"""
    
    # FFT分析
    fft = np.fft.fft(audio)
    freqs = np.fft.fftfreq(len(audio), 1/sr)
    magnitude = np.abs(fft)
    
    # 只分析正频率
    positive_freqs = freqs[:len(freqs)//2]
    positive_magnitude = magnitude[:len(magnitude)//2]
    
    # 找到基频
    fundamental_idx = np.argmin(np.abs(positive_freqs - expected_freq))
    fundamental_power = positive_magnitude[fundamental_idx]
    fundamental_power_db = 20 * np.log10(fundamental_power + 1e-12)
    
    # 寻找谐波 (2f, 3f, 4f, 5f)
    harmonic_powers = []
    
    for harmonic_order in range(2, 6):  # 2次到5次谐波
        harmonic_freq = expected_freq * harmonic_order
        
        if harmonic_freq < sr/2:  # 在奈奎斯特频率内
            harmonic_idx = np.argmin(np.abs(positive_freqs - harmonic_freq))
            
            # 在谐波频率附近±5Hz范围内找峰值
            search_bandwidth = max(1, int(5 * len(positive_freqs) / (sr/2)))
            search_start = max(0, harmonic_idx - search_bandwidth)
            search_end = min(len(positive_magnitude), harmonic_idx + search_bandwidth)
            
            harmonic_power = np.max(positive_magnitude[search_start:search_end])
            harmonic_powers.append(harmonic_power)
    
    # 计算总谐波失真 (THD)
    if len(harmonic_powers) > 0 and fundamental_power > 0:
        total_harmonic_power = np.sum(np.array(harmonic_powers) ** 2)
        fundamental_power_squared = fundamental_power ** 2
        
        thd = np.sqrt(total_harmonic_power) / fundamental_power
        thd_db = 20 * np.log10(thd + 1e-12)
    else:
        thd_db = -120
    
    # 计算信号与谐波+噪声的比值 (SINAD)
    total_power = np.sum(positive_magnitude ** 2)
    signal_power = fundamental_power ** 2
    noise_and_distortion_power = total_power - signal_power
    
    if noise_and_distortion_power > 0:
        sinad = signal_power / noise_and_distortion_power
        sinad_db = 10 * np.log10(sinad)
    else:
        sinad_db = 120
    
    return {
        'thd_db': thd_db,
        'sinad_db': sinad_db,
        'fundamental_power_db': fundamental_power_db,
        'harmonic_count': len(harmonic_powers)
    }

def analyze_improved_features_separation(df, target_files):
    """分析改进特征的分离效果"""
    print(f"\n🔍 分析改进特征的分离效果")
    print("="*70)
    
    # 获取改进的特征列
    improved_feature_cols = [
        'snr_db', 'signal_power_db', 'noise_power_db',
        'residual_noise_mean_db', 'residual_noise_std_db', 'signal_removal_ratio',
        'spectral_concentration', 'spectral_flatness', 'spectral_entropy',
        'thd_db', 'sinad_db', 'fundamental_power_db'
    ]
    
    target_data = df[df['is_target'] == True]
    normal_data = df[df['is_target'] == False]
    
    print(f"📊 数据概览:")
    print(f"   噪声样本记录数: {len(target_data)}")
    print(f"   正常样本记录数: {len(normal_data)}")
    
    # 分析每个改进特征的分离能力
    separable_features = []
    
    for feature in improved_feature_cols:
        if feature not in df.columns:
            continue
            
        target_values = target_data[feature].dropna()
        normal_values = normal_data[feature].dropna()
        
        if len(target_values) == 0 or len(normal_values) == 0:
            continue
        
        target_min, target_max = np.min(target_values), np.max(target_values)
        normal_min, normal_max = np.min(normal_values), np.max(normal_values)
        
        # 检查完全分离
        if target_max < normal_min:
            separation_gap = normal_min - target_max
            separation_type = 'target_below_normal'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'normal_range': [normal_min, normal_max],
                'target_mean': np.mean(target_values),
                'normal_mean': np.mean(normal_values)
            })
        elif target_min > normal_max:
            separation_gap = target_min - normal_max
            separation_type = 'target_above_normal'
            separable_features.append({
                'feature': feature,
                'separation_gap': separation_gap,
                'separation_type': separation_type,
                'target_range': [target_min, target_max],
                'normal_range': [normal_min, normal_max],
                'target_mean': np.mean(target_values),
                'normal_mean': np.mean(normal_values)
            })
    
    # 排序并显示结果
    separable_features.sort(key=lambda x: x['separation_gap'], reverse=True)
    
    print(f"\n✅ 找到 {len(separable_features)} 个可完全分离的改进特征:")
    print(f"{'特征名':>25} {'分离间隙':>12} {'分离类型':>18} {'目标均值':>12} {'正常均值':>12}")
    print("-" * 85)
    
    for i, feature_info in enumerate(separable_features):
        print(f"{feature_info['feature']:>25} {feature_info['separation_gap']:>12.6f} "
              f"{feature_info['separation_type']:>18} {feature_info['target_mean']:>12.3f} "
              f"{feature_info['normal_mean']:>12.3f}")
    
    # 对比改进特征与原始特征
    print(f"\n📊 改进效果对比:")
    print("-" * 50)
    print(f"   改进特征可分离数: {len(separable_features)}")
    print(f"   改进特征覆盖率: {len(separable_features)/len(improved_feature_cols)*100:.1f}%")
    
    if len(separable_features) > 0:
        best_feature = separable_features[0]
        print(f"\n🏆 最佳改进特征: {best_feature['feature']}")
        print(f"   分离间隙: {best_feature['separation_gap']:.6f}")
        print(f"   物理意义: {get_feature_meaning(best_feature['feature'])}")

def get_feature_meaning(feature_name):
    """获取特征的物理意义"""
    meanings = {
        'snr_db': '信噪比 - 信号与噪声的功率比',
        'signal_power_db': '信号功率 - 主频功率水平',
        'noise_power_db': '噪声功率 - 背景噪声水平',
        'residual_noise_mean_db': '残余噪声 - 去除主频后的噪声',
        'signal_removal_ratio': '信号去除率 - 主频去除效果',
        'spectral_concentration': '频谱集中度 - 主频能量集中程度',
        'spectral_flatness': '频谱平坦度 - 频谱分布均匀性',
        'thd_db': '总谐波失真 - 非线性失真程度',
        'sinad_db': '信号与噪声失真比 - 信号质量指标'
    }
    return meanings.get(feature_name, '未知特征')

if __name__ == "__main__":
    df = implement_improved_noise_features()
    print(f"\n✅ 改进噪声特征实现完成！")
