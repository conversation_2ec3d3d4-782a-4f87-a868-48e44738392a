#!/usr/bin/env python3
"""
基于异常频次的瞬时检测器
Frequency-Based Transient Detector
基于异常出现频次来区分真正的瞬时异常和持续性异常
"""

import numpy as np
import librosa
import pandas as pd
from scipy.signal import stft
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class FrequencyBasedTransientDetector:
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        
        # STFT参数
        self.stft_params = {
            'nperseg': 1024,        # 约21ms @48kHz
            'noverlap': 768,        # 75%重叠
            'window': 'hann',
            'nfft': 1024
        }
        
        # 基于频次的检测参数
        self.detection_params = {
            'frequency_range': (100, 8000),
            'segment_duration': 0.5,            # 每个频段持续时间(秒)
            'edge_exclude_ratio': 0.15,         # 排除频段边界的比例
            'min_anomaly_span': 800,            # 最小异常频率跨度(Hz)
            'energy_threshold_sigma': 1.8,      # 能量阈值
            'min_affected_bands': 0.12,         # 最少影响频段比例
            'min_relative_increase': 2.5,       # 最小相对增幅
            
            # 关键：基于频次的判断参数
            'min_anomaly_frequency': 0.3,       # 最小异常频次 (30%的频段有异常)
            'high_frequency_threshold': 0.5,    # 高频次异常阈值 (50%以上)
            'low_frequency_threshold': 0.1      # 低频次异常阈值 (10%以下)
        }
        
        print(f"基于异常频次的瞬时检测器初始化完成")
        print(f"关键参数: 最小异常频次={self.detection_params['min_anomaly_frequency']*100:.0f}%")
        print(f"高频次阈值={self.detection_params['high_frequency_threshold']*100:.0f}%, 低频次阈值={self.detection_params['low_frequency_threshold']*100:.0f}%")
    
    def detect_transient_anomalies(self, audio_path):
        """基于异常频次检测瞬时异常"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            if len(y) == 0:
                return self._get_error_result(audio_path, "音频文件为空")
            
            # 标准化
            if np.max(np.abs(y)) > 0:
                y = y / np.max(np.abs(y)) * 0.9
            
            # 短时傅里叶变换
            frequencies, times, Zxx = stft(
                y, sr, 
                nperseg=self.stft_params['nperseg'],
                noverlap=self.stft_params['noverlap'],
                window=self.stft_params['window'],
                nfft=self.stft_params['nfft']
            )
            
            # 计算功率谱
            power_spectrogram = np.abs(Zxx) ** 2
            
            # 频率范围筛选
            freq_mask = (frequencies >= self.detection_params['frequency_range'][0]) & \
                       (frequencies <= self.detection_params['frequency_range'][1])
            
            frequencies = frequencies[freq_mask]
            power_spectrogram = power_spectrogram[freq_mask, :]
            
            # 检测是否为扫频文件
            is_sweep_file = self._is_sweep_file(audio_path)
            
            # 分析异常频次
            frequency_analysis = self._analyze_anomaly_frequency(
                power_spectrogram, frequencies, times, is_sweep_file
            )
            
            # 基于频次判断是否为瞬时异常
            anomaly_score, anomaly_detected = self._frequency_based_decision(
                frequency_analysis, is_sweep_file
            )
            
            # 计算置信度
            confidence = min(0.99, anomaly_score) if anomaly_detected else max(0.01, 1 - anomaly_score)
            
            return {
                'file_path': audio_path,
                'anomaly_detected': anomaly_detected,
                'confidence': confidence,
                'anomaly_score': anomaly_score,
                'is_sweep_file': is_sweep_file,
                'frequency_analysis': frequency_analysis,
                'detection_method': 'frequency_based_transient',
                'error': False
            }
            
        except Exception as e:
            return self._get_error_result(audio_path, str(e))
    
    def _is_sweep_file(self, audio_path):
        """判断是否为扫频文件"""
        filename = os.path.basename(audio_path).lower()
        sweep_keywords = ['扫频', 'sweep', 'chirp', '100hz', '20000hz']
        return any(keyword in filename for keyword in sweep_keywords)
    
    def _analyze_anomaly_frequency(self, power_spectrogram, frequencies, times, is_sweep_file):
        """分析异常出现频次"""
        analysis = {
            'total_segments': 0,
            'anomalous_segments': 0,
            'anomaly_frequency_ratio': 0.0,
            'avg_anomaly_intensity': 0.0,
            'avg_frequency_span': 0.0,
            'anomaly_distribution': [],
            'segment_details': []
        }
        
        if is_sweep_file:
            # 扫频文件：分段分析
            segment_duration = self.detection_params['segment_duration']
            total_duration = times[-1]
            num_segments = int(total_duration / segment_duration)
            
            analysis['total_segments'] = num_segments
            
            anomaly_intensities = []
            frequency_spans = []
            
            for seg_idx in range(num_segments):
                seg_start_time = seg_idx * segment_duration
                seg_end_time = (seg_idx + 1) * segment_duration
                
                # 找到对应的时间索引
                seg_start_idx = np.argmin(np.abs(times - seg_start_time))
                seg_end_idx = np.argmin(np.abs(times - seg_end_time))
                
                # 排除频段边界
                edge_exclude = int((seg_end_idx - seg_start_idx) * self.detection_params['edge_exclude_ratio'])
                seg_core_start = seg_start_idx + edge_exclude
                seg_core_end = seg_end_idx - edge_exclude
                
                if seg_core_end <= seg_core_start:
                    continue
                
                # 分析频段核心部分
                segment_result = self._analyze_segment_anomalies(
                    power_spectrogram[:, seg_core_start:seg_core_end],
                    frequencies, times[seg_core_start:seg_core_end],
                    seg_idx
                )
                
                if segment_result['has_anomaly']:
                    analysis['anomalous_segments'] += 1
                    anomaly_intensities.append(segment_result['max_intensity'])
                    frequency_spans.append(segment_result['frequency_span'])
                    
                    analysis['segment_details'].append(segment_result)
                
                analysis['anomaly_distribution'].append(segment_result['has_anomaly'])
            
            # 计算异常频次比例
            if analysis['total_segments'] > 0:
                analysis['anomaly_frequency_ratio'] = analysis['anomalous_segments'] / analysis['total_segments']
            
            # 计算平均值
            if anomaly_intensities:
                analysis['avg_anomaly_intensity'] = np.mean(anomaly_intensities)
                analysis['avg_frequency_span'] = np.mean(frequency_spans)
        
        else:
            # 非扫频文件：整体分析
            analysis['total_segments'] = 1
            
            segment_result = self._analyze_segment_anomalies(
                power_spectrogram, frequencies, times, 0
            )
            
            if segment_result['has_anomaly']:
                analysis['anomalous_segments'] = 1
                analysis['anomaly_frequency_ratio'] = 1.0
                analysis['avg_anomaly_intensity'] = segment_result['max_intensity']
                analysis['avg_frequency_span'] = segment_result['frequency_span']
                analysis['segment_details'].append(segment_result)
            else:
                analysis['anomaly_frequency_ratio'] = 0.0
        
        return analysis
    
    def _analyze_segment_anomalies(self, segment_power, frequencies, segment_times, seg_idx):
        """分析单个频段的异常"""
        result = {
            'segment_index': seg_idx,
            'has_anomaly': False,
            'max_intensity': 0.0,
            'frequency_span': 0.0,
            'affected_bands_ratio': 0.0,
            'anomaly_count': 0
        }
        
        # 计算每个时间片的总能量
        time_energy = np.sum(segment_power, axis=0)
        
        if len(time_energy) == 0:
            return result
        
        # 计算基线和阈值
        baseline_energy = np.percentile(time_energy, 25)
        energy_std = np.std(time_energy)
        threshold = baseline_energy + self.detection_params['energy_threshold_sigma'] * energy_std
        
        # 检测能量突增
        burst_indices = np.where(time_energy > threshold)[0]
        
        if len(burst_indices) == 0:
            return result
        
        # 分析突增的频率特征
        valid_anomalies = 0
        max_intensity = 0.0
        max_freq_span = 0.0
        max_affected_ratio = 0.0
        
        for burst_idx in burst_indices:
            burst_power = segment_power[:, burst_idx]
            
            # 计算基线功率
            baseline_mask = np.ones(segment_power.shape[1], dtype=bool)
            baseline_mask[max(0, burst_idx-3):min(len(time_energy), burst_idx+4)] = False
            if np.sum(baseline_mask) > 0:
                baseline_power = np.mean(segment_power[:, baseline_mask], axis=1)
            else:
                baseline_power = np.mean(segment_power, axis=1)
            
            # 计算相对增幅
            relative_increase = (burst_power - baseline_power) / (baseline_power + 1e-12)
            
            # 检测受影响的频段
            significant_mask = relative_increase > self.detection_params['min_relative_increase']
            affected_ratio = np.sum(significant_mask) / len(frequencies)
            
            # 计算频率跨度
            if np.any(significant_mask):
                affected_freqs = frequencies[significant_mask]
                freq_span = np.max(affected_freqs) - np.min(affected_freqs)
                
                # 检查是否满足异常条件
                if (affected_ratio >= self.detection_params['min_affected_bands'] and 
                    freq_span >= self.detection_params['min_anomaly_span']):
                    
                    valid_anomalies += 1
                    max_intensity = max(max_intensity, np.max(relative_increase))
                    max_freq_span = max(max_freq_span, freq_span)
                    max_affected_ratio = max(max_affected_ratio, affected_ratio)
        
        # 更新结果
        if valid_anomalies > 0:
            result['has_anomaly'] = True
            result['max_intensity'] = max_intensity
            result['frequency_span'] = max_freq_span
            result['affected_bands_ratio'] = max_affected_ratio
            result['anomaly_count'] = valid_anomalies
        
        return result
    
    def _frequency_based_decision(self, frequency_analysis, is_sweep_file):
        """基于异常频次做出判断"""
        anomaly_freq_ratio = frequency_analysis['anomaly_frequency_ratio']
        
        if is_sweep_file:
            # 扫频文件：基于异常频次判断
            if anomaly_freq_ratio >= self.detection_params['high_frequency_threshold']:
                # 高频次异常 - 很可能是真正的瞬时异常
                base_score = 0.8
                intensity_bonus = min(0.15, frequency_analysis['avg_anomaly_intensity'] / 10000.0)
                span_bonus = min(0.05, frequency_analysis['avg_frequency_span'] / 5000.0)
                anomaly_score = base_score + intensity_bonus + span_bonus
                anomaly_detected = True
                
            elif anomaly_freq_ratio >= self.detection_params['min_anomaly_frequency']:
                # 中等频次异常 - 可能是瞬时异常
                base_score = 0.5
                freq_bonus = (anomaly_freq_ratio - self.detection_params['min_anomaly_frequency']) * 0.6
                intensity_bonus = min(0.1, frequency_analysis['avg_anomaly_intensity'] / 15000.0)
                anomaly_score = base_score + freq_bonus + intensity_bonus
                anomaly_detected = anomaly_score > 0.5
                
            else:
                # 低频次异常 - 可能是正常的频段转换
                anomaly_score = anomaly_freq_ratio * 0.3
                anomaly_detected = False
        
        else:
            # 非扫频文件：更严格的判断
            if frequency_analysis['anomalous_segments'] > 0:
                # 有异常，但需要检查是否为真正的瞬时异常
                intensity = frequency_analysis['avg_anomaly_intensity']
                freq_span = frequency_analysis['avg_frequency_span']
                
                # 只有强度很高且频率跨度很大才认为是瞬时异常
                if intensity > 5000 and freq_span > 2000:
                    anomaly_score = min(0.9, 0.4 + intensity/20000.0 + freq_span/10000.0)
                    anomaly_detected = True
                else:
                    # 可能是持续性异常，不是瞬时异常
                    anomaly_score = 0.2
                    anomaly_detected = False
            else:
                anomaly_score = 0.0
                anomaly_detected = False
        
        return anomaly_score, anomaly_detected
    
    def _get_error_result(self, audio_path, error_msg):
        """返回错误结果"""
        return {
            'file_path': audio_path,
            'anomaly_detected': False,
            'confidence': 0.0,
            'anomaly_score': 0.0,
            'is_sweep_file': False,
            'frequency_analysis': {},
            'detection_method': 'frequency_based_transient',
            'error': True,
            'error_message': error_msg
        }
    
    def test_negative_samples(self):
        """测试负样本"""
        print("\n" + "="*80)
        print("基于异常频次的瞬时检测 - 负样本测试")
        print("="*80)
        
        # 测试文件列表
        test_files = [
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153632.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_153842_高低音互换.wav",
            "../test20250717/neg/录音_步进扫频_100Hz至20000Hz_20250714_155101.wav",
            "../test20250717/neg/主板隔音eva取消.wav",
            "../test20250717/neg/主板隔音eva取消_1.wav",
            "../test20250717/neg/喇叭eva没贴.wav",
            "../test20250717/neg/喇叭eva没贴_1.wav"
        ]
        
        results = []
        for file_path in test_files:
            if os.path.exists(file_path):
                print(f"\n测试文件: {os.path.basename(file_path)}")
                print("-" * 50)
                
                result = self.detect_transient_anomalies(file_path)
                result['filename'] = os.path.basename(file_path)
                results.append(result)
                
                # 显示结果
                if not result['error']:
                    status = "异常" if result['anomaly_detected'] else "正常"
                    file_type = "扫频文件" if result['is_sweep_file'] else "非扫频文件"
                    
                    print(f"文件类型: {file_type}")
                    print(f"检测结果: {status}")
                    print(f"异常分数: {result['anomaly_score']:.3f}")
                    print(f"置信度: {result['confidence']:.1%}")
                    
                    analysis = result['frequency_analysis']
                    print(f"总频段数: {analysis.get('total_segments', 0)}")
                    print(f"异常频段数: {analysis.get('anomalous_segments', 0)}")
                    print(f"异常频次比例: {analysis.get('anomaly_frequency_ratio', 0)*100:.1f}%")
                    print(f"平均异常强度: {analysis.get('avg_anomaly_intensity', 0):.1f}")
                    print(f"平均频率跨度: {analysis.get('avg_frequency_span', 0):.0f}Hz")
                else:
                    print(f"处理失败: {result['error_message']}")
            else:
                print(f"文件不存在: {file_path}")
        
        # 统计结果
        valid_results = [r for r in results if not r['error']]
        sweep_files = [r for r in valid_results if r['is_sweep_file']]
        non_sweep_files = [r for r in valid_results if not r['is_sweep_file']]
        
        sweep_anomalies = [r for r in sweep_files if r['anomaly_detected']]
        non_sweep_anomalies = [r for r in non_sweep_files if r['anomaly_detected']]
        
        print(f"\n" + "="*60)
        print(f"基于异常频次的检测统计:")
        print(f"  扫频文件: {len(sweep_files)}个, 检出异常: {len(sweep_anomalies)}个 ({len(sweep_anomalies)/len(sweep_files)*100:.1f}%)" if sweep_files else "  扫频文件: 0个")
        print(f"  非扫频文件: {len(non_sweep_files)}个, 检出异常: {len(non_sweep_anomalies)}个 ({len(non_sweep_anomalies)/len(non_sweep_files)*100:.1f}%)" if non_sweep_files else "  非扫频文件: 0个")
        print(f"  总体检出率: {(len(sweep_anomalies)+len(non_sweep_anomalies))/len(valid_results)*100:.1f}%" if valid_results else "  总体检出率: 0%")
        
        return results

def main():
    """主函数"""
    # 初始化检测器
    detector = FrequencyBasedTransientDetector()
    
    # 测试负样本
    results = detector.test_negative_samples()
    
    # 保存结果
    results_data = []
    for result in results:
        if not result['error']:
            analysis = result['frequency_analysis']
            row = {
                'filename': result['filename'],
                'is_sweep_file': result['is_sweep_file'],
                'anomaly_detected': result['anomaly_detected'],
                'confidence': result['confidence'],
                'anomaly_score': result['anomaly_score'],
                'total_segments': analysis.get('total_segments', 0),
                'anomalous_segments': analysis.get('anomalous_segments', 0),
                'anomaly_frequency_ratio': analysis.get('anomaly_frequency_ratio', 0),
                'avg_anomaly_intensity': analysis.get('avg_anomaly_intensity', 0),
                'avg_frequency_span': analysis.get('avg_frequency_span', 0),
                'error': result['error']
            }
            results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    results_df.to_csv('frequency_based_transient_results.csv', index=False)
    
    print(f"\n基于异常频次的检测结果已保存: frequency_based_transient_results.csv")
    
    return detector, results

if __name__ == "__main__":
    detector, results = main()
