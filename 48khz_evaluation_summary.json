{"best_single_feature": {"feature": "mfcc_0_std", "accuracy": 0.9285714285714286, "threshold": 46.36913103125001, "strategy": "mean_split", "pos_mean": 36.9542564375, "neg_mean": 55.784005625000006, "separation": 1.8284705700566042}, "best_combination": {"combination": "Comprehensive", "features": ["mfcc_0_std", "mfcc_0_delta_mean", "ultra_high_energy_variance", "low_mid_bandwidth", "high_freq_sweep_quality"], "feature_count": 5, "cv_accuracy": 0.9113060428849901, "cv_std": 0.048946898750447256}, "best_algorithm": {"algorithm": "SVM (Linear)", "cv_accuracy": 0.9651515151515152, "cv_std": 0.04274768478686633, "min_accuracy": 0.9090909090909091, "max_accuracy": 1.0}, "optimal_features": ["mfcc_0_std", "mfcc_0_delta_mean", "mfcc_0_mean", "ultra_high_energy_variance", "high_freq_sweep_quality"]}