#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析neg文件夹样本并与pos样本对比
"""

import os
import glob
import re
import numpy as np

def extract_diff_stats_from_summary(summary_file):
    """
    从汇总文件中提取差值统计信息
    """
    if not os.path.exists(summary_file):
        return None
    
    try:
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        stats = {}
        
        # 提取各种差值统计
        patterns = {
            'min_avg_diff': r'平均差值最小值:\s*([\d.]+)\s*dB',
            'max_avg_diff': r'平均差值最大值:\s*([\d.]+)\s*dB',
            'mean_avg_diff': r'平均差值均值:\s*([\d.]+)\s*dB',
            'std_avg_diff': r'平均差值标准差:\s*([\d.]+)\s*dB',
            'mean_max_diff': r'最大差值均值:\s*([\d.]+)\s*dB'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                stats[key] = float(match.group(1))
        
        # 提取最大差值范围
        pattern = r'最大差值范围:\s*([\d.]+)\s*-\s*([\d.]+)\s*dB'
        match = re.search(pattern, content)
        if match:
            stats['min_max_diff'] = float(match.group(1))
            stats['max_max_diff'] = float(match.group(2))
        
        return stats if stats else None
        
    except Exception as e:
        print(f"Error reading {summary_file}: {e}")
        return None

def analyze_neg_samples():
    """
    分析neg样本的差值统计
    """
    print("分析neg文件夹样本的差值统计")
    print("="*60)
    
    # 查找当前目录下的neg样本分析结果
    neg_analysis_dirs = []
    
    # 检查可能的neg样本分析目录
    potential_neg_dirs = [
        "主板隔音eva取消_梅尔频谱分析",
        "喇叭eva没贴_梅尔频谱分析", 
        "录音_步进扫频_100Hz至20000Hz_20250714_153743_低音戳洞_梅尔频谱分析"
    ]
    
    for dir_name in potential_neg_dirs:
        if os.path.exists(dir_name):
            neg_analysis_dirs.append(dir_name)
    
    if not neg_analysis_dirs:
        print("未找到neg样本分析结果")
        return {}
    
    print(f"找到 {len(neg_analysis_dirs)} 个neg样本分析结果")
    
    neg_statistics = {}
    
    for analysis_dir in neg_analysis_dirs:
        summary_file = os.path.join(analysis_dir, "频谱分析汇总.txt")
        dir_name = os.path.basename(analysis_dir)
        
        print(f"\n检查: {dir_name}")
        
        stats = extract_diff_stats_from_summary(summary_file)
        
        if stats:
            print(f"  ✅ 找到差值统计")
            print(f"     平均差值范围: {stats.get('min_avg_diff', 'N/A')} - {stats.get('max_avg_diff', 'N/A')} dB")
            print(f"     平均差值均值: {stats.get('mean_avg_diff', 'N/A')} dB")
            
            neg_statistics[dir_name] = stats
        else:
            print(f"  ❌ 未找到差值统计")
    
    return neg_statistics

def analyze_pos_samples():
    """
    分析pos样本的差值统计
    """
    print("\n分析pos样本的差值统计")
    print("="*60)
    
    # 查找当前目录下的pos样本分析结果
    pos_analysis_dirs = []
    
    potential_pos_dirs = [
        "ok1_梅尔频谱分析",
        "sd1_梅尔频谱分析",
        "tw1_梅尔频谱分析",
        "zjb6_1_梅尔频谱分析"
    ]
    
    for dir_name in potential_pos_dirs:
        if os.path.exists(dir_name):
            pos_analysis_dirs.append(dir_name)
    
    print(f"找到 {len(pos_analysis_dirs)} 个pos样本分析结果")
    
    pos_statistics = {}
    
    for analysis_dir in pos_analysis_dirs:
        summary_file = os.path.join(analysis_dir, "频谱分析汇总.txt")
        dir_name = os.path.basename(analysis_dir)
        
        stats = extract_diff_stats_from_summary(summary_file)
        
        if stats:
            pos_statistics[dir_name] = stats
    
    return pos_statistics

def compare_neg_vs_pos(neg_stats, pos_stats):
    """
    对比neg和pos样本的差值统计
    """
    print(f"\n" + "="*60)
    print("NEG vs POS 样本对比分析")
    print("="*60)
    
    if not neg_stats:
        print("❌ 没有neg样本数据进行对比")
        return
    
    if not pos_stats:
        print("❌ 没有pos样本数据进行对比")
        return
    
    # 收集数据
    neg_max_avg_diffs = [s['max_avg_diff'] for s in neg_stats.values() if 'max_avg_diff' in s]
    neg_mean_avg_diffs = [s['mean_avg_diff'] for s in neg_stats.values() if 'mean_avg_diff' in s]
    
    pos_max_avg_diffs = [s['max_avg_diff'] for s in pos_stats.values() if 'max_avg_diff' in s]
    pos_mean_avg_diffs = [s['mean_avg_diff'] for s in pos_stats.values() if 'mean_avg_diff' in s]
    
    print(f"\n📊 统计对比:")
    print(f"{'指标':<20} {'NEG样本':<15} {'POS样本':<15} {'差异'}")
    print("-" * 65)
    
    if neg_max_avg_diffs and pos_max_avg_diffs:
        neg_max_mean = np.mean(neg_max_avg_diffs)
        pos_max_mean = np.mean(pos_max_avg_diffs)
        diff_max = neg_max_mean - pos_max_mean
        print(f"{'最大平均差值均值':<20} {neg_max_mean:<15.2f} {pos_max_mean:<15.2f} {diff_max:+.2f}")
        
        neg_max_range = f"{min(neg_max_avg_diffs):.2f}-{max(neg_max_avg_diffs):.2f}"
        pos_max_range = f"{min(pos_max_avg_diffs):.2f}-{max(pos_max_avg_diffs):.2f}"
        print(f"{'最大平均差值范围':<20} {neg_max_range:<15} {pos_max_range:<15}")
    
    if neg_mean_avg_diffs and pos_mean_avg_diffs:
        neg_mean_mean = np.mean(neg_mean_avg_diffs)
        pos_mean_mean = np.mean(pos_mean_avg_diffs)
        diff_mean = neg_mean_mean - pos_mean_mean
        print(f"{'平均差值均值':<20} {neg_mean_mean:<15.2f} {pos_mean_mean:<15.2f} {diff_mean:+.2f}")
    
    # 详细样本对比
    print(f"\n📋 详细样本对比:")
    print(f"{'样本类型':<8} {'样本名称':<40} {'最大平均差值':<12} {'均值'}")
    print("-" * 80)
    
    # NEG样本
    for name, stats in neg_stats.items():
        sample_type = "NEG"
        max_avg = stats.get('max_avg_diff', 0)
        mean_avg = stats.get('mean_avg_diff', 0)
        print(f"{sample_type:<8} {name:<40} {max_avg:<12.2f} {mean_avg:.2f}")
    
    print("-" * 80)
    
    # POS样本
    for name, stats in pos_stats.items():
        sample_type = "POS"
        max_avg = stats.get('max_avg_diff', 0)
        mean_avg = stats.get('mean_avg_diff', 0)
        print(f"{sample_type:<8} {name:<40} {max_avg:<12.2f} {mean_avg:.2f}")
    
    # 分析结论
    print(f"\n🔍 分析结论:")
    
    if neg_max_avg_diffs and pos_max_avg_diffs:
        if np.mean(neg_max_avg_diffs) > np.mean(pos_max_avg_diffs):
            print("  ❗ NEG样本的平均差值反而比POS样本更大")
            print("     这可能表明:")
            print("     - NEG样本存在异常的高频成分")
            print("     - 故障导致了额外的信号成分")
            print("     - 需要进一步分析频率分布特征")
        else:
            print("  ✅ POS样本的平均差值比NEG样本更大")
            print("     这符合预期，表明:")
            print("     - POS样本信号质量更好")
            print("     - NEG样本信号衰减或失真")
    
    # 异常检测建议
    print(f"\n💡 异常检测建议:")
    if neg_max_avg_diffs and pos_max_avg_diffs:
        pos_threshold = np.mean(pos_max_avg_diffs) - 2 * np.std(pos_max_avg_diffs)
        print(f"  建议异常检测阈值: < {pos_threshold:.2f} dB")
        print(f"  基于POS样本均值减去2倍标准差")

def main():
    """
    主函数
    """
    # 分析neg样本
    neg_stats = analyze_neg_samples()
    
    # 分析pos样本
    pos_stats = analyze_pos_samples()
    
    # 对比分析
    compare_neg_vs_pos(neg_stats, pos_stats)

if __name__ == "__main__":
    main()
